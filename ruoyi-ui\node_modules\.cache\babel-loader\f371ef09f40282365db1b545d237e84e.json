{"remainingRequest": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\src\\views\\suppPunishment\\materialInfo-module.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\src\\views\\suppPunishment\\materialInfo-module.vue", "mtime": 1755499162058}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\babel.config.js", "mtime": 1688548084091}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_materialInfo", "require", "name", "data", "visible", "loading", "total", "materialList", "queryParams", "pageNum", "pageSize", "itemId", "itemName", "methods", "show", "reset<PERSON><PERSON>y", "getList", "hide", "handleClose", "reset", "_this", "listMaterialInfo", "then", "response", "rows", "catch", "handleQuery", "resetForm", "handleRowClick", "row", "handleRowDoubleClick", "handleSelect", "$emit"], "sources": ["src/views/suppPunishment/materialInfo-module.vue"], "sourcesContent": ["<template>\r\n  <el-dialog\r\n    title=\"物料信息查询\"\r\n    :visible.sync=\"visible\"\r\n    width=\"900px\"\r\n    append-to-body\r\n    @close=\"handleClose\"\r\n  >\r\n    <!-- 查询条件 -->\r\n    <el-form :model=\"queryParams\" ref=\"queryForm\" :inline=\"true\" label-width=\"100px\">\r\n      <el-form-item label=\"物料小类代码\" prop=\"itemId\">\r\n        <el-input\r\n          v-model=\"queryParams.itemId\"\r\n          placeholder=\"请输入物料小类代码\"\r\n          clearable\r\n          size=\"small\"\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"物料小类名称\" prop=\"itemName\">\r\n        <el-input\r\n          v-model=\"queryParams.itemName\"\r\n          placeholder=\"请输入物料小类名称\"\r\n          clearable\r\n          size=\"small\"\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item >\r\n        <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleQuery\">搜索</el-button>\r\n        <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\r\n      </el-form-item>\r\n    </el-form>\r\n\r\n    <!-- 物料列表 -->\r\n    <el-table \r\n      v-loading=\"loading\" \r\n      :data=\"materialList\" \r\n      @row-click=\"handleRowClick\"\r\n      @row-dblclick=\"handleRowDoubleClick\"\r\n      highlight-current-row\r\n      style=\"cursor: pointer;\"\r\n    >\r\n      <el-table-column label=\"物料小类代码\" align=\"center\" prop=\"itemId\" width=\"150\" />\r\n      <el-table-column label=\"物料小类名称\" align=\"center\" prop=\"itemName\" />\r\n      <el-table-column label=\"操作\" align=\"center\" width=\"100\">\r\n        <template slot-scope=\"scope\">\r\n          <el-button\r\n            size=\"mini\"\r\n            type=\"text\"\r\n            icon=\"el-icon-check\"\r\n            @click=\"handleSelect(scope.row)\"\r\n          >选择</el-button>\r\n        </template>\r\n      </el-table-column>\r\n    </el-table>\r\n\r\n    <!-- 分页 -->\r\n    <pagination\r\n      v-show=\"total > 0\"\r\n      :total=\"total\"\r\n      :page.sync=\"queryParams.pageNum\"\r\n      :limit.sync=\"queryParams.pageSize\"\r\n      @pagination=\"getList\"\r\n    />\r\n\r\n    <!-- 底部按钮 -->\r\n    <div slot=\"footer\" class=\"dialog-footer\">\r\n      <el-button @click=\"handleClose\">取 消</el-button>\r\n    </div>\r\n  </el-dialog>\r\n</template>\r\n\r\n<script>\r\nimport { listMaterialInfo } from \"@/api/suppPunishment/materialInfo\";\r\n\r\nexport default {\r\n  name: \"MaterialInfoDialog\",\r\n  data() {\r\n    return {\r\n      // 是否显示弹出层\r\n      visible: false,\r\n      // 遮罩层\r\n      loading: true,\r\n      // 总条数\r\n      total: 0,\r\n      // 物料信息表格数据\r\n      materialList: [],\r\n      // 查询参数\r\n      queryParams: {\r\n        pageNum: 1,\r\n        pageSize: 10,\r\n        itemId: null,\r\n        itemName: null\r\n      }\r\n    };\r\n  },\r\n  methods: {\r\n    /** 显示弹窗 */\r\n    show() {\r\n      this.visible = true;\r\n      this.resetQuery();\r\n      this.getList();\r\n    },\r\n    \r\n    /** 隐藏弹窗 */\r\n    hide() {\r\n      this.visible = false;\r\n    },\r\n    \r\n    /** 关闭弹窗 */\r\n    handleClose() {\r\n      this.visible = false;\r\n      this.reset();\r\n    },\r\n    \r\n    /** 重置数据 */\r\n    reset() {\r\n      this.materialList = [];\r\n      this.total = 0;\r\n      this.queryParams = {\r\n        pageNum: 1,\r\n        pageSize: 10,\r\n        itemId: null,\r\n        itemName: null\r\n      };\r\n    },\r\n    \r\n    /** 查询物料信息列表 */\r\n    getList() {\r\n      this.loading = true;\r\n      listMaterialInfo(this.queryParams).then(response => {\r\n        this.materialList = response.rows;\r\n        this.total = response.total;\r\n        this.loading = false;\r\n      }).catch(() => {\r\n        this.loading = false;\r\n      });\r\n    },\r\n    \r\n    /** 搜索按钮操作 */\r\n    handleQuery() {\r\n      this.queryParams.pageNum = 1;\r\n      this.getList();\r\n    },\r\n    \r\n    /** 重置按钮操作 */\r\n    resetQuery() {\r\n      this.resetForm(\"queryForm\");\r\n      this.handleQuery();\r\n    },\r\n    \r\n    /** 行点击事件 */\r\n    handleRowClick(row) {\r\n      // 可以在这里添加行选中效果\r\n    },\r\n    \r\n    /** 行双击事件 */\r\n    handleRowDoubleClick(row) {\r\n      this.handleSelect(row);\r\n    },\r\n    \r\n    /** 选择物料 */\r\n    handleSelect(row) {\r\n      this.$emit('select', row);\r\n      this.handleClose();\r\n    }\r\n  }\r\n};\r\n</script>\r\n\r\n<style scoped>\r\n/* 表格行悬停效果 */\r\n::v-deep .el-table tbody tr:hover {\r\n  background-color: #f5f7fa;\r\n}\r\n\r\n/* 查询表单样式 - 输入框左对齐，按钮右对齐 */\r\n.el-form--inline {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 20px;\r\n}\r\n\r\n/* 输入框区域左对齐 */\r\n.el-form--inline .el-form-item:not(:last-child) {\r\n  margin-right: 15px;\r\n  margin-bottom: 0;\r\n}\r\n\r\n/* 按钮区域右对齐 */\r\n.el-form--inline .el-form-item:last-child {\r\n  margin-left: auto;\r\n  margin-right: 0;\r\n  margin-bottom: 0;\r\n}\r\n\r\n/* 统一输入框宽度 */\r\n.el-form--inline .el-form-item .el-input {\r\n  width: 200px;\r\n}\r\n\r\n/* 弹窗标题居中 */\r\n::v-deep .el-dialog__header {\r\n  text-align: center;\r\n}\r\n\r\n::v-deep .el-dialog__title {\r\n  text-align: center;\r\n  width: 100%;\r\n  display: block;\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;AA0EA,IAAAA,aAAA,GAAAC,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCAEA;EACAC,IAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACA;MACAC,OAAA;MACA;MACAC,OAAA;MACA;MACAC,KAAA;MACA;MACAC,YAAA;MACA;MACAC,WAAA;QACAC,OAAA;QACAC,QAAA;QACAC,MAAA;QACAC,QAAA;MACA;IACA;EACA;EACAC,OAAA;IACA,WACAC,IAAA,WAAAA,KAAA;MACA,KAAAV,OAAA;MACA,KAAAW,UAAA;MACA,KAAAC,OAAA;IACA;IAEA,WACAC,IAAA,WAAAA,KAAA;MACA,KAAAb,OAAA;IACA;IAEA,WACAc,WAAA,WAAAA,YAAA;MACA,KAAAd,OAAA;MACA,KAAAe,KAAA;IACA;IAEA,WACAA,KAAA,WAAAA,MAAA;MACA,KAAAZ,YAAA;MACA,KAAAD,KAAA;MACA,KAAAE,WAAA;QACAC,OAAA;QACAC,QAAA;QACAC,MAAA;QACAC,QAAA;MACA;IACA;IAEA,eACAI,OAAA,WAAAA,QAAA;MAAA,IAAAI,KAAA;MACA,KAAAf,OAAA;MACA,IAAAgB,8BAAA,OAAAb,WAAA,EAAAc,IAAA,WAAAC,QAAA;QACAH,KAAA,CAAAb,YAAA,GAAAgB,QAAA,CAAAC,IAAA;QACAJ,KAAA,CAAAd,KAAA,GAAAiB,QAAA,CAAAjB,KAAA;QACAc,KAAA,CAAAf,OAAA;MACA,GAAAoB,KAAA;QACAL,KAAA,CAAAf,OAAA;MACA;IACA;IAEA,aACAqB,WAAA,WAAAA,YAAA;MACA,KAAAlB,WAAA,CAAAC,OAAA;MACA,KAAAO,OAAA;IACA;IAEA,aACAD,UAAA,WAAAA,WAAA;MACA,KAAAY,SAAA;MACA,KAAAD,WAAA;IACA;IAEA,YACAE,cAAA,WAAAA,eAAAC,GAAA;MACA;IAAA,CACA;IAEA,YACAC,oBAAA,WAAAA,qBAAAD,GAAA;MACA,KAAAE,YAAA,CAAAF,GAAA;IACA;IAEA,WACAE,YAAA,WAAAA,aAAAF,GAAA;MACA,KAAAG,KAAA,WAAAH,GAAA;MACA,KAAAX,WAAA;IACA;EACA;AACA", "ignoreList": []}]}