{"remainingRequest": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??vue-loader-options!E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\src\\views\\leave\\plan\\edit.vue?vue&type=template&id=7971f6a4&scoped=true", "dependencies": [{"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\src\\views\\leave\\plan\\edit.vue", "mtime": 1755499098435}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}]}