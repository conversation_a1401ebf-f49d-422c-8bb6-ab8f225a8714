{"remainingRequest": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\src\\views\\qualityCost\\companySummary1\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\src\\views\\qualityCost\\companySummary1\\index.vue", "mtime": 1755499162051}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\babel.config.js", "mtime": 1688548084091}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_companySummary", "require", "_testData", "name", "data", "loading", "qualityCostList", "mergeCells", "tableHeight", "needScrollbar", "selected<PERSON>ear", "Date", "getFullYear", "yearOptions", "selectedType", "typeOptions", "computed", "containerStyle", "height", "concat", "tableContainerStyle", "mounted", "calculateTableHeight", "window", "addEventListener", "generateYearOptions", "<PERSON><PERSON><PERSON><PERSON>", "removeEventListener", "methods", "getCompanySummaryData", "_this", "params", "yearMonth", "companySummaryType", "listCompanySummaryData", "then", "response", "console", "log", "processFactoryData", "catch", "error", "$message", "rawData", "isFactoryDataFormat", "cleanedData", "cleanMockData", "tableData", "convertFactoryDataToTable", "updateTableWithFactoryData", "_typeof2", "default", "factoryKeys", "Object", "keys", "length", "firstFactory", "month<PERSON>eys", "firstMonth", "_this2", "for<PERSON>ach", "factoryName", "factoryData", "validMonths", "<PERSON><PERSON><PERSON>", "monthData", "isMockData", "costCenterName", "costEx", "costTon", "factoryMonths", "rowData", "month", "push", "factoryNames", "_toConsumableArray2", "Set", "map", "row", "company", "months", "index", "monthNumber", "find", "toString", "totalAmount", "reduce", "sum", "totalPerTon", "formatCurrency", "value", "undefined", "Number", "num", "toLocaleString", "minimumFractionDigits", "maximumFractionDigits", "formatTon", "_this3", "$nextTick", "windowHeight", "innerHeight", "availableHeight", "Math", "max", "min", "currentYear", "i", "handleYearChange", "year", "handleTypeChange", "type", "testFactoryDataProcessing", "_this4", "testFactoryData", "setTimeout", "testFactoryDataWithRealValues", "testDataMapping", "verifyDataMapping", "januaryAmount", "january<PERSON><PERSON><PERSON><PERSON>", "februaryAmount", "februaryPerTon", "marchAmount", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "sources": ["src/views/qualityCost/companySummary1/index.vue"], "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <!-- 质量成本表格 -->\r\n    <div :style=\"containerStyle\">\r\n      <!-- 标题和产量信息 -->\r\n      <div style=\"position: relative; width: 100%; display: flex; justify-content: center; margin: 20px 0;\">\r\n        <h3 style=\"text-align: center; margin: 0;\">各公司质量成本汇总表</h3>\r\n        <div style=\"position: absolute; right: 0; bottom: -5px; font-size: 16px; font-weight: bold; color: #333;\">\r\n          <!-- 类型选择框 -->\r\n          <el-select \r\n            v-model=\"selectedType\" \r\n            placeholder=\"选择类型\" \r\n            style=\"width: 100px; margin-right: 10px;\"\r\n            @change=\"handleTypeChange\">\r\n            <el-option\r\n              v-for=\"type in typeOptions\"\r\n              :key=\"type\"\r\n              :label=\"type\"\r\n              :value=\"type\">\r\n            </el-option>\r\n          </el-select>\r\n          <!-- 年份选择框 -->\r\n          <el-select \r\n            v-model=\"selectedYear\" \r\n            placeholder=\"选择年份\" \r\n            style=\"width: 100px; margin-right: 20px;\"\r\n            @change=\"handleYearChange\">\r\n            <el-option\r\n              v-for=\"year in yearOptions\"\r\n              :key=\"year\"\r\n              :label=\"year + '年'\"\r\n              :value=\"year\">\r\n            </el-option>\r\n          </el-select>\r\n          <!-- <span style=\"margin-right: 20px;\">单位：元</span> -->\r\n          <el-button type=\"info\" size=\"mini\" @click=\"testFactoryDataProcessing\" style=\"margin-left: 10px;\">测试分厂数据</el-button>\r\n        </div>\r\n      </div>\r\n\r\n      <div :style=\"tableContainerStyle\">\r\n        <vxe-table\r\n          :loading=\"loading\"\r\n          :data=\"qualityCostList\"\r\n          border\r\n          v-bind=\"tableHeight ? { height: tableHeight } : {}\"\r\n          :class=\"{ 'no-scroll': !needScrollbar }\"\r\n          style=\"table-layout: fixed; width: 100%; margin: 0 auto; padding: 0;\"\r\n          :header-cell-style=\"{ backgroundColor: '#f5f7fa', color: '#303133' }\"\r\n          :merge-cells=\"mergeCells\"\r\n          stripe\r\n          :auto-resize=\"!tableHeight\"\r\n          :scroll-y=\"{enabled: needScrollbar}\"\r\n          :scroll-x=\"{enabled: true}\">\r\n          <vxe-column title=\"分厂\" align=\"center\" field=\"company\" width=\"15%\" fixed=\"left\">\r\n            <template #default=\"{ row }\">\r\n              <span :style=\"{ fontWeight: 'bold' }\">\r\n                {{ row.company }}\r\n              </span>\r\n            </template>\r\n          </vxe-column>\r\n\r\n          <!-- 一月分组 -->\r\n          <vxe-colgroup title=\"一月\" align=\"center\">\r\n            <vxe-column title=\"金额（元）\" align=\"center\" field=\"januaryAmount\" width=\"10%\">\r\n              <template #default=\"{ row }\">\r\n                <span :style=\"{ fontWeight: 'normal' }\">\r\n                  {{ formatCurrency(row.januaryAmount) }}\r\n                </span>\r\n              </template>\r\n            </vxe-column>\r\n            <vxe-column title=\"吨位\" align=\"center\" field=\"januaryPerTon\" width=\"10%\">\r\n              <template #default=\"{ row }\">\r\n                <span :style=\"{ fontWeight: 'normal' }\">\r\n                  {{ formatTon(row.januaryPerTon) }}\r\n                </span>\r\n              </template>\r\n            </vxe-column>\r\n          </vxe-colgroup>\r\n\r\n          <!-- 二月分组 -->\r\n          <vxe-colgroup title=\"二月\" align=\"center\">\r\n            <vxe-column title=\"金额（元）\" align=\"center\" field=\"februaryAmount\" width=\"10%\">\r\n              <template #default=\"{ row }\">\r\n                <span :style=\"{ fontWeight: 'normal' }\">\r\n                  {{ formatCurrency(row.februaryAmount) }}\r\n                </span>\r\n              </template>\r\n            </vxe-column>\r\n            <vxe-column title=\"吨位\" align=\"center\" field=\"februaryPerTon\" width=\"10%\">\r\n              <template #default=\"{ row }\">\r\n                <span :style=\"{ fontWeight: 'normal' }\">\r\n                  {{ formatTon(row.februaryPerTon) }}\r\n                </span>\r\n              </template>\r\n            </vxe-column>\r\n          </vxe-colgroup>\r\n\r\n          <!-- 三月分组 -->\r\n          <vxe-colgroup title=\"三月\" align=\"center\">\r\n            <vxe-column title=\"金额（元）\" align=\"center\" field=\"marchAmount\" width=\"10%\">\r\n              <template #default=\"{ row }\">\r\n                <span :style=\"{ fontWeight: 'normal' }\">\r\n                  {{ formatCurrency(row.marchAmount) }}\r\n                </span>\r\n              </template>\r\n            </vxe-column>\r\n            <vxe-column title=\"吨位\" align=\"center\" field=\"marchPerTon\" width=\"10%\">\r\n              <template #default=\"{ row }\">\r\n                <span :style=\"{ fontWeight: 'normal' }\">\r\n                  {{ formatTon(row.marchPerTon) }}\r\n                </span>\r\n              </template>\r\n            </vxe-column>\r\n          </vxe-colgroup>\r\n\r\n          <!-- 四月分组 -->\r\n          <vxe-colgroup title=\"四月\" align=\"center\">\r\n            <vxe-column title=\"金额（元）\" align=\"center\" field=\"aprilAmount\" width=\"10%\">\r\n              <template #default=\"{ row }\">\r\n                <span :style=\"{ fontWeight: 'normal' }\">\r\n                  {{ formatCurrency(row.aprilAmount) }}\r\n                </span>\r\n              </template>\r\n            </vxe-column>\r\n            <vxe-column title=\"吨位\" align=\"center\" field=\"aprilPerTon\" width=\"10%\">\r\n              <template #default=\"{ row }\">\r\n                <span :style=\"{ fontWeight: 'normal' }\">\r\n                  {{ formatTon(row.aprilPerTon) }}\r\n                </span>\r\n              </template>\r\n            </vxe-column>\r\n          </vxe-colgroup>\r\n\r\n          <!-- 五月分组 -->\r\n          <vxe-colgroup title=\"五月\" align=\"center\">\r\n            <vxe-column title=\"金额（元）\" align=\"center\" field=\"mayAmount\" width=\"10%\">\r\n              <template #default=\"{ row }\">\r\n                <span :style=\"{ fontWeight: 'normal' }\">\r\n                  {{ formatCurrency(row.mayAmount) }}\r\n                </span>\r\n              </template>\r\n            </vxe-column>\r\n            <vxe-column title=\"吨位\" align=\"center\" field=\"mayPerTon\" width=\"10%\">\r\n              <template #default=\"{ row }\">\r\n                <span :style=\"{ fontWeight: 'normal' }\">\r\n                  {{ formatTon(row.mayPerTon) }}\r\n                </span>\r\n              </template>\r\n            </vxe-column>\r\n          </vxe-colgroup>\r\n\r\n          <!-- 六月分组 -->\r\n          <vxe-colgroup title=\"六月\" align=\"center\">\r\n            <vxe-column title=\"金额（元）\" align=\"center\" field=\"juneAmount\" width=\"10%\">\r\n              <template #default=\"{ row }\">\r\n                <span :style=\"{ fontWeight: 'normal' }\">\r\n                  {{ formatCurrency(row.juneAmount) }}\r\n                </span>\r\n              </template>\r\n            </vxe-column>\r\n            <vxe-column title=\"吨位\" align=\"center\" field=\"junePerTon\" width=\"10%\">\r\n              <template #default=\"{ row }\">\r\n                <span :style=\"{ fontWeight: 'normal' }\">\r\n                  {{ formatTon(row.junePerTon) }}\r\n                </span>\r\n              </template>\r\n            </vxe-column>\r\n          </vxe-colgroup>\r\n\r\n          <!-- 七月分组 -->\r\n          <vxe-colgroup title=\"七月\" align=\"center\">\r\n            <vxe-column title=\"金额（元）\" align=\"center\" field=\"julyAmount\" width=\"10%\">\r\n              <template #default=\"{ row }\">\r\n                <span :style=\"{ fontWeight: 'normal' }\">\r\n                  {{ formatCurrency(row.julyAmount) }}\r\n                </span>\r\n              </template>\r\n            </vxe-column>\r\n            <vxe-column title=\"吨位\" align=\"center\" field=\"julyPerTon\" width=\"10%\">\r\n              <template #default=\"{ row }\">\r\n                <span :style=\"{ fontWeight: 'normal' }\">\r\n                  {{ formatTon(row.julyPerTon) }}\r\n                </span>\r\n              </template>\r\n            </vxe-column>\r\n          </vxe-colgroup>\r\n\r\n          <!-- 八月分组 -->\r\n          <vxe-colgroup title=\"八月\" align=\"center\">\r\n            <vxe-column title=\"金额（元）\" align=\"center\" field=\"augustAmount\" width=\"10%\">\r\n              <template #default=\"{ row }\">\r\n                <span :style=\"{ fontWeight: 'normal' }\">\r\n                  {{ formatCurrency(row.augustAmount) }}\r\n                </span>\r\n              </template>\r\n            </vxe-column>\r\n            <vxe-column title=\"吨位\" align=\"center\" field=\"augustPerTon\" width=\"10%\">\r\n              <template #default=\"{ row }\">\r\n                <span :style=\"{ fontWeight: 'normal' }\">\r\n                  {{ formatTon(row.augustPerTon) }}\r\n                </span>\r\n              </template>\r\n            </vxe-column>\r\n          </vxe-colgroup>\r\n\r\n          <!-- 九月分组 -->\r\n          <vxe-colgroup title=\"九月\" align=\"center\">\r\n            <vxe-column title=\"金额（元）\" align=\"center\" field=\"septemberAmount\" width=\"10%\">\r\n              <template #default=\"{ row }\">\r\n                <span :style=\"{ fontWeight: 'normal' }\">\r\n                  {{ formatCurrency(row.septemberAmount) }}\r\n                </span>\r\n              </template>\r\n            </vxe-column>\r\n            <vxe-column title=\"吨位\" align=\"center\" field=\"septemberPerTon\" width=\"10%\">\r\n              <template #default=\"{ row }\">\r\n                <span :style=\"{ fontWeight: 'normal' }\">\r\n                  {{ formatTon(row.septemberPerTon) }}\r\n                </span>\r\n              </template>\r\n            </vxe-column>\r\n          </vxe-colgroup>\r\n\r\n          <!-- 十月分组 -->\r\n          <vxe-colgroup title=\"十月\" align=\"center\">\r\n            <vxe-column title=\"金额（元）\" align=\"center\" field=\"octoberAmount\" width=\"10%\">\r\n              <template #default=\"{ row }\">\r\n                <span :style=\"{ fontWeight: 'normal' }\">\r\n                  {{ formatCurrency(row.octoberAmount) }}\r\n                </span>\r\n              </template>\r\n            </vxe-column>\r\n            <vxe-column title=\"吨位\" align=\"center\" field=\"octoberPerTon\" width=\"10%\">\r\n              <template #default=\"{ row }\">\r\n                <span :style=\"{ fontWeight: 'normal' }\">\r\n                  {{ formatTon(row.octoberPerTon) }}\r\n                </span>\r\n              </template>\r\n            </vxe-column>\r\n          </vxe-colgroup>\r\n\r\n          <!-- 十一月分组 -->\r\n          <vxe-colgroup title=\"十一月\" align=\"center\">\r\n            <vxe-column title=\"金额（元）\" align=\"center\" field=\"novemberAmount\" width=\"10%\">\r\n              <template #default=\"{ row }\">\r\n                <span :style=\"{ fontWeight: 'normal' }\">\r\n                  {{ formatCurrency(row.novemberAmount) }}\r\n                </span>\r\n              </template>\r\n            </vxe-column>\r\n            <vxe-column title=\"吨位\" align=\"center\" field=\"novemberPerTon\" width=\"10%\">\r\n              <template #default=\"{ row }\">\r\n                <span :style=\"{ fontWeight: 'normal' }\">\r\n                  {{ formatTon(row.novemberPerTon) }}\r\n                </span>\r\n              </template>\r\n            </vxe-column>\r\n          </vxe-colgroup>\r\n\r\n          <!-- 十二月分组 -->\r\n          <vxe-colgroup title=\"十二月\" align=\"center\">\r\n            <vxe-column title=\"金额（元）\" align=\"center\" field=\"decemberAmount\" width=\"10%\">\r\n              <template #default=\"{ row }\">\r\n                <span :style=\"{ fontWeight: 'normal' }\">\r\n                  {{ formatCurrency(row.decemberAmount) }}\r\n                </span>\r\n              </template>\r\n            </vxe-column>\r\n            <vxe-column title=\"吨位\" align=\"center\" field=\"decemberPerTon\" width=\"10%\">\r\n              <template #default=\"{ row }\">\r\n                <span :style=\"{ fontWeight: 'normal' }\">\r\n                  {{ formatTon(row.decemberPerTon) }}\r\n                </span>\r\n              </template>\r\n            </vxe-column>\r\n          </vxe-colgroup>\r\n\r\n          <!-- 合计分组 -->\r\n          <!-- <vxe-colgroup title=\"合计\" align=\"center\">\r\n            <vxe-column title=\"金额（元）\" align=\"center\" field=\"totalAmount\" width=\"10%\">\r\n              <template #default=\"{ row }\">\r\n                <span :style=\"{ fontWeight: 'bold' }\">\r\n                  {{ formatCurrency(row.totalAmount) }}\r\n                </span>\r\n              </template>\r\n            </vxe-column>\r\n            <vxe-column title=\"吨位\" align=\"center\" field=\"totalPerTon\" width=\"10%\">\r\n              <template #default=\"{ row }\">\r\n                <span :style=\"{ fontWeight: 'bold' }\">\r\n                  {{ formatCurrency(row.totalPerTon) }}\r\n                </span>\r\n              </template>\r\n            </vxe-column>\r\n          </vxe-colgroup> -->\r\n        </vxe-table>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { listCompanySummaryData } from \"@/api/qualityCost/companySummary1\";\r\nimport { testFactoryData, testFactoryDataWithRealValues, testDataMapping } from \"./testData\";\r\n\r\n\r\nexport default {\r\n  name: \"CompanySummary1\",\r\n  data() {\r\n    return {\r\n      // 遮罩层\r\n      loading: false,\r\n      // 质量成本数据列表\r\n      qualityCostList: [],\r\n      // 合并单元格配置\r\n      mergeCells: [],\r\n      // 表格高度\r\n      tableHeight: null,\r\n      // 是否需要滚动条\r\n      needScrollbar: true,\r\n      // 年份选择\r\n      selectedYear: new Date().getFullYear(),\r\n      // 年份选项\r\n      yearOptions: [],\r\n      // 类型选择\r\n      selectedType: '改判',\r\n      // 类型选项\r\n      typeOptions: ['改判', '报废', '脱合同', '退货']\r\n    };\r\n  },\r\n  computed: {\r\n    /** 容器样式 */\r\n    containerStyle() {\r\n      return {\r\n        height: this.tableHeight ? `${this.tableHeight}px` : 'auto'\r\n      };\r\n    },\r\n    /** 表格容器样式 */\r\n    tableContainerStyle() {\r\n      return {\r\n        height: this.tableHeight ? `${this.tableHeight - 80}px` : 'auto'\r\n      };\r\n    }\r\n  },\r\n  mounted() {\r\n    this.calculateTableHeight();\r\n    window.addEventListener('resize', this.calculateTableHeight);\r\n    this.generateYearOptions();\r\n  },\r\n  beforeDestroy() {\r\n    window.removeEventListener('resize', this.calculateTableHeight);\r\n  },\r\n  methods: {\r\n    getCompanySummaryData() {\r\n      this.loading = true;\r\n      \r\n      const params = {\r\n        yearMonth: this.selectedYear,\r\n        companySummaryType: this.selectedType\r\n      };\r\n\r\n      listCompanySummaryData(params).then(response => {\r\n        console.log('listCompanySummaryData:', response);\r\n        if (response.data) {\r\n          // 处理分厂数据格式\r\n          this.processFactoryData(response.data);\r\n        } else {\r\n          this.qualityCostList = [];\r\n        }\r\n        this.loading = false;\r\n      }).catch(error => {\r\n        console.error('获取数据失败:', error);\r\n        this.$message.error('获取质量成本数据失败');\r\n        this.loading = false;\r\n      });\r\n    },\r\n\r\n    /** 处理分厂数据格式 */\r\n    processFactoryData(rawData) {\r\n      console.log('开始处理分厂数据:', rawData);\r\n      \r\n      // 检查是否为分厂数据格式\r\n      if (this.isFactoryDataFormat(rawData)) {\r\n        console.log('检测到分厂数据格式，开始处理...');\r\n        \r\n        // 清理模拟数据\r\n        const cleanedData = this.cleanMockData(rawData);\r\n        \r\n        // 转换为表格格式\r\n        const tableData = this.convertFactoryDataToTable(cleanedData);\r\n        \r\n        // 更新表格数据\r\n        this.updateTableWithFactoryData(tableData);\r\n      } else {\r\n        console.log('不是分厂数据格式，使用默认处理');\r\n        this.qualityCostList = [];\r\n      }\r\n    },\r\n\r\n    /** 检查是否为分厂数据格式 */\r\n    isFactoryDataFormat(data) {\r\n      if (!data || typeof data !== 'object') return false;\r\n      \r\n      const factoryKeys = Object.keys(data);\r\n      if (factoryKeys.length === 0) return false;\r\n      \r\n      const firstFactory = data[factoryKeys[0]];\r\n      if (!firstFactory || typeof firstFactory !== 'object') return false;\r\n      \r\n      const monthKeys = Object.keys(firstFactory);\r\n      if (monthKeys.length === 0) return false;\r\n      \r\n      const firstMonth = firstFactory[monthKeys[0]];\r\n      return firstMonth && \r\n             typeof firstMonth === 'object' && \r\n             'costEx' in firstMonth && \r\n             'costTon' in firstMonth;\r\n    },\r\n\r\n    /** 清理模拟数据 */\r\n    cleanMockData(data) {\r\n      const cleanedData = {};\r\n      \r\n      Object.keys(data).forEach(factoryName => {\r\n        const factoryData = data[factoryName];\r\n        const validMonths = {};\r\n        \r\n        Object.keys(factoryData).forEach(monthKey => {\r\n          const monthData = factoryData[monthKey];\r\n          \r\n          // 检查是否为模拟数据\r\n          if (!this.isMockData(monthData)) {\r\n            validMonths[monthKey] = monthData;\r\n          }\r\n        });\r\n        \r\n        // 只有当分厂有有效数据时才保留\r\n        if (Object.keys(validMonths).length > 0) {\r\n          cleanedData[factoryName] = validMonths;\r\n        }\r\n      });\r\n      \r\n      console.log('清理后的数据:', cleanedData);\r\n      return cleanedData;\r\n    },\r\n\r\n    /** 判断是否为模拟数据 */\r\n    isMockData(monthData) {\r\n      return (\r\n        monthData.costCenterName === null &&\r\n        monthData.costEx === 0 &&\r\n        monthData.costTon === 0 &&\r\n        monthData.yearMonth === null\r\n      );\r\n    },\r\n\r\n    /** 将分厂数据转换为表格格式 */\r\n    convertFactoryDataToTable(factoryData) {\r\n      const tableData = [];\r\n      \r\n      Object.keys(factoryData).forEach(factoryName => {\r\n        const factoryMonths = factoryData[factoryName];\r\n        \r\n        Object.keys(factoryMonths).forEach(monthKey => {\r\n          const monthData = factoryMonths[monthKey];\r\n          \r\n          // 创建表格行数据\r\n          const rowData = {\r\n            factoryName: factoryName,\r\n            month: monthKey,\r\n            costEx: monthData.costEx || 0,\r\n            costTon: monthData.costTon || 0,\r\n            yearMonth: monthData.yearMonth,\r\n            costCenterName: monthData.costCenterName\r\n          };\r\n          \r\n          tableData.push(rowData);\r\n        });\r\n      });\r\n      \r\n      console.log('转换后的表格数据:', tableData);\r\n      return tableData;\r\n    },\r\n\r\n    /** 更新表格显示分厂数据 */\r\n    updateTableWithFactoryData(tableData) {\r\n      if (tableData.length === 0) {\r\n        this.qualityCostList = [];\r\n        return;\r\n      }\r\n      \r\n      // 获取所有分厂名称\r\n      const factoryNames = [...new Set(tableData.map(row => row.factoryName))];\r\n      \r\n      // 创建表格行数据\r\n      this.qualityCostList = factoryNames.map(factoryName => {\r\n        const rowData = {\r\n          company: factoryName\r\n        };\r\n        \r\n        // 初始化所有月份的数据为0\r\n        const months = ['january', 'february', 'march', 'april', 'may', 'june',\r\n                       'july', 'august', 'september', 'october', 'november', 'december'];\r\n        \r\n        months.forEach((month, index) => {\r\n          const monthNumber = index + 1;\r\n          const monthData = tableData.find(row => \r\n            row.factoryName === factoryName && row.month === monthNumber.toString()\r\n          );\r\n          \r\n          if (monthData) {\r\n            rowData[`${month}Amount`] = monthData.costEx;\r\n            rowData[`${month}PerTon`] = monthData.costTon;\r\n          } else {\r\n            rowData[`${month}Amount`] = 0;\r\n            rowData[`${month}PerTon`] = 0;\r\n          }\r\n        });\r\n        \r\n        // 计算合计\r\n        rowData.totalAmount = months.reduce((sum, month) => sum + (rowData[`${month}Amount`] || 0), 0);\r\n        rowData.totalPerTon = months.reduce((sum, month) => sum + (rowData[`${month}PerTon`] || 0), 0);\r\n        \r\n        return rowData;\r\n      });\r\n      \r\n      console.log('更新后的表格数据:', this.qualityCostList);\r\n    },\r\n\r\n\r\n    /** 格式化货币 */\r\n    formatCurrency(value) {\r\n      if (value === null || value === undefined || value === '' || Number(value) === 0) {\r\n        return '-';\r\n      }\r\n      const num = Number(value);\r\n      // 金额显示为元，不转换为万\r\n      return num.toLocaleString('zh-CN', {\r\n        minimumFractionDigits: 2,\r\n        maximumFractionDigits: 2\r\n      });\r\n    },\r\n\r\n    /** 格式化吨位 */\r\n    formatTon(value) {\r\n      if (value === null || value === undefined || value === '' || Number(value) === 0) {\r\n        return '-';\r\n      }\r\n      const num = Number(value);\r\n      // 吨位显示为吨，保留2位小数\r\n      return num.toLocaleString('zh-CN', {\r\n        minimumFractionDigits: 2,\r\n        maximumFractionDigits: 2\r\n      });\r\n    },\r\n\r\n    /** 计算表格高度 */\r\n    calculateTableHeight() {\r\n      this.$nextTick(() => {\r\n        const windowHeight = window.innerHeight;\r\n        // 减去页面头部、标题等高度，大约100px\r\n        const availableHeight = windowHeight - 100;\r\n        // 设置表格最大高度，最小400px，最大不超过可用高度的90%\r\n        this.tableHeight = Math.max(400, Math.min(800, availableHeight * 0.9));\r\n        this.needScrollbar = this.qualityCostList.length > 8;\r\n      });\r\n    },\r\n\r\n    /** 生成年份选项 */\r\n    generateYearOptions() {\r\n      const currentYear = new Date().getFullYear();\r\n      for (let i = 2000; i <= currentYear; i++) {\r\n        this.yearOptions.push(i);\r\n      }\r\n      this.getCompanySummaryData()\r\n    },\r\n\r\n         /** 年份选择变化 */\r\n     handleYearChange(year) {\r\n       this.selectedYear = year;\r\n       // 重新获取数据\r\n       this.getCompanySummaryData();\r\n     },\r\n\r\n     /** 类型选择变化 */\r\n     handleTypeChange(type) {\r\n       this.selectedType = type;\r\n       // 重新获取数据\r\n       this.getCompanySummaryData();\r\n     },\r\n\r\n     /** 测试分厂数据处理 */\r\n     testFactoryDataProcessing() {\r\n       console.log('开始测试分厂数据处理...');\r\n       \r\n       // 测试1: 纯模拟数据\r\n       console.log('=== 测试1: 纯模拟数据 ===');\r\n       this.processFactoryData(testFactoryData);\r\n       \r\n       // 等待2秒后测试真实数据\r\n       setTimeout(() => {\r\n         console.log('=== 测试2: 包含真实数据 ===');\r\n         this.processFactoryData(testFactoryDataWithRealValues);\r\n       }, 2000);\r\n       \r\n       // 等待4秒后测试数据映射\r\n       setTimeout(() => {\r\n         console.log('=== 测试3: 数据映射验证 ===');\r\n         this.processFactoryData(testDataMapping);\r\n         this.verifyDataMapping();\r\n       }, 4000);\r\n     },\r\n\r\n     /** 验证数据映射 */\r\n     verifyDataMapping() {\r\n       console.log('验证数据映射...');\r\n       \r\n       if (this.qualityCostList.length > 0) {\r\n         this.qualityCostList.forEach((row, index) => {\r\n           console.log(`第${index + 1}行 - 分厂: ${row.company}`);\r\n           console.log(`  一月金额: ${row.januaryAmount}, 一月吨位: ${row.januaryPerTon}`);\r\n           console.log(`  二月金额: ${row.februaryAmount}, 二月吨位: ${row.februaryPerTon}`);\r\n           console.log(`  三月金额: ${row.marchAmount}, 三月吨位: ${row.marchPerTon}`);\r\n         });\r\n       }\r\n     }\r\n  }\r\n};\r\n</script>\r\n\r\n<style scoped>\r\n/* 页面容器样式 */\r\n.app-container {\r\n  position: relative;\r\n  padding: 20px;\r\n}\r\n\r\n/* 表格容器样式 */\r\n.table-container {\r\n  margin-top: 20px;\r\n}\r\n\r\n/* 表格滚动容器 */\r\n.table-scroll-container {\r\n  width: 100%;\r\n  position: relative;\r\n  border: 1px solid #ebeef5;\r\n  border-radius: 4px;\r\n}\r\n\r\n/* 表格单元格内容 */\r\n.table-cell-content {\r\n  padding: 10px 0;\r\n}\r\n</style>\r\n\r\n<style>\r\n/* 非scoped样式，确保能够覆盖vxe-table的默认样式 */\r\n\r\n/* 强制覆盖所有滚动条样式 - 15px粗细，淡蓝色主题 */\r\n.vxe-table ::-webkit-scrollbar {\r\n  width: 15px !important;\r\n  height: 15px !important;\r\n}\r\n\r\n.vxe-table ::-webkit-scrollbar-track {\r\n  background: #e3f2fd !important;\r\n  border-radius: 7px !important;\r\n}\r\n\r\n.vxe-table ::-webkit-scrollbar-thumb {\r\n  background: #64b5f6 !important;\r\n  border-radius: 7px !important;\r\n  border: none !important;\r\n}\r\n\r\n.vxe-table ::-webkit-scrollbar-thumb:hover {\r\n  background: #42a5f5 !important;\r\n}\r\n\r\n.vxe-table ::-webkit-scrollbar-thumb:active {\r\n  background: #2196f3 !important;\r\n}\r\n\r\n.vxe-table ::-webkit-scrollbar-corner {\r\n  background: #e3f2fd !important;\r\n}\r\n\r\n/* vxe-table 表格边框和样式 */\r\n.vxe-table ::v-deep .vxe-table {\r\n  border: 1px solid #ebeef5;\r\n}\r\n\r\n.vxe-table ::v-deep .vxe-table--border-line {\r\n  border-color: #ebeef5;\r\n}\r\n\r\n/* 表头样式 */\r\n.vxe-table ::v-deep .vxe-table--header-wrapper .vxe-table--header {\r\n  background-color: #f5f7fa;\r\n}\r\n\r\n.vxe-table ::v-deep .vxe-table--header .vxe-header--column {\r\n  background-color: #f5f7fa;\r\n  color: #303133;\r\n  font-weight: 500;\r\n}\r\n\r\n/* 表头分组样式 */\r\n.vxe-table ::v-deep .vxe-table--header .vxe-header--group {\r\n  background-color: #e8f4fd !important;\r\n  color: #1890ff !important;\r\n  font-weight: 600 !important;\r\n  border: 1px solid #d1e7ff !important;\r\n}\r\n\r\n/* 确保分组表头显示 */\r\n.vxe-table ::v-deep .vxe-table--header .vxe-header--group .vxe-header--group-title {\r\n  background-color: #e8f4fd !important;\r\n  color: #1890ff !important;\r\n  font-weight: 600 !important;\r\n  padding: 8px 4px !important;\r\n  text-align: center !important;\r\n}\r\n\r\n/* 子列表头样式 */\r\n.vxe-table ::v-deep .vxe-table--header .vxe-header--column {\r\n  background-color: #f5f7fa !important;\r\n  color: #303133 !important;\r\n  font-weight: 500 !important;\r\n  border: 1px solid #ebeef5 !important;\r\n}\r\n\r\n/* 确保表头分组正确显示 */\r\n.vxe-table ::v-deep .vxe-table--header-wrapper {\r\n  border: 1px solid #ebeef5 !important;\r\n}\r\n\r\n.vxe-table ::v-deep .vxe-table--header {\r\n  border-bottom: 1px solid #ebeef5 !important;\r\n}\r\n\r\n/* 强制显示分组表头 */\r\n.vxe-table ::v-deep .vxe-table--header .vxe-header--group {\r\n  display: table-cell !important;\r\n  vertical-align: middle !important;\r\n}\r\n\r\n/* 分组表头文字样式 */\r\n.vxe-table ::v-deep .vxe-header--group-title {\r\n  display: block !important;\r\n  width: 100% !important;\r\n  text-align: center !important;\r\n  font-size: 14px !important;\r\n  line-height: 1.5 !important;\r\n}\r\n\r\n/* 确保表头分组边框显示 */\r\n.vxe-table ::v-deep .vxe-table--header .vxe-header--group {\r\n  border-right: 1px solid #d1e7ff !important;\r\n  border-bottom: 1px solid #d1e7ff !important;\r\n}\r\n\r\n/* 最后一个分组表头右边框 */\r\n.vxe-table ::v-deep .vxe-table--header .vxe-header--group:last-child {\r\n  border-right: 1px solid #ebeef5 !important;\r\n}\r\n\r\n/* 确保表头文字居中 */\r\n.vxe-table ::v-deep .vxe-table--header .vxe-header--column .vxe-cell--title {\r\n  text-align: center !important;\r\n  display: flex !important;\r\n  align-items: center !important;\r\n  justify-content: center !important;\r\n  height: 100% !important;\r\n}\r\n</style> "], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;AA6SA,IAAAA,eAAA,GAAAC,OAAA;AACA,IAAAC,SAAA,GAAAD,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCAGA;EACAE,IAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACA;MACAC,OAAA;MACA;MACAC,eAAA;MACA;MACAC,UAAA;MACA;MACAC,WAAA;MACA;MACAC,aAAA;MACA;MACAC,YAAA,MAAAC,IAAA,GAAAC,WAAA;MACA;MACAC,WAAA;MACA;MACAC,YAAA;MACA;MACAC,WAAA;IACA;EACA;EACAC,QAAA;IACA,WACAC,cAAA,WAAAA,eAAA;MACA;QACAC,MAAA,OAAAV,WAAA,MAAAW,MAAA,MAAAX,WAAA;MACA;IACA;IACA,aACAY,mBAAA,WAAAA,oBAAA;MACA;QACAF,MAAA,OAAAV,WAAA,MAAAW,MAAA,MAAAX,WAAA;MACA;IACA;EACA;EACAa,OAAA,WAAAA,QAAA;IACA,KAAAC,oBAAA;IACAC,MAAA,CAAAC,gBAAA,gBAAAF,oBAAA;IACA,KAAAG,mBAAA;EACA;EACAC,aAAA,WAAAA,cAAA;IACAH,MAAA,CAAAI,mBAAA,gBAAAL,oBAAA;EACA;EACAM,OAAA;IACAC,qBAAA,WAAAA,sBAAA;MAAA,IAAAC,KAAA;MACA,KAAAzB,OAAA;MAEA,IAAA0B,MAAA;QACAC,SAAA,OAAAtB,YAAA;QACAuB,kBAAA,OAAAnB;MACA;MAEA,IAAAoB,sCAAA,EAAAH,MAAA,EAAAI,IAAA,WAAAC,QAAA;QACAC,OAAA,CAAAC,GAAA,4BAAAF,QAAA;QACA,IAAAA,QAAA,CAAAhC,IAAA;UACA;UACA0B,KAAA,CAAAS,kBAAA,CAAAH,QAAA,CAAAhC,IAAA;QACA;UACA0B,KAAA,CAAAxB,eAAA;QACA;QACAwB,KAAA,CAAAzB,OAAA;MACA,GAAAmC,KAAA,WAAAC,KAAA;QACAJ,OAAA,CAAAI,KAAA,YAAAA,KAAA;QACAX,KAAA,CAAAY,QAAA,CAAAD,KAAA;QACAX,KAAA,CAAAzB,OAAA;MACA;IACA;IAEA,eACAkC,kBAAA,WAAAA,mBAAAI,OAAA;MACAN,OAAA,CAAAC,GAAA,cAAAK,OAAA;;MAEA;MACA,SAAAC,mBAAA,CAAAD,OAAA;QACAN,OAAA,CAAAC,GAAA;;QAEA;QACA,IAAAO,WAAA,QAAAC,aAAA,CAAAH,OAAA;;QAEA;QACA,IAAAI,SAAA,QAAAC,yBAAA,CAAAH,WAAA;;QAEA;QACA,KAAAI,0BAAA,CAAAF,SAAA;MACA;QACAV,OAAA,CAAAC,GAAA;QACA,KAAAhC,eAAA;MACA;IACA;IAEA,kBACAsC,mBAAA,WAAAA,oBAAAxC,IAAA;MACA,KAAAA,IAAA,QAAA8C,QAAA,CAAAC,OAAA,EAAA/C,IAAA;MAEA,IAAAgD,WAAA,GAAAC,MAAA,CAAAC,IAAA,CAAAlD,IAAA;MACA,IAAAgD,WAAA,CAAAG,MAAA;MAEA,IAAAC,YAAA,GAAApD,IAAA,CAAAgD,WAAA;MACA,KAAAI,YAAA,QAAAN,QAAA,CAAAC,OAAA,EAAAK,YAAA;MAEA,IAAAC,SAAA,GAAAJ,MAAA,CAAAC,IAAA,CAAAE,YAAA;MACA,IAAAC,SAAA,CAAAF,MAAA;MAEA,IAAAG,UAAA,GAAAF,YAAA,CAAAC,SAAA;MACA,OAAAC,UAAA,IACA,IAAAR,QAAA,CAAAC,OAAA,EAAAO,UAAA,kBACA,YAAAA,UAAA,IACA,aAAAA,UAAA;IACA;IAEA,aACAZ,aAAA,WAAAA,cAAA1C,IAAA;MAAA,IAAAuD,MAAA;MACA,IAAAd,WAAA;MAEAQ,MAAA,CAAAC,IAAA,CAAAlD,IAAA,EAAAwD,OAAA,WAAAC,WAAA;QACA,IAAAC,WAAA,GAAA1D,IAAA,CAAAyD,WAAA;QACA,IAAAE,WAAA;QAEAV,MAAA,CAAAC,IAAA,CAAAQ,WAAA,EAAAF,OAAA,WAAAI,QAAA;UACA,IAAAC,SAAA,GAAAH,WAAA,CAAAE,QAAA;;UAEA;UACA,KAAAL,MAAA,CAAAO,UAAA,CAAAD,SAAA;YACAF,WAAA,CAAAC,QAAA,IAAAC,SAAA;UACA;QACA;;QAEA;QACA,IAAAZ,MAAA,CAAAC,IAAA,CAAAS,WAAA,EAAAR,MAAA;UACAV,WAAA,CAAAgB,WAAA,IAAAE,WAAA;QACA;MACA;MAEA1B,OAAA,CAAAC,GAAA,YAAAO,WAAA;MACA,OAAAA,WAAA;IACA;IAEA,gBACAqB,UAAA,WAAAA,WAAAD,SAAA;MACA,OACAA,SAAA,CAAAE,cAAA,aACAF,SAAA,CAAAG,MAAA,UACAH,SAAA,CAAAI,OAAA,UACAJ,SAAA,CAAAjC,SAAA;IAEA;IAEA,mBACAgB,yBAAA,WAAAA,0BAAAc,WAAA;MACA,IAAAf,SAAA;MAEAM,MAAA,CAAAC,IAAA,CAAAQ,WAAA,EAAAF,OAAA,WAAAC,WAAA;QACA,IAAAS,aAAA,GAAAR,WAAA,CAAAD,WAAA;QAEAR,MAAA,CAAAC,IAAA,CAAAgB,aAAA,EAAAV,OAAA,WAAAI,QAAA;UACA,IAAAC,SAAA,GAAAK,aAAA,CAAAN,QAAA;;UAEA;UACA,IAAAO,OAAA;YACAV,WAAA,EAAAA,WAAA;YACAW,KAAA,EAAAR,QAAA;YACAI,MAAA,EAAAH,SAAA,CAAAG,MAAA;YACAC,OAAA,EAAAJ,SAAA,CAAAI,OAAA;YACArC,SAAA,EAAAiC,SAAA,CAAAjC,SAAA;YACAmC,cAAA,EAAAF,SAAA,CAAAE;UACA;UAEApB,SAAA,CAAA0B,IAAA,CAAAF,OAAA;QACA;MACA;MAEAlC,OAAA,CAAAC,GAAA,cAAAS,SAAA;MACA,OAAAA,SAAA;IACA;IAEA,iBACAE,0BAAA,WAAAA,2BAAAF,SAAA;MACA,IAAAA,SAAA,CAAAQ,MAAA;QACA,KAAAjD,eAAA;QACA;MACA;;MAEA;MACA,IAAAoE,YAAA,OAAAC,mBAAA,CAAAxB,OAAA,MAAAyB,GAAA,CAAA7B,SAAA,CAAA8B,GAAA,WAAAC,GAAA;QAAA,OAAAA,GAAA,CAAAjB,WAAA;MAAA;;MAEA;MACA,KAAAvD,eAAA,GAAAoE,YAAA,CAAAG,GAAA,WAAAhB,WAAA;QACA,IAAAU,OAAA;UACAQ,OAAA,EAAAlB;QACA;;QAEA;QACA,IAAAmB,MAAA,4DACA;QAEAA,MAAA,CAAApB,OAAA,WAAAY,KAAA,EAAAS,KAAA;UACA,IAAAC,WAAA,GAAAD,KAAA;UACA,IAAAhB,SAAA,GAAAlB,SAAA,CAAAoC,IAAA,WAAAL,GAAA;YAAA,OACAA,GAAA,CAAAjB,WAAA,KAAAA,WAAA,IAAAiB,GAAA,CAAAN,KAAA,KAAAU,WAAA,CAAAE,QAAA;UAAA,CACA;UAEA,IAAAnB,SAAA;YACAM,OAAA,IAAApD,MAAA,CAAAqD,KAAA,eAAAP,SAAA,CAAAG,MAAA;YACAG,OAAA,IAAApD,MAAA,CAAAqD,KAAA,eAAAP,SAAA,CAAAI,OAAA;UACA;YACAE,OAAA,IAAApD,MAAA,CAAAqD,KAAA;YACAD,OAAA,IAAApD,MAAA,CAAAqD,KAAA;UACA;QACA;;QAEA;QACAD,OAAA,CAAAc,WAAA,GAAAL,MAAA,CAAAM,MAAA,WAAAC,GAAA,EAAAf,KAAA;UAAA,OAAAe,GAAA,IAAAhB,OAAA,IAAApD,MAAA,CAAAqD,KAAA;QAAA;QACAD,OAAA,CAAAiB,WAAA,GAAAR,MAAA,CAAAM,MAAA,WAAAC,GAAA,EAAAf,KAAA;UAAA,OAAAe,GAAA,IAAAhB,OAAA,IAAApD,MAAA,CAAAqD,KAAA;QAAA;QAEA,OAAAD,OAAA;MACA;MAEAlC,OAAA,CAAAC,GAAA,mBAAAhC,eAAA;IACA;IAGA,YACAmF,cAAA,WAAAA,eAAAC,KAAA;MACA,IAAAA,KAAA,aAAAA,KAAA,KAAAC,SAAA,IAAAD,KAAA,WAAAE,MAAA,CAAAF,KAAA;QACA;MACA;MACA,IAAAG,GAAA,GAAAD,MAAA,CAAAF,KAAA;MACA;MACA,OAAAG,GAAA,CAAAC,cAAA;QACAC,qBAAA;QACAC,qBAAA;MACA;IACA;IAEA,YACAC,SAAA,WAAAA,UAAAP,KAAA;MACA,IAAAA,KAAA,aAAAA,KAAA,KAAAC,SAAA,IAAAD,KAAA,WAAAE,MAAA,CAAAF,KAAA;QACA;MACA;MACA,IAAAG,GAAA,GAAAD,MAAA,CAAAF,KAAA;MACA;MACA,OAAAG,GAAA,CAAAC,cAAA;QACAC,qBAAA;QACAC,qBAAA;MACA;IACA;IAEA,aACA1E,oBAAA,WAAAA,qBAAA;MAAA,IAAA4E,MAAA;MACA,KAAAC,SAAA;QACA,IAAAC,YAAA,GAAA7E,MAAA,CAAA8E,WAAA;QACA;QACA,IAAAC,eAAA,GAAAF,YAAA;QACA;QACAF,MAAA,CAAA1F,WAAA,GAAA+F,IAAA,CAAAC,GAAA,MAAAD,IAAA,CAAAE,GAAA,MAAAH,eAAA;QACAJ,MAAA,CAAAzF,aAAA,GAAAyF,MAAA,CAAA5F,eAAA,CAAAiD,MAAA;MACA;IACA;IAEA,aACA9B,mBAAA,WAAAA,oBAAA;MACA,IAAAiF,WAAA,OAAA/F,IAAA,GAAAC,WAAA;MACA,SAAA+F,CAAA,SAAAA,CAAA,IAAAD,WAAA,EAAAC,CAAA;QACA,KAAA9F,WAAA,CAAA4D,IAAA,CAAAkC,CAAA;MACA;MACA,KAAA9E,qBAAA;IACA;IAEA,aACA+E,gBAAA,WAAAA,iBAAAC,IAAA;MACA,KAAAnG,YAAA,GAAAmG,IAAA;MACA;MACA,KAAAhF,qBAAA;IACA;IAEA,aACAiF,gBAAA,WAAAA,iBAAAC,IAAA;MACA,KAAAjG,YAAA,GAAAiG,IAAA;MACA;MACA,KAAAlF,qBAAA;IACA;IAEA,eACAmF,yBAAA,WAAAA,0BAAA;MAAA,IAAAC,MAAA;MACA5E,OAAA,CAAAC,GAAA;;MAEA;MACAD,OAAA,CAAAC,GAAA;MACA,KAAAC,kBAAA,CAAA2E,yBAAA;;MAEA;MACAC,UAAA;QACA9E,OAAA,CAAAC,GAAA;QACA2E,MAAA,CAAA1E,kBAAA,CAAA6E,uCAAA;MACA;;MAEA;MACAD,UAAA;QACA9E,OAAA,CAAAC,GAAA;QACA2E,MAAA,CAAA1E,kBAAA,CAAA8E,yBAAA;QACAJ,MAAA,CAAAK,iBAAA;MACA;IACA;IAEA,aACAA,iBAAA,WAAAA,kBAAA;MACAjF,OAAA,CAAAC,GAAA;MAEA,SAAAhC,eAAA,CAAAiD,MAAA;QACA,KAAAjD,eAAA,CAAAsD,OAAA,WAAAkB,GAAA,EAAAG,KAAA;UACA5C,OAAA,CAAAC,GAAA,UAAAnB,MAAA,CAAA8D,KAAA,iCAAA9D,MAAA,CAAA2D,GAAA,CAAAC,OAAA;UACA1C,OAAA,CAAAC,GAAA,gCAAAnB,MAAA,CAAA2D,GAAA,CAAAyC,aAAA,kCAAApG,MAAA,CAAA2D,GAAA,CAAA0C,aAAA;UACAnF,OAAA,CAAAC,GAAA,gCAAAnB,MAAA,CAAA2D,GAAA,CAAA2C,cAAA,kCAAAtG,MAAA,CAAA2D,GAAA,CAAA4C,cAAA;UACArF,OAAA,CAAAC,GAAA,gCAAAnB,MAAA,CAAA2D,GAAA,CAAA6C,WAAA,kCAAAxG,MAAA,CAAA2D,GAAA,CAAA8C,WAAA;QACA;MACA;IACA;EACA;AACA", "ignoreList": []}]}