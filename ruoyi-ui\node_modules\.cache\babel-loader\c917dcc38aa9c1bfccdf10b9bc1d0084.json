{"remainingRequest": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js??ref--13-0!E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\src\\api\\suppPunishment\\punishment.js", "dependencies": [{"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\src\\api\\suppPunishment\\punishment.js", "mtime": 1755499162038}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\babel.config.js", "mtime": 1688548084091}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_request", "_interopRequireDefault", "require", "listPunishment", "query", "request", "url", "method", "params", "getPunishment", "id", "addPunishment", "data", "updatePunishment", "delPunishment", "confirmPunishment", "getUserCompany", "userName", "exportPunishment", "getUserGroup"], "sources": ["E:/java_workspace/new_workspace/xctg/ruoyi-ui/src/api/suppPunishment/punishment.js"], "sourcesContent": ["import request from '@/utils/request'\r\n\r\n// 查询【请填写功能名称】列表\r\nexport function listPunishment(query) {\r\n  return request({\r\n    url: '/supp/punishment/list',\r\n    method: 'get',\r\n    params: query\r\n  })\r\n}\r\n\r\n// 查询【请填写功能名称】详细\r\nexport function getPunishment(id) {\r\n  return request({\r\n    url: '/supp/punishment/' + id,\r\n    method: 'get'\r\n  })\r\n}\r\n\r\n// 新增【请填写功能名称】\r\nexport function addPunishment(data) {\r\n  return request({\r\n    url: '/supp/punishment',\r\n    method: 'post',\r\n    data: data\r\n  })\r\n}\r\n\r\n// 修改【请填写功能名称】\r\nexport function updatePunishment(data) {\r\n  return request({\r\n    url: '/supp/punishment',\r\n    method: 'put',\r\n    data: data\r\n  })\r\n}\r\n\r\n// 删除【请填写功能名称】\r\nexport function delPunishment(id) {\r\n  return request({\r\n    url: '/supp/punishment/' + id,\r\n    method: 'delete'\r\n  })\r\n}\r\n\r\n// 确认供应商处罚记录\r\nexport function confirmPunishment(id) {\r\n  return request({\r\n    url: '/supp/punishment/confirm/' + id,\r\n    method: 'put'\r\n  })\r\n}\r\n\r\n// 根据用户名获取单位信息\r\nexport function getUserCompany(userName) {\r\n  return request({\r\n    url: '/supp/punishment/userName',\r\n    method: 'get',\r\n    params: { userName: userName }\r\n  })\r\n}\r\n\r\n// 导出供应商处罚记录\r\nexport function exportPunishment(query) {\r\n  return request({\r\n    url: '/supp/punishment/export',\r\n    method: 'get',\r\n    params: query\r\n  })\r\n}\r\n\r\n// 查询用户分组权限\r\nexport function getUserGroup() {\r\n  return request({\r\n    url: '/supp/punishment/userGroup',\r\n    method: 'get'\r\n  })\r\n}"], "mappings": ";;;;;;;;;;;;;;;AAAA,IAAAA,QAAA,GAAAC,sBAAA,CAAAC,OAAA;AAEA;AACO,SAASC,cAAcA,CAACC,KAAK,EAAE;EACpC,OAAO,IAAAC,gBAAO,EAAC;IACbC,GAAG,EAAE,uBAAuB;IAC5BC,MAAM,EAAE,KAAK;IACbC,MAAM,EAAEJ;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASK,aAAaA,CAACC,EAAE,EAAE;EAChC,OAAO,IAAAL,gBAAO,EAAC;IACbC,GAAG,EAAE,mBAAmB,GAAGI,EAAE;IAC7BH,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASI,aAAaA,CAACC,IAAI,EAAE;EAClC,OAAO,IAAAP,gBAAO,EAAC;IACbC,GAAG,EAAE,kBAAkB;IACvBC,MAAM,EAAE,MAAM;IACdK,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAASC,gBAAgBA,CAACD,IAAI,EAAE;EACrC,OAAO,IAAAP,gBAAO,EAAC;IACbC,GAAG,EAAE,kBAAkB;IACvBC,MAAM,EAAE,KAAK;IACbK,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAASE,aAAaA,CAACJ,EAAE,EAAE;EAChC,OAAO,IAAAL,gBAAO,EAAC;IACbC,GAAG,EAAE,mBAAmB,GAAGI,EAAE;IAC7BH,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASQ,iBAAiBA,CAACL,EAAE,EAAE;EACpC,OAAO,IAAAL,gBAAO,EAAC;IACbC,GAAG,EAAE,2BAA2B,GAAGI,EAAE;IACrCH,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASS,cAAcA,CAACC,QAAQ,EAAE;EACvC,OAAO,IAAAZ,gBAAO,EAAC;IACbC,GAAG,EAAE,2BAA2B;IAChCC,MAAM,EAAE,KAAK;IACbC,MAAM,EAAE;MAAES,QAAQ,EAAEA;IAAS;EAC/B,CAAC,CAAC;AACJ;;AAEA;AACO,SAASC,gBAAgBA,CAACd,KAAK,EAAE;EACtC,OAAO,IAAAC,gBAAO,EAAC;IACbC,GAAG,EAAE,yBAAyB;IAC9BC,MAAM,EAAE,KAAK;IACbC,MAAM,EAAEJ;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASe,YAAYA,CAAA,EAAG;EAC7B,OAAO,IAAAd,gBAAO,EAAC;IACbC,GAAG,EAAE,4BAA4B;IACjCC,MAAM,EAAE;EACV,CAAC,CAAC;AACJ", "ignoreList": []}]}