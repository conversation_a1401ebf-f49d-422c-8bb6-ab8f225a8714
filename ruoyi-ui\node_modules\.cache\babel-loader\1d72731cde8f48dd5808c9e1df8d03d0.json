{"remainingRequest": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js??ref--13-0!E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\src\\api\\drawing\\drawing.js", "dependencies": [{"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\src\\api\\drawing\\drawing.js", "mtime": 1755499162033}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\babel.config.js", "mtime": 1688548084091}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:InVzZSBzdHJpY3QiOwoKdmFyIF9pbnRlcm9wUmVxdWlyZURlZmF1bHQgPSByZXF1aXJlKCJFOi9qYXZhX3dvcmtzcGFjZS9uZXdfd29ya3NwYWNlL3hjdGcvcnVveWktdWkvbm9kZV9tb2R1bGVzL0BiYWJlbC9ydW50aW1lL2hlbHBlcnMvaW50ZXJvcFJlcXVpcmVEZWZhdWx0LmpzIikuZGVmYXVsdDsKT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsICJfX2VzTW9kdWxlIiwgewogIHZhbHVlOiB0cnVlCn0pOwpleHBvcnRzLmFkZERyYXdpbmcgPSBhZGREcmF3aW5nOwpleHBvcnRzLmFkZFRlY2huaWNhbEFncmVlbWVudCA9IGFkZFRlY2huaWNhbEFncmVlbWVudDsKZXhwb3J0cy5jaGVja1Bhc3MgPSBjaGVja1Bhc3M7CmV4cG9ydHMuY2hlY2tSZWplY3QgPSBjaGVja1JlamVjdDsKZXhwb3J0cy5kZWxEcmF3aW5nID0gZGVsRHJhd2luZzsKZXhwb3J0cy5kb3dubG9hZFRlY2huaWNhbEFncmVlbWVudCA9IGRvd25sb2FkVGVjaG5pY2FsQWdyZWVtZW50OwpleHBvcnRzLmRvd25sb2FkV2F0ZXJNYXJrRmlsZSA9IGRvd25sb2FkV2F0ZXJNYXJrRmlsZTsKZXhwb3J0cy5leHBvcnREcmF3aW5nID0gZXhwb3J0RHJhd2luZzsKZXhwb3J0cy5nZXREcmF3aW5nID0gZ2V0RHJhd2luZzsKZXhwb3J0cy5nZXREcmF3aW5nQnlEcmF3aW5nTm8gPSBnZXREcmF3aW5nQnlEcmF3aW5nTm87CmV4cG9ydHMuZ2V0TG9nTGlzdCA9IGdldExvZ0xpc3Q7CmV4cG9ydHMuZ2V0VGVjaG5pY2FsQWdyZWVtZW50ID0gZ2V0VGVjaG5pY2FsQWdyZWVtZW50OwpleHBvcnRzLmxpc3REcmF3aW5nID0gbGlzdERyYXdpbmc7CmV4cG9ydHMuc2VuZEFwcHJvdmUgPSBzZW5kQXBwcm92ZTsKZXhwb3J0cy51cGRhdGVEcmF3aW5nID0gdXBkYXRlRHJhd2luZzsKZXhwb3J0cy53YWl0QXBwcm92ZUxpc3QgPSB3YWl0QXBwcm92ZUxpc3Q7CnZhciBfcmVxdWVzdCA9IF9pbnRlcm9wUmVxdWlyZURlZmF1bHQocmVxdWlyZSgiQC91dGlscy9yZXF1ZXN0IikpOwp2YXIgX2NvbnN0YW50cyA9IF9pbnRlcm9wUmVxdWlyZURlZmF1bHQocmVxdWlyZSgiY29uc3RhbnRzIikpOwovLyDmn6Xor6Llm77nurjlupPliJfooagKZnVuY3Rpb24gbGlzdERyYXdpbmcocXVlcnkpIHsKICByZXR1cm4gKDAsIF9yZXF1ZXN0LmRlZmF1bHQpKHsKICAgIHVybDogJy93ZWIvZHJhd2luZy9saXN0JywKICAgIG1ldGhvZDogJ2dldCcsCiAgICBwYXJhbXM6IHF1ZXJ5CiAgfSk7Cn0KCi8vIOafpeivouWIhuWOguW+he<PERSON><PERSON>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"}, {"version": 3, "names": ["_request", "_interopRequireDefault", "require", "_constants", "listDrawing", "query", "request", "url", "method", "params", "waitApproveList", "getDrawing", "id", "getTechnicalAgreement", "getDrawingByDrawingNo", "drawingNo", "addDrawing", "data", "addTechnicalAgreement", "updateDrawing", "delDrawing", "getLogList", "exportDrawing", "sendApprove", "checkPass", "checkReject", "downloadWaterMarkFile", "downloadTechnicalAgreement"], "sources": ["E:/java_workspace/new_workspace/xctg/ruoyi-ui/src/api/drawing/drawing.js"], "sourcesContent": ["import request from '@/utils/request'\r\nimport exp from 'constants'\r\n\r\n// 查询图纸库列表\r\nexport function listDrawing(query) {\r\n  return request({\r\n    url: '/web/drawing/list',\r\n    method: 'get',\r\n    params: query\r\n  })\r\n}\r\n\r\n// 查询分厂待审核列表\r\nexport function waitApproveList(query) {\r\n  return request({\r\n    url: '/web/drawing/waitApproveList',\r\n    method: 'get',\r\n    params: query\r\n  })\r\n}\r\n\r\n// 查询图纸库详细\r\nexport function getDrawing(id) {\r\n  return request({\r\n    url: '/web/drawing/' + id,\r\n    method: 'get',\r\n  })\r\n}\r\n\r\n//查看技术协议详情\r\nexport function getTechnicalAgreement(id) {\r\n  return request({\r\n    url: '/web/drawing/getTechnicalAgreement/' + id,\r\n    method: 'get',\r\n  })\r\n}\r\n\r\n// 查询图纸库详细\r\nexport function getDrawingByDrawingNo(drawingNo) {\r\n  return request({\r\n    url: '/web/drawing/drawingNo/' + drawingNo,\r\n    method: 'get',\r\n  })\r\n}\r\n\r\n// 新增图纸库\r\nexport function addDrawing(data) {\r\n  return request({\r\n    url: '/web/drawing',\r\n    method: 'post',\r\n    data: data\r\n  })\r\n}\r\n\r\n//新增技术协议\r\nexport function addTechnicalAgreement(data) {\r\n  return request({\r\n    url: '/web/drawing/addTechnicalAgreement',\r\n    method: 'post',\r\n    data: data\r\n  })\r\n}\r\n\r\n// 修改图纸库\r\nexport function updateDrawing(data) {\r\n  return request({\r\n    url: '/web/drawing',\r\n    method: 'put',\r\n    data: data\r\n  })\r\n}\r\n\r\n// 删除图纸库\r\nexport function delDrawing(id) {\r\n  return request({\r\n    url: '/web/drawing/deleteDrawing',\r\n    method: 'post',\r\n    data: id\r\n  })\r\n}\r\n\r\n//根据drawingNo查询日志\r\nexport function getLogList(query){\r\n  return request({\r\n    url:'/web/drawing/getLogList',\r\n    method:'get',\r\n    params:query\r\n  })\r\n}\r\n\r\n//导出图纸库\r\nexport function exportDrawing(query){\r\n  return request({\r\n    url:'/web/drawing/export',\r\n    method:'get',\r\n    params:query\r\n  })\r\n}\r\n\r\n//推送审核消息\r\nexport function sendApprove(data) {\r\n  return request({\r\n    url: '/web/drawing/sendApproveMessage',\r\n    method: 'post',\r\n    data: data,\r\n  });\r\n}\r\n\r\n//审核通过\r\nexport function checkPass(data){\r\n  return request({\r\n    url:'/web/drawing/checkPass',\r\n    method:'post',\r\n    data:data\r\n  })\r\n}\r\n\r\n//审核驳回\r\nexport function checkReject(data){\r\n  return request({\r\n    url:'/web/drawing/checkReject',\r\n    method:'post',\r\n    data:data\r\n  })\r\n}\r\n\r\nexport function downloadWaterMarkFile(data){\r\n  return request({\r\n    url:'/web/drawing/downloadWaterMarkFile',\r\n    method: 'post',\r\n    data: data\r\n  })\r\n}\r\n\r\nexport function downloadTechnicalAgreement(data){\r\n  return request({\r\n    url:'/web/drawing/downloadTechnicalAgreement',\r\n    method: 'post',\r\n    data: data\r\n  })\r\n}\r\n\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;AAAA,IAAAA,QAAA,GAAAC,sBAAA,CAAAC,OAAA;AACA,IAAAC,UAAA,GAAAF,sBAAA,CAAAC,OAAA;AAEA;AACO,SAASE,WAAWA,CAACC,KAAK,EAAE;EACjC,OAAO,IAAAC,gBAAO,EAAC;IACbC,GAAG,EAAE,mBAAmB;IACxBC,MAAM,EAAE,KAAK;IACbC,MAAM,EAAEJ;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASK,eAAeA,CAACL,KAAK,EAAE;EACrC,OAAO,IAAAC,gBAAO,EAAC;IACbC,GAAG,EAAE,8BAA8B;IACnCC,MAAM,EAAE,KAAK;IACbC,MAAM,EAAEJ;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASM,UAAUA,CAACC,EAAE,EAAE;EAC7B,OAAO,IAAAN,gBAAO,EAAC;IACbC,GAAG,EAAE,eAAe,GAAGK,EAAE;IACzBJ,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASK,qBAAqBA,CAACD,EAAE,EAAE;EACxC,OAAO,IAAAN,gBAAO,EAAC;IACbC,GAAG,EAAE,qCAAqC,GAAGK,EAAE;IAC/CJ,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASM,qBAAqBA,CAACC,SAAS,EAAE;EAC/C,OAAO,IAAAT,gBAAO,EAAC;IACbC,GAAG,EAAE,yBAAyB,GAAGQ,SAAS;IAC1CP,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASQ,UAAUA,CAACC,IAAI,EAAE;EAC/B,OAAO,IAAAX,gBAAO,EAAC;IACbC,GAAG,EAAE,cAAc;IACnBC,MAAM,EAAE,MAAM;IACdS,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAASC,qBAAqBA,CAACD,IAAI,EAAE;EAC1C,OAAO,IAAAX,gBAAO,EAAC;IACbC,GAAG,EAAE,oCAAoC;IACzCC,MAAM,EAAE,MAAM;IACdS,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAASE,aAAaA,CAACF,IAAI,EAAE;EAClC,OAAO,IAAAX,gBAAO,EAAC;IACbC,GAAG,EAAE,cAAc;IACnBC,MAAM,EAAE,KAAK;IACbS,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAASG,UAAUA,CAACR,EAAE,EAAE;EAC7B,OAAO,IAAAN,gBAAO,EAAC;IACbC,GAAG,EAAE,4BAA4B;IACjCC,MAAM,EAAE,MAAM;IACdS,IAAI,EAAEL;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAASS,UAAUA,CAAChB,KAAK,EAAC;EAC/B,OAAO,IAAAC,gBAAO,EAAC;IACbC,GAAG,EAAC,yBAAyB;IAC7BC,MAAM,EAAC,KAAK;IACZC,MAAM,EAACJ;EACT,CAAC,CAAC;AACJ;;AAEA;AACO,SAASiB,aAAaA,CAACjB,KAAK,EAAC;EAClC,OAAO,IAAAC,gBAAO,EAAC;IACbC,GAAG,EAAC,qBAAqB;IACzBC,MAAM,EAAC,KAAK;IACZC,MAAM,EAACJ;EACT,CAAC,CAAC;AACJ;;AAEA;AACO,SAASkB,WAAWA,CAACN,IAAI,EAAE;EAChC,OAAO,IAAAX,gBAAO,EAAC;IACbC,GAAG,EAAE,iCAAiC;IACtCC,MAAM,EAAE,MAAM;IACdS,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAASO,SAASA,CAACP,IAAI,EAAC;EAC7B,OAAO,IAAAX,gBAAO,EAAC;IACbC,GAAG,EAAC,wBAAwB;IAC5BC,MAAM,EAAC,MAAM;IACbS,IAAI,EAACA;EACP,CAAC,CAAC;AACJ;;AAEA;AACO,SAASQ,WAAWA,CAACR,IAAI,EAAC;EAC/B,OAAO,IAAAX,gBAAO,EAAC;IACbC,GAAG,EAAC,0BAA0B;IAC9BC,MAAM,EAAC,MAAM;IACbS,IAAI,EAACA;EACP,CAAC,CAAC;AACJ;AAEO,SAASS,qBAAqBA,CAACT,IAAI,EAAC;EACzC,OAAO,IAAAX,gBAAO,EAAC;IACbC,GAAG,EAAC,oCAAoC;IACxCC,MAAM,EAAE,MAAM;IACdS,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;AAEO,SAASU,0BAA0BA,CAACV,IAAI,EAAC;EAC9C,OAAO,IAAAX,gBAAO,EAAC;IACbC,GAAG,EAAC,yCAAyC;IAC7CC,MAAM,EAAE,MAAM;IACdS,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ", "ignoreList": []}]}