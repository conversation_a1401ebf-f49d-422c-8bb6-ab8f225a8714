package com.ruoyi.app.leave.domain;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 出门证计划申请对象 leave_plan
 * 
 * <AUTHOR>
 * @date 2025-03-19
 */
@Data
public class LeavePlan extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键 */
    private Long id;

    /** 申请编号 */
    private String applyNo;


    private String isSendCar;
    /** 计划号 */
    @Excel(name = "计划号")
    private String planNo;

    /** 计划类型 1-出厂不返回 2-出厂返回 3-跨区调拨 4-退货申请 */
    @Excel(name = "计划类型 1-出厂不返回 2-出厂返回 3-跨区调拨 4-退货申请")
    private Integer planType;

    /** 业务类型
1-通用（出厂不返回）
11-通用（出厂返回）12-委外加工（出厂返回）
21-有计划量计量（跨区调拨）22-短期（跨区调拨）23-钢板（圆钢）（跨区调拨）
31-通用（退货申请） */
    @Excel(name = "业务类型 1-通用", readConverterExp = "出=厂不返回")
    private Integer businessCategory;

    /** 是否计量 计量-1 不计量-0 */
    @Excel(name = "是否计量 计量-1 不计量-0")
    private Integer measureFlag;

    /** 计划量 计划量计量时才存在 */
    @Excel(name = "计划量 计划量计量时才存在")
    private BigDecimal plannedAmount;

    /** 收货单位 */
    @Excel(name = "收货单位")
    private String receiveCompany;

    /** 收货单位code */
    @Excel(name = "收货单位code")
    private String receiveCompanyCode;

    /** 返回单位 */
    @Excel(name = "返回单位")
    private String targetCompany;

    /** 返回单位code */
    @Excel(name = "返回单位code")
    private String targetCompanyCode;

    /** 申请单位 */
    @Excel(name = "申请单位")
        private String sourceCompany;

    /** 申请单位code */
    @Excel(name = "申请单位code")
    private String sourceCompanyCode;

    /** 计划返回时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "计划返回时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date planReturnTime;

    /** 实际返回时间  多次返厂情况下填最新一次 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "实际返回时间  多次返厂情况下填最新一次", width = 30, dateFormat = "yyyy-MM-dd")
    private Date realReturnTime;

    /** 监装人 */
    @Excel(name = "监装人")
    private String monitor;

    /** 物资专管员 */
    @Excel(name = "物资专管员")
    private String specialManager;

    /** 申请有效期 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "申请有效期", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date expireTime;

    /** 出厂原因 */
    @Excel(name = "出厂原因")
    private String reason;

    /** 物资类型 1-钢材 2-钢板 3-其他 */
    @Excel(name = "物资类型 1-钢材 2-钢板 3-其他")
    private Integer itemType;

    /** 合同号 */
    @Excel(name = "合同号")
    private String contractNo;

    /** 计划状态 1-待分厂审批 2-待分厂复审 3-待生产指挥中心审批 4-审批完成  5-已出厂 6-部分收货 7-已完成
11-驳回 12-废弃 13-过期 */
    @Excel(name = "计划状态 1-待分厂审批 2-待分厂复审 3-待生产指挥中心审批 4-审批完成  5-已出厂 6-部分收货 7-已完成 11-驳回 12-废弃 13-过期")
    private Integer planStatus;

    /** 是否复审 1-是 0-否 */
    @Excel(name = "是否复审 1-是 0-否")
    private Integer secApproveFlag;

    /** 开始时间 跨区调拨-有计划量调拨专用 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "开始时间 跨区调拨-有计划量调拨专用", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date startTime;

    /** 申请时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "申请时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date applyTime;

    /** 结束时间 跨区调拨-有计划量调拨专用 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "结束时间 跨区调拨-有计划量调拨专用", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date endTime;

    /** 申请人 */
    @Excel(name = "申请人")
    private String applyWorkNo;

    /** 分厂领导审批时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "分厂领导审批时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date factoryApproveTime;

    /** 退货单位 退货申请专用 */
    @Excel(name = "退货单位 退货申请专用")
    private String refundDepartment;

    /** 退货单位code 退货申请专用 */
    @Excel(name = "退货单位code 退货申请专用")
    private String refundDepartmentCode;

    /** 分厂领导工号 */
    @Excel(name = "分厂领导工号")
    private String factoryApproveWorkNo;

    /** 分厂审核结果 0-拒绝 1-同意 */
    @Excel(name = "分厂审核结果 0-拒绝 1-同意")
    private Integer factoryApproveFlag;

    /** 分厂领导审核意见 */
    @Excel(name = "分厂领导审核意见")
    private String factoryApproveContent;

    /** 分厂复审结果 0-拒绝 1-同意 */
    @Excel(name = "分厂复审结果 0-拒绝 1-同意")
    private Integer factorySecApproveFlag;

    /** 分厂复审时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "分厂复审时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date factorySecApproveTime;

    /** 分厂复审审批人工号 */
    @Excel(name = "分厂复审审批人工号")
    private String factorySecApproveWorkNo;

    /** 分厂复审审核意见 */
    @Excel(name = "分厂复审审核意见")
    private String factorySecApproveContent;

    /** 生产指挥中心审批时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "生产指挥中心审批时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date centerApproveTime;

    /** 生产指挥中心审批人工号 */
    @Excel(name = "生产指挥中心审批人工号")
    private String centerApproveWorkNo;

    /** 生产指挥中心审核结果 0-拒绝 1-同意 */
    @Excel(name = "生产指挥中心审核结果 0-拒绝 1-同意")
    private Integer centerApproveFlag;

    /** 生产指挥中心审核意见 */
    @Excel(name = "生产指挥中心审核意见")
    private String centerApproveContent;

    /** 申请文件，允许多个 */
    @Excel(name = "申请文件，允许多个")
    private String applyFileUrl;

    /**申请图片，允许多个 */
    @Excel(name = "申请图片，允许多个")
    private String applyImgUrl;

    /** 申请人 */
    @Excel(name = "申请人")
    private String applyUserName;
    
    /** 物资列表 */
    private List<LeavePlanMaterial> materials;

    /** 操作日志列表 */
    private List<LeaveLog> leaveLogs;

    /**
     * 小程序列表页 查询开始时间
     */
    private String queryStartTime;

    /**
     * 小程序列表页 查询截止时间
     */
    private String queryEndTime;

    //是否显示审批按钮
    private Boolean approveButtonShow;

    //是否显示驳回按钮
    private Boolean rejectButtonShow;

    //是否显示废弃按钮
    private Boolean discardButtonShow;

    //用于列表状态筛选
    private List<Integer>statusList;

    //判断是否是待审核列表
    private Boolean waitApprove;

    private String statusDesc;

    //是否显示新增按钮
    private Boolean showAddBtn;

    //是否显示修改按钮
    private Boolean showModifyBtn;

    //是否显示废弃按钮
    private Boolean showCancelBtn;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }
    public void setApplyNo(String applyNo) 
    {
        this.applyNo = applyNo;
    }

    public String getApplyNo() 
    {
        return applyNo;
    }
    public void setPlanNo(String planNo) 
    {
        this.planNo = planNo;
    }

    public String getPlanNo() 
    {
        return planNo;
    }
    public void setPlanType(Integer planType) 
    {
        this.planType = planType;
    }

    public Integer getPlanType() 
    {
        return planType;
    }
    public void setBusinessCategory(Integer businessCategory) 
    {
        this.businessCategory = businessCategory;
    }

    public Integer getBusinessCategory() 
    {
        return businessCategory;
    }
    public void setMeasureFlag(Integer measureFlag) 
    {
        this.measureFlag = measureFlag;
    }

    public Integer getMeasureFlag() 
    {
        return measureFlag;
    }
    public void setPlannedAmount(BigDecimal plannedAmount) 
    {
        this.plannedAmount = plannedAmount;
    }

    public BigDecimal getPlannedAmount() 
    {
        return plannedAmount;
    }
    public void setReceiveCompany(String receiveCompany) 
    {
        this.receiveCompany = receiveCompany;
    }

    public String getReceiveCompany() 
    {
        return receiveCompany;
    }
    public void setReceiveCompanyCode(String receiveCompanyCode) 
    {
        this.receiveCompanyCode = receiveCompanyCode;
    }

    public String getReceiveCompanyCode() 
    {
        return receiveCompanyCode;
    }
    public void setTargetCompany(String targetCompany) 
    {
        this.targetCompany = targetCompany;
    }

    public String getTargetCompany() 
    {
        return targetCompany;
    }
    public void setTargetCompanyCode(String targetCompanyCode) 
    {
        this.targetCompanyCode = targetCompanyCode;
    }

    public String getTargetCompanyCode() 
    {
        return targetCompanyCode;
    }
    public void setSourceCompany(String sourceCompany) 
    {
        this.sourceCompany = sourceCompany;
    }

    public String getSourceCompany() 
    {
        return sourceCompany;
    }
    public void setSourceCompanyCode(String sourceCompanyCode) 
    {
        this.sourceCompanyCode = sourceCompanyCode;
    }

    public String getSourceCompanyCode() 
    {
        return sourceCompanyCode;
    }
    public void setPlanReturnTime(Date planReturnTime) 
    {
        this.planReturnTime = planReturnTime;
    }

    public Date getPlanReturnTime() 
    {
        return planReturnTime;
    }
    public void setRealReturnTime(Date realReturnTime) 
    {
        this.realReturnTime = realReturnTime;
    }

    public Date getRealReturnTime() 
    {
        return realReturnTime;
    }
    public void setMonitor(String monitor) 
    {
        this.monitor = monitor;
    }

    public String getMonitor() 
    {
        return monitor;
    }
    public void setSpecialManager(String specialManager) 
    {
        this.specialManager = specialManager;
    }

    public String getSpecialManager() 
    {
        return specialManager;
    }
    public void setExpireTime(Date expireTime) 
    {
        this.expireTime = expireTime;
    }

    public Date getExpireTime() 
    {
        return expireTime;
    }
    public void setReason(String reason) 
    {
        this.reason = reason;
    }

    public String getReason() 
    {
        return reason;
    }
    public void setItemType(Integer itemType) 
    {
        this.itemType = itemType;
    }

    public Integer getItemType() 
    {
        return itemType;
    }
    public void setContractNo(String contractNo) 
    {
        this.contractNo = contractNo;
    }

    public String getContractNo() 
    {
        return contractNo;
    }
    public void setPlanStatus(Integer planStatus) 
    {
        this.planStatus = planStatus;
    }

    public Integer getPlanStatus() 
    {
        return planStatus;
    }
    public void setSecApproveFlag(Integer secApproveFlag) 
    {
        this.secApproveFlag = secApproveFlag;
    }

    public Integer getSecApproveFlag() 
    {
        return secApproveFlag;
    }
    public void setStartTime(Date startTime) 
    {
        this.startTime = startTime;
    }

    public Date getStartTime() 
    {
        return startTime;
    }
    public void setApplyTime(Date applyTime) 
    {
        this.applyTime = applyTime;
    }

    public Date getApplyTime() 
    {
        return applyTime;
    }
    public void setEndTime(Date endTime) 
    {
        this.endTime = endTime;
    }

    public Date getEndTime() 
    {
        return endTime;
    }
    public void setApplyWorkNo(String applyWorkNo) 
    {
        this.applyWorkNo = applyWorkNo;
    }

    public String getApplyWorkNo() 
    {
        return applyWorkNo;
    }
    public void setFactoryApproveTime(Date factoryApproveTime) 
    {
        this.factoryApproveTime = factoryApproveTime;
    }

    public Date getFactoryApproveTime() 
    {
        return factoryApproveTime;
    }
    public void setRefundDepartment(String refundDepartment) 
    {
        this.refundDepartment = refundDepartment;
    }

    public String getRefundDepartment() 
    {
        return refundDepartment;
    }
    public void setRefundDepartmentCode(String refundDepartmentCode) 
    {
        this.refundDepartmentCode = refundDepartmentCode;
    }

    public String getRefundDepartmentCode() 
    {
        return refundDepartmentCode;
    }
    public void setFactoryApproveWorkNo(String factoryApproveWorkNo) 
    {
        this.factoryApproveWorkNo = factoryApproveWorkNo;
    }

    public String getFactoryApproveWorkNo() 
    {
        return factoryApproveWorkNo;
    }
    public void setFactoryApproveFlag(Integer factoryApproveFlag) 
    {
        this.factoryApproveFlag = factoryApproveFlag;
    }

    public Integer getFactoryApproveFlag() 
    {
        return factoryApproveFlag;
    }
    public void setFactoryApproveContent(String factoryApproveContent) 
    {
        this.factoryApproveContent = factoryApproveContent;
    }

    public String getFactoryApproveContent() 
    {
        return factoryApproveContent;
    }
    public void setFactorySecApproveFlag(Integer factorySecApproveFlag) 
    {
        this.factorySecApproveFlag = factorySecApproveFlag;
    }

    public Integer getFactorySecApproveFlag() 
    {
        return factorySecApproveFlag;
    }
    public void setFactorySecApproveTime(Date factorySecApproveTime) 
    {
        this.factorySecApproveTime = factorySecApproveTime;
    }

    public Date getFactorySecApproveTime() 
    {
        return factorySecApproveTime;
    }
    public void setFactorySecApproveWorkNo(String factorySecApproveWorkNo) 
    {
        this.factorySecApproveWorkNo = factorySecApproveWorkNo;
    }

    public String getFactorySecApproveWorkNo() 
    {
        return factorySecApproveWorkNo;
    }
    public void setFactorySecApproveContent(String factorySecApproveContent) 
    {
        this.factorySecApproveContent = factorySecApproveContent;
    }

    public String getFactorySecApproveContent() 
    {
        return factorySecApproveContent;
    }
    public void setCenterApproveTime(Date centerApproveTime) 
    {
        this.centerApproveTime = centerApproveTime;
    }

    public Date getCenterApproveTime() 
    {
        return centerApproveTime;
    }
    public void setCenterApproveWorkNo(String centerApproveWorkNo) 
    {
        this.centerApproveWorkNo = centerApproveWorkNo;
    }

    public String getCenterApproveWorkNo() 
    {
        return centerApproveWorkNo;
    }
    public void setCenterApproveFlag(Integer centerApproveFlag) 
    {
        this.centerApproveFlag = centerApproveFlag;
    }

    public Integer getCenterApproveFlag() 
    {
        return centerApproveFlag;
    }
    public void setCenterApproveContent(String centerApproveContent) 
    {
        this.centerApproveContent = centerApproveContent;
    }

    public String getCenterApproveContent() 
    {
        return centerApproveContent;
    }
    public void setApplyFileUrl(String applyFileUrl) 
    {
        this.applyFileUrl = applyFileUrl;
    }

    public String getApplyFileUrl() 
    {
        return applyFileUrl;
    }
    
    public List<LeavePlanMaterial> getMaterials() 
    {
        return materials;
    }

    public void setMaterials(List<LeavePlanMaterial> materials) 
    {
        this.materials = materials;
    }

    public List<LeaveLog> getLeaveLogs() 
    {
        return leaveLogs;
    }

    public void setLeaveLogs(List<LeaveLog> leaveLogs) 
    {
        this.leaveLogs = leaveLogs;
    }

    public String getApplyUserName() 
    {
        return applyUserName;
    }

    public void setApplyUserName(String applyUserName) 
    {
        this.applyUserName = applyUserName;
    }

    public String getApplyImgUrl() 
    {
        return applyImgUrl;
    }

    public void setApplyImgUrl(String applyImgUrl) 
    {
        this.applyImgUrl = applyImgUrl;
    }

    public String getQueryStartTime() {
        return queryStartTime;
    }

    public void setQueryStartTime(String queryStartTime) {
        this.queryStartTime = queryStartTime;
    }

    public String getQueryEndTime() {
        return queryEndTime;
    }

    public void setQueryEndTime(String queryEndTime) {
        this.queryEndTime = queryEndTime;
    }

    public Boolean getApproveButtonShow() {return approveButtonShow;}

    public void setApproveButtonShow(Boolean approveButtonShow) {this.approveButtonShow = approveButtonShow;}

    public Boolean getRejectButtonShow() {return rejectButtonShow;}

    public void setRejectButtonShow(Boolean rejectButtonShow) {this.rejectButtonShow = rejectButtonShow;}

    public Boolean getDiscardButtonShow() {return discardButtonShow;}

    public void setDiscardButtonShow(Boolean discardButtonShow) {this.discardButtonShow = discardButtonShow;}

    public List<Integer> getStatusList() {return statusList;}

    public void setStatusList(List<Integer> statusList) {this.statusList = statusList;}

    public Boolean getWaitApprove() {
        return waitApprove;
    }

    public void setWaitApprove(Boolean waitApprove) {
        this.waitApprove = waitApprove;
    }

    public String getStatusDesc() {
        return statusDesc;
    }

    public void setStatusDesc(String statusDesc) {
        this.statusDesc = statusDesc;
    }

    public Boolean getShowAddBtn() {
        return showAddBtn;
    }

    public void setShowAddBtn(Boolean showAddBtn) {
        this.showAddBtn = showAddBtn;
    }

    public Boolean getShowModifyBtn() {
        return showModifyBtn;
    }

    public void setShowModifyBtn(Boolean showModifyBtn) {
        this.showModifyBtn = showModifyBtn;
    }

    public Boolean getShowCancelBtn() {
        return showCancelBtn;
    }

    public void setShowCancelBtn(Boolean showCancelBtn) {
        this.showCancelBtn = showCancelBtn;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("applyNo", getApplyNo())
            .append("planNo", getPlanNo())
            .append("planType", getPlanType())
            .append("businessCategory", getBusinessCategory())
            .append("measureFlag", getMeasureFlag())
            .append("plannedAmount", getPlannedAmount())
            .append("receiveCompany", getReceiveCompany())
            .append("receiveCompanyCode", getReceiveCompanyCode())
            .append("targetCompany", getTargetCompany())
            .append("targetCompanyCode", getTargetCompanyCode())
            .append("sourceCompany", getSourceCompany())
            .append("sourceCompanyCode", getSourceCompanyCode())
            .append("planReturnTime", getPlanReturnTime())
            .append("realReturnTime", getRealReturnTime())
            .append("monitor", getMonitor())
            .append("specialManager", getSpecialManager())
            .append("expireTime", getExpireTime())
            .append("reason", getReason())
            .append("itemType", getItemType())
            .append("contractNo", getContractNo())
            .append("planStatus", getPlanStatus())
            .append("secApproveFlag", getSecApproveFlag())
            .append("startTime", getStartTime())
            .append("applyTime", getApplyTime())
            .append("endTime", getEndTime())
            .append("applyWorkNo", getApplyWorkNo())
            .append("factoryApproveTime", getFactoryApproveTime())
            .append("refundDepartment", getRefundDepartment())
            .append("refundDepartmentCode", getRefundDepartmentCode())
            .append("factoryApproveWorkNo", getFactoryApproveWorkNo())
            .append("factoryApproveFlag", getFactoryApproveFlag())
            .append("factoryApproveContent", getFactoryApproveContent())
            .append("factorySecApproveFlag", getFactorySecApproveFlag())
            .append("factorySecApproveTime", getFactorySecApproveTime())
            .append("factorySecApproveWorkNo", getFactorySecApproveWorkNo())
            .append("factorySecApproveContent", getFactorySecApproveContent())
            .append("centerApproveTime", getCenterApproveTime())
            .append("centerApproveWorkNo", getCenterApproveWorkNo())
            .append("centerApproveFlag", getCenterApproveFlag())
            .append("centerApproveContent", getCenterApproveContent())
            .append("applyFileUrl", getApplyFileUrl())
            .append("materials", getMaterials())
            .append("remark", getRemark())
            .append("createTime", getCreateTime())
            .append("createBy", getCreateBy())
            .append("updateTime", getUpdateTime())
            .append("updateBy", getUpdateBy())
            .toString();
    }
}
