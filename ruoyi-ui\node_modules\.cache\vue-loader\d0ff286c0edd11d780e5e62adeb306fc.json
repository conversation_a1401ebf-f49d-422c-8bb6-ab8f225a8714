{"remainingRequest": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\src\\views\\suppPunishment\\punishmentMeasure-module.vue?vue&type=style&index=0&id=ab4bba84&scoped=true&lang=css", "dependencies": [{"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\src\\views\\suppPunishment\\punishmentMeasure-module.vue", "mtime": 1755499162062}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\@vue\\cli-service\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["punishmentMeasure-module.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAoPA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA", "file": "punishmentMeasure-module.vue", "sourceRoot": "src/views/suppPunishment", "sourcesContent": ["<template>\r\n  <el-dialog\r\n    title=\"处罚措施选择\"\r\n    :visible.sync=\"visible\"\r\n    width=\"380px\"\r\n    append-to-body\r\n    @close=\"handleClose\"\r\n  >\r\n    <!-- 处罚措施选项 -->\r\n    <div class=\"measure-options\">\r\n      <el-checkbox-group v-model=\"selectedMeasures\" @change=\"handleMeasureChange\">\r\n        <!-- 处罚金额 -->\r\n        <div class=\"measure-item\">\r\n          <el-checkbox label=\"penalty\" class=\"measure-checkbox\">\r\n            <span class=\"measure-text\">\r\n              处罚\r\n              <el-input-number\r\n                v-model=\"penaltyAmount\"\r\n                controls-position=\"right\"\r\n                :min=\"0\"\r\n                :max=\"9999999\"\r\n                size=\"small\"\r\n                class=\"inline-input-number\"\r\n                @change=\"updateMeasureText\"\r\n                @focus=\"checkPenalty\"\r\n              />\r\n              元\r\n            </span>\r\n          </el-checkbox>\r\n        </div>\r\n\r\n        <!-- 降级 -->\r\n        <div class=\"measure-item\">\r\n          <el-checkbox label=\"downgrade\" class=\"measure-checkbox\">\r\n            <span class=\"measure-text\">降级</span>\r\n          </el-checkbox>\r\n        </div>\r\n\r\n        <!-- 淘汰（禁用） -->\r\n        <div class=\"measure-item\">\r\n          <el-checkbox label=\"eliminate\" class=\"measure-checkbox\">\r\n            <span class=\"measure-text\">淘汰（禁用）</span>\r\n          </el-checkbox>\r\n        </div>\r\n\r\n        <!-- 暂缓 -->\r\n        <div class=\"measure-item\">\r\n          <el-checkbox label=\"suspend\" class=\"measure-checkbox\">\r\n            <span class=\"measure-text\">\r\n              暂缓\r\n              <el-input-number\r\n                v-model=\"suspendMonths\"\r\n                controls-position=\"right\"\r\n                :min=\"0\"\r\n                :max=\"999\"\r\n                size=\"small\"\r\n                class=\"inline-input-number\"\r\n                @change=\"updateMeasureText\"\r\n                @focus=\"checkSuspend\"\r\n              />\r\n              月\r\n            </span>\r\n          </el-checkbox>\r\n        </div>\r\n      </el-checkbox-group>\r\n    </div>\r\n\r\n    <!-- 预览区域 -->\r\n    <div class=\"preview-area\">\r\n      <el-form-item label=\"预览结果：\">\r\n        <el-input\r\n          v-model=\"previewText\"\r\n          type=\"textarea\"\r\n          :rows=\"3\"\r\n          readonly\r\n          placeholder=\"选择处罚措施后将在此显示预览\"\r\n        />\r\n      </el-form-item>\r\n    </div>\r\n\r\n    <!-- 底部按钮 -->\r\n    <div slot=\"footer\" class=\"dialog-footer\">\r\n      <el-button type=\"primary\" @click=\"handleConfirm\">确 定</el-button>\r\n      <el-button @click=\"handleClose\">取 消</el-button>\r\n    </div>\r\n  </el-dialog>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  name: \"PunishmentMeasureDialog\",\r\n  data() {\r\n    return {\r\n      // 是否显示弹出层\r\n      visible: false,\r\n      // 选中的处罚措施\r\n      selectedMeasures: [],\r\n      // 处罚金额\r\n      penaltyAmount: 0,\r\n      // 暂缓月数\r\n      suspendMonths: 0,\r\n      // 预览文本\r\n      previewText: ''\r\n    };\r\n  },\r\n  methods: {\r\n    /** 显示弹窗 */\r\n    show(currentValue = '') {\r\n      this.visible = true;\r\n      this.parseCurrentValue(currentValue);\r\n      this.updatePreview();\r\n    },\r\n    \r\n    /** 隐藏弹窗 */\r\n    hide() {\r\n      this.visible = false;\r\n    },\r\n    \r\n    /** 关闭弹窗 */\r\n    handleClose() {\r\n      this.visible = false;\r\n      this.reset();\r\n    },\r\n    \r\n    /** 重置数据 */\r\n    reset() {\r\n      this.selectedMeasures = [];\r\n      this.penaltyAmount = 0;\r\n      this.suspendMonths = 0;\r\n      this.previewText = '';\r\n    },\r\n    \r\n    /** 解析当前值 */\r\n    parseCurrentValue(value) {\r\n      if (!value) {\r\n        this.reset();\r\n        return;\r\n      }\r\n      \r\n      this.selectedMeasures = [];\r\n      \r\n      // 解析处罚金额\r\n      const penaltyMatch = value.match(/处罚(\\d+)元/);\r\n      if (penaltyMatch) {\r\n        this.selectedMeasures.push('penalty');\r\n        this.penaltyAmount = parseInt(penaltyMatch[1]);\r\n      }\r\n\r\n      // 解析降级\r\n      if (value.includes('降级')) {\r\n        this.selectedMeasures.push('downgrade');\r\n      }\r\n\r\n      // 解析淘汰\r\n      if (value.includes('淘汰（禁用）')) {\r\n        this.selectedMeasures.push('eliminate');\r\n      }\r\n\r\n      // 解析暂缓\r\n      const suspendMatch = value.match(/暂缓(\\d+)月/);\r\n      if (suspendMatch) {\r\n        this.selectedMeasures.push('suspend');\r\n        this.suspendMonths = parseInt(suspendMatch[1]);\r\n      }\r\n    },\r\n    \r\n    /** 处罚措施变化 */\r\n    handleMeasureChange() {\r\n      this.updatePreview();\r\n    },\r\n    \r\n    /** 更新措施文本 */\r\n    updateMeasureText() {\r\n      this.updatePreview();\r\n    },\r\n\r\n    /** 点击处罚金额输入框时自动选中 */\r\n    checkPenalty() {\r\n      if (!this.selectedMeasures.includes('penalty')) {\r\n        this.selectedMeasures.push('penalty');\r\n        this.updatePreview();\r\n      }\r\n    },\r\n\r\n    /** 点击暂缓月数输入框时自动选中 */\r\n    checkSuspend() {\r\n      if (!this.selectedMeasures.includes('suspend')) {\r\n        this.selectedMeasures.push('suspend');\r\n        this.updatePreview();\r\n      }\r\n    },\r\n    \r\n    /** 更新预览 */\r\n    updatePreview() {\r\n      const measures = [];\r\n\r\n      if (this.selectedMeasures.includes('penalty') && this.penaltyAmount > 0) {\r\n        measures.push(`处罚${this.penaltyAmount}元`);\r\n      }\r\n\r\n      if (this.selectedMeasures.includes('downgrade')) {\r\n        measures.push('降级');\r\n      }\r\n\r\n      if (this.selectedMeasures.includes('eliminate')) {\r\n        measures.push('淘汰（禁用）');\r\n      }\r\n\r\n      if (this.selectedMeasures.includes('suspend') && this.suspendMonths > 0) {\r\n        measures.push(`暂缓${this.suspendMonths}月`);\r\n      }\r\n\r\n      this.previewText = measures.join('；');\r\n    },\r\n    \r\n    /** 确认选择 */\r\n    handleConfirm() {\r\n      this.updatePreview();\r\n\r\n      // 验证是否至少选择了一条处罚措施\r\n      if (this.selectedMeasures.length === 0) {\r\n        this.$message.warning('请至少选择一条处罚措施');\r\n        return;\r\n      }\r\n\r\n      // 验证有数值要求的措施是否填写了数值\r\n      if (this.selectedMeasures.includes('penalty') && (!this.penaltyAmount || this.penaltyAmount <= 0)) {\r\n        this.$message.warning('请输入处罚金额');\r\n        return;\r\n      }\r\n\r\n      if (this.selectedMeasures.includes('suspend') && (!this.suspendMonths || this.suspendMonths <= 0)) {\r\n        this.$message.warning('请输入暂缓月数');\r\n        return;\r\n      }\r\n\r\n      this.$emit('select', this.previewText);\r\n      this.handleClose();\r\n    }\r\n  }\r\n};\r\n</script>\r\n\r\n<style scoped>\r\n/* 处罚措施选项样式 */\r\n.measure-options {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.measure-item {\r\n  margin-bottom: 15px;\r\n  padding: 0;\r\n}\r\n\r\n.measure-checkbox {\r\n  width: 100%;\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.measure-text {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n}\r\n\r\n.inline-input {\r\n  width: 120px;\r\n}\r\n\r\n.inline-input ::v-deep .el-input__inner {\r\n  height: 28px;\r\n  line-height: 28px;\r\n}\r\n\r\n.inline-input-number {\r\n  width: 120px;\r\n}\r\n\r\n/* 预览区域样式 */\r\n.preview-area {\r\n  margin-top: 20px;\r\n  padding-top: 20px;\r\n  border-top: 1px solid #e4e7ed;\r\n}\r\n\r\n/* 弹窗标题居中 */\r\n::v-deep .el-dialog__header {\r\n  text-align: center;\r\n  background: #f8f9fa;\r\n  border-bottom: 1px solid #e9ecef;\r\n}\r\n\r\n::v-deep .el-dialog__title {\r\n  text-align: center;\r\n  width: 100%;\r\n  display: block;\r\n  font-weight: 600;\r\n  color: #303133;\r\n}\r\n\r\n/* 弹窗内容区域样式 */\r\n::v-deep .el-dialog__body {\r\n  padding: 20px;\r\n}\r\n\r\n/* 复选框组样式 */\r\n::v-deep .el-checkbox-group {\r\n  width: 100%;\r\n}\r\n\r\n::v-deep .el-checkbox {\r\n  margin-right: 0;\r\n  margin-bottom: 0;\r\n}\r\n\r\n::v-deep .el-checkbox__label {\r\n  display: flex;\r\n  align-items: center;\r\n  width: 100%;\r\n  font-size: 14px;\r\n  color: #606266;\r\n}\r\n\r\n::v-deep .el-checkbox__input.is-checked + .el-checkbox__label {\r\n  color: #409EFF;\r\n}\r\n</style>\r\n"]}]}