{"remainingRequest": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js??ref--13-0!E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\src\\api\\suppPunishment\\service.js", "dependencies": [{"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\src\\api\\suppPunishment\\service.js", "mtime": 1755499162038}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\babel.config.js", "mtime": 1688548084091}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:InVzZSBzdHJpY3QiOwoKdmFyIF9pbnRlcm9wUmVxdWlyZURlZmF1bHQgPSByZXF1aXJlKCJFOi9qYXZhX3dvcmtzcGFjZS9uZXdfd29ya3NwYWNlL3hjdGcvcnVveWktdWkvbm9kZV9tb2R1bGVzL0BiYWJlbC9ydW50aW1lL2hlbHBlcnMvaW50ZXJvcFJlcXVpcmVEZWZhdWx0LmpzIikuZGVmYXVsdDsKT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsICJfX2VzTW9kdWxlIiwgewogIHZhbHVlOiB0cnVlCn0pOwpleHBvcnRzLmxpc3RTZXJ2aWNlID0gbGlzdFNlcnZpY2U7CnZhciBfcmVxdWVzdCA9IF9pbnRlcm9wUmVxdWlyZURlZmF1bHQocmVxdWlyZSgiQC91dGlscy9yZXF1ZXN0IikpOwovLyDmn6Xor6Llt6XnqIvmnI3liqHkv6Hmga/liJfooagKZnVuY3Rpb24gbGlzdFNlcnZpY2UocXVlcnkpIHsKICByZXR1cm4gKDAsIF9yZXF1ZXN0LmRlZmF1bHQpKHsKICAgIHVybDogJy9zdXBwL3B1bmlzaG1lbnQvc2VydmljZUxpc3QnLAogICAgbWV0aG9kOiAnZ2V0JywKICAgIHBhcmFtczogcXVlcnkKICB9KTsKfQ=="}, {"version": 3, "names": ["_request", "_interopRequireDefault", "require", "listService", "query", "request", "url", "method", "params"], "sources": ["E:/java_workspace/new_workspace/xctg/ruoyi-ui/src/api/suppPunishment/service.js"], "sourcesContent": ["import request from '@/utils/request'\r\n\r\n// 查询工程服务信息列表\r\nexport function listService(query) {\r\n  return request({\r\n    url: '/supp/punishment/serviceList',\r\n    method: 'get',\r\n    params: query\r\n  })\r\n}\r\n"], "mappings": ";;;;;;;AAAA,IAAAA,QAAA,GAAAC,sBAAA,CAAAC,OAAA;AAEA;AACO,SAASC,WAAWA,CAACC,KAAK,EAAE;EACjC,OAAO,IAAAC,gBAAO,EAAC;IACbC,GAAG,EAAE,8BAA8B;IACnCC,MAAM,EAAE,KAAK;IACbC,MAAM,EAAEJ;EACV,CAAC,CAAC;AACJ", "ignoreList": []}]}