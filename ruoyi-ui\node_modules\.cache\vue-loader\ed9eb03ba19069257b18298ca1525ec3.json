{"remainingRequest": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\src\\views\\truck\\alloy\\list.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\src\\views\\truck\\alloy\\list.vue", "mtime": 1755499162068}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgeyBsaXN0QWxsb3lPcmRlciwgZXhwb3J0QWxsb3lPcmRlciwgdXBkYXRlQWxsb3lPcmRlclN0YXR1cyB9IGZyb20gJ0AvYXBpL3RydWNrL2FsbG95L3Jlc2VydmF0aW9uJzsNCg0KZXhwb3J0IGRlZmF1bHQgew0KICBuYW1lOiAnQWxsb3lPcmRlckxpc3QnLA0KICBkYXRhKCkgew0KICAgIHJldHVybiB7DQogICAgICBsb2FkaW5nOiB0cnVlLA0KICAgICAgc2hvd1NlYXJjaDogdHJ1ZSwNCiAgICAgIHRvdGFsOiAwLA0KICAgICAgb3JkZXJMaXN0OiBbXSwNCiAgICAgIHF1ZXJ5UGFyYW1zOiB7DQogICAgICAgIHBhZ2VOdW06IDEsDQogICAgICAgIHBhZ2VTaXplOiAxMCwNCiAgICAgICAgcmVzZXJ2YXRpb25ObzogbnVsbCwNCiAgICAgICAgc3VwcGxpZXJTYWxlc05hbWU6IG51bGwsDQogICAgICAgIGFwcGx5Q29tcGFueU5hbWU6IG51bGwsDQogICAgICAgIGFsbG95VHlwZTogbnVsbCwNCiAgICAgICAgYWxsb3lMYWJlbDogbnVsbCwNCiAgICAgICAgY2FyTm86IG51bGwsDQogICAgICAgIGRyaXZlck5hbWU6IG51bGwsDQogICAgICAgIGVudGVyRG9vcjogbnVsbCwNCiAgICAgICAgc3RhdHVzOiBudWxsDQogICAgICB9LA0KICAgICAgc3RhdHVzT3B0aW9uczogWw0KICAgICAgICB7IHZhbHVlOiAnMScsIGxhYmVsOiAn5b6F5a6h5qC4JyB9LA0KICAgICAgICB7IHZhbHVlOiAnMicsIGxhYmVsOiAn5b6F562+5YiwJyB9LA0KICAgICAgICB7IHZhbHVlOiAnMycsIGxhYmVsOiAn54mp566h5YiG6YWNJyB9LA0KICAgICAgICB7IHZhbHVlOiAnNCcsIGxhYmVsOiAn5b6F5YWl5Y6CJyB9LA0KICAgICAgICB7IHZhbHVlOiAnNScsIGxhYmVsOiAn5bey5YWl5Y6CJyB9LA0KICAgICAgICB7IHZhbHVlOiAnNicsIGxhYmVsOiAn54mp566h56Gu6K6kJyB9LA0KICAgICAgICB7IHZhbHVlOiAnNycsIGxhYmVsOiAn5bey5Ye65Y6CJyB9LA0KICAgICAgICB7IHZhbHVlOiAnMjEnLCBsYWJlbDogJ+mps+WbnicgfSwNCiAgICAgICAgeyB2YWx1ZTogJzIyJywgbGFiZWw6ICflvoXlj5bmtognIH0sDQogICAgICAgIHsgdmFsdWU6ICcyMycsIGxhYmVsOiAn5bey5Y+W5raIJyB9DQogICAgICBdLA0KICAgICAgZWRpdFN0YXR1c0RpYWxvZ1Zpc2libGU6IGZhbHNlLA0KICAgICAgZWRpdFN0YXR1c0Zvcm06IHsNCiAgICAgICAgcmVzZXJ2YXRpb25ObzogJycsDQogICAgICAgIHN0YXR1czogJycNCiAgICAgIH0NCiAgICB9Ow0KICB9LA0KICBjcmVhdGVkKCkgew0KICAgIHRoaXMuZ2V0TGlzdCgpOw0KICB9LA0KICBtZXRob2RzOiB7DQogICAgZ2V0TGlzdCgpIHsNCiAgICAgIHRoaXMubG9hZGluZyA9IHRydWU7DQogICAgICBsaXN0QWxsb3lPcmRlcih0aGlzLnF1ZXJ5UGFyYW1zKS50aGVuKHJlc3BvbnNlID0+IHsNCiAgICAgICAgdGhpcy5vcmRlckxpc3QgPSByZXNwb25zZS5yb3dzIHx8IFtdOw0KICAgICAgICB0aGlzLnRvdGFsID0gcmVzcG9uc2UudG90YWwgfHwgMDsNCiAgICAgICAgdGhpcy5sb2FkaW5nID0gZmFsc2U7DQogICAgICB9KTsNCiAgICB9LA0KICAgIGhhbmRsZVF1ZXJ5KCkgew0KICAgICAgdGhpcy5xdWVyeVBhcmFtcy5wYWdlTnVtID0gMTsNCiAgICAgIHRoaXMuZ2V0TGlzdCgpOw0KICAgIH0sDQogICAgcmVzZXRRdWVyeSgpIHsNCiAgICAgIHRoaXMuJHJlZnMucXVlcnlGb3JtLnJlc2V0RmllbGRzKCk7DQogICAgICB0aGlzLmhhbmRsZVF1ZXJ5KCk7DQogICAgfSwNCiAgICBoYW5kbGVEZXRhaWwocm93KSB7DQogICAgICB0aGlzLiRyb3V0ZXIucHVzaChgL3RydWNrL2FsbG95L2RldGFpbC8ke3Jvdy5yZXNlcnZhdGlvbk5vfWApOw0KICAgIH0sDQogICAgaGFuZGxlRXhwb3J0KCkgew0KICAgICAgY29uc3QgcXVlcnlQYXJhbXMgPSB0aGlzLnF1ZXJ5UGFyYW1zOw0KICAgICAgdGhpcy4kY29uZmlybSgn5piv5ZCm56Gu6K6k5a+85Ye65omA5pyJ5ZCI6YeR6L2m6L6G6aKE57qm5pWw5o2u6aG5PycsICLorablkYoiLCB7DQogICAgICAgIGNvbmZpcm1CdXR0b25UZXh0OiAi56Gu5a6aIiwNCiAgICAgICAgY2FuY2VsQnV0dG9uVGV4dDogIuWPlua2iCIsDQogICAgICAgIHR5cGU6ICJ3YXJuaW5nIg0KICAgICAgfSkudGhlbigoKSA9PiB7DQogICAgICAgIHJldHVybiBleHBvcnRBbGxveU9yZGVyKHF1ZXJ5UGFyYW1zKTsNCiAgICAgIH0pLnRoZW4ocmVzcG9uc2UgPT4gew0KICAgICAgICB0aGlzLmRvd25sb2FkKHJlc3BvbnNlLm1zZyk7DQogICAgICB9KTsNCiAgICB9LA0KICAgIG9wZW5FZGl0U3RhdHVzRGlhbG9nKHJvdykgew0KICAgICAgdGhpcy5lZGl0U3RhdHVzRm9ybS5yZXNlcnZhdGlvbk5vID0gcm93LnJlc2VydmF0aW9uTm87DQogICAgICB0aGlzLmVkaXRTdGF0dXNGb3JtLnN0YXR1cyA9IHJvdy5zdGF0dXM7DQogICAgICB0aGlzLmVkaXRTdGF0dXNEaWFsb2dWaXNpYmxlID0gdHJ1ZTsNCiAgICB9LA0KICAgIHN1Ym1pdEVkaXRTdGF0dXMoKSB7DQogICAgICB1cGRhdGVBbGxveU9yZGVyU3RhdHVzKHRoaXMuZWRpdFN0YXR1c0Zvcm0pLnRoZW4ocmVzcG9uc2UgPT4gew0KICAgICAgICBpZiAocmVzcG9uc2UuY29kZSA9PT0gMjAwKSB7DQogICAgICAgICAgdGhpcy4kbWVzc2FnZS5zdWNjZXNzKCfnirbmgIHkv67mlLnmiJDlip8nKTsNCiAgICAgICAgICB0aGlzLmVkaXRTdGF0dXNEaWFsb2dWaXNpYmxlID0gZmFsc2U7DQogICAgICAgICAgdGhpcy5nZXRMaXN0KCk7DQogICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcihyZXNwb25zZS5tc2cgfHwgJ+eKtuaAgeS/ruaUueWksei0pScpOw0KICAgICAgICB9DQogICAgICB9KTsNCiAgICB9LA0KICAgIHBhcnNlVGltZSh0aW1lLCBwYXR0ZXJuKSB7DQogICAgICBpZiAoIXRpbWUpIHJldHVybiAnJzsNCiAgICAgIGNvbnN0IGRhdGUgPSBuZXcgRGF0ZSh0aW1lKTsNCiAgICAgIGlmIChpc05hTihkYXRlLmdldFRpbWUoKSkpIHJldHVybiB0aW1lOw0KDQogICAgICBjb25zdCB5ID0gZGF0ZS5nZXRGdWxsWWVhcigpOw0KICAgICAgY29uc3QgbSA9IChkYXRlLmdldE1vbnRoKCkgKyAxKS50b1N0cmluZygpLnBhZFN0YXJ0KDIsICcwJyk7DQogICAgICBjb25zdCBkID0gZGF0ZS5nZXREYXRlKCkudG9TdHJpbmcoKS5wYWRTdGFydCgyLCAnMCcpOw0KICAgICAgY29uc3QgaCA9IGRhdGUuZ2V0SG91cnMoKS50b1N0cmluZygpLnBhZFN0YXJ0KDIsICcwJyk7DQogICAgICBjb25zdCBpID0gZGF0ZS5nZXRNaW51dGVzKCkudG9TdHJpbmcoKS5wYWRTdGFydCgyLCAnMCcpOw0KICAgICAgY29uc3QgcyA9IGRhdGUuZ2V0U2Vjb25kcygpLnRvU3RyaW5nKCkucGFkU3RhcnQoMiwgJzAnKTsNCg0KICAgICAgaWYgKHBhdHRlcm4gPT09ICd7eX0te219LXtkfScpIHsNCiAgICAgICAgcmV0dXJuIGAke3l9LSR7bX0tJHtkfWA7DQogICAgICB9IGVsc2UgaWYgKHBhdHRlcm4gPT09ICd7eX0te219LXtkfSB7aH06e2l9Jykgew0KICAgICAgICByZXR1cm4gYCR7eX0tJHttfS0ke2R9ICR7aH06JHtpfWA7DQogICAgICB9IGVsc2UgaWYgKHBhdHRlcm4gPT09ICd7eX0te219LXtkfSB7aH06e2l9OntzfScpIHsNCiAgICAgICAgcmV0dXJuIGAke3l9LSR7bX0tJHtkfSAke2h9OiR7aX06JHtzfWA7DQogICAgICB9DQogICAgICByZXR1cm4gdGltZTsNCiAgICB9LA0KICAgIGdldEJ1c2luZXNzRGVwdCh2YWwpIHsNCiAgICAgIGlmICh2YWwgPT0gJzEnKSByZXR1cm4gJ+mHh+i0reS4reW/gyc7DQogICAgICBpZiAodmFsID09ICcyJykgcmV0dXJuICfllYbliqHpg6gnOw0KICAgICAgcmV0dXJuICcnOw0KICAgIH0sDQogICAgZ2V0RW50cmFuY2VHYXRlKHZhbCkgew0KICAgICAgaWYgKHZhbCA9PSAnMScpIHJldHVybiAn5a6J5YWo5p2RJzsNCiAgICAgIGlmICh2YWwgPT0gJzInKSByZXR1cm4gJ+S4ieWPt+mXqCc7DQogICAgICByZXR1cm4gJyc7DQogICAgfSwNCiAgICBnZXRFbGVjdHJvZGVEZXNjKHZhbCkgew0KICAgICAgaWYgKHZhbCA9PSAnMScpIHJldHVybiAnNDAwJzsNCiAgICAgIGlmICh2YWwgPT0gJzInKSByZXR1cm4gJzQ1MCc7DQogICAgICBpZiAodmFsID09ICczJykgcmV0dXJuICc3MDAnOw0KICAgICAgcmV0dXJuICcnOw0KICAgIH0sDQogICAgZ2V0U3RhdHVzTGFiZWwodmFsKSB7DQogICAgICBjb25zdCBpdGVtID0gdGhpcy5zdGF0dXNPcHRpb25zLmZpbmQoaSA9PiBpLnZhbHVlID09IHZhbCk7DQogICAgICByZXR1cm4gaXRlbSA/IGl0ZW0ubGFiZWwgOiAn5pyq55+lJzsNCiAgICB9LA0KICAgIGdldFN0YXR1c1RhZ1R5cGUodmFsKSB7DQogICAgICBjb25zdCBzdGF0dXNUeXBlTWFwID0gew0KICAgICAgICAnMSc6ICd3YXJuaW5nJywgICAgLy8g5b6F5a6h5qC4DQogICAgICAgICcyJzogJ2luZm8nLCAgICAgICAvLyDlvoXnrb7liLANCiAgICAgICAgJzMnOiAncHJpbWFyeScsICAgIC8vIOeJqeeuoeWIhumFjQ0KICAgICAgICAnNCc6ICd3YXJuaW5nJywgICAgLy8g5b6F5YWl5Y6CDQogICAgICAgICc1JzogJ3N1Y2Nlc3MnLCAgICAvLyDlt7LlhaXljoINCiAgICAgICAgJzYnOiAncHJpbWFyeScsICAgIC8vIOeJqeeuoeehruiupA0KICAgICAgICAnNyc6ICdzdWNjZXNzJywgICAgLy8g5bey5Ye65Y6CDQogICAgICAgICcyMSc6ICdkYW5nZXInLCAgICAvLyDpqbPlm54NCiAgICAgICAgJzIyJzogJ3dhcm5pbmcnLCAgIC8vIOW+heWPlua2iA0KICAgICAgICAnMjMnOiAnaW5mbycgICAgICAgLy8g5bey5Y+W5raIDQogICAgICB9Ow0KICAgICAgcmV0dXJuIHN0YXR1c1R5cGVNYXBbdmFsXSB8fCAnaW5mbyc7DQogICAgfQ0KICB9DQp9Ow0K"}, {"version": 3, "sources": ["list.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAoJA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "list.vue", "sourceRoot": "src/views/truck/alloy", "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <el-form :model=\"queryParams\" ref=\"queryForm\" :inline=\"true\" v-show=\"showSearch\" label-width=\"auto\">\r\n      <el-form-item label=\"预约编号\" prop=\"reservationNo\">\r\n        <el-input v-model=\"queryParams.reservationNo\" placeholder=\"请输入预约编号\" clearable size=\"small\"\r\n          @keyup.enter.native=\"handleQuery\" />\r\n      </el-form-item>\r\n      <el-form-item label=\"业务部门\" prop=\"approvalDept\">\r\n        <el-select v-model=\"queryParams.approvalDept\" placeholder=\"请选择业务部门\" clearable size=\"small\">\r\n          <el-option label=\"采购中心\" :value=\"1\" />\r\n          <el-option label=\"商务部\" :value=\"2\" />\r\n        </el-select>\r\n      </el-form-item>\r\n      <el-form-item label=\"供方业务员姓名\" prop=\"supplierSalesName\">\r\n        <el-input v-model=\"queryParams.supplierSalesName\" placeholder=\"请输入供方业务员姓名\" clearable size=\"small\"\r\n          @keyup.enter.native=\"handleQuery\" />\r\n      </el-form-item>\r\n      <el-form-item label=\"申请单位名称\" prop=\"applyCompanyName\">\r\n        <el-input v-model=\"queryParams.applyCompanyName\" placeholder=\"请输入申请单位名称\" clearable size=\"small\"\r\n          @keyup.enter.native=\"handleQuery\" />\r\n      </el-form-item>\r\n      <el-form-item label=\"合金类型\" prop=\"alloyType\">\r\n        <el-input v-model=\"queryParams.alloyType\" placeholder=\"请输入合金类型\" clearable size=\"small\"\r\n          @keyup.enter.native=\"handleQuery\" />\r\n      </el-form-item>\r\n      <el-form-item label=\"合金\" prop=\"alloyLabel\">\r\n        <el-input v-model=\"queryParams.alloyLabel\" placeholder=\"请输入合金名称\" clearable size=\"small\"\r\n          @keyup.enter.native=\"handleQuery\" />\r\n      </el-form-item>\r\n      <el-form-item label=\"电极规格\" prop=\"electrodeType\">\r\n        <el-select v-model=\"queryParams.electrodeType\" placeholder=\"电极规格\" clearable size=\"small\">\r\n          <el-option label=\"400\" :value=\"1\" />\r\n          <el-option label=\"450\" :value=\"2\" />\r\n          <el-option label=\"700\" :value=\"3\" />\r\n        </el-select>\r\n      </el-form-item>\r\n      <el-form-item label=\"车牌号\" prop=\"carNo\">\r\n        <el-input v-model=\"queryParams.carNo\" placeholder=\"请输入车牌号\" clearable size=\"small\"\r\n          @keyup.enter.native=\"handleQuery\" />\r\n      </el-form-item>\r\n      <el-form-item label=\"司机名\" prop=\"driverName\">\r\n        <el-input v-model=\"queryParams.driverName\" placeholder=\"请输入司机名\" clearable size=\"small\"\r\n          @keyup.enter.native=\"handleQuery\" />\r\n      </el-form-item>\r\n      <el-form-item label=\"进厂大门\" prop=\"enterDoor\">\r\n        <el-select v-model=\"queryParams.enterDoor\" placeholder=\"请选择进厂大门\" clearable size=\"small\">\r\n          <el-option label=\"安全村\" :value=\"1\" />\r\n          <el-option label=\"三号门\" :value=\"2\" />\r\n        </el-select>\r\n      </el-form-item>\r\n      <el-form-item label=\"状态\" prop=\"status\">\r\n        <el-select v-model=\"queryParams.status\" placeholder=\"请选择状态\" clearable size=\"small\">\r\n          <el-option v-for=\"item in statusOptions\" :key=\"item.value\" :label=\"item.label\" :value=\"item.value\" />\r\n        </el-select>\r\n      </el-form-item>\r\n      <el-form-item>\r\n        <el-button type=\"cyan\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleQuery\">搜索</el-button>\r\n        <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\r\n      </el-form-item>\r\n    </el-form>\r\n\r\n    <el-row :gutter=\"10\" class=\"mb8\">\r\n      <el-col :span=\"1.5\">\r\n        <el-button type=\"warning\" icon=\"el-icon-download\" size=\"mini\" @click=\"handleExport\">导出</el-button>\r\n      </el-col>\r\n      <right-toolbar :showSearch.sync=\"showSearch\" @queryTable=\"getList\"></right-toolbar>\r\n    </el-row>\r\n\r\n    <el-table v-loading=\"loading\" :data=\"orderList\">\r\n      <el-table-column label=\"预约编号\" align=\"center\" prop=\"reservationNo\" width=\"150\" />\r\n      <el-table-column label=\"业务部门\" align=\"center\">\r\n        <template slot-scope=\"scope\">\r\n          <span>{{ getBusinessDept(scope.row.approvalDept) }}</span>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"申请单位\" align=\"center\" prop=\"applyCompanyName\" width=\"180\" />\r\n      <el-table-column label=\"合金类型\" align=\"center\" prop=\"alloyType\" />\r\n      <el-table-column label=\"合金\" align=\"center\" prop=\"alloyLabel\" />\r\n      <el-table-column label=\"合金吨数\" align=\"center\" prop=\"estimatedWeight\" />\r\n      <el-table-column label=\"电极规格\" align=\"center\" prop=\"electrodeType\" width=\"90\">\r\n        <template slot-scope=\"scope\">\r\n          <span>{{ getElectrodeDesc(scope.row.electrodeType) }}</span>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"预计送货日期\" align=\"center\" prop=\"expectedDeliveryTime\" width=\"120\">\r\n        <template slot-scope=\"scope\">\r\n          <span>{{ parseTime(scope.row.expectedDeliveryTime, '{y}-{m}-{d}') }}</span>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"车牌\" align=\"center\" prop=\"carNo\" width=\"90\" />\r\n      <el-table-column label=\"有效开始时间\" align=\"center\" prop=\"effectiveStartTime\" width=\"180\">\r\n        <template slot-scope=\"scope\">\r\n          <span>{{ parseTime(scope.row.effectiveStartTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"有效结束时间\" align=\"center\" prop=\"effectiveEndTime\" width=\"180\">\r\n        <template slot-scope=\"scope\">\r\n          <span>{{ parseTime(scope.row.effectiveEndTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"进厂大门\" align=\"center\" prop=\"enterDoor\">\r\n        <template slot-scope=\"scope\">\r\n          <span>{{ getEntranceGate(scope.row.enterDoor) }}</span>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"司机\" align=\"center\" prop=\"driverName\" />\r\n      <el-table-column label=\"状态\" align=\"center\" prop=\"status\">\r\n        <template slot-scope=\"scope\">\r\n          <el-tag :type=\"getStatusTagType(scope.row.status)\" size=\"small\">\r\n            {{ getStatusLabel(scope.row.status) }}\r\n          </el-tag>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"供方业务员\" align=\"center\" prop=\"supplierSalesName\" width=\"100\" />\r\n      <el-table-column label=\"创建时间\" align=\"center\" prop=\"createTime\" width=\"180\">\r\n        <template slot-scope=\"scope\">\r\n          <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"操作\" align=\"center\" class-name=\"small-padding fixed-width\" fixed=\"right\">\r\n        <template slot-scope=\"scope\">\r\n          <el-button size=\"mini\" type=\"text\" icon=\"el-icon-view\" @click=\"handleDetail(scope.row)\">详情</el-button>\r\n          <el-button size=\"mini\" type=\"text\" icon=\"el-icon-edit\"\r\n            @click=\"openEditStatusDialog(scope.row)\">修改状态</el-button>\r\n        </template>\r\n      </el-table-column>\r\n    </el-table>\r\n\r\n    <pagination v-show=\"total > 0\" :total=\"total\" :page.sync=\"queryParams.pageNum\" :limit.sync=\"queryParams.pageSize\"\r\n      @pagination=\"getList\" />\r\n\r\n    <el-dialog :title=\"'修改预约单状态'\" :visible.sync=\"editStatusDialogVisible\" width=\"400px\">\r\n      <el-form :model=\"editStatusForm\" label-width=\"80px\">\r\n        <el-form-item label=\"状态\">\r\n          <el-select v-model=\"editStatusForm.status\" placeholder=\"请选择状态\">\r\n            <el-option v-for=\"item in statusOptions\" :key=\"item.value\" :label=\"item.label\" :value=\"item.value\" />\r\n          </el-select>\r\n        </el-form-item>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"editStatusDialogVisible = false\">取消</el-button>\r\n        <el-button type=\"primary\" @click=\"submitEditStatus\">确定</el-button>\r\n      </div>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { listAlloyOrder, exportAlloyOrder, updateAlloyOrderStatus } from '@/api/truck/alloy/reservation';\r\n\r\nexport default {\r\n  name: 'AlloyOrderList',\r\n  data() {\r\n    return {\r\n      loading: true,\r\n      showSearch: true,\r\n      total: 0,\r\n      orderList: [],\r\n      queryParams: {\r\n        pageNum: 1,\r\n        pageSize: 10,\r\n        reservationNo: null,\r\n        supplierSalesName: null,\r\n        applyCompanyName: null,\r\n        alloyType: null,\r\n        alloyLabel: null,\r\n        carNo: null,\r\n        driverName: null,\r\n        enterDoor: null,\r\n        status: null\r\n      },\r\n      statusOptions: [\r\n        { value: '1', label: '待审核' },\r\n        { value: '2', label: '待签到' },\r\n        { value: '3', label: '物管分配' },\r\n        { value: '4', label: '待入厂' },\r\n        { value: '5', label: '已入厂' },\r\n        { value: '6', label: '物管确认' },\r\n        { value: '7', label: '已出厂' },\r\n        { value: '21', label: '驳回' },\r\n        { value: '22', label: '待取消' },\r\n        { value: '23', label: '已取消' }\r\n      ],\r\n      editStatusDialogVisible: false,\r\n      editStatusForm: {\r\n        reservationNo: '',\r\n        status: ''\r\n      }\r\n    };\r\n  },\r\n  created() {\r\n    this.getList();\r\n  },\r\n  methods: {\r\n    getList() {\r\n      this.loading = true;\r\n      listAlloyOrder(this.queryParams).then(response => {\r\n        this.orderList = response.rows || [];\r\n        this.total = response.total || 0;\r\n        this.loading = false;\r\n      });\r\n    },\r\n    handleQuery() {\r\n      this.queryParams.pageNum = 1;\r\n      this.getList();\r\n    },\r\n    resetQuery() {\r\n      this.$refs.queryForm.resetFields();\r\n      this.handleQuery();\r\n    },\r\n    handleDetail(row) {\r\n      this.$router.push(`/truck/alloy/detail/${row.reservationNo}`);\r\n    },\r\n    handleExport() {\r\n      const queryParams = this.queryParams;\r\n      this.$confirm('是否确认导出所有合金车辆预约数据项?', \"警告\", {\r\n        confirmButtonText: \"确定\",\r\n        cancelButtonText: \"取消\",\r\n        type: \"warning\"\r\n      }).then(() => {\r\n        return exportAlloyOrder(queryParams);\r\n      }).then(response => {\r\n        this.download(response.msg);\r\n      });\r\n    },\r\n    openEditStatusDialog(row) {\r\n      this.editStatusForm.reservationNo = row.reservationNo;\r\n      this.editStatusForm.status = row.status;\r\n      this.editStatusDialogVisible = true;\r\n    },\r\n    submitEditStatus() {\r\n      updateAlloyOrderStatus(this.editStatusForm).then(response => {\r\n        if (response.code === 200) {\r\n          this.$message.success('状态修改成功');\r\n          this.editStatusDialogVisible = false;\r\n          this.getList();\r\n        } else {\r\n          this.$message.error(response.msg || '状态修改失败');\r\n        }\r\n      });\r\n    },\r\n    parseTime(time, pattern) {\r\n      if (!time) return '';\r\n      const date = new Date(time);\r\n      if (isNaN(date.getTime())) return time;\r\n\r\n      const y = date.getFullYear();\r\n      const m = (date.getMonth() + 1).toString().padStart(2, '0');\r\n      const d = date.getDate().toString().padStart(2, '0');\r\n      const h = date.getHours().toString().padStart(2, '0');\r\n      const i = date.getMinutes().toString().padStart(2, '0');\r\n      const s = date.getSeconds().toString().padStart(2, '0');\r\n\r\n      if (pattern === '{y}-{m}-{d}') {\r\n        return `${y}-${m}-${d}`;\r\n      } else if (pattern === '{y}-{m}-{d} {h}:{i}') {\r\n        return `${y}-${m}-${d} ${h}:${i}`;\r\n      } else if (pattern === '{y}-{m}-{d} {h}:{i}:{s}') {\r\n        return `${y}-${m}-${d} ${h}:${i}:${s}`;\r\n      }\r\n      return time;\r\n    },\r\n    getBusinessDept(val) {\r\n      if (val == '1') return '采购中心';\r\n      if (val == '2') return '商务部';\r\n      return '';\r\n    },\r\n    getEntranceGate(val) {\r\n      if (val == '1') return '安全村';\r\n      if (val == '2') return '三号门';\r\n      return '';\r\n    },\r\n    getElectrodeDesc(val) {\r\n      if (val == '1') return '400';\r\n      if (val == '2') return '450';\r\n      if (val == '3') return '700';\r\n      return '';\r\n    },\r\n    getStatusLabel(val) {\r\n      const item = this.statusOptions.find(i => i.value == val);\r\n      return item ? item.label : '未知';\r\n    },\r\n    getStatusTagType(val) {\r\n      const statusTypeMap = {\r\n        '1': 'warning',    // 待审核\r\n        '2': 'info',       // 待签到\r\n        '3': 'primary',    // 物管分配\r\n        '4': 'warning',    // 待入厂\r\n        '5': 'success',    // 已入厂\r\n        '6': 'primary',    // 物管确认\r\n        '7': 'success',    // 已出厂\r\n        '21': 'danger',    // 驳回\r\n        '22': 'warning',   // 待取消\r\n        '23': 'info'       // 已取消\r\n      };\r\n      return statusTypeMap[val] || 'info';\r\n    }\r\n  }\r\n};\r\n</script>\r\n"]}]}