{"remainingRequest": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\src\\views\\truck\\alloy\\detail.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\src\\views\\truck\\alloy\\detail.vue", "mtime": 1755499162067}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgeyBnZXRBbGxveU9yZGVyRGV0YWlsLCB1cGRhdGVBbGxveU9yZGVyU3RhdHVzIH0gZnJvbSAnQC9hcGkvdHJ1Y2svYWxsb3kvcmVzZXJ2YXRpb24nOw0KDQpleHBvcnQgZGVmYXVsdCB7DQogIG5hbWU6ICdBbGxveU9yZGVyRGV0YWlsJywNCiAgZGF0YSgpIHsNCiAgICByZXR1cm4gew0KICAgICAgb3JkZXJJbmZvOiB7fSwNCiAgICAgIGxvZ0xpc3Q6IFtdLA0KICAgICAgZHJpdmVySW1nOiBbXSwNCiAgICAgIHBob3RvVGVtcDogW10sDQogICAgICB2ZWhpY2xlTGljZW5zZUxpc3Q6IFtdLA0KICAgICAgdmVoaWNsZUxpY2Vuc2VMaXN0VGVtcDogW10sDQogICAgICBkcml2ZXJMaWNlbnNlTGlzdDogW10sDQogICAgICBkcml2ZXJMaWNlbnNlTGlzdFRlbXA6IFtdLA0KICAgICAgbGFiZWxTdHlsZTogew0KICAgICAgICB3aWR0aDogJzIwMHB4JywNCiAgICAgIH0sDQogICAgICBlZGl0U3RhdHVzRGlhbG9nVmlzaWJsZTogZmFsc2UsDQogICAgICBlZGl0U3RhdHVzRm9ybTogew0KICAgICAgICByZXNlcnZhdGlvbk5vOiAnJywNCiAgICAgICAgc3RhdHVzOiAnJw0KICAgICAgfSwNCiAgICAgIHN0YXR1c09wdGlvbnM6IFsNCiAgICAgICAgeyB2YWx1ZTogJzEnLCBsYWJlbDogJ+W+heWuoeaguCcgfSwNCiAgICAgICAgeyB2YWx1ZTogJzInLCBsYWJlbDogJ+W+heetvuWIsCcgfSwNCiAgICAgICAgeyB2YWx1ZTogJzMnLCBsYWJlbDogJ+eJqeeuoeWIhumFjScgfSwNCiAgICAgICAgeyB2YWx1ZTogJzQnLCBsYWJlbDogJ+W+heWFpeWOgicgfSwNCiAgICAgICAgeyB2YWx1ZTogJzUnLCBsYWJlbDogJ+W3suWFpeWOgicgfSwNCiAgICAgICAgeyB2YWx1ZTogJzYnLCBsYWJlbDogJ+eJqeeuoeehruiupCcgfSwNCiAgICAgICAgeyB2YWx1ZTogJzcnLCBsYWJlbDogJ+W3suWHuuWOgicgfSwNCiAgICAgICAgeyB2YWx1ZTogJzIxJywgbGFiZWw6ICfpqbPlm54nIH0sDQogICAgICAgIHsgdmFsdWU6ICcyMicsIGxhYmVsOiAn5b6F5Y+W5raIJyB9LA0KICAgICAgICB7IHZhbHVlOiAnMjMnLCBsYWJlbDogJ+W3suWPlua2iCcgfQ0KICAgICAgXSwNCiAgICB9Ow0KICB9LA0KICBjcmVhdGVkKCkgew0KICAgIHRoaXMuZ2V0RGV0YWlsKCk7DQogIH0sDQogIG1ldGhvZHM6IHsNCiAgICBnZXREZXRhaWwoKSB7DQogICAgICBjb25zdCByZXNlcnZhdGlvbk5vID0gdGhpcy4kcm91dGUucGFyYW1zLnJlc2VydmF0aW9uTm87DQogICAgICBnZXRBbGxveU9yZGVyRGV0YWlsKHsgcmVzZXJ2YXRpb25ObyB9KS50aGVuKChyZXNwb25zZSkgPT4gew0KICAgICAgICBpZiAocmVzcG9uc2UuY29kZSA9PT0gMjAwKSB7DQogICAgICAgICAgdGhpcy5vcmRlckluZm8gPSByZXNwb25zZS5kYXRhOw0KICAgICAgICAgIHRoaXMubG9nTGlzdCA9IHJlc3BvbnNlLmRhdGEubWF0TWdtdEFsbG95TG9ncyB8fCBbXTsNCiAgICAgICAgICAvLyDlpITnkIblm77niYflrZfmrrUNCiAgICAgICAgICB0cnkgew0KICAgICAgICAgICAgdGhpcy5waG90b1RlbXAgPSB0aGlzLm9yZGVySW5mby5kcml2ZXJGYWNlSW1nID8gSlNPTi5wYXJzZSh0aGlzLm9yZGVySW5mby5kcml2ZXJGYWNlSW1nKSA6IFtdOw0KICAgICAgICAgIH0gY2F0Y2ggeyB0aGlzLnBob3RvVGVtcCA9IFtdOyB9DQogICAgICAgICAgdHJ5IHsNCiAgICAgICAgICAgIHRoaXMuZHJpdmVyTGljZW5zZUxpc3RUZW1wID0gdGhpcy5vcmRlckluZm8uZHJpdmVyTGljZW5zZUltZ3MgPyBKU09OLnBhcnNlKHRoaXMub3JkZXJJbmZvLmRyaXZlckxpY2Vuc2VJbWdzKSA6IFtdOw0KICAgICAgICAgIH0gY2F0Y2ggeyB0aGlzLmRyaXZlckxpY2Vuc2VMaXN0VGVtcCA9IFtdOyB9DQogICAgICAgICAgdHJ5IHsNCiAgICAgICAgICAgIHRoaXMudmVoaWNsZUxpY2Vuc2VMaXN0VGVtcCA9IHRoaXMub3JkZXJJbmZvLmRyaXZpbmdMaWNlbnNlSW1nID8gSlNPTi5wYXJzZSh0aGlzLm9yZGVySW5mby5kcml2aW5nTGljZW5zZUltZykgOiBbXTsNCiAgICAgICAgICB9IGNhdGNoIHsgdGhpcy52ZWhpY2xlTGljZW5zZUxpc3RUZW1wID0gW107IH0NCiAgICAgICAgICB0aGlzLmRyaXZlckltZyA9IHRoaXMucGhvdG9UZW1wLm1hcChlbCA9PiBlbC51cmwpOw0KICAgICAgICAgIHRoaXMuZHJpdmVyTGljZW5zZUxpc3QgPSB0aGlzLmRyaXZlckxpY2Vuc2VMaXN0VGVtcC5tYXAoZWwgPT4gZWwudXJsKTsNCiAgICAgICAgICB0aGlzLnZlaGljbGVMaWNlbnNlTGlzdCA9IHRoaXMudmVoaWNsZUxpY2Vuc2VMaXN0VGVtcC5tYXAoZWwgPT4gZWwudXJsKTsNCiAgICAgICAgfQ0KICAgICAgfSk7DQogICAgfSwNCiAgICBmb3JtYXRUaW1lKHRpbWUpIHsNCiAgICAgIGlmICghdGltZSkgcmV0dXJuICcnOw0KICAgICAgY29uc3QgZGF0ZSA9IG5ldyBEYXRlKHRpbWUpOw0KICAgICAgaWYgKGlzTmFOKGRhdGUuZ2V0VGltZSgpKSkgcmV0dXJuIHRpbWU7DQoNCiAgICAgIGNvbnN0IHkgPSBkYXRlLmdldEZ1bGxZZWFyKCk7DQogICAgICBjb25zdCBtID0gKGRhdGUuZ2V0TW9udGgoKSArIDEpLnRvU3RyaW5nKCkucGFkU3RhcnQoMiwgJzAnKTsNCiAgICAgIGNvbnN0IGQgPSBkYXRlLmdldERhdGUoKS50b1N0cmluZygpLnBhZFN0YXJ0KDIsICcwJyk7DQogICAgICBjb25zdCBoID0gZGF0ZS5nZXRIb3VycygpLnRvU3RyaW5nKCkucGFkU3RhcnQoMiwgJzAnKTsNCiAgICAgIGNvbnN0IGkgPSBkYXRlLmdldE1pbnV0ZXMoKS50b1N0cmluZygpLnBhZFN0YXJ0KDIsICcwJyk7DQogICAgICBjb25zdCBzID0gZGF0ZS5nZXRTZWNvbmRzKCkudG9TdHJpbmcoKS5wYWRTdGFydCgyLCAnMCcpOw0KDQogICAgICByZXR1cm4gYCR7eX0tJHttfS0ke2R9ICR7aH06JHtpfToke3N9YDsNCiAgICB9LA0KICAgIGdldEJ1c2luZXNzRGVwdCh2YWwpIHsNCiAgICAgIGlmICh2YWwgPT0gJzEnKSByZXR1cm4gJ+mHh+i0reS4reW/gyc7DQogICAgICBpZiAodmFsID09ICcyJykgcmV0dXJuICfllYbliqHpg6gnOw0KICAgICAgcmV0dXJuICcnOw0KICAgIH0sDQogICAgZ2V0RW50cmFuY2VHYXRlKHZhbCkgew0KICAgICAgaWYgKHZhbCA9PT0gMSB8fCB2YWwgPT09ICcxJykgcmV0dXJuICflronlhajmnZEnOw0KICAgICAgaWYgKHZhbCA9PT0gMiB8fCB2YWwgPT09ICcyJykgcmV0dXJuICfkuInlj7fpl6gnOw0KICAgICAgcmV0dXJuICcnOw0KICAgIH0sDQogICAgZ2V0RWxlY3Ryb2RlRGVzYyh2YWwpIHsNCiAgICAgIGlmICh2YWwgPT0gJzEnKSByZXR1cm4gJzQwMCc7DQogICAgICBpZiAodmFsID09ICcyJykgcmV0dXJuICc0NTAnOw0KICAgICAgaWYgKHZhbCA9PSAnMycpIHJldHVybiAnNzAwJzsNCiAgICAgIHJldHVybiAnJzsNCiAgICB9LA0KICAgIGdldFN0YXR1c0xhYmVsKHZhbCkgew0KICAgICAgY29uc3Qgc3RhdHVzTWFwID0gew0KICAgICAgICAxOiAn5b6F5a6h5qC4JywNCiAgICAgICAgMjogJ+W+heetvuWIsCcsDQogICAgICAgIDM6ICfniannrqHliIbphY0nLA0KICAgICAgICA0OiAn5b6F5YWl5Y6CJywNCiAgICAgICAgNTogJ+W3suWFpeWOgicsDQogICAgICAgIDY6ICfniannrqHnoa7orqQnLA0KICAgICAgICA3OiAn5bey5Ye65Y6CJywNCiAgICAgICAgMjE6ICfpqbPlm54nLA0KICAgICAgICAyMjogJ+W+heWPlua2iCcsDQogICAgICAgIDIzOiAn5bey5Y+W5raIJw0KICAgICAgfTsNCiAgICAgIHJldHVybiBzdGF0dXNNYXBbdmFsXSB8fCAn5pyq55+lJzsNCiAgICB9LA0KICAgIGdldFN0YXR1c1RhZ1R5cGUodmFsKSB7DQogICAgICBjb25zdCBzdGF0dXNUeXBlTWFwID0gew0KICAgICAgICAxOiAnd2FybmluZycsICAgIC8vIOW+heWuoeaguA0KICAgICAgICAyOiAnaW5mbycsICAgICAgIC8vIOW+heetvuWIsA0KICAgICAgICAzOiAncHJpbWFyeScsICAgIC8vIOeJqeeuoeWIhumFjQ0KICAgICAgICA0OiAnd2FybmluZycsICAgIC8vIOW+heWFpeWOgg0KICAgICAgICA1OiAnc3VjY2VzcycsICAgIC8vIOW3suWFpeWOgg0KICAgICAgICA2OiAncHJpbWFyeScsICAgIC8vIOeJqeeuoeehruiupA0KICAgICAgICA3OiAnc3VjY2VzcycsICAgIC8vIOW3suWHuuWOgg0KICAgICAgICAyMTogJ2RhbmdlcicsICAgIC8vIOmps+Wbng0KICAgICAgICAyMjogJ3dhcm5pbmcnLCAgIC8vIOW+heWPlua2iA0KICAgICAgICAyMzogJ2luZm8nICAgICAgIC8vIOW3suWPlua2iA0KICAgICAgfTsNCiAgICAgIHJldHVybiBzdGF0dXNUeXBlTWFwW3ZhbF0gfHwgJ2luZm8nOw0KICAgIH0sDQogICAgb3BlbkVkaXRTdGF0dXNEaWFsb2coKSB7DQogICAgICB0aGlzLmVkaXRTdGF0dXNGb3JtLnJlc2VydmF0aW9uTm8gPSB0aGlzLm9yZGVySW5mby5yZXNlcnZhdGlvbk5vOw0KICAgICAgdGhpcy5lZGl0U3RhdHVzRm9ybS5zdGF0dXMgPSB0aGlzLm9yZGVySW5mby5zdGF0dXM7DQogICAgICB0aGlzLmVkaXRTdGF0dXNEaWFsb2dWaXNpYmxlID0gdHJ1ZTsNCiAgICB9LA0KICAgIHN1Ym1pdEVkaXRTdGF0dXMoKSB7DQogICAgICB1cGRhdGVBbGxveU9yZGVyU3RhdHVzKHRoaXMuZWRpdFN0YXR1c0Zvcm0pLnRoZW4ocmVzcG9uc2UgPT4gew0KICAgICAgICBpZiAocmVzcG9uc2UuY29kZSA9PT0gMjAwKSB7DQogICAgICAgICAgdGhpcy4kbWVzc2FnZS5zdWNjZXNzKCfnirbmgIHkv67mlLnmiJDlip8nKTsNCiAgICAgICAgICB0aGlzLmVkaXRTdGF0dXNEaWFsb2dWaXNpYmxlID0gZmFsc2U7DQogICAgICAgICAgdGhpcy5nZXREZXRhaWwoKTsNCiAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKHJlc3BvbnNlLm1zZyB8fCAn54q25oCB5L+u5pS55aSx6LSlJyk7DQogICAgICAgIH0NCiAgICAgIH0pOw0KICAgIH0NCiAgfQ0KfTsNCg=="}, {"version": 3, "sources": ["detail.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA0LA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "detail.vue", "sourceRoot": "src/views/truck/alloy", "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <h3>基本信息</h3>\r\n    <el-descriptions class=\"margin-top\" :column=\"2\" border :labelStyle=\"labelStyle\">\r\n      <el-descriptions-item>\r\n        <template slot=\"label\">\r\n          <i class=\"el-icon-document\"></i>\r\n          预约编号\r\n        </template>\r\n        {{ orderInfo.reservationNo }}\r\n      </el-descriptions-item>\r\n      <el-descriptions-item>\r\n        <template slot=\"label\">\r\n          <i class=\"el-icon-document\"></i>\r\n          业务部门\r\n        </template>\r\n        {{ getBusinessDept(orderInfo.approvalDept) }}\r\n      </el-descriptions-item>\r\n      <el-descriptions-item>\r\n        <template slot=\"label\">\r\n          <i class=\"el-icon-document\"></i>\r\n          供方业务员姓名\r\n        </template>\r\n        {{ orderInfo.supplierSalesName }}\r\n      </el-descriptions-item>\r\n      <el-descriptions-item>\r\n        <template slot=\"label\">\r\n          <i class=\"el-icon-document\"></i>\r\n          供方业务员手机号\r\n        </template>\r\n        {{ orderInfo.supplierSalesPhone }}\r\n      </el-descriptions-item>\r\n      <el-descriptions-item>\r\n        <template slot=\"label\">\r\n          <i class=\"el-icon-document\"></i>\r\n          申请单位名称\r\n        </template>\r\n        {{ orderInfo.applyCompanyName }}\r\n      </el-descriptions-item>\r\n      <el-descriptions-item>\r\n        <template slot=\"label\">\r\n          <i class=\"el-icon-document\"></i>\r\n          合金类型\r\n        </template>\r\n        {{ orderInfo.alloyType }}\r\n      </el-descriptions-item>\r\n      <el-descriptions-item>\r\n        <template slot=\"label\">\r\n          <i class=\"el-icon-document\"></i>\r\n          合金\r\n        </template>\r\n        {{ orderInfo.alloyLabel }}\r\n      </el-descriptions-item>\r\n      <el-descriptions-item>\r\n        <template slot=\"label\">\r\n          <i class=\"el-icon-document\"></i>\r\n          电极规格\r\n        </template>\r\n        {{ getElectrodeDesc(orderInfo.electrodeType) }}\r\n      </el-descriptions-item>\r\n      <el-descriptions-item>\r\n        <template slot=\"label\">\r\n          <i class=\"el-icon-document\"></i>\r\n          合金吨数\r\n        </template>\r\n        {{ orderInfo.estimatedWeight }}\r\n      </el-descriptions-item>\r\n      <el-descriptions-item>\r\n        <template slot=\"label\">\r\n          <i class=\"el-icon-document\"></i>\r\n          预计送货日期\r\n        </template>\r\n        {{ formatTime(orderInfo.expectedDeliveryTime) }}\r\n      </el-descriptions-item>\r\n      <el-descriptions-item>\r\n        <template slot=\"label\">\r\n          <i class=\"el-icon-time\"></i>\r\n          有效开始时间\r\n        </template>\r\n        {{ formatTime(orderInfo.effectiveStartTime) }}\r\n      </el-descriptions-item>\r\n      <el-descriptions-item>\r\n        <template slot=\"label\">\r\n          <i class=\"el-icon-time\"></i>\r\n          有效结束时间\r\n        </template>\r\n        {{ formatTime(orderInfo.effectiveEndTime) }}\r\n      </el-descriptions-item>\r\n      <el-descriptions-item>\r\n        <template slot=\"label\">\r\n          <i class=\"el-icon-document\"></i>\r\n          车牌号\r\n        </template>\r\n        {{ orderInfo.carNo }}\r\n      </el-descriptions-item>\r\n      <el-descriptions-item>\r\n        <template slot=\"label\">\r\n          <i class=\"el-icon-document\"></i>\r\n          司机名\r\n        </template>\r\n        {{ orderInfo.driverName }}\r\n      </el-descriptions-item>\r\n      <el-descriptions-item>\r\n        <template slot=\"label\">\r\n          <i class=\"el-icon-document\"></i>\r\n          司机身份证\r\n        </template>\r\n        {{ orderInfo.driverCardNo }}\r\n      </el-descriptions-item>\r\n      <el-descriptions-item>\r\n        <template slot=\"label\">\r\n          <i class=\"el-icon-document\"></i>\r\n          司机手机号\r\n        </template>\r\n        {{ orderInfo.driverMobile }}\r\n      </el-descriptions-item>\r\n      <el-descriptions-item>\r\n        <template slot=\"label\">\r\n          <i class=\"el-icon-document\"></i>\r\n          入厂大门\r\n        </template>\r\n        {{ getEntranceGate(orderInfo.enterDoor) }}\r\n      </el-descriptions-item>\r\n      <el-descriptions-item>\r\n        <template slot=\"label\">\r\n          <i class=\"el-icon-document\"></i>\r\n          状态\r\n        </template>\r\n        <el-tag :type=\"getStatusTagType(orderInfo.status)\" size=\"small\">\r\n          {{ getStatusLabel(orderInfo.status) }}\r\n        </el-tag>\r\n      </el-descriptions-item>\r\n      <el-descriptions-item>\r\n        <template slot=\"label\">\r\n          <i class=\"el-icon-document\"></i>\r\n          创建时间\r\n        </template>\r\n        {{ formatTime(orderInfo.createTime) }}\r\n      </el-descriptions-item>\r\n      <el-descriptions-item>\r\n        <template slot=\"label\"><i class=\"el-icon-truck\"></i> 驾驶证 </template>\r\n        <el-image v-for=\"(item, index) in driverLicenseListTemp\" :key=\"index\" style=\"width: 100px; height: 100px\"\r\n          :src=\"item.url\" fit=\"fit\" :preview-src-list=\"driverLicenseList\">\r\n        </el-image>\r\n      </el-descriptions-item>\r\n      <el-descriptions-item>\r\n        <template slot=\"label\"><i class=\"el-icon-truck\"></i> 行驶证 </template>\r\n        <el-image v-for=\"(item, index) in vehicleLicenseListTemp\" :key=\"index\" style=\"width: 100px; height: 100px\"\r\n          :src=\"item.url\" fit=\"fit\" :preview-src-list=\"vehicleLicenseList\">\r\n        </el-image>\r\n      </el-descriptions-item>\r\n      <el-descriptions-item>\r\n        <template slot=\"label\"><i class=\"el-icon-truck\"></i> 司机照片 </template>\r\n        <el-image v-for=\"(item, index) in photoTemp\" :key=\"index\" style=\"width: 100px; height: 100px\" :src=\"item.url\"\r\n          fit=\"fit\" :preview-src-list=\"driverImg\">\r\n        </el-image>\r\n      </el-descriptions-item>\r\n    </el-descriptions>\r\n    <h3>操作</h3>\r\n    <el-button type=\"primary\" size=\"mini\" @click=\"openEditStatusDialog\">修改状态</el-button>\r\n    <el-dialog :title=\"'修改预约单状态'\" :visible.sync=\"editStatusDialogVisible\" width=\"400px\">\r\n      <el-form :model=\"editStatusForm\" label-width=\"80px\">\r\n        <el-form-item label=\"状态\">\r\n          <el-select v-model=\"editStatusForm.status\" placeholder=\"请选择状态\">\r\n            <el-option v-for=\"item in statusOptions\" :key=\"item.value\" :label=\"item.label\" :value=\"item.value\" />\r\n          </el-select>\r\n        </el-form-item>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"editStatusDialogVisible = false\">取消</el-button>\r\n        <el-button type=\"primary\" @click=\"submitEditStatus\">确定</el-button>\r\n      </div>\r\n    </el-dialog>\r\n    <h3>预约单日志</h3>\r\n    <el-table ref=\"statusTable\" :data=\"logList\" style=\"width: 100%\">\r\n      <el-table-column prop=\"createTime\" label=\"时间\" min-width=\"200\">\r\n        <template slot-scope=\"scope\">\r\n          {{ formatTime(scope.row.createTime) }}\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column prop=\"info\" label=\"描述\" min-width=\"500\">\r\n      </el-table-column>\r\n    </el-table>\r\n  </div>\r\n</template>\r\n<script>\r\nimport { getAlloyOrderDetail, updateAlloyOrderStatus } from '@/api/truck/alloy/reservation';\r\n\r\nexport default {\r\n  name: 'AlloyOrderDetail',\r\n  data() {\r\n    return {\r\n      orderInfo: {},\r\n      logList: [],\r\n      driverImg: [],\r\n      photoTemp: [],\r\n      vehicleLicenseList: [],\r\n      vehicleLicenseListTemp: [],\r\n      driverLicenseList: [],\r\n      driverLicenseListTemp: [],\r\n      labelStyle: {\r\n        width: '200px',\r\n      },\r\n      editStatusDialogVisible: false,\r\n      editStatusForm: {\r\n        reservationNo: '',\r\n        status: ''\r\n      },\r\n      statusOptions: [\r\n        { value: '1', label: '待审核' },\r\n        { value: '2', label: '待签到' },\r\n        { value: '3', label: '物管分配' },\r\n        { value: '4', label: '待入厂' },\r\n        { value: '5', label: '已入厂' },\r\n        { value: '6', label: '物管确认' },\r\n        { value: '7', label: '已出厂' },\r\n        { value: '21', label: '驳回' },\r\n        { value: '22', label: '待取消' },\r\n        { value: '23', label: '已取消' }\r\n      ],\r\n    };\r\n  },\r\n  created() {\r\n    this.getDetail();\r\n  },\r\n  methods: {\r\n    getDetail() {\r\n      const reservationNo = this.$route.params.reservationNo;\r\n      getAlloyOrderDetail({ reservationNo }).then((response) => {\r\n        if (response.code === 200) {\r\n          this.orderInfo = response.data;\r\n          this.logList = response.data.matMgmtAlloyLogs || [];\r\n          // 处理图片字段\r\n          try {\r\n            this.photoTemp = this.orderInfo.driverFaceImg ? JSON.parse(this.orderInfo.driverFaceImg) : [];\r\n          } catch { this.photoTemp = []; }\r\n          try {\r\n            this.driverLicenseListTemp = this.orderInfo.driverLicenseImgs ? JSON.parse(this.orderInfo.driverLicenseImgs) : [];\r\n          } catch { this.driverLicenseListTemp = []; }\r\n          try {\r\n            this.vehicleLicenseListTemp = this.orderInfo.drivingLicenseImg ? JSON.parse(this.orderInfo.drivingLicenseImg) : [];\r\n          } catch { this.vehicleLicenseListTemp = []; }\r\n          this.driverImg = this.photoTemp.map(el => el.url);\r\n          this.driverLicenseList = this.driverLicenseListTemp.map(el => el.url);\r\n          this.vehicleLicenseList = this.vehicleLicenseListTemp.map(el => el.url);\r\n        }\r\n      });\r\n    },\r\n    formatTime(time) {\r\n      if (!time) return '';\r\n      const date = new Date(time);\r\n      if (isNaN(date.getTime())) return time;\r\n\r\n      const y = date.getFullYear();\r\n      const m = (date.getMonth() + 1).toString().padStart(2, '0');\r\n      const d = date.getDate().toString().padStart(2, '0');\r\n      const h = date.getHours().toString().padStart(2, '0');\r\n      const i = date.getMinutes().toString().padStart(2, '0');\r\n      const s = date.getSeconds().toString().padStart(2, '0');\r\n\r\n      return `${y}-${m}-${d} ${h}:${i}:${s}`;\r\n    },\r\n    getBusinessDept(val) {\r\n      if (val == '1') return '采购中心';\r\n      if (val == '2') return '商务部';\r\n      return '';\r\n    },\r\n    getEntranceGate(val) {\r\n      if (val === 1 || val === '1') return '安全村';\r\n      if (val === 2 || val === '2') return '三号门';\r\n      return '';\r\n    },\r\n    getElectrodeDesc(val) {\r\n      if (val == '1') return '400';\r\n      if (val == '2') return '450';\r\n      if (val == '3') return '700';\r\n      return '';\r\n    },\r\n    getStatusLabel(val) {\r\n      const statusMap = {\r\n        1: '待审核',\r\n        2: '待签到',\r\n        3: '物管分配',\r\n        4: '待入厂',\r\n        5: '已入厂',\r\n        6: '物管确认',\r\n        7: '已出厂',\r\n        21: '驳回',\r\n        22: '待取消',\r\n        23: '已取消'\r\n      };\r\n      return statusMap[val] || '未知';\r\n    },\r\n    getStatusTagType(val) {\r\n      const statusTypeMap = {\r\n        1: 'warning',    // 待审核\r\n        2: 'info',       // 待签到\r\n        3: 'primary',    // 物管分配\r\n        4: 'warning',    // 待入厂\r\n        5: 'success',    // 已入厂\r\n        6: 'primary',    // 物管确认\r\n        7: 'success',    // 已出厂\r\n        21: 'danger',    // 驳回\r\n        22: 'warning',   // 待取消\r\n        23: 'info'       // 已取消\r\n      };\r\n      return statusTypeMap[val] || 'info';\r\n    },\r\n    openEditStatusDialog() {\r\n      this.editStatusForm.reservationNo = this.orderInfo.reservationNo;\r\n      this.editStatusForm.status = this.orderInfo.status;\r\n      this.editStatusDialogVisible = true;\r\n    },\r\n    submitEditStatus() {\r\n      updateAlloyOrderStatus(this.editStatusForm).then(response => {\r\n        if (response.code === 200) {\r\n          this.$message.success('状态修改成功');\r\n          this.editStatusDialogVisible = false;\r\n          this.getDetail();\r\n        } else {\r\n          this.$message.error(response.msg || '状态修改失败');\r\n        }\r\n      });\r\n    }\r\n  }\r\n};\r\n</script>\r\n<style scoped lang=\"scss\">\r\n.title-div {\r\n  display: flex;\r\n  flex-direction: row;\r\n  align-items: center;\r\n  justify-content: space-between;\r\n}\r\n\r\n.url-div {\r\n  display: flex;\r\n  flex-direction: row;\r\n  align-items: center;\r\n  justify-content: flex-start;\r\n}\r\n\r\n/*去除页眉页脚*/\r\n@page {\r\n  size: auto;\r\n  /* auto is the initial value */\r\n  margin: 3mm;\r\n  /* this affects the margin in the printer settings */\r\n}\r\n\r\nhtml {\r\n  background-color: #ffffff;\r\n  margin: 0;\r\n  /* this affects the margin on the html before sending to printer */\r\n}\r\n\r\nbody {\r\n  border: solid 1px blue;\r\n  margin: 10mm 15mm 10mm 15mm;\r\n  /* margin you want for the content */\r\n}\r\n\r\n/*去除页眉页脚*/\r\n</style>\r\n"]}]}