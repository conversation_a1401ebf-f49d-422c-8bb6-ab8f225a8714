{"remainingRequest": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\src\\views\\dataReport\\form\\admin.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\src\\views\\dataReport\\form\\admin.vue", "mtime": 1755499162047}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\babel.config.js", "mtime": 1688548084091}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_Tree<PERSON>iew", "_interopRequireDefault", "require", "_dimensionality", "_dimensionalitypermission", "_form", "_dept", "_axios", "xlsx", "_interopRequireWildcard", "name", "components", "TreeView", "data", "loading", "newOpen", "SpecialImportOpen", "mouthImportOpen", "searchopen", "total", "pageSizes", "queryParams", "pageNum", "pageSize", "dimensionalityName", "isUse", "rootList", "detail", "rootId", "drawer", "query", "startDate", "endDate", "title", "exportOpen", "deadlineOpen", "deadlineTitle", "deadlineForm", "dimensionalityPath", "dateValue", "deptList", "form", "userList", "adminOpen", "adminTitle", "specialFcDate", "dimensionalityId", "created", "getList", "getDept", "methods", "clickNode", "$event", "node", "target", "parentElement", "first<PERSON><PERSON><PERSON><PERSON><PERSON>", "click", "_this", "getStatusList<PERSON><PERSON><PERSON>min", "then", "res", "rows", "i", "length", "id", "containsSubstring", "showboot", "showMouth", "_this2", "listDept", "children", "console", "log", "dealdeptList", "row", "count", "value", "path", "label", "deptName", "handleQueryDept", "$refs", "cascaderHandle", "dropDownVisible", "reset<PERSON><PERSON>y", "resetForm", "handleQuery", "handleAdd", "handleDetail", "rootRuleType", "ruleType", "getDetail", "handleDeadLine", "submitForm", "_this3", "deadlineSwitch", "deadlineDate", "$modal", "msgError", "deadlineDateCheck", "split", "test", "deadlinebranch", "response", "msgSuccess", "cancel", "_this4", "getRootListById", "undefined", "$forceUpdate", "handleClose", "handleExport", "clickChangeTime", "addClick", "_this5", "addDimensionality", "exportData", "$notify", "error", "message", "downloadFile", "_objectSpread2", "default", "exportDataPreview", "_this6", "downloadXlsx", "blob", "reader", "FileReader", "readAsA<PERSON>y<PERSON><PERSON>er", "onload", "evt", "customBlobContent", "result", "ints", "Uint8Array", "slice", "size", "workBook", "read", "type", "sheetNames", "SheetNames", "sheetName", "workSheet", "Sheets", "excelTable", "utils", "sheet_to_json", "tableThead", "Array", "from", "Object", "keys", "map", "item", "excelData", "exceltitle", "excelHtml", "exportMouthDataPreview", "_this7", "exportMouthData", "onDateChange", "toUpdateUsers", "$router", "push", "handleDateChange", "handleaAdminList", "_this8", "listPermission", "handlefill", "fcDate", "handleAnswer", "handleSpecial", "handleMouth", "downloadTemplateSpecialPreview", "_this9", "queryImport", "url", "downloadTemplateSpecial", "substring", "string", "includes", "mouthCheck", "aloneList", "handlePreview", "_this0", "handlePreview1", "_this1", "now", "Date", "getFirstOfYear", "getFirstOfMonth", "firstDayOfYear", "getFullYear", "formatDate", "firstDayOfMonth", "getMonth", "date", "year", "month", "String", "padStart", "day", "getDate", "concat"], "sources": ["src/views/dataReport/form/admin.vue"], "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <el-form\r\n      :model=\"queryParams\"\r\n      ref=\"queryForm\"\r\n      :inline=\"true\"\r\n      label-width=\"68px\"\r\n    >\r\n      <el-form-item label=\"扎口部门\" prop=\"deptCode\">\r\n        <el-cascader\r\n          ref=\"cascaderHandle\"\r\n          :options=\"deptList\"\r\n          clearable\r\n          filterable\r\n          v-model=\"queryParams.deptCode\"\r\n          :props=\"{ expandTrigger: 'hover', emitPath: false,checkStrictly: true }\"\r\n          :show-all-levels=\"false\"\r\n          @change=\"handleQueryDept\"\r\n        >\r\n        <span\r\n              slot-scope=\"{ node, data }\"\r\n              style=\"margin-left: -10px; padding-left: 10px; display: block\"\r\n              @click=\"clickNode($event, node)\"\r\n              >{{ data.label }}</span\r\n            >\r\n        </el-cascader>\r\n      </el-form-item>\r\n      <el-form-item label=\"报表名称\" prop=\"dimensionalityName\">\r\n        <el-input\r\n          v-model=\"queryParams.dimensionalityName\"\r\n          placeholder=\"请输入名称\"\r\n          clearable\r\n          size=\"small\"\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"填报时间\">\r\n                <el-date-picker\r\n                  v-model=\"queryParams.fcDate\"\r\n                  value-format=\"yyyy-MM-dd\"\r\n                  type=\"date\"\r\n                  @change=\"handleDateChange\"\r\n                  placeholder=\"选择日期\">\r\n                </el-date-picker>\r\n      </el-form-item>\r\n      <!-- <el-form-item label=\"是否在用启用\" prop=\"isUse\">\r\n        <el-select v-model=\"queryParams.isUse\" placeholder=\"请选择\">\r\n          <el-option label=\"启用\" value=\"1\"></el-option>\r\n          <el-option label=\"停用\" value=\"0\"></el-option>\r\n        </el-select>\r\n      </el-form-item> -->\r\n      <el-form-item>\r\n        <el-button\r\n          type=\"cyan\"\r\n          icon=\"el-icon-search\"\r\n          size=\"mini\"\r\n          @click=\"handleQuery\"\r\n          >搜索</el-button\r\n        >\r\n        <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\"\r\n          >重置</el-button\r\n        >\r\n      </el-form-item>\r\n    </el-form>\r\n    <el-row :gutter=\"10\" class=\"mb8\">\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"primary\"\r\n          icon=\"el-icon-plus\"\r\n          size=\"mini\"\r\n          @click=\"handleAdd\"\r\n          >新建报表</el-button\r\n        >\r\n      </el-col>\r\n    </el-row>\r\n    <el-table v-loading=\"loading\" :data=\"rootList\" border>\r\n      <el-table-column label=\"扎口部门\" align=\"center\" prop=\"deptName\" width=\"240\"/>\r\n      <!-- <el-table-column\r\n        label=\"扎口部门及人员\"\r\n        align=\"center\"\r\n        class-name=\"small-padding fixed-width\"\r\n      >\r\n        <template slot-scope=\"scope\">\r\n          <el-button\r\n              size=\"mini\"\r\n              type=\"text\"\r\n              @click=\"handleaAdminList(scope.row)\"\r\n              >{{scope.row.deptName}}</el-button\r\n            >\r\n        </template>\r\n      </el-table-column> -->\r\n      <!-- <el-table-column label=\"报表名称\" align=\"center\" prop=\"dimensionalityName\"/> -->\r\n      <el-table-column\r\n        label=\"报表名称\"\r\n        align=\"center\"\r\n        class-name=\"small-padding fixed-width\"\r\n      >\r\n        <template slot-scope=\"scope\">\r\n          <el-button\r\n              size=\"mini\"\r\n              type=\"text\"\r\n              @click=\"handleAnswer(scope.row)\"\r\n              >{{scope.row.dimensionalityName}}</el-button\r\n            >\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"当期完成率\" align=\"center\" prop=\"countRate\" width=\"160\"/>\r\n      <el-table-column label=\"当期应填数量\" align=\"center\" prop=\"shouldCount\" width=\"160\" />\r\n      <!-- <el-table-column\r\n        label=\"当期应填数量\"\r\n        align=\"center\"\r\n        class-name=\"small-padding fixed-width\"\r\n      >\r\n        <template slot-scope=\"scope\">\r\n          <el-button\r\n              size=\"mini\"\r\n              type=\"text\"\r\n              @click=\"handlefill(scope.row)\"\r\n              >{{scope.row.shouldCount}}</el-button\r\n            >\r\n        </template>\r\n      </el-table-column> -->\r\n\r\n      <el-table-column\r\n        label=\"当期未填数量\"\r\n        align=\"center\"\r\n        class-name=\"small-padding fixed-width\"\r\n        width=\"160\"\r\n      >\r\n        <template slot-scope=\"scope\">\r\n          <el-button\r\n              size=\"mini\"\r\n              type=\"text\"\r\n              @click=\"handlefill(scope.row)\"\r\n              >{{scope.row.notCount}}</el-button\r\n            >\r\n        </template>\r\n      </el-table-column>\r\n\r\n      <!-- <el-table-column label=\"当期应填数量\" align=\"center\" prop=\"\"  @cell-click=\"handleDetail(scope.row)\"/>\r\n      <el-table-column label=\"当期未填数量\" align=\"center\" prop=\"hasCount\"/> -->\r\n      \r\n      <!-- <el-table-column label=\"是否在用\" align=\"center\" prop=\"isUse\">\r\n        <template slot-scope=\"scope\">\r\n          <el-tag\r\n            style=\"margin-left: 10px\"\r\n            :type=\"scope.row.isUse == '1'? 'success' : 'danger'\"\r\n            >{{ scope.row.isUse == \"1\" ? \"启用\" : \"停用\" }}</el-tag\r\n          >\r\n        </template>\r\n      </el-table-column> -->\r\n      <el-table-column\r\n        label=\"操作\"\r\n        align=\"center\"\r\n        class-name=\"small-padding fixed-width\"\r\n      >\r\n        <template slot-scope=\"scope\">\r\n          <el-button\r\n            v-if=\"scope.row.ruleType != '5' \"\r\n            size=\"mini\"\r\n            type=\"text\"\r\n            icon=\"el-icon-edit\"\r\n            @click=\"handleDetail(scope.row)\"\r\n            >维度管理</el-button\r\n          >\r\n          <el-button\r\n            v-if=\"scope.row.ruleType != '5' && scope.row.ruleType != '0'\"\r\n            size=\"mini\"\r\n            type=\"text\"\r\n            icon=\"el-icon-edit\"\r\n            @click=\"handleDeadLine(scope.row)\"\r\n            >截止日期</el-button\r\n          >\r\n          <el-button\r\n            v-if=\"\r\n                  scope.row.ruleType == '1' ||\r\n                  scope.row.ruleType == '3' ||\r\n                  scope.row.ruleType == '4'\r\n                 \"\r\n            size=\"mini\"\r\n            type=\"text\"\r\n            icon=\"el-icon-edit\"\r\n            @click=\"toUpdateUsers(scope.row)\"\r\n            >分配权限</el-button\r\n          >\r\n          <el-button\r\n              v-if=\"\r\n                  scope.row.ruleType == '0' ||\r\n                  scope.row.ruleType == '2' ||\r\n                  scope.row.ruleType == '4' ||\r\n                  scope.row.ruleType == '5'\r\n                 \"\r\n            size=\"mini\"\r\n            type=\"text\"\r\n            icon=\"el-icon-edit\"\r\n            @click=\"handleExport(scope.row)\"\r\n            >导出数据</el-button>\r\n            <el-button\r\n              v-if=\"\r\n                  (scope.row.ruleType == '0' ||\r\n                  scope.row.ruleType == '2' ||\r\n                  scope.row.ruleType == '4' ||\r\n                  scope.row.ruleType == '5') &&\r\n                  aloneList(scope.row.dimensionalityName)\r\n                 \"\r\n            size=\"mini\"\r\n            type=\"text\"\r\n            icon=\"el-icon-edit\"\r\n            @click=\"handleSpecial(scope.row)\"\r\n            >单周期数据导出</el-button>\r\n            <el-button\r\n              v-if=\"\r\n                  (scope.row.ruleType == '0' ||\r\n                  scope.row.ruleType == '2' ||\r\n                  scope.row.ruleType == '4' ||\r\n                  scope.row.ruleType == '5') &&\r\n                  containsSubstring('工装',scope.row.dimensionalityName)\r\n                 \"\r\n            size=\"mini\"\r\n            type=\"text\"\r\n            icon=\"el-icon-edit\"\r\n            @click=\"handleMouth(scope.row)\"\r\n            >规整化数据导出</el-button>\r\n        </template>\r\n      </el-table-column>\r\n    </el-table>\r\n\r\n    <pagination\r\n      v-show=\"total > 0\"\r\n      :total=\"total\"\r\n      :page.sync=\"queryParams.pageNum\"\r\n      :limit.sync=\"queryParams.pageSize\"\r\n      :pageSizes=\"pageSizes\"\r\n      @pagination=\"getList\"\r\n    />\r\n\r\n    <el-drawer\r\n      title=\"详情\"\r\n      :visible.sync=\"drawer\"\r\n      direction=\"rtl\"\r\n      size=\"80%\"\r\n      :before-close=\"handleClose\"\r\n    >\r\n      <tree-view :node=\"detail\" @refreshData=\"getDetail\"></tree-view>\r\n    </el-drawer>\r\n    <el-dialog title=\"选择导出范围\" :visible.sync=\"exportOpen\" width=\"400px\" append-to-body destroy-on-close>\r\n      <span>数据日期范围：</span>\r\n        <el-date-picker\r\n          v-model=\"dateValue\"\r\n          type=\"daterange\"\r\n          range-separator=\"至\"\r\n          start-placeholder=\"开始日期\"\r\n          end-placeholder=\"结束日期\"\r\n          value-format=\"yyyy-MM-dd\"\r\n          @change=\"onDateChange\">\r\n        </el-date-picker>        \r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n      <el-button type=\"success\" @click=\"exportDataPreview\">预 览</el-button>\r\n      <el-button type=\"primary\" @click=\"exportData\">导 出</el-button>\r\n      <!-- <el-button @click=\"exportOpen = false\">取 消</el-button> -->\r\n    </div>\r\n    </el-dialog>\r\n\r\n    \r\n    <el-dialog title=\"选择导出范围\" :visible.sync=\"mouthImportOpen\" width=\"400px\" append-to-body destroy-on-close>\r\n      <span>数据日期范围：</span>\r\n        <el-date-picker\r\n          v-model=\"dateValue\"\r\n          type=\"daterange\"\r\n          range-separator=\"至\"\r\n          start-placeholder=\"开始日期\"\r\n          end-placeholder=\"结束日期\"\r\n          value-format=\"yyyy-MM-dd\"\r\n          @change=\"onDateChange\">\r\n        </el-date-picker>        \r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n      <el-button type=\"primary\" @click=\"exportMouthDataPreview\">预 览</el-button>\r\n      <el-button type=\"primary\" @click=\"exportMouthData\">导 出</el-button>\r\n      <!-- <el-button @click=\"mouthImportOpen = false\">取 消</el-button> -->\r\n    </div>\r\n    </el-dialog>\r\n\r\n    <el-dialog title=\"选择导出范围\" :visible.sync=\"newOpen\" width=\"400px\" append-to-body destroy-on-close>\r\n      <el-form ref=\"form\" :model=\"form\" label-width=\"80px\">\r\n        <el-form-item label=\"名称\" prop=\"dimensionalityName\">\r\n          <el-input\r\n            v-model=\"form.dimensionalityName\"\r\n            placeholder=\"请输入名称\"\r\n            clearable\r\n            size=\"small\"\r\n            @keyup.enter.native=\"handleQuery\"\r\n          />\r\n        </el-form-item>\r\n        <el-form-item label=\"扎口部门\" prop=\"deptCode\">\r\n          <el-cascader\r\n            :options=\"deptList\"\r\n            clearable\r\n            v-model=\"form.deptCode\"\r\n            :props=\"{ expandTrigger: 'hover', emitPath: false,checkStrictly: true }\"\r\n            :show-all-levels=\"false\"\r\n          >\r\n          </el-cascader>\r\n        </el-form-item>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button type=\"primary\" @click=\"addClick\">确 定</el-button>\r\n        <el-button @click=\"newOpen = false\">取 消</el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <el-dialog :title=\"adminTitle\" :visible.sync=\"adminOpen\" width=\"1000px\" append-to-body>\r\n      <el-table v-loading=\"loading\" :data=\"userList\">\r\n        <el-table-column label=\"用户工号\" align=\"center\" prop=\"workNo\" />\r\n        <el-table-column label=\"用户姓名\" align=\"center\" prop=\"userName\" />\r\n        <!-- <el-table-column label=\"用户姓名\" align=\"center\" prop=\"userName\" /> -->\r\n      </el-table>\r\n    </el-dialog>\r\n\r\n    <el-dialog :title=\"deadlineTitle\" :visible.sync=\"deadlineOpen\" width=\"800px\" append-to-body>\r\n      <el-form ref=\"deadlineForm\" :model=\"deadlineForm\" label-width=\"160px\">\r\n        <el-form-item label=\"截止日期开关\" prop=\"deadlineSwitch\">\r\n          <el-switch\r\n            v-model=\"deadlineForm.deadlineSwitch\"\r\n            active-color=\"#13ce66\"\r\n            inactive-color=\"#ff4949\"\r\n            active-value=\"1\"\r\n            inactive-value=\"0\"\r\n            >\r\n          </el-switch>\r\n        </el-form-item>\r\n        <el-form-item v-if=\" deadlineForm.deadlineSwitch == '1' \" label=\"截止日期\" prop=\"deadlineDate\">\r\n          <el-input type=\"text\"\r\n                    v-model=\"deadlineForm.deadlineDate\" \r\n                    placeholder=\"截止日期格式为(年/月/日)\"></el-input>\r\n        </el-form-item>\r\n        <el-form-item v-if=\" deadlineForm.deadlineSwitch == '1' \"  label=\"邮件通知开关\" prop=\"mailSwitch\">\r\n          <el-switch\r\n            v-model=\"deadlineForm.mailSwitch\"\r\n            active-color=\"#13ce66\"\r\n            inactive-color=\"#ff4949\"\r\n            active-value=\"1\"\r\n            inactive-value=\"0\"\r\n            >\r\n          </el-switch>\r\n        </el-form-item>\r\n        <el-form-item v-if=\" deadlineForm.mailSwitch == '1' \" label=\"通知时间\" prop=\"deadlineDate\">\r\n          <el-input type=\"text\"\r\n                    v-model=\"deadlineForm.countdown\" \r\n                    placeholder=\"设置在截止日期前几天进行通知\"></el-input>\r\n        </el-form-item>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button type=\"primary\" @click=\"submitForm\">确 定</el-button>\r\n        <el-button @click=\"cancel\">取 消</el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <!-- <el-dialog\r\n      title=\"单周期报表导出\"\r\n      :visible.sync=\"SpecialImportOpen\"\r\n      width=\"400px\"\r\n      append-to-body\r\n      destroy-on-close\r\n    >\r\n      <span>选择导出时间：</span>\r\n      <el-date-picker\r\n            v-model=\"specialFcDate\"\r\n            value-format=\"yyyy-MM-dd\"\r\n            type=\"date\"\r\n            :default-value=\"new Date()\"\r\n            placeholder=\"选择时间\"\r\n          >\r\n      </el-date-picker>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button type=\"primary\" @click=\"downloadTemplateSpecial\">数据下载</el-button>\r\n      </div>\r\n    </el-dialog> -->\r\n\r\n    <el-dialog title=\"单周期报表导出\" :visible.sync=\"SpecialImportOpen\" width=\"400px\" append-to-body destroy-on-close>\r\n      <span>选择导出时间：</span>\r\n      <el-date-picker\r\n            v-model=\"specialFcDate\"\r\n            value-format=\"yyyy-MM-dd\"\r\n            type=\"date\"\r\n            :default-value=\"new Date()\"\r\n            placeholder=\"选择时间\"\r\n          >\r\n      </el-date-picker>     \r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n      <el-button type=\"success\" @click=\"downloadTemplateSpecialPreview\">数据预览</el-button>\r\n      <el-button type=\"primary\" @click=\"downloadTemplateSpecial\">数据下载</el-button>\r\n    </div>\r\n    </el-dialog>\r\n\r\n    <el-dialog  title=\"数据预览\"  :visible.sync=\"searchopen\" width=\"1800px\" >\r\n        <div class=\"test\">\r\n          <vue-office-excel\r\n              :src=\"customBlobContent\"\r\n              style=\"height: 100vh;\"\r\n          />\r\n        </div>\r\n    </el-dialog>\r\n\r\n  </div>\r\n</template>\r\n  \r\n  <script>\r\nimport TreeView from \"@/components/TreeView\";\r\nimport {\r\n  rootListDimensionality,\r\n  getRootListById,\r\n  addDimensionality,\r\n  getStatusListWithadmin\r\n} from \"@/api/tYjy/dimensionality\";\r\n\r\nimport {\r\n  listPermission,\r\n} from \"@/api/tYjy/dimensionalitypermission\";\r\n\r\nimport {\r\n  deadlinebranch,\r\n  updateForm,\r\n} from \"@/api/tYjy/form\";\r\n\r\nimport { listDept } from \"@/api/tYjy/dept\";\r\nimport axios from \"axios\";\r\nimport * as xlsx from 'xlsx';\r\nexport default {\r\n  name: \"Dimensionality\",\r\n  components: {\r\n    TreeView,\r\n  },\r\n  data() {\r\n    return {\r\n      // 遮罩层\r\n      loading: true,\r\n      newOpen:false,\r\n      SpecialImportOpen:false,\r\n      mouthImportOpen:false,\r\n      searchopen:false,\r\n      total:0,\r\n      pageSizes:[20,50,100],\r\n      queryParams: {\r\n        pageNum: 1,\r\n        pageSize: 20,\r\n        dimensionalityName: null,\r\n        isUse: null,\r\n      },\r\n      rootList: [],\r\n      detail: {},\r\n      rootId:null,\r\n      drawer:false,\r\n      query:{\r\n        startDate:null,\r\n        endDate:null,\r\n        rootId:null,\r\n        title:null,\r\n      },\r\n      exportOpen:false,\r\n      deadlineOpen:false,\r\n      deadlineTitle:\"批量修改截止日期\",\r\n      deadlineForm:\r\n      {\r\n        dimensionalityPath:null\r\n      },\r\n      dateValue:null,\r\n      deptList: [],\r\n      form:{},\r\n      userList:[],\r\n      adminOpen:false,\r\n      adminTitle:\"管理员名单\",\r\n      specialFcDate:null,\r\n      dimensionalityName:null,\r\n      dimensionalityId:null,\r\n\r\n\r\n    };\r\n  },\r\n  created() {\r\n    this.getList();\r\n    this.getDept();\r\n  },\r\n  methods: {\r\n    clickNode($event, node) {\r\n      $event.target.parentElement.parentElement.firstElementChild.click();\r\n    },\r\n    getList() {\r\n      this.loading = true;\r\n      getStatusListWithadmin(this.queryParams).then((res) => {\r\n        this.rootList = res.rows;\r\n        for(let i=0;i<this.rootList.length;i++)\r\n        {\r\n          if(this.rootList[i].id==273 || this.rootList[i].id==840 || this.rootList[i].id==873 || this.rootList[i].id==1077 || this.rootList[i].id==1059 || this.containsSubstring('安全责任工资',this.rootList[i].dimensionalityName))\r\n          {\r\n            this.rootList[i].showboot=1\r\n          }\r\n          else\r\n          {\r\n            this.rootList[i].showboot=0\r\n          }\r\n          if(this.containsSubstring('工装',this.rootList[i].dimensionalityName))\r\n          {\r\n            this.rootList[i].showMouth=1\r\n          }\r\n          else\r\n          {\r\n            this.rootList[i].showMouth=0\r\n          }\r\n        }\r\n        this.total = res.total;\r\n        this.loading = false;\r\n      });\r\n      // rootListDimensionality(this.queryParams).then((res) => {\r\n      //   this.rootList = res.rows;\r\n      //   this.total = res.total;\r\n      //   this.loading = false;\r\n      // });\r\n    },\r\n    getDept() {\r\n      listDept().then((res) => {\r\n        this.deptList = res.rows[0].children;\r\n        console.log(res);\r\n        for(let i=0;i<this.deptList.length;i++)\r\n        {\r\n          this.dealdeptList(this.deptList[i],0)\r\n        }\r\n      });\r\n    },\r\n    dealdeptList(row,count)\r\n    {\r\n       row.value=row.path\r\n       row.label=row.deptName\r\n       if(row.children.length>0 && count<1)\r\n       {\r\n          for(let i=0;i<row.children.length;i++)\r\n          {\r\n            this.dealdeptList(row.children[i],count+1)\r\n          }\r\n       }\r\n       else\r\n       {\r\n          row.children=null\r\n       }\r\n    },\r\n    handleQueryDept() {\r\n      this.$refs.cascaderHandle.dropDownVisible = false;\r\n      this.queryParams.pageNum = 1;\r\n      this.getList();\r\n    },\r\n\r\n    /** 重置按钮操作 */\r\n    resetQuery() {\r\n      this.resetForm(\"queryForm\");\r\n      this.handleQuery();\r\n    },\r\n    /** 搜索按钮操作 */\r\n    handleQuery() {\r\n      this.queryParams.pageNum = 1;\r\n      this.getList();\r\n    },\r\n\r\n\r\n    handleAdd() {\r\n      this.newOpen=true\r\n      // let that = this;\r\n      // this.$prompt(\"请输入名称\", \"提示\", {\r\n      //   confirmButtonText: \"确定\",\r\n      //   cancelButtonText: \"取消\",\r\n      // })\r\n      //   .then(({ value }) => {\r\n      //     let form = {};\r\n      //     form.dimensionalityName = value;\r\n      //     addDimensionality(form).then((res) => {\r\n      //       that.getList();\r\n      //     });\r\n      //   })\r\n      //   .catch(() => {\r\n      //     that.$message({\r\n      //       type: \"info\",\r\n      //       message: \"取消操作\",\r\n      //     });\r\n      //   });\r\n    },\r\n    handleDetail(row){\r\n      this.rootId = row.id;\r\n      this.rootRuleType = row.ruleType;\r\n      this.getDetail();\r\n      this.drawer = true;\r\n    },\r\n    handleDeadLine(row){\r\n      this.deadlineForm={dimensionalityPath:null}\r\n      this.deadlineForm.dimensionalityPath=row.path\r\n      this.deadlineOpen=true\r\n    },\r\n    /** 提交按钮 */\r\n    submitForm() {\r\n      if(this.deadlineForm.deadlineSwitch==1)\r\n      {\r\n        if(this.deadlineForm.deadlineDate==null)\r\n        {\r\n          this.$modal.msgError(\"截止日期不能为空\");\r\n          return\r\n        }\r\n        let deadlineDateCheck=this.deadlineForm.deadlineDate.split(\"/\")\r\n        if(deadlineDateCheck.length!=3)\r\n        {\r\n          this.$modal.msgError(\"截止日期格式不正确，正确格式是 年/月/日 \");\r\n          return\r\n        }\r\n        if(!/^-?(0|([1-9]?\\d)|100)$/.test(deadlineDateCheck[0]))\r\n        {\r\n          this.$modal.msgError(\"截止日期中年应是在-100到100之间的整数\");\r\n          return\r\n        }\r\n        if(!/^-?(0|([0]?\\d)|11|12)$/.test(deadlineDateCheck[1]))\r\n        {\r\n          this.$modal.msgError(\"截止日期中月应是在-12到12之间的整数\");\r\n          return\r\n        }\r\n        if(!/^-?(0|([1-2]?\\d)|31|30)$/.test(deadlineDateCheck[2]))\r\n        {\r\n          this.$modal.msgError(\"截止日期中日应是在-31到31之间的整数\");\r\n          return\r\n        }\r\n      }\r\n      deadlinebranch(this.deadlineForm).then((response) => \r\n      {\r\n        this.msgSuccess(\"批量修改截止日期成功\");\r\n        this.deadlineOpen = false;\r\n      });\r\n    },\r\n\r\n    cancel() {\r\n      this.deadlineOpen = false;\r\n    },\r\n\r\n    getDetail(){\r\n    getRootListById({id : this.rootId,ruleType:this.rootRuleType}).then(res => {\r\n        this.detail = res.data;\r\n        if(this.detail == null || this.detail == undefined)this.detail = {}\r\n        console.log(this.detail)\r\n        this.$forceUpdate();\r\n      });\r\n    },\r\n    handleClose(){\r\n      this.drawer = false;\r\n      this.getList();\r\n      this.$forceUpdate();\r\n    },\r\n    handleExport(row){\r\n      this.query.rootId  = row.id;\r\n      this.query.title = row.dimensionalityName;\r\n      this.clickChangeTime();\r\n      this.exportOpen = true;\r\n    },\r\n\r\n    addClick() {\r\n      // this.form.deptId=parseInt(this.form.deptId.split(\",\")[-1])\r\n      addDimensionality(this.form).then((res) => {\r\n        this.newOpen = false;\r\n        this.getList();\r\n        this.form={};\r\n      });\r\n    },\r\n    exportData() {\r\n      if (\r\n        this.query.startDate == null ||\r\n        this.query.startDate == \"\"||\r\n        this.query.endDate == null||\r\n        this.query.endDate == \"\"\r\n      ) {\r\n        this.$notify.error({\r\n          title: \"错误\",\r\n          message: \"导出前请先输入开始结束时间\",\r\n        });\r\n        return;\r\n      }\r\n      this.downloadFile(\r\n        \"/web/TYjy/dimensionality/exportStatistics\",\r\n        {\r\n          ...this.query,\r\n        },\r\n        \"(\" +\r\n          this.query.startDate +\r\n          \"-\" +\r\n          this.query.endDate +\r\n          \")\" +\r\n          this.query.title +\r\n          `.xlsx`\r\n      );\r\n    },\r\n    exportDataPreview()\r\n    {\r\n      if (\r\n        this.query.startDate == null ||\r\n        this.query.startDate == \"\"||\r\n        this.query.endDate == null||\r\n        this.query.endDate == \"\"\r\n      ) {\r\n        this.$notify.error({\r\n          title: \"错误\",\r\n          message: \"导出前请先输入开始结束时间\",\r\n        });\r\n        return;\r\n      }\r\n      this.downloadXlsx(\r\n        \"/web/TYjy/dimensionality/exportStatistics\",\r\n        {\r\n          ...this.query,\r\n        },\r\n        this.dimensionalityName+\"(\" +this.specialFcDate+\r\n          \")\" +\r\n          `数据.xlsx`\r\n      ).then((blob) => {\r\n        let reader = new FileReader();\r\n        reader.readAsArrayBuffer(blob);\r\n        reader.onload = (evt) => {\r\n          this.customBlobContent=reader.result;\r\n          let ints = new Uint8Array(evt.target.result); //要使用读取的内容，所以将读取内容转化成Uint8Array\r\n          ints = ints.slice(0, blob.size);\r\n          let workBook = xlsx.read(ints, { type: \"array\" });\r\n          let sheetNames = workBook.SheetNames;\r\n          let sheetName = sheetNames[0];\r\n          let workSheet = workBook.Sheets[sheetName];\r\n          //获取Excle内容，并将空内容用空值保存\r\n          let excelTable = xlsx.utils.sheet_to_json(workSheet);\r\n          // 获取Excel头部\r\n          let tableThead = Array.from(Object.keys(excelTable[0])).map(\r\n            (item) => {\r\n              return item\r\n            }\r\n          );\r\n          this.excelData = excelTable;\r\n          this.exceltitle=tableThead\r\n          this.excelHtml= excelTable\r\n          this.searchopen = true;\r\n        }\r\n      });\r\n    },\r\n    exportMouthDataPreview(){\r\n      if (\r\n        this.query.startDate == null ||\r\n        this.query.startDate == \"\"||\r\n        this.query.endDate == null||\r\n        this.query.endDate == \"\"\r\n      ) {\r\n        this.$notify.error({\r\n          title: \"错误\",\r\n          message: \"导出前请先输入开始结束时间\",\r\n        });\r\n        return;\r\n      }\r\n      this.query.rootId = this.dimensionalityId\r\n      this.query.type=\"1\"\r\n      this.downloadXlsx(\r\n          \"/web/TYjy/answer/exportEverymouth\",\r\n          {\r\n            ...this.query,\r\n          },\r\n          this.dimensionalityName+\"(\" +this.specialFcDate+\r\n            \")\" +\r\n            `数据.xlsx`\r\n        ).then((blob) => {\r\n          let reader = new FileReader();\r\n          reader.readAsArrayBuffer(blob);\r\n          \r\n          reader.onload = (evt) => {\r\n            this.customBlobContent=reader.result;\r\n            let ints = new Uint8Array(evt.target.result); //要使用读取的内容，所以将读取内容转化成Uint8Array\r\n            ints = ints.slice(0, blob.size);\r\n            let workBook = xlsx.read(ints, { type: \"array\" });\r\n            let sheetNames = workBook.SheetNames;\r\n            let sheetName = sheetNames[0];\r\n            let workSheet = workBook.Sheets[sheetName];\r\n            //获取Excle内容，并将空内容用空值保存\r\n            let excelTable = xlsx.utils.sheet_to_json(workSheet);\r\n            // 获取Excel头部\r\n            let tableThead = Array.from(Object.keys(excelTable[0])).map(\r\n              (item) => {\r\n                return item\r\n              }\r\n            );\r\n            this.excelData = excelTable;\r\n            this.exceltitle=tableThead\r\n            this.excelHtml= excelTable\r\n            this.searchopen = true;\r\n          }\r\n        });\r\n    },\r\n    exportMouthData(){\r\n      if (\r\n        this.query.startDate == null ||\r\n        this.query.startDate == \"\"||\r\n        this.query.endDate == null||\r\n        this.query.endDate == \"\"\r\n      ) {\r\n        this.$notify.error({\r\n          title: \"错误\",\r\n          message: \"导出前请先输入开始结束时间\",\r\n        });\r\n        return;\r\n      }\r\n      this.query.rootId = this.dimensionalityId\r\n      this.query.type=\"1\"\r\n      this.downloadFile(\r\n        \"/web/TYjy/answer/exportEverymouth\",\r\n        {\r\n          ...this.query,\r\n        },\r\n        \"(\" +\r\n          this.query.startDate +\r\n          \"-\" +\r\n          this.query.endDate +\r\n          \")\" +\r\n          this.dimensionalityName +\r\n          `.xlsx`\r\n      );\r\n    },\r\n    onDateChange(){\r\n      console.log(this.dateValue)\r\n      if(this.dateValue != null && this.dateValue != \"\"){\r\n        this.query.startDate = this.dateValue[0] ;\r\n        this.query.endDate = this.dateValue[1];\r\n      }else{\r\n        this.query.startDate = \"\";\r\n        this.query.endDate = \"\";\r\n      }\r\n    },\r\n    toUpdateUsers(row){\r\n      const dimensionalityId = row.id;\r\n      this.$router.push(\"/dataReport/dimensionality-auth/dimensionalityPermission/\" + dimensionalityId);\r\n      // this.$router.go(0)\r\n    },\r\n    handleDateChange() {\r\n      this.getList();\r\n    },\r\n    handleaAdminList(row){\r\n\r\n      const dimensionalityId = row.id;\r\n      listPermission({dimensionalityId:dimensionalityId}).then((response) => {\r\n        this.userList = response.rows;\r\n        // this.total = response.total;\r\n        // this.loading = false;\r\n        this.adminOpen = true;\r\n      });\r\n\r\n      // const fcDate = this.queryParams.fcDate;\r\n      // const dimensionalityName = row.dimensionalityName;\r\n      // this.$router.push({ path: '/dataReport/adminfill-auth/adminfillstatus', query: { dimensionalityId:dimensionalityId, fcDate:fcDate,dimensionalityName:dimensionalityName} });\r\n    },\r\n    handlefill(row){\r\n      // const dimensionalityId = row.id;\r\n      // this.$router.push(\"/dataReport/adminfill-auth/adminfillstatus/\" + dimensionalityId);\r\n      const dimensionalityId = row.id;\r\n      const fcDate = this.queryParams.fcDate;\r\n      const dimensionalityName = row.dimensionalityName;\r\n      this.$router.push({ path: '/dataReport/adminfill-auth/adminfillstatus/'+ dimensionalityId, query: { dimensionalityId:dimensionalityId, fcDate:fcDate,dimensionalityName:dimensionalityName} });\r\n    },\r\n    handleAnswer(row){\r\n      // const dimensionalityId = row.id;\r\n      // this.$router.push(\"/dataReport/adminfill-auth/adminfillstatus/\" + dimensionalityId);\r\n      const dimensionalityId = row.id;\r\n      const fcDate = this.queryParams.fcDate;\r\n      const dimensionalityName= row.dimensionalityName;\r\n      this.$router.push({ path: '/dataReport/answerShow-auth/answerShow/'+ dimensionalityId, query: { dimensionalityId:dimensionalityId, fcDate:fcDate,dimensionalityName:dimensionalityName} });\r\n    },\r\n\r\n  handleSpecial(row){\r\n      // this.query.rootId  = row.id;\r\n      this.dimensionalityName = row.dimensionalityName;\r\n      this.dimensionalityId=row.id;\r\n      this.SpecialImportOpen = true;\r\n      \r\n    },\r\n  handleMouth(row){\r\n      // this.query.rootId  = row.id;\r\n      this.dimensionalityName = row.dimensionalityName;\r\n      this.dimensionalityId=row.id;\r\n      this.clickChangeTime();\r\n      this.mouthImportOpen = true;\r\n    },\r\n  downloadTemplateSpecialPreview(){\r\n    if (this.specialFcDate == null ) {\r\n        this.specialFcDate= this.queryParams.fcDate\r\n      }\r\n\r\n      // if (this.specialFcDate == null ) {\r\n      //   this.$notify.error({\r\n      //     title: \"错误\",\r\n      //     message: \"未选择时间\",\r\n      //   });\r\n      //   return;\r\n      // }\r\n      let queryImport={}\r\n      queryImport.rootId = this.dimensionalityId\r\n      queryImport.fcDate = this.specialFcDate\r\n      queryImport.type=\"1\"\r\n      let url=\"\"\r\n      if(this.dimensionalityName=='研究院目标指标一览')\r\n      {\r\n        url=\"/web/TYjy/answer/exportWithTemplate\"\r\n      }\r\n      else\r\n      {\r\n        url=\"/web/TYjy/answer/exportTemplateSpecial\"\r\n      }\r\n      this.downloadXlsx(\r\n          url,\r\n          {\r\n            ...queryImport,\r\n          },\r\n          this.dimensionalityName+\"(\" +this.specialFcDate+\r\n            \")\" +\r\n            `数据.xlsx`\r\n        ).then((blob) => {\r\n          let reader = new FileReader();\r\n          reader.readAsArrayBuffer(blob);\r\n          \r\n          reader.onload = (evt) => {\r\n            this.customBlobContent=reader.result;\r\n            let ints = new Uint8Array(evt.target.result); //要使用读取的内容，所以将读取内容转化成Uint8Array\r\n            ints = ints.slice(0, blob.size);\r\n            let workBook = xlsx.read(ints, { type: \"array\" });\r\n            let sheetNames = workBook.SheetNames;\r\n            let sheetName = sheetNames[0];\r\n            let workSheet = workBook.Sheets[sheetName];\r\n            //获取Excle内容，并将空内容用空值保存\r\n            let excelTable = xlsx.utils.sheet_to_json(workSheet);\r\n            // 获取Excel头部\r\n            let tableThead = Array.from(Object.keys(excelTable[0])).map(\r\n              (item) => {\r\n                return item\r\n              }\r\n            );\r\n            this.excelData = excelTable;\r\n            this.exceltitle=tableThead\r\n            this.excelHtml= excelTable\r\n            this.searchopen = true;\r\n          }\r\n        });\r\n  },\r\n  downloadTemplateSpecial(){\r\n      if (this.specialFcDate == null ) {\r\n        this.specialFcDate= this.queryParams.fcDate\r\n      }\r\n\r\n      // if (this.specialFcDate == null ) {\r\n      //   this.$notify.error({\r\n      //     title: \"错误\",\r\n      //     message: \"未选择时间\",\r\n      //   });\r\n      //   return;\r\n      // }\r\n      let queryImport={}\r\n      queryImport.rootId = this.dimensionalityId\r\n      queryImport.fcDate = this.specialFcDate\r\n      queryImport.type=\"1\"\r\n      let url=\"\"\r\n      if(this.dimensionalityName=='研究院目标指标一览')\r\n      {\r\n        url=\"/web/TYjy/answer/exportWithTemplate\"\r\n      }\r\n      else\r\n      {\r\n        url=\"/web/TYjy/answer/exportTemplateSpecial\"\r\n      }\r\n      this.downloadFile(\r\n        url,\r\n        {\r\n          ...queryImport,\r\n        },\r\n        this.dimensionalityName+\"(\" +this.specialFcDate+\r\n          \")\" +\r\n          `数据.xlsx`\r\n      );\r\n    },\r\n\r\n    //此处为按钮判断管理\r\n    containsSubstring(substring, string) \r\n    {\r\n      return string.includes(substring);\r\n    },\r\n    //支持预览和导出\r\n    mouthCheck(string)\r\n    {\r\n      if(string== '六化指标')\r\n      {\r\n        return true;\r\n      }\r\n      if(string== '工装上线验收合格率统计')\r\n      {\r\n        return true;\r\n      }\r\n      if(string== '2025年经济责任制技经指标')\r\n      {\r\n        return true;\r\n      }\r\n      if(string== '研究院技经提升指标跟踪')\r\n      {\r\n        return true;\r\n      }\r\n      return false;\r\n    },\r\n    //支持单周期导出\r\n    aloneList(string) {\r\n      if(string== '气体结算月报')\r\n      {\r\n        return true;\r\n      }\r\n      if(string== '高炉、转炉煤气月报表')\r\n      {\r\n        return true;\r\n      }\r\n      if(string== '天然气消耗月报表')\r\n      {\r\n        return true;\r\n      }\r\n      if(string== '蒸汽消耗月报表')\r\n      {\r\n        return true;\r\n      }\r\n      if(string== '电量月报表')\r\n      {\r\n        return true;\r\n      }\r\n      if(string== '特板事业部2025年经济责任制奖罚汇总')\r\n      {\r\n        return true;\r\n      }\r\n      if(string== '研究院目标指标一览')\r\n      {\r\n        return true;\r\n      }\r\n      if(string== '安全责任工资考核表')\r\n      {\r\n        return true;\r\n      }\r\n      if(string== '安全责任工资考核汇总')\r\n      {\r\n        return true;\r\n      }\r\n      return false;\r\n    },\r\n\r\n    //数据预览模块处理\r\n    handlePreview() {\r\n      let queryImport={}\r\n      queryImport.rootId = this.queryParams.dimensionalityId\r\n      queryImport.fcDate = this.queryParams.fcDate\r\n      queryImport.type=\"1\"\r\n      if(this.dimensionalityName=='研究院目标指标一览')\r\n      {\r\n          this.downloadXlsx(\r\n          \"/web/TYjy/answer/exportWithTemplate\",\r\n          {\r\n            ...queryImport,\r\n          },\r\n          this.dimensionalityName+\"(\" +this.specialFcDate+\r\n            \")\" +\r\n            `数据.xlsx`\r\n        ).then((blob) => {\r\n          let reader = new FileReader();\r\n          reader.readAsArrayBuffer(blob);\r\n          \r\n          reader.onload = (evt) => {\r\n            this.customBlobContent=reader.result;\r\n            let ints = new Uint8Array(evt.target.result); //要使用读取的内容，所以将读取内容转化成Uint8Array\r\n            ints = ints.slice(0, blob.size);\r\n            let workBook = xlsx.read(ints, { type: \"array\" });\r\n            let sheetNames = workBook.SheetNames;\r\n            let sheetName = sheetNames[0];\r\n            let workSheet = workBook.Sheets[sheetName];\r\n            //获取Excle内容，并将空内容用空值保存\r\n            let excelTable = xlsx.utils.sheet_to_json(workSheet);\r\n            // 获取Excel头部\r\n            let tableThead = Array.from(Object.keys(excelTable[0])).map(\r\n              (item) => {\r\n                return item\r\n              }\r\n            );\r\n            this.excelData = excelTable;\r\n            this.exceltitle=tableThead\r\n            this.excelHtml= excelTable\r\n            this.searchopen = true;\r\n          }\r\n        });\r\n      }\r\n      else\r\n      {\r\n        this.downloadXlsx(\r\n        \"/web/TYjy/answer/exportTemplateSpecial\",\r\n        {\r\n          ...queryImport,\r\n        },\r\n        this.dimensionalityName+\"(\" +this.specialFcDate+\r\n          \")\" +\r\n          `数据.xlsx`\r\n      ).then((blob) => {\r\n        let reader = new FileReader();\r\n        reader.readAsArrayBuffer(blob);\r\n        reader.onload = (evt) => {\r\n          this.customBlobContent=reader.result;\r\n          let ints = new Uint8Array(evt.target.result); //要使用读取的内容，所以将读取内容转化成Uint8Array\r\n          ints = ints.slice(0, blob.size);\r\n          let workBook = xlsx.read(ints, { type: \"array\" });\r\n          let sheetNames = workBook.SheetNames;\r\n          let sheetName = sheetNames[0];\r\n          let workSheet = workBook.Sheets[sheetName];\r\n          //获取Excle内容，并将空内容用空值保存\r\n          let excelTable = xlsx.utils.sheet_to_json(workSheet);\r\n          // 获取Excel头部\r\n          let tableThead = Array.from(Object.keys(excelTable[0])).map(\r\n            (item) => {\r\n              return item\r\n            }\r\n          );\r\n          this.excelData = excelTable;\r\n          this.exceltitle=tableThead\r\n          this.excelHtml= excelTable\r\n          this.searchopen = true;\r\n        }\r\n      });\r\n      }\r\n    },\r\n    // 时间段预览\r\n    handlePreview1() {\r\n      // let queryImport={}\r\n      // queryImport.rootId = this.queryParams.dimensionalityId\r\n      // queryImport.fcDate = this.queryParams.fcDate\r\n      // queryImport.type=\"1\"\r\n        if (\r\n        this.queryImport.startDate == null ||\r\n        this.queryImport.startDate == \"\"||\r\n        this.queryImport.endDate == null||\r\n        this.queryImport.endDate == \"\"\r\n      ) {\r\n        this.$notify.error({\r\n          title: \"错误\",\r\n          message: \"导出前请先输入开始结束时间\",\r\n        });\r\n        return;\r\n      }\r\n      this.queryImport.rootId = this.queryParams.dimensionalityId\r\n      this.queryImport.type=\"1\"\r\n      this.downloadXlsx(\r\n        \"/web/TYjy/answer/exportTemplateNomral\",\r\n        {\r\n          ...this.queryImport,\r\n        },\r\n        this.dimensionalityName+\"(\" +this.specialFcDate+\r\n          \")\" +\r\n          `数据.xlsx`\r\n      ).then((blob) => {\r\n        let reader = new FileReader();\r\n        reader.readAsArrayBuffer(blob);\r\n        \r\n        reader.onload = (evt) => {\r\n          this.customBlobContent=reader.result;\r\n          let ints = new Uint8Array(evt.target.result); //要使用读取的内容，所以将读取内容转化成Uint8Array\r\n          ints = ints.slice(0, blob.size);\r\n          let workBook = xlsx.read(ints, { type: \"array\" });\r\n          let sheetNames = workBook.SheetNames;\r\n          let sheetName = sheetNames[0];\r\n          let workSheet = workBook.Sheets[sheetName];\r\n          //获取Excle内容，并将空内容用空值保存\r\n          let excelTable = xlsx.utils.sheet_to_json(workSheet);\r\n          // 获取Excel头部\r\n          let tableThead = Array.from(Object.keys(excelTable[0])).map(\r\n            (item) => {\r\n              return item\r\n            }\r\n          );\r\n          this.excelData = excelTable;\r\n          this.exceltitle=tableThead\r\n          this.excelHtml= excelTable\r\n          this.searchopen = true;\r\n        }\r\n      });\r\n    },\r\n\r\n    clickChangeTime()\r\n    {\r\n         let now =new Date();\r\n         this.query.startDate=this.getFirstOfYear(now);\r\n         this.query.endDate=this.getFirstOfMonth(now);\r\n         this.dateValue=[];\r\n         this.dateValue.push(this.query.startDate);\r\n         this.dateValue.push(this.query.endDate);\r\n    },\r\n    // 获取时间的优化处理\r\n    getFirstOfYear(now)\r\n    {\r\n      let  firstDayOfYear = new Date(now.getFullYear(), 0, 1);\r\n      return this.formatDate(firstDayOfYear);\r\n    },\r\n    getFirstOfMonth(now)\r\n    {\r\n      let firstDayOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);\r\n      return this.formatDate(firstDayOfMonth);\r\n    },\r\n    // 日期格式化函数（转为 yyyy-MM-dd）\r\n    formatDate(date) \r\n    {\r\n      const year = date.getFullYear();\r\n      const month = String(date.getMonth() + 1).padStart(2, '0'); // 月份从0开始需+1\r\n      const day = String(date.getDate()).padStart(2, '0');\r\n      return `${year}-${month}-${day}`;\r\n    },\r\n\r\n  },\r\n};\r\n</script>\r\n<style lang=\"less\">\r\n.v-modal {\r\n  display: none;\r\n}\r\n</style>\r\n  "], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAuZA,IAAAA,SAAA,GAAAC,sBAAA,CAAAC,OAAA;AACA,IAAAC,eAAA,GAAAD,OAAA;AAOA,IAAAE,yBAAA,GAAAF,OAAA;AAIA,IAAAG,KAAA,GAAAH,OAAA;AAKA,IAAAI,KAAA,GAAAJ,OAAA;AACA,IAAAK,MAAA,GAAAN,sBAAA,CAAAC,OAAA;AACA,IAAAM,IAAA,GAAAC,uBAAA,CAAAP,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCACA;EACAQ,IAAA;EACAC,UAAA;IACAC,QAAA,EAAAA;EACA;EACAC,IAAA,WAAAA,KAAA;IACA;MACA;MACAC,OAAA;MACAC,OAAA;MACAC,iBAAA;MACAC,eAAA;MACAC,UAAA;MACAC,KAAA;MACAC,SAAA;MACAC,WAAA;QACAC,OAAA;QACAC,QAAA;QACAC,kBAAA;QACAC,KAAA;MACA;MACAC,QAAA;MACAC,MAAA;MACAC,MAAA;MACAC,MAAA;MACAC,KAAA;QACAC,SAAA;QACAC,OAAA;QACAJ,MAAA;QACAK,KAAA;MACA;MACAC,UAAA;MACAC,YAAA;MACAC,aAAA;MACAC,YAAA,EACA;QACAC,kBAAA;MACA;MACAC,SAAA;MACAC,QAAA;MACAC,IAAA;MACAC,QAAA;MACAC,SAAA;MACAC,UAAA;MACAC,aAAA;MACArB,kBAAA;MACAsB,gBAAA;IAGA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA,KAAAC,OAAA;IACA,KAAAC,OAAA;EACA;EACAC,OAAA;IACAC,SAAA,WAAAA,UAAAC,MAAA,EAAAC,IAAA;MACAD,MAAA,CAAAE,MAAA,CAAAC,aAAA,CAAAA,aAAA,CAAAC,iBAAA,CAAAC,KAAA;IACA;IACAT,OAAA,WAAAA,QAAA;MAAA,IAAAU,KAAA;MACA,KAAA5C,OAAA;MACA,IAAA6C,sCAAA,OAAAtC,WAAA,EAAAuC,IAAA,WAAAC,GAAA;QACAH,KAAA,CAAAhC,QAAA,GAAAmC,GAAA,CAAAC,IAAA;QACA,SAAAC,CAAA,MAAAA,CAAA,GAAAL,KAAA,CAAAhC,QAAA,CAAAsC,MAAA,EAAAD,CAAA,IACA;UACA,IAAAL,KAAA,CAAAhC,QAAA,CAAAqC,CAAA,EAAAE,EAAA,WAAAP,KAAA,CAAAhC,QAAA,CAAAqC,CAAA,EAAAE,EAAA,WAAAP,KAAA,CAAAhC,QAAA,CAAAqC,CAAA,EAAAE,EAAA,WAAAP,KAAA,CAAAhC,QAAA,CAAAqC,CAAA,EAAAE,EAAA,YAAAP,KAAA,CAAAhC,QAAA,CAAAqC,CAAA,EAAAE,EAAA,YAAAP,KAAA,CAAAQ,iBAAA,WAAAR,KAAA,CAAAhC,QAAA,CAAAqC,CAAA,EAAAvC,kBAAA,GACA;YACAkC,KAAA,CAAAhC,QAAA,CAAAqC,CAAA,EAAAI,QAAA;UACA,OAEA;YACAT,KAAA,CAAAhC,QAAA,CAAAqC,CAAA,EAAAI,QAAA;UACA;UACA,IAAAT,KAAA,CAAAQ,iBAAA,OAAAR,KAAA,CAAAhC,QAAA,CAAAqC,CAAA,EAAAvC,kBAAA,GACA;YACAkC,KAAA,CAAAhC,QAAA,CAAAqC,CAAA,EAAAK,SAAA;UACA,OAEA;YACAV,KAAA,CAAAhC,QAAA,CAAAqC,CAAA,EAAAK,SAAA;UACA;QACA;QACAV,KAAA,CAAAvC,KAAA,GAAA0C,GAAA,CAAA1C,KAAA;QACAuC,KAAA,CAAA5C,OAAA;MACA;MACA;MACA;MACA;MACA;MACA;IACA;IACAmC,OAAA,WAAAA,QAAA;MAAA,IAAAoB,MAAA;MACA,IAAAC,cAAA,IAAAV,IAAA,WAAAC,GAAA;QACAQ,MAAA,CAAA7B,QAAA,GAAAqB,GAAA,CAAAC,IAAA,IAAAS,QAAA;QACAC,OAAA,CAAAC,GAAA,CAAAZ,GAAA;QACA,SAAAE,CAAA,MAAAA,CAAA,GAAAM,MAAA,CAAA7B,QAAA,CAAAwB,MAAA,EAAAD,CAAA,IACA;UACAM,MAAA,CAAAK,YAAA,CAAAL,MAAA,CAAA7B,QAAA,CAAAuB,CAAA;QACA;MACA;IACA;IACAW,YAAA,WAAAA,aAAAC,GAAA,EAAAC,KAAA,EACA;MACAD,GAAA,CAAAE,KAAA,GAAAF,GAAA,CAAAG,IAAA;MACAH,GAAA,CAAAI,KAAA,GAAAJ,GAAA,CAAAK,QAAA;MACA,IAAAL,GAAA,CAAAJ,QAAA,CAAAP,MAAA,QAAAY,KAAA,MACA;QACA,SAAAb,CAAA,MAAAA,CAAA,GAAAY,GAAA,CAAAJ,QAAA,CAAAP,MAAA,EAAAD,CAAA,IACA;UACA,KAAAW,YAAA,CAAAC,GAAA,CAAAJ,QAAA,CAAAR,CAAA,GAAAa,KAAA;QACA;MACA,OAEA;QACAD,GAAA,CAAAJ,QAAA;MACA;IACA;IACAU,eAAA,WAAAA,gBAAA;MACA,KAAAC,KAAA,CAAAC,cAAA,CAAAC,eAAA;MACA,KAAA/D,WAAA,CAAAC,OAAA;MACA,KAAA0B,OAAA;IACA;IAEA,aACAqC,UAAA,WAAAA,WAAA;MACA,KAAAC,SAAA;MACA,KAAAC,WAAA;IACA;IACA,aACAA,WAAA,WAAAA,YAAA;MACA,KAAAlE,WAAA,CAAAC,OAAA;MACA,KAAA0B,OAAA;IACA;IAGAwC,SAAA,WAAAA,UAAA;MACA,KAAAzE,OAAA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;IACA;IACA0E,YAAA,WAAAA,aAAAd,GAAA;MACA,KAAA/C,MAAA,GAAA+C,GAAA,CAAAV,EAAA;MACA,KAAAyB,YAAA,GAAAf,GAAA,CAAAgB,QAAA;MACA,KAAAC,SAAA;MACA,KAAA/D,MAAA;IACA;IACAgE,cAAA,WAAAA,eAAAlB,GAAA;MACA,KAAAtC,YAAA;QAAAC,kBAAA;MAAA;MACA,KAAAD,YAAA,CAAAC,kBAAA,GAAAqC,GAAA,CAAAG,IAAA;MACA,KAAA3C,YAAA;IACA;IACA,WACA2D,UAAA,WAAAA,WAAA;MAAA,IAAAC,MAAA;MACA,SAAA1D,YAAA,CAAA2D,cAAA,OACA;QACA,SAAA3D,YAAA,CAAA4D,YAAA,UACA;UACA,KAAAC,MAAA,CAAAC,QAAA;UACA;QACA;QACA,IAAAC,iBAAA,QAAA/D,YAAA,CAAA4D,YAAA,CAAAI,KAAA;QACA,IAAAD,iBAAA,CAAApC,MAAA,OACA;UACA,KAAAkC,MAAA,CAAAC,QAAA;UACA;QACA;QACA,8BAAAG,IAAA,CAAAF,iBAAA,MACA;UACA,KAAAF,MAAA,CAAAC,QAAA;UACA;QACA;QACA,8BAAAG,IAAA,CAAAF,iBAAA,MACA;UACA,KAAAF,MAAA,CAAAC,QAAA;UACA;QACA;QACA,gCAAAG,IAAA,CAAAF,iBAAA,MACA;UACA,KAAAF,MAAA,CAAAC,QAAA;UACA;QACA;MACA;MACA,IAAAI,oBAAA,OAAAlE,YAAA,EAAAuB,IAAA,WAAA4C,QAAA,EACA;QACAT,MAAA,CAAAU,UAAA;QACAV,MAAA,CAAA5D,YAAA;MACA;IACA;IAEAuE,MAAA,WAAAA,OAAA;MACA,KAAAvE,YAAA;IACA;IAEAyD,SAAA,WAAAA,UAAA;MAAA,IAAAe,MAAA;MACA,IAAAC,+BAAA;QAAA3C,EAAA,OAAArC,MAAA;QAAA+D,QAAA,OAAAD;MAAA,GAAA9B,IAAA,WAAAC,GAAA;QACA8C,MAAA,CAAAhF,MAAA,GAAAkC,GAAA,CAAAhD,IAAA;QACA,IAAA8F,MAAA,CAAAhF,MAAA,YAAAgF,MAAA,CAAAhF,MAAA,IAAAkF,SAAA,EAAAF,MAAA,CAAAhF,MAAA;QACA6C,OAAA,CAAAC,GAAA,CAAAkC,MAAA,CAAAhF,MAAA;QACAgF,MAAA,CAAAG,YAAA;MACA;IACA;IACAC,WAAA,WAAAA,YAAA;MACA,KAAAlF,MAAA;MACA,KAAAmB,OAAA;MACA,KAAA8D,YAAA;IACA;IACAE,YAAA,WAAAA,aAAArC,GAAA;MACA,KAAA7C,KAAA,CAAAF,MAAA,GAAA+C,GAAA,CAAAV,EAAA;MACA,KAAAnC,KAAA,CAAAG,KAAA,GAAA0C,GAAA,CAAAnD,kBAAA;MACA,KAAAyF,eAAA;MACA,KAAA/E,UAAA;IACA;IAEAgF,QAAA,WAAAA,SAAA;MAAA,IAAAC,MAAA;MACA;MACA,IAAAC,iCAAA,OAAA3E,IAAA,EAAAmB,IAAA,WAAAC,GAAA;QACAsD,MAAA,CAAApG,OAAA;QACAoG,MAAA,CAAAnE,OAAA;QACAmE,MAAA,CAAA1E,IAAA;MACA;IACA;IACA4E,UAAA,WAAAA,WAAA;MACA,IACA,KAAAvF,KAAA,CAAAC,SAAA,YACA,KAAAD,KAAA,CAAAC,SAAA,UACA,KAAAD,KAAA,CAAAE,OAAA,YACA,KAAAF,KAAA,CAAAE,OAAA,QACA;QACA,KAAAsF,OAAA,CAAAC,KAAA;UACAtF,KAAA;UACAuF,OAAA;QACA;QACA;MACA;MACA,KAAAC,YAAA,CACA,iDAAAC,cAAA,CAAAC,OAAA,MAEA,KAAA7F,KAAA,GAEA,MACA,KAAAA,KAAA,CAAAC,SAAA,GACA,MACA,KAAAD,KAAA,CAAAE,OAAA,GACA,MACA,KAAAF,KAAA,CAAAG,KAAA,UAEA;IACA;IACA2F,iBAAA,WAAAA,kBAAA,EACA;MAAA,IAAAC,MAAA;MACA,IACA,KAAA/F,KAAA,CAAAC,SAAA,YACA,KAAAD,KAAA,CAAAC,SAAA,UACA,KAAAD,KAAA,CAAAE,OAAA,YACA,KAAAF,KAAA,CAAAE,OAAA,QACA;QACA,KAAAsF,OAAA,CAAAC,KAAA;UACAtF,KAAA;UACAuF,OAAA;QACA;QACA;MACA;MACA,KAAAM,YAAA,CACA,iDAAAJ,cAAA,CAAAC,OAAA,MAEA,KAAA7F,KAAA,GAEA,KAAAN,kBAAA,cAAAqB,aAAA,GACA,yBAEA,EAAAe,IAAA,WAAAmE,IAAA;QACA,IAAAC,MAAA,OAAAC,UAAA;QACAD,MAAA,CAAAE,iBAAA,CAAAH,IAAA;QACAC,MAAA,CAAAG,MAAA,aAAAC,GAAA;UACAP,MAAA,CAAAQ,iBAAA,GAAAL,MAAA,CAAAM,MAAA;UACA,IAAAC,IAAA,OAAAC,UAAA,CAAAJ,GAAA,CAAA9E,MAAA,CAAAgF,MAAA;UACAC,IAAA,GAAAA,IAAA,CAAAE,KAAA,IAAAV,IAAA,CAAAW,IAAA;UACA,IAAAC,QAAA,GAAAnI,IAAA,CAAAoI,IAAA,CAAAL,IAAA;YAAAM,IAAA;UAAA;UACA,IAAAC,UAAA,GAAAH,QAAA,CAAAI,UAAA;UACA,IAAAC,SAAA,GAAAF,UAAA;UACA,IAAAG,SAAA,GAAAN,QAAA,CAAAO,MAAA,CAAAF,SAAA;UACA;UACA,IAAAG,UAAA,GAAA3I,IAAA,CAAA4I,KAAA,CAAAC,aAAA,CAAAJ,SAAA;UACA;UACA,IAAAK,UAAA,GAAAC,KAAA,CAAAC,IAAA,CAAAC,MAAA,CAAAC,IAAA,CAAAP,UAAA,MAAAQ,GAAA,CACA,UAAAC,IAAA;YACA,OAAAA,IAAA;UACA,CACA;UACA/B,MAAA,CAAAgC,SAAA,GAAAV,UAAA;UACAtB,MAAA,CAAAiC,UAAA,GAAAR,UAAA;UACAzB,MAAA,CAAAkC,SAAA,GAAAZ,UAAA;UACAtB,MAAA,CAAA3G,UAAA;QACA;MACA;IACA;IACA8I,sBAAA,WAAAA,uBAAA;MAAA,IAAAC,MAAA;MACA,IACA,KAAAnI,KAAA,CAAAC,SAAA,YACA,KAAAD,KAAA,CAAAC,SAAA,UACA,KAAAD,KAAA,CAAAE,OAAA,YACA,KAAAF,KAAA,CAAAE,OAAA,QACA;QACA,KAAAsF,OAAA,CAAAC,KAAA;UACAtF,KAAA;UACAuF,OAAA;QACA;QACA;MACA;MACA,KAAA1F,KAAA,CAAAF,MAAA,QAAAkB,gBAAA;MACA,KAAAhB,KAAA,CAAA+G,IAAA;MACA,KAAAf,YAAA,CACA,yCAAAJ,cAAA,CAAAC,OAAA,MAEA,KAAA7F,KAAA,GAEA,KAAAN,kBAAA,cAAAqB,aAAA,GACA,yBAEA,EAAAe,IAAA,WAAAmE,IAAA;QACA,IAAAC,MAAA,OAAAC,UAAA;QACAD,MAAA,CAAAE,iBAAA,CAAAH,IAAA;QAEAC,MAAA,CAAAG,MAAA,aAAAC,GAAA;UACA6B,MAAA,CAAA5B,iBAAA,GAAAL,MAAA,CAAAM,MAAA;UACA,IAAAC,IAAA,OAAAC,UAAA,CAAAJ,GAAA,CAAA9E,MAAA,CAAAgF,MAAA;UACAC,IAAA,GAAAA,IAAA,CAAAE,KAAA,IAAAV,IAAA,CAAAW,IAAA;UACA,IAAAC,QAAA,GAAAnI,IAAA,CAAAoI,IAAA,CAAAL,IAAA;YAAAM,IAAA;UAAA;UACA,IAAAC,UAAA,GAAAH,QAAA,CAAAI,UAAA;UACA,IAAAC,SAAA,GAAAF,UAAA;UACA,IAAAG,SAAA,GAAAN,QAAA,CAAAO,MAAA,CAAAF,SAAA;UACA;UACA,IAAAG,UAAA,GAAA3I,IAAA,CAAA4I,KAAA,CAAAC,aAAA,CAAAJ,SAAA;UACA;UACA,IAAAK,UAAA,GAAAC,KAAA,CAAAC,IAAA,CAAAC,MAAA,CAAAC,IAAA,CAAAP,UAAA,MAAAQ,GAAA,CACA,UAAAC,IAAA;YACA,OAAAA,IAAA;UACA,CACA;UACAK,MAAA,CAAAJ,SAAA,GAAAV,UAAA;UACAc,MAAA,CAAAH,UAAA,GAAAR,UAAA;UACAW,MAAA,CAAAF,SAAA,GAAAZ,UAAA;UACAc,MAAA,CAAA/I,UAAA;QACA;MACA;IACA;IACAgJ,eAAA,WAAAA,gBAAA;MACA,IACA,KAAApI,KAAA,CAAAC,SAAA,YACA,KAAAD,KAAA,CAAAC,SAAA,UACA,KAAAD,KAAA,CAAAE,OAAA,YACA,KAAAF,KAAA,CAAAE,OAAA,QACA;QACA,KAAAsF,OAAA,CAAAC,KAAA;UACAtF,KAAA;UACAuF,OAAA;QACA;QACA;MACA;MACA,KAAA1F,KAAA,CAAAF,MAAA,QAAAkB,gBAAA;MACA,KAAAhB,KAAA,CAAA+G,IAAA;MACA,KAAApB,YAAA,CACA,yCAAAC,cAAA,CAAAC,OAAA,MAEA,KAAA7F,KAAA,GAEA,MACA,KAAAA,KAAA,CAAAC,SAAA,GACA,MACA,KAAAD,KAAA,CAAAE,OAAA,GACA,MACA,KAAAR,kBAAA,UAEA;IACA;IACA2I,YAAA,WAAAA,aAAA;MACA3F,OAAA,CAAAC,GAAA,MAAAlC,SAAA;MACA,SAAAA,SAAA,iBAAAA,SAAA;QACA,KAAAT,KAAA,CAAAC,SAAA,QAAAQ,SAAA;QACA,KAAAT,KAAA,CAAAE,OAAA,QAAAO,SAAA;MACA;QACA,KAAAT,KAAA,CAAAC,SAAA;QACA,KAAAD,KAAA,CAAAE,OAAA;MACA;IACA;IACAoI,aAAA,WAAAA,cAAAzF,GAAA;MACA,IAAA7B,gBAAA,GAAA6B,GAAA,CAAAV,EAAA;MACA,KAAAoG,OAAA,CAAAC,IAAA,+DAAAxH,gBAAA;MACA;IACA;IACAyH,gBAAA,WAAAA,iBAAA;MACA,KAAAvH,OAAA;IACA;IACAwH,gBAAA,WAAAA,iBAAA7F,GAAA;MAAA,IAAA8F,MAAA;MAEA,IAAA3H,gBAAA,GAAA6B,GAAA,CAAAV,EAAA;MACA,IAAAyG,wCAAA;QAAA5H,gBAAA,EAAAA;MAAA,GAAAc,IAAA,WAAA4C,QAAA;QACAiE,MAAA,CAAA/H,QAAA,GAAA8D,QAAA,CAAA1C,IAAA;QACA;QACA;QACA2G,MAAA,CAAA9H,SAAA;MACA;;MAEA;MACA;MACA;IACA;IACAgI,UAAA,WAAAA,WAAAhG,GAAA;MACA;MACA;MACA,IAAA7B,gBAAA,GAAA6B,GAAA,CAAAV,EAAA;MACA,IAAA2G,MAAA,QAAAvJ,WAAA,CAAAuJ,MAAA;MACA,IAAApJ,kBAAA,GAAAmD,GAAA,CAAAnD,kBAAA;MACA,KAAA6I,OAAA,CAAAC,IAAA;QAAAxF,IAAA,kDAAAhC,gBAAA;QAAAhB,KAAA;UAAAgB,gBAAA,EAAAA,gBAAA;UAAA8H,MAAA,EAAAA,MAAA;UAAApJ,kBAAA,EAAAA;QAAA;MAAA;IACA;IACAqJ,YAAA,WAAAA,aAAAlG,GAAA;MACA;MACA;MACA,IAAA7B,gBAAA,GAAA6B,GAAA,CAAAV,EAAA;MACA,IAAA2G,MAAA,QAAAvJ,WAAA,CAAAuJ,MAAA;MACA,IAAApJ,kBAAA,GAAAmD,GAAA,CAAAnD,kBAAA;MACA,KAAA6I,OAAA,CAAAC,IAAA;QAAAxF,IAAA,8CAAAhC,gBAAA;QAAAhB,KAAA;UAAAgB,gBAAA,EAAAA,gBAAA;UAAA8H,MAAA,EAAAA,MAAA;UAAApJ,kBAAA,EAAAA;QAAA;MAAA;IACA;IAEAsJ,aAAA,WAAAA,cAAAnG,GAAA;MACA;MACA,KAAAnD,kBAAA,GAAAmD,GAAA,CAAAnD,kBAAA;MACA,KAAAsB,gBAAA,GAAA6B,GAAA,CAAAV,EAAA;MACA,KAAAjD,iBAAA;IAEA;IACA+J,WAAA,WAAAA,YAAApG,GAAA;MACA;MACA,KAAAnD,kBAAA,GAAAmD,GAAA,CAAAnD,kBAAA;MACA,KAAAsB,gBAAA,GAAA6B,GAAA,CAAAV,EAAA;MACA,KAAAgD,eAAA;MACA,KAAAhG,eAAA;IACA;IACA+J,8BAAA,WAAAA,+BAAA;MAAA,IAAAC,MAAA;MACA,SAAApI,aAAA;QACA,KAAAA,aAAA,QAAAxB,WAAA,CAAAuJ,MAAA;MACA;;MAEA;MACA;MACA;MACA;MACA;MACA;MACA;MACA,IAAAM,WAAA;MACAA,WAAA,CAAAtJ,MAAA,QAAAkB,gBAAA;MACAoI,WAAA,CAAAN,MAAA,QAAA/H,aAAA;MACAqI,WAAA,CAAArC,IAAA;MACA,IAAAsC,GAAA;MACA,SAAA3J,kBAAA,iBACA;QACA2J,GAAA;MACA,OAEA;QACAA,GAAA;MACA;MACA,KAAArD,YAAA,CACAqD,GAAA,MAAAzD,cAAA,CAAAC,OAAA,MAEAuD,WAAA,GAEA,KAAA1J,kBAAA,cAAAqB,aAAA,GACA,yBAEA,EAAAe,IAAA,WAAAmE,IAAA;QACA,IAAAC,MAAA,OAAAC,UAAA;QACAD,MAAA,CAAAE,iBAAA,CAAAH,IAAA;QAEAC,MAAA,CAAAG,MAAA,aAAAC,GAAA;UACA6C,MAAA,CAAA5C,iBAAA,GAAAL,MAAA,CAAAM,MAAA;UACA,IAAAC,IAAA,OAAAC,UAAA,CAAAJ,GAAA,CAAA9E,MAAA,CAAAgF,MAAA;UACAC,IAAA,GAAAA,IAAA,CAAAE,KAAA,IAAAV,IAAA,CAAAW,IAAA;UACA,IAAAC,QAAA,GAAAnI,IAAA,CAAAoI,IAAA,CAAAL,IAAA;YAAAM,IAAA;UAAA;UACA,IAAAC,UAAA,GAAAH,QAAA,CAAAI,UAAA;UACA,IAAAC,SAAA,GAAAF,UAAA;UACA,IAAAG,SAAA,GAAAN,QAAA,CAAAO,MAAA,CAAAF,SAAA;UACA;UACA,IAAAG,UAAA,GAAA3I,IAAA,CAAA4I,KAAA,CAAAC,aAAA,CAAAJ,SAAA;UACA;UACA,IAAAK,UAAA,GAAAC,KAAA,CAAAC,IAAA,CAAAC,MAAA,CAAAC,IAAA,CAAAP,UAAA,MAAAQ,GAAA,CACA,UAAAC,IAAA;YACA,OAAAA,IAAA;UACA,CACA;UACAqB,MAAA,CAAApB,SAAA,GAAAV,UAAA;UACA8B,MAAA,CAAAnB,UAAA,GAAAR,UAAA;UACA2B,MAAA,CAAAlB,SAAA,GAAAZ,UAAA;UACA8B,MAAA,CAAA/J,UAAA;QACA;MACA;IACA;IACAkK,uBAAA,WAAAA,wBAAA;MACA,SAAAvI,aAAA;QACA,KAAAA,aAAA,QAAAxB,WAAA,CAAAuJ,MAAA;MACA;;MAEA;MACA;MACA;MACA;MACA;MACA;MACA;MACA,IAAAM,WAAA;MACAA,WAAA,CAAAtJ,MAAA,QAAAkB,gBAAA;MACAoI,WAAA,CAAAN,MAAA,QAAA/H,aAAA;MACAqI,WAAA,CAAArC,IAAA;MACA,IAAAsC,GAAA;MACA,SAAA3J,kBAAA,iBACA;QACA2J,GAAA;MACA,OAEA;QACAA,GAAA;MACA;MACA,KAAA1D,YAAA,CACA0D,GAAA,MAAAzD,cAAA,CAAAC,OAAA,MAEAuD,WAAA,GAEA,KAAA1J,kBAAA,cAAAqB,aAAA,GACA,yBAEA;IACA;IAEA;IACAqB,iBAAA,WAAAA,kBAAAmH,SAAA,EAAAC,MAAA,EACA;MACA,OAAAA,MAAA,CAAAC,QAAA,CAAAF,SAAA;IACA;IACA;IACAG,UAAA,WAAAA,WAAAF,MAAA,EACA;MACA,IAAAA,MAAA,YACA;QACA;MACA;MACA,IAAAA,MAAA,mBACA;QACA;MACA;MACA,IAAAA,MAAA,sBACA;QACA;MACA;MACA,IAAAA,MAAA,mBACA;QACA;MACA;MACA;IACA;IACA;IACAG,SAAA,WAAAA,UAAAH,MAAA;MACA,IAAAA,MAAA,cACA;QACA;MACA;MACA,IAAAA,MAAA,kBACA;QACA;MACA;MACA,IAAAA,MAAA,gBACA;QACA;MACA;MACA,IAAAA,MAAA,eACA;QACA;MACA;MACA,IAAAA,MAAA,aACA;QACA;MACA;MACA,IAAAA,MAAA,2BACA;QACA;MACA;MACA,IAAAA,MAAA,iBACA;QACA;MACA;MACA,IAAAA,MAAA,iBACA;QACA;MACA;MACA,IAAAA,MAAA,kBACA;QACA;MACA;MACA;IACA;IAEA;IACAI,aAAA,WAAAA,cAAA;MAAA,IAAAC,MAAA;MACA,IAAAT,WAAA;MACAA,WAAA,CAAAtJ,MAAA,QAAAP,WAAA,CAAAyB,gBAAA;MACAoI,WAAA,CAAAN,MAAA,QAAAvJ,WAAA,CAAAuJ,MAAA;MACAM,WAAA,CAAArC,IAAA;MACA,SAAArH,kBAAA,iBACA;QACA,KAAAsG,YAAA,CACA,2CAAAJ,cAAA,CAAAC,OAAA,MAEAuD,WAAA,GAEA,KAAA1J,kBAAA,cAAAqB,aAAA,GACA,yBAEA,EAAAe,IAAA,WAAAmE,IAAA;UACA,IAAAC,MAAA,OAAAC,UAAA;UACAD,MAAA,CAAAE,iBAAA,CAAAH,IAAA;UAEAC,MAAA,CAAAG,MAAA,aAAAC,GAAA;YACAuD,MAAA,CAAAtD,iBAAA,GAAAL,MAAA,CAAAM,MAAA;YACA,IAAAC,IAAA,OAAAC,UAAA,CAAAJ,GAAA,CAAA9E,MAAA,CAAAgF,MAAA;YACAC,IAAA,GAAAA,IAAA,CAAAE,KAAA,IAAAV,IAAA,CAAAW,IAAA;YACA,IAAAC,QAAA,GAAAnI,IAAA,CAAAoI,IAAA,CAAAL,IAAA;cAAAM,IAAA;YAAA;YACA,IAAAC,UAAA,GAAAH,QAAA,CAAAI,UAAA;YACA,IAAAC,SAAA,GAAAF,UAAA;YACA,IAAAG,SAAA,GAAAN,QAAA,CAAAO,MAAA,CAAAF,SAAA;YACA;YACA,IAAAG,UAAA,GAAA3I,IAAA,CAAA4I,KAAA,CAAAC,aAAA,CAAAJ,SAAA;YACA;YACA,IAAAK,UAAA,GAAAC,KAAA,CAAAC,IAAA,CAAAC,MAAA,CAAAC,IAAA,CAAAP,UAAA,MAAAQ,GAAA,CACA,UAAAC,IAAA;cACA,OAAAA,IAAA;YACA,CACA;YACA+B,MAAA,CAAA9B,SAAA,GAAAV,UAAA;YACAwC,MAAA,CAAA7B,UAAA,GAAAR,UAAA;YACAqC,MAAA,CAAA5B,SAAA,GAAAZ,UAAA;YACAwC,MAAA,CAAAzK,UAAA;UACA;QACA;MACA,OAEA;QACA,KAAA4G,YAAA,CACA,8CAAAJ,cAAA,CAAAC,OAAA,MAEAuD,WAAA,GAEA,KAAA1J,kBAAA,cAAAqB,aAAA,GACA,yBAEA,EAAAe,IAAA,WAAAmE,IAAA;UACA,IAAAC,MAAA,OAAAC,UAAA;UACAD,MAAA,CAAAE,iBAAA,CAAAH,IAAA;UACAC,MAAA,CAAAG,MAAA,aAAAC,GAAA;YACAuD,MAAA,CAAAtD,iBAAA,GAAAL,MAAA,CAAAM,MAAA;YACA,IAAAC,IAAA,OAAAC,UAAA,CAAAJ,GAAA,CAAA9E,MAAA,CAAAgF,MAAA;YACAC,IAAA,GAAAA,IAAA,CAAAE,KAAA,IAAAV,IAAA,CAAAW,IAAA;YACA,IAAAC,QAAA,GAAAnI,IAAA,CAAAoI,IAAA,CAAAL,IAAA;cAAAM,IAAA;YAAA;YACA,IAAAC,UAAA,GAAAH,QAAA,CAAAI,UAAA;YACA,IAAAC,SAAA,GAAAF,UAAA;YACA,IAAAG,SAAA,GAAAN,QAAA,CAAAO,MAAA,CAAAF,SAAA;YACA;YACA,IAAAG,UAAA,GAAA3I,IAAA,CAAA4I,KAAA,CAAAC,aAAA,CAAAJ,SAAA;YACA;YACA,IAAAK,UAAA,GAAAC,KAAA,CAAAC,IAAA,CAAAC,MAAA,CAAAC,IAAA,CAAAP,UAAA,MAAAQ,GAAA,CACA,UAAAC,IAAA;cACA,OAAAA,IAAA;YACA,CACA;YACA+B,MAAA,CAAA9B,SAAA,GAAAV,UAAA;YACAwC,MAAA,CAAA7B,UAAA,GAAAR,UAAA;YACAqC,MAAA,CAAA5B,SAAA,GAAAZ,UAAA;YACAwC,MAAA,CAAAzK,UAAA;UACA;QACA;MACA;IACA;IACA;IACA0K,cAAA,WAAAA,eAAA;MAAA,IAAAC,MAAA;MACA;MACA;MACA;MACA;MACA,IACA,KAAAX,WAAA,CAAAnJ,SAAA,YACA,KAAAmJ,WAAA,CAAAnJ,SAAA,UACA,KAAAmJ,WAAA,CAAAlJ,OAAA,YACA,KAAAkJ,WAAA,CAAAlJ,OAAA,QACA;QACA,KAAAsF,OAAA,CAAAC,KAAA;UACAtF,KAAA;UACAuF,OAAA;QACA;QACA;MACA;MACA,KAAA0D,WAAA,CAAAtJ,MAAA,QAAAP,WAAA,CAAAyB,gBAAA;MACA,KAAAoI,WAAA,CAAArC,IAAA;MACA,KAAAf,YAAA,CACA,6CAAAJ,cAAA,CAAAC,OAAA,MAEA,KAAAuD,WAAA,GAEA,KAAA1J,kBAAA,cAAAqB,aAAA,GACA,yBAEA,EAAAe,IAAA,WAAAmE,IAAA;QACA,IAAAC,MAAA,OAAAC,UAAA;QACAD,MAAA,CAAAE,iBAAA,CAAAH,IAAA;QAEAC,MAAA,CAAAG,MAAA,aAAAC,GAAA;UACAyD,MAAA,CAAAxD,iBAAA,GAAAL,MAAA,CAAAM,MAAA;UACA,IAAAC,IAAA,OAAAC,UAAA,CAAAJ,GAAA,CAAA9E,MAAA,CAAAgF,MAAA;UACAC,IAAA,GAAAA,IAAA,CAAAE,KAAA,IAAAV,IAAA,CAAAW,IAAA;UACA,IAAAC,QAAA,GAAAnI,IAAA,CAAAoI,IAAA,CAAAL,IAAA;YAAAM,IAAA;UAAA;UACA,IAAAC,UAAA,GAAAH,QAAA,CAAAI,UAAA;UACA,IAAAC,SAAA,GAAAF,UAAA;UACA,IAAAG,SAAA,GAAAN,QAAA,CAAAO,MAAA,CAAAF,SAAA;UACA;UACA,IAAAG,UAAA,GAAA3I,IAAA,CAAA4I,KAAA,CAAAC,aAAA,CAAAJ,SAAA;UACA;UACA,IAAAK,UAAA,GAAAC,KAAA,CAAAC,IAAA,CAAAC,MAAA,CAAAC,IAAA,CAAAP,UAAA,MAAAQ,GAAA,CACA,UAAAC,IAAA;YACA,OAAAA,IAAA;UACA,CACA;UACAiC,MAAA,CAAAhC,SAAA,GAAAV,UAAA;UACA0C,MAAA,CAAA/B,UAAA,GAAAR,UAAA;UACAuC,MAAA,CAAA9B,SAAA,GAAAZ,UAAA;UACA0C,MAAA,CAAA3K,UAAA;QACA;MACA;IACA;IAEA+F,eAAA,WAAAA,gBAAA,EACA;MACA,IAAA6E,GAAA,OAAAC,IAAA;MACA,KAAAjK,KAAA,CAAAC,SAAA,QAAAiK,cAAA,CAAAF,GAAA;MACA,KAAAhK,KAAA,CAAAE,OAAA,QAAAiK,eAAA,CAAAH,GAAA;MACA,KAAAvJ,SAAA;MACA,KAAAA,SAAA,CAAA+H,IAAA,MAAAxI,KAAA,CAAAC,SAAA;MACA,KAAAQ,SAAA,CAAA+H,IAAA,MAAAxI,KAAA,CAAAE,OAAA;IACA;IACA;IACAgK,cAAA,WAAAA,eAAAF,GAAA,EACA;MACA,IAAAI,cAAA,OAAAH,IAAA,CAAAD,GAAA,CAAAK,WAAA;MACA,YAAAC,UAAA,CAAAF,cAAA;IACA;IACAD,eAAA,WAAAA,gBAAAH,GAAA,EACA;MACA,IAAAO,eAAA,OAAAN,IAAA,CAAAD,GAAA,CAAAK,WAAA,IAAAL,GAAA,CAAAQ,QAAA;MACA,YAAAF,UAAA,CAAAC,eAAA;IACA;IACA;IACAD,UAAA,WAAAA,WAAAG,IAAA,EACA;MACA,IAAAC,IAAA,GAAAD,IAAA,CAAAJ,WAAA;MACA,IAAAM,KAAA,GAAAC,MAAA,CAAAH,IAAA,CAAAD,QAAA,QAAAK,QAAA;MACA,IAAAC,GAAA,GAAAF,MAAA,CAAAH,IAAA,CAAAM,OAAA,IAAAF,QAAA;MACA,UAAAG,MAAA,CAAAN,IAAA,OAAAM,MAAA,CAAAL,KAAA,OAAAK,MAAA,CAAAF,GAAA;IACA;EAEA;AACA", "ignoreList": []}]}