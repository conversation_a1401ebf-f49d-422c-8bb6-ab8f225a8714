{"remainingRequest": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\src\\views\\drawing\\technicalDetail\\index.vue?vue&type=style&index=0&id=eb418294&scoped=true&lang=css", "dependencies": [{"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\src\\views\\drawing\\technicalDetail\\index.vue", "mtime": 1755499162049}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\@vue\\cli-service\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKDQouYnRuLWZvb3RlciB7DQogIHBhZGRpbmc6IDE4cHggMDsNCiAgdGV4dC1hbGlnbjogY2VudGVyOw0KfQ0KDQoubWFyZ2luLXRvcCB7DQogIG1hcmdpbi10b3A6IDIwcHg7DQp9DQoNCi5kaWFsb2ctZm9vdGVyIHsNCiAgbWFyZ2luLXRvcDogMzBweDsNCn0NCg0KLnRpdGxlLWRpdiB7DQogIGRpc3BsYXk6IGZsZXg7DQogIGZsZXgtZGlyZWN0aW9uOiByb3c7DQogIGFsaWduLWl0ZW1zOiBjZW50ZXI7DQogIGp1c3RpZnktY29udGVudDogc3BhY2UtYmV0d2VlbjsNCiAgbWFyZ2luLWJvdHRvbTogMjBweDsNCn0NCg0KLnVybC1kaXYgew0KICBkaXNwbGF5OiBmbGV4Ow0KICBmbGV4LWRpcmVjdGlvbjogcm93Ow0KICBhbGlnbi1pdGVtczogY2VudGVyOw0KICBqdXN0aWZ5LWNvbnRlbnQ6IGZsZXgtc3RhcnQ7DQp9DQoNCi5xcmNvZGUtZGl2IHsNCiAgZGlzcGxheTogZmxleDsNCiAgZmxleC1kaXJlY3Rpb246IGNvbHVtbjsNCiAgYWxpZ24taXRlbXM6IGZsZXgtc3RhcnQ7DQogIGp1c3RpZnktY29udGVudDogZmxleC1zdGFydDsNCn0NCg=="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAqCA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/drawing/technicalDetail", "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <!-- 信息确认 -->\r\n    <el-descriptions title=\"基础信息\" :column=\"4\" border>\r\n      <el-descriptions-item>\r\n        <template slot=\"label\"> 物料编码 </template>\r\n        {{ this.form.materialsCode }}\r\n      </el-descriptions-item>\r\n      <el-descriptions-item :span=\"2\">\r\n        <template slot=\"label\">技术协议</template>\r\n        <div v-if=\"!technicalAgreementList\" class=\"attachment-empty\">无技术协议</div>\r\n        <div v-if=\"technicalAgreementList\" class=\"attachment-empty\">\r\n          <a v-for=\"(item, index) in technicalAgreementList\" :key=\"index\" style=\"display: block; margin-bottom: 10px;\">\r\n            <el-button icon=\"el-icon-paperclip\" type=\"primary\" size=\"small\" @click=\"handleTechnical\">下载技术协议{{ index + 1\r\n              }}</el-button>\r\n          </a>\r\n        </div>\r\n      </el-descriptions-item>\r\n    </el-descriptions>\r\n\r\n    <h3>日志</h3>\r\n    <el-table v-loading=\"loading\" ref=\"logTable\" :data=\"logList\" style=\"width: 100%\">\r\n      <el-table-column prop=\"text\" label=\"时间\" width=\"200\">\r\n        <template slot-scope=\"scopes\">\r\n          {{ scopes.row.createTime }}\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column prop=\"info\" label=\"描述\" min-width=\"600\">\r\n      </el-table-column>\r\n    </el-table>\r\n  </div>\r\n</template>\r\n\r\n<pagination v-show=\"total >0\" :total=\"total\" :page.sync=\"logQuerParams.pageNum\" :limit.sync=\"logQueryParams.pageSize\"\r\n  @pagination=\"getLogs\"></pagination>\r\n\r\n<style scoped>\r\n.btn-footer {\r\n  padding: 18px 0;\r\n  text-align: center;\r\n}\r\n\r\n.margin-top {\r\n  margin-top: 20px;\r\n}\r\n\r\n.dialog-footer {\r\n  margin-top: 30px;\r\n}\r\n\r\n.title-div {\r\n  display: flex;\r\n  flex-direction: row;\r\n  align-items: center;\r\n  justify-content: space-between;\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.url-div {\r\n  display: flex;\r\n  flex-direction: row;\r\n  align-items: center;\r\n  justify-content: flex-start;\r\n}\r\n\r\n.qrcode-div {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: flex-start;\r\n  justify-content: flex-start;\r\n}\r\n</style>\r\n\r\n<script>\r\nimport { getLogList, getDrawing,  downloadTechnicalAgreement,getTechnicalAgreement } from '@/api/drawing/drawing';\r\nimport { getListByRoleKey } from '@/api/system/role';\r\n\r\nexport default {\r\n  name: \"TechnicalDetail\",\r\n  components: {},\r\n  data() {\r\n    return {\r\n      // 遮罩层\r\n      loading: true,\r\n      desLabelStyle: { width: '160px' },\r\n      // 总条数\r\n      orderInfo: {},\r\n      total: 0,\r\n      userTotal: 0,\r\n      // drawingNo: null,\r\n      selectedUserId: null,\r\n      selectedUserName: null,\r\n      form: {\r\n        // fileUrl: '[]',\r\n      },\r\n      logList: [],\r\n      drawingList: [],\r\n      tblList: [],\r\n      userList: [],\r\n      // fileList: [],\r\n      technicalAgreementList: [],\r\n      upload: {\r\n        // 设置上传的请求头部\r\n        headers: {},\r\n        // 上传的地址\r\n        url: process.env.VUE_APP_BASE_API + \"/app/common/uploadMinio\",\r\n        isUploading: false,\r\n      },\r\n      // fileList: [],\r\n      // rejectOpen: false,\r\n      // showDialog: false,\r\n      showSearch: true,\r\n      rejectDrawing: {\r\n        rejectReason: ''\r\n      },\r\n      passOpen: false,\r\n      passForm: {\r\n        id: '',\r\n        factoryAapproveReason: '',\r\n        equipmentApproveReason: '',\r\n        equipmentSearch: ''\r\n      },\r\n      isEquipmentApprove: false,\r\n      approveReason: '',\r\n      attachmentUuid: [],\r\n      // fileUrl: [],\r\n      logQueryParams: {\r\n        ticketNo: null,\r\n        pageNum: 1,\r\n        pageSize: 10,\r\n      },\r\n      queryParams: {\r\n        pageNum: 1,\r\n        pageSize: 10,\r\n        userName: undefined,\r\n      },\r\n    };\r\n  },\r\n\r\n  created() {\r\n    const id = this.$route.params && this.$route.params.id;\r\n    this.logQueryParams.approveId = id;\r\n    console.log(\"图纸号\", id);\r\n    // this.getUserList();\r\n    if (id) {\r\n      this.id = id;\r\n      this.getInfo();\r\n      this.getLogs();\r\n    } else {\r\n      this.$message({ message: \"未获取图纸号\", type: \"warning\" });\r\n    }\r\n  },\r\n  methods: {\r\n  \r\n    handleTechnical() {\r\n      const queryParams = { id: Number(this.id) };\r\n      this.$confirm('是否确认下载对应技术协议附件？', \"警告\", {\r\n        confirmButtonText: \"确定\",\r\n        cancelButtonText: \"取消\",\r\n        type: \"warning\"\r\n      }).then(function () {\r\n        return downloadTechnicalAgreement(queryParams);\r\n      }).then(response => {\r\n        this.download(response.msg);\r\n      })\r\n      console.log()\r\n    },\r\n\r\n    handleApproverRowClick(row) {\r\n      // 单击行时触发\r\n      this.selectedUserId = row.userName;\r\n    },\r\n\r\n    //清空选择状态\r\n    handleClearSelection() {\r\n      this.selectedUserId = null;\r\n    },\r\n\r\n    // 获取图纸详情\r\n    getInfo() {\r\n      const id = this.id;\r\n      getTechnicalAgreement(id).then((response) => {\r\n        this.form = response.data;\r\n        console.log(\"获取\", this.form);\r\n        this.technicalAgreementList = JSON.parse(this.form.technicalAgreement);\r\n      });\r\n    },\r\n\r\n    // 预览文件\r\n    handlePictureCardPreview(file) {\r\n      let localUrl = window.location.host;\r\n      if (file.url != null) {\r\n        if (localUrl === \"************:8099\") {\r\n          file.url = file.url.replace(\"ydxt.citicsteel.com:8099\", \"************:8099\");\r\n        }\r\n        window.open(file.url);\r\n      }\r\n      if (file.response && file.response.url) {\r\n        if (localUrl === \"************:8099\") {\r\n          let tmpUrl = file.response.url.replace(\"ydxt.citicsteel.com:8099\", \"************:8099\");\r\n          window.open(tmpUrl);\r\n        } else {\r\n          window.open(file.response.url);\r\n        }\r\n      }\r\n    },\r\n\r\n\r\n    //时间格式化\r\n    formatTime(isoTimeString) {\r\n      if (!isoTimeString) return '';\r\n\r\n      // 解析 ISO 8601 时间字符串\r\n      const date = new Date(isoTimeString);\r\n\r\n      // 提取年月日时分秒\r\n      const year = date.getFullYear();\r\n      const month = String(date.getMonth() + 1).padStart(2, '0'); // 月份是从0开始的，所以加1\r\n      const day = String(date.getDate()).padStart(2, '0');\r\n      const hours = String(date.getHours()).padStart(2, '0');\r\n      const minutes = String(date.getMinutes()).padStart(2, '0');\r\n      const seconds = String(date.getSeconds()).padStart(2, '0');\r\n\r\n      // 返回格式化的字符串\r\n      return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;\r\n\r\n    },\r\n    // 字符串时间格式化\r\n    formatStringTime(time) {\r\n      if (typeof time === \"string\") {\r\n        return (\r\n          time.substring(0, 4) +\r\n          \"-\" +\r\n          time.substring(4, 6) +\r\n          \"-\" +\r\n          time.substring(6, 8) +\r\n          \" \" +\r\n          time.substring(8, 10) +\r\n          \":\" +\r\n          time.substring(10, 12) +\r\n          \":\" +\r\n          time.substring(12, 14)\r\n        );\r\n      } else {\r\n        return time;\r\n      }\r\n    },\r\n\r\n    // 获取日志\r\n    getLogs() {\r\n      getLogList(this.logQueryParams).then((response) => {\r\n        console.log(\"日志\", response);\r\n        this.logList = response.rows;\r\n        this.total = response.total;\r\n        this.loading = false;\r\n      });\r\n    },\r\n\r\n  },\r\n};\r\n</script>"]}]}