{"remainingRequest": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??vue-loader-options!E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\src\\views\\truck\\alloy\\detail.vue?vue&type=template&id=4c787c5f&scoped=true", "dependencies": [{"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\src\\views\\truck\\alloy\\detail.vue", "mtime": 1755499162067}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}]}