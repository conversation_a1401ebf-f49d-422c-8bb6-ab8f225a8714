{"remainingRequest": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\src\\views\\suppPunishment\\project-module.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\src\\views\\suppPunishment\\project-module.vue", "mtime": 1755499162060}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["project-module.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAyEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "project-module.vue", "sourceRoot": "src/views/suppPunishment", "sourcesContent": ["<template>\r\n  <el-dialog\r\n    title=\"项目查询\"\r\n    :visible.sync=\"visible\"\r\n    width=\"900px\"\r\n    append-to-body\r\n    @close=\"handleClose\"\r\n  >\r\n    <!-- 查询条件 -->\r\n    <el-form :model=\"queryParams\" ref=\"queryForm\" :inline=\"true\" label-width=\"100px\">\r\n      <el-form-item label=\"项目编号\" prop=\"projectNo\">\r\n        <el-input\r\n          v-model=\"queryParams.projectNo\"\r\n          placeholder=\"请输入项目编号\"\r\n          clearable\r\n          size=\"small\"\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"项目名称\" prop=\"projectName\">\r\n        <el-input\r\n          v-model=\"queryParams.projectName\"\r\n          placeholder=\"请输入项目名称\"\r\n          clearable\r\n          size=\"small\"\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item >\r\n        <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleQuery\">搜索</el-button>\r\n        <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\r\n      </el-form-item>\r\n    </el-form>\r\n\r\n    <!-- 项目列表 -->\r\n    <el-table \r\n      v-loading=\"loading\" \r\n      :data=\"projectList\" \r\n      @row-click=\"handleRowClick\"\r\n      @row-dblclick=\"handleRowDoubleClick\"\r\n      highlight-current-row\r\n      style=\"cursor: pointer;\"\r\n    >\r\n      <el-table-column label=\"项目编号\" align=\"center\" prop=\"projectNo\" width=\"150\" />\r\n      <el-table-column label=\"项目名称\" align=\"center\" prop=\"projectName\" />\r\n      <el-table-column label=\"操作\" align=\"center\" width=\"100\">\r\n        <template slot-scope=\"scope\">\r\n          <el-button\r\n            size=\"mini\"\r\n            type=\"text\"\r\n            @click=\"handleSelect(scope.row)\"\r\n          >选择</el-button>\r\n        </template>\r\n      </el-table-column>\r\n    </el-table>\r\n\r\n    <!-- 分页 -->\r\n    <pagination\r\n      v-show=\"total>0\"\r\n      :total=\"total\"\r\n      :page.sync=\"queryParams.pageNum\"\r\n      :limit.sync=\"queryParams.pageSize\"\r\n      @pagination=\"getList\"\r\n    />\r\n\r\n    <!-- 底部按钮 -->\r\n    <div slot=\"footer\" class=\"dialog-footer\">\r\n      <el-button @click=\"handleClose\">取 消</el-button>\r\n    </div>\r\n  </el-dialog>\r\n</template>\r\n\r\n<script>\r\nimport { listProject } from \"@/api/suppPunishment/project\";\r\n\r\nexport default {\r\n  name: \"ProjectDialog\",\r\n  data() {\r\n    return {\r\n      // 遮罩层\r\n      loading: true,\r\n      // 弹窗显示状态\r\n      visible: false,\r\n      // 总条数\r\n      total: 0,\r\n      // 项目列表\r\n      projectList: [],\r\n      // 当前选中行\r\n      currentRow: null,\r\n      // 查询参数\r\n      queryParams: {\r\n        pageNum: 1,\r\n        pageSize: 10,\r\n        projectNo: null,\r\n        projectName: null\r\n      }\r\n    };\r\n  },\r\n  methods: {\r\n    /** 显示弹窗 */\r\n    show() {\r\n      this.visible = true;\r\n      this.resetQuery();\r\n      this.getList();\r\n    },\r\n    /** 关闭弹窗 */\r\n    handleClose() {\r\n      this.visible = false;\r\n      this.reset();\r\n    },\r\n    /** 重置数据 */\r\n    reset() {\r\n      this.projectList = [];\r\n      this.currentRow = null;\r\n      this.total = 0;\r\n      this.loading = false;\r\n    },\r\n    /** 查询项目列表 */\r\n    getList() {\r\n      this.loading = true;\r\n      listProject(this.queryParams).then(response => {\r\n        this.projectList = response.rows;\r\n        this.total = response.total;\r\n        this.loading = false;\r\n      }).catch(() => {\r\n        this.loading = false;\r\n      });\r\n    },\r\n    /** 搜索按钮操作 */\r\n    handleQuery() {\r\n      this.queryParams.pageNum = 1;\r\n      this.getList();\r\n    },\r\n    /** 重置按钮操作 */\r\n    resetQuery() {\r\n      this.queryParams = {\r\n        pageNum: 1,\r\n        pageSize: 10,\r\n        projectNo: null,\r\n        projectName: null\r\n      };\r\n      this.handleQuery();\r\n    },\r\n    /** 行点击事件 */\r\n    handleRowClick(row) {\r\n      this.currentRow = row;\r\n    },\r\n    /** 行双击事件 */\r\n    handleRowDoubleClick(row) {\r\n      this.handleSelect(row);\r\n    },\r\n    /** 选择项目 */\r\n    handleSelect(row) {\r\n      this.$emit('select', {\r\n        projectNo: row.projectNo,\r\n        projectName: row.projectName\r\n      });\r\n      this.handleClose();\r\n    }\r\n  }\r\n};\r\n</script>\r\n\r\n<style scoped>\r\n.dialog-footer {\r\n  text-align: center;\r\n}\r\n\r\n::v-deep .el-table tbody tr:hover > td {\r\n  background-color: #f5f7fa !important;\r\n}\r\n\r\n::v-deep .el-table tbody tr.current-row > td {\r\n  background-color: #ecf5ff !important;\r\n}\r\n</style>\r\n\r\n"]}]}