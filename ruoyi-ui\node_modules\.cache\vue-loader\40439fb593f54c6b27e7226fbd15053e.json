{"remainingRequest": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\src\\views\\drawing\\technicalDetail\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\src\\views\\drawing\\technicalDetail\\index.vue", "mtime": 1755499162049}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA0EA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAGA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA", "file": "index.vue", "sourceRoot": "src/views/drawing/technicalDetail", "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <!-- 信息确认 -->\r\n    <el-descriptions title=\"基础信息\" :column=\"4\" border>\r\n      <el-descriptions-item>\r\n        <template slot=\"label\"> 物料编码 </template>\r\n        {{ this.form.materialsCode }}\r\n      </el-descriptions-item>\r\n      <el-descriptions-item :span=\"2\">\r\n        <template slot=\"label\">技术协议</template>\r\n        <div v-if=\"!technicalAgreementList\" class=\"attachment-empty\">无技术协议</div>\r\n        <div v-if=\"technicalAgreementList\" class=\"attachment-empty\">\r\n          <a v-for=\"(item, index) in technicalAgreementList\" :key=\"index\" style=\"display: block; margin-bottom: 10px;\">\r\n            <el-button icon=\"el-icon-paperclip\" type=\"primary\" size=\"small\" @click=\"handleTechnical\">下载技术协议{{ index + 1\r\n              }}</el-button>\r\n          </a>\r\n        </div>\r\n      </el-descriptions-item>\r\n    </el-descriptions>\r\n\r\n    <h3>日志</h3>\r\n    <el-table v-loading=\"loading\" ref=\"logTable\" :data=\"logList\" style=\"width: 100%\">\r\n      <el-table-column prop=\"text\" label=\"时间\" width=\"200\">\r\n        <template slot-scope=\"scopes\">\r\n          {{ scopes.row.createTime }}\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column prop=\"info\" label=\"描述\" min-width=\"600\">\r\n      </el-table-column>\r\n    </el-table>\r\n  </div>\r\n</template>\r\n\r\n<pagination v-show=\"total >0\" :total=\"total\" :page.sync=\"logQuerParams.pageNum\" :limit.sync=\"logQueryParams.pageSize\"\r\n  @pagination=\"getLogs\"></pagination>\r\n\r\n<style scoped>\r\n.btn-footer {\r\n  padding: 18px 0;\r\n  text-align: center;\r\n}\r\n\r\n.margin-top {\r\n  margin-top: 20px;\r\n}\r\n\r\n.dialog-footer {\r\n  margin-top: 30px;\r\n}\r\n\r\n.title-div {\r\n  display: flex;\r\n  flex-direction: row;\r\n  align-items: center;\r\n  justify-content: space-between;\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.url-div {\r\n  display: flex;\r\n  flex-direction: row;\r\n  align-items: center;\r\n  justify-content: flex-start;\r\n}\r\n\r\n.qrcode-div {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: flex-start;\r\n  justify-content: flex-start;\r\n}\r\n</style>\r\n\r\n<script>\r\nimport { getLogList, getDrawing,  downloadTechnicalAgreement,getTechnicalAgreement } from '@/api/drawing/drawing';\r\nimport { getListByRoleKey } from '@/api/system/role';\r\n\r\nexport default {\r\n  name: \"TechnicalDetail\",\r\n  components: {},\r\n  data() {\r\n    return {\r\n      // 遮罩层\r\n      loading: true,\r\n      desLabelStyle: { width: '160px' },\r\n      // 总条数\r\n      orderInfo: {},\r\n      total: 0,\r\n      userTotal: 0,\r\n      // drawingNo: null,\r\n      selectedUserId: null,\r\n      selectedUserName: null,\r\n      form: {\r\n        // fileUrl: '[]',\r\n      },\r\n      logList: [],\r\n      drawingList: [],\r\n      tblList: [],\r\n      userList: [],\r\n      // fileList: [],\r\n      technicalAgreementList: [],\r\n      upload: {\r\n        // 设置上传的请求头部\r\n        headers: {},\r\n        // 上传的地址\r\n        url: process.env.VUE_APP_BASE_API + \"/app/common/uploadMinio\",\r\n        isUploading: false,\r\n      },\r\n      // fileList: [],\r\n      // rejectOpen: false,\r\n      // showDialog: false,\r\n      showSearch: true,\r\n      rejectDrawing: {\r\n        rejectReason: ''\r\n      },\r\n      passOpen: false,\r\n      passForm: {\r\n        id: '',\r\n        factoryAapproveReason: '',\r\n        equipmentApproveReason: '',\r\n        equipmentSearch: ''\r\n      },\r\n      isEquipmentApprove: false,\r\n      approveReason: '',\r\n      attachmentUuid: [],\r\n      // fileUrl: [],\r\n      logQueryParams: {\r\n        ticketNo: null,\r\n        pageNum: 1,\r\n        pageSize: 10,\r\n      },\r\n      queryParams: {\r\n        pageNum: 1,\r\n        pageSize: 10,\r\n        userName: undefined,\r\n      },\r\n    };\r\n  },\r\n\r\n  created() {\r\n    const id = this.$route.params && this.$route.params.id;\r\n    this.logQueryParams.approveId = id;\r\n    console.log(\"图纸号\", id);\r\n    // this.getUserList();\r\n    if (id) {\r\n      this.id = id;\r\n      this.getInfo();\r\n      this.getLogs();\r\n    } else {\r\n      this.$message({ message: \"未获取图纸号\", type: \"warning\" });\r\n    }\r\n  },\r\n  methods: {\r\n  \r\n    handleTechnical() {\r\n      const queryParams = { id: Number(this.id) };\r\n      this.$confirm('是否确认下载对应技术协议附件？', \"警告\", {\r\n        confirmButtonText: \"确定\",\r\n        cancelButtonText: \"取消\",\r\n        type: \"warning\"\r\n      }).then(function () {\r\n        return downloadTechnicalAgreement(queryParams);\r\n      }).then(response => {\r\n        this.download(response.msg);\r\n      })\r\n      console.log()\r\n    },\r\n\r\n    handleApproverRowClick(row) {\r\n      // 单击行时触发\r\n      this.selectedUserId = row.userName;\r\n    },\r\n\r\n    //清空选择状态\r\n    handleClearSelection() {\r\n      this.selectedUserId = null;\r\n    },\r\n\r\n    // 获取图纸详情\r\n    getInfo() {\r\n      const id = this.id;\r\n      getTechnicalAgreement(id).then((response) => {\r\n        this.form = response.data;\r\n        console.log(\"获取\", this.form);\r\n        this.technicalAgreementList = JSON.parse(this.form.technicalAgreement);\r\n      });\r\n    },\r\n\r\n    // 预览文件\r\n    handlePictureCardPreview(file) {\r\n      let localUrl = window.location.host;\r\n      if (file.url != null) {\r\n        if (localUrl === \"************:8099\") {\r\n          file.url = file.url.replace(\"ydxt.citicsteel.com:8099\", \"************:8099\");\r\n        }\r\n        window.open(file.url);\r\n      }\r\n      if (file.response && file.response.url) {\r\n        if (localUrl === \"************:8099\") {\r\n          let tmpUrl = file.response.url.replace(\"ydxt.citicsteel.com:8099\", \"************:8099\");\r\n          window.open(tmpUrl);\r\n        } else {\r\n          window.open(file.response.url);\r\n        }\r\n      }\r\n    },\r\n\r\n\r\n    //时间格式化\r\n    formatTime(isoTimeString) {\r\n      if (!isoTimeString) return '';\r\n\r\n      // 解析 ISO 8601 时间字符串\r\n      const date = new Date(isoTimeString);\r\n\r\n      // 提取年月日时分秒\r\n      const year = date.getFullYear();\r\n      const month = String(date.getMonth() + 1).padStart(2, '0'); // 月份是从0开始的，所以加1\r\n      const day = String(date.getDate()).padStart(2, '0');\r\n      const hours = String(date.getHours()).padStart(2, '0');\r\n      const minutes = String(date.getMinutes()).padStart(2, '0');\r\n      const seconds = String(date.getSeconds()).padStart(2, '0');\r\n\r\n      // 返回格式化的字符串\r\n      return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;\r\n\r\n    },\r\n    // 字符串时间格式化\r\n    formatStringTime(time) {\r\n      if (typeof time === \"string\") {\r\n        return (\r\n          time.substring(0, 4) +\r\n          \"-\" +\r\n          time.substring(4, 6) +\r\n          \"-\" +\r\n          time.substring(6, 8) +\r\n          \" \" +\r\n          time.substring(8, 10) +\r\n          \":\" +\r\n          time.substring(10, 12) +\r\n          \":\" +\r\n          time.substring(12, 14)\r\n        );\r\n      } else {\r\n        return time;\r\n      }\r\n    },\r\n\r\n    // 获取日志\r\n    getLogs() {\r\n      getLogList(this.logQueryParams).then((response) => {\r\n        console.log(\"日志\", response);\r\n        this.logList = response.rows;\r\n        this.total = response.total;\r\n        this.loading = false;\r\n      });\r\n    },\r\n\r\n  },\r\n};\r\n</script>"]}]}