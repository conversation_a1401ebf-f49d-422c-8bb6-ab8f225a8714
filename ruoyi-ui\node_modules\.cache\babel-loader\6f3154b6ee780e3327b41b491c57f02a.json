{"remainingRequest": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\src\\views\\leave\\permission\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\src\\views\\leave\\permission\\index.vue", "mtime": 1755499098416}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\babel.config.js", "mtime": 1688548084091}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_permit", "require", "name", "components", "data", "showSearch", "deptList", "roleOptions", "userList", "hasPermitUserList", "total", "loading", "totalPermit", "loadingPermit", "queryParams", "userName", "nick<PERSON><PERSON>", "pageNum", "pageSize", "userQueryParams", "deptId", "form", "title", "open", "rules", "required", "message", "trigger", "created", "init", "methods", "getHasPermitUsers", "getUserList", "getDeptList", "getRoleList", "_this", "listDept", "then", "res", "_this2", "listRole", "_this3", "listAllUsers", "response", "rows", "_this4", "listHasPermitUsers", "handleQuery", "reset<PERSON><PERSON>y", "resetForm", "handleQuerySec", "resetQuerySec", "reset", "userId", "<PERSON><PERSON><PERSON><PERSON>", "handleUpdate", "row", "_this5", "getInfo", "undefined", "handleDelete", "_this6", "$modal", "confirm", "deleteInfo", "msgSuccess", "catch", "submitForm", "_this7", "$refs", "validate", "valid", "updateInfo", "cancel", "tableRowClassName", "_ref", "rowIndex", "status"], "sources": ["src/views/leave/permission/index.vue"], "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <div class=\"card-title\">\r\n      <el-tag>全体用户</el-tag>\r\n    </div>\r\n    <el-card>\r\n      <!--用户数据-->\r\n      <el-form\r\n        :model=\"queryParams\"\r\n        ref=\"queryForm\"\r\n        :inline=\"true\"\r\n        v-show=\"showSearch\"\r\n        label-width=\"auto\"\r\n      >\r\n        <el-form-item label=\"用户账号\" prop=\"userName\">\r\n          <el-input\r\n            v-model=\"queryParams.userName\"\r\n            placeholder=\"请输入用户账号\"\r\n            clearable\r\n            size=\"small\"\r\n            style=\"width: 240px\"\r\n            @keyup.enter.native=\"handleQuery\"\r\n          />\r\n        </el-form-item>\r\n        <el-form-item label=\"用户姓名\" prop=\"nickName\">\r\n          <el-input\r\n            v-model=\"queryParams.nickName\"\r\n            placeholder=\"请输入用户姓名\"\r\n            clearable\r\n            size=\"small\"\r\n            style=\"width: 240px\"\r\n            @keyup.enter.native=\"handleQuery\"\r\n          />\r\n        </el-form-item>\r\n        <el-form-item>\r\n          <el-button\r\n            type=\"cyan\"\r\n            icon=\"el-icon-search\"\r\n            size=\"mini\"\r\n            @click=\"handleQuery\"\r\n            >搜索</el-button\r\n          >\r\n          <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\"\r\n            >重置</el-button\r\n          >\r\n        </el-form-item>\r\n      </el-form>\r\n      <el-table\r\n        v-loading=\"loading\"\r\n        :data=\"userList\"\r\n        :row-class-name=\"tableRowClassName\"\r\n      >\r\n        <el-table-column\r\n          label=\"用户账号\"\r\n          align=\"center\"\r\n          prop=\"userName\"\r\n          :show-overflow-tooltip=\"true\"\r\n        />\r\n        <el-table-column\r\n          label=\"用户姓名\"\r\n          align=\"center\"\r\n          prop=\"nickName\"\r\n          :show-overflow-tooltip=\"true\"\r\n        />\r\n        <el-table-column\r\n          label=\"操作\"\r\n          align=\"center\"\r\n          class-name=\"small-padding fixed-width\"\r\n        >\r\n          <template slot-scope=\"scope\">\r\n            <el-button\r\n              type=\"primary\"\r\n              icon=\"el-icon-edit\"\r\n              @click=\"handleUpdate(scope.row)\"\r\n              >修改</el-button\r\n            >\r\n          </template>\r\n        </el-table-column>\r\n      </el-table>\r\n\r\n      <pagination\r\n        v-show=\"total > 0\"\r\n        :total=\"total\"\r\n        :page.sync=\"queryParams.pageNum\"\r\n        :limit.sync=\"queryParams.pageSize\"\r\n        @pagination=\"getUserList\"\r\n      />\r\n    </el-card>\r\n    <div class=\"card-title-Sec\">\r\n      <el-tag type=\"success\">已分配用户</el-tag>\r\n    </div>\r\n    <el-card>\r\n      <!--已分配用户数据-->\r\n      <el-form\r\n        :model=\"userQueryParams\"\r\n        ref=\"userQueryForm\"\r\n        :inline=\"true\"\r\n        v-show=\"showSearch\"\r\n        label-width=\"auto\"\r\n      >\r\n        <el-form-item label=\"用户账号\" prop=\"userName\">\r\n          <el-input\r\n            v-model=\"userQueryParams.userName\"\r\n            placeholder=\"请输入用户账号\"\r\n            clearable\r\n            size=\"small\"\r\n            style=\"width: 240px\"\r\n            @keyup.enter.native=\"handleQuerySec\"\r\n          />\r\n        </el-form-item>\r\n        <el-form-item label=\"用户姓名\" prop=\"nickName\">\r\n          <el-input\r\n            v-model=\"userQueryParams.nickName\"\r\n            placeholder=\"请输入用户姓名\"\r\n            clearable\r\n            size=\"small\"\r\n            style=\"width: 240px\"\r\n            @keyup.enter.native=\"handleQuerySec\"\r\n          />\r\n        </el-form-item>\r\n        <el-form-item label=\"部门\" prop=\"deptId\">\r\n          <el-select\r\n            v-model=\"userQueryParams.deptId\"\r\n            style=\"width: 200px\"\r\n            size=\"small\"\r\n            placeholder=\"请选择部门\"\r\n            clearable\r\n            filterable\r\n          >\r\n            <el-option\r\n              v-for=\"item in deptList\"\r\n              :key=\"item.id\"\r\n              :label=\"item.storeName\"\r\n              :value=\"item.id\"\r\n            >\r\n              <span style=\"float: left\">{{ item.storeName }}</span>\r\n            </el-option>\r\n          </el-select>\r\n        </el-form-item>\r\n        <el-form-item>\r\n          <el-button\r\n            type=\"cyan\"\r\n            icon=\"el-icon-search\"\r\n            size=\"mini\"\r\n            @click=\"handleQuerySec\"\r\n            >搜索</el-button\r\n          >\r\n          <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuerySec\"\r\n            >重置</el-button\r\n          >\r\n        </el-form-item>\r\n      </el-form>\r\n      <el-table\r\n        v-loading=\"loadingPermit\"\r\n        :data=\"hasPermitUserList\"\r\n        :row-class-name=\"tableRowClassName\"\r\n      >\r\n        <el-table-column\r\n          label=\"用户账号\"\r\n          align=\"center\"\r\n          prop=\"userName\"\r\n          width=\"150px\"\r\n          :show-overflow-tooltip=\"true\"\r\n        />\r\n        <el-table-column\r\n          label=\"用户姓名\"\r\n          align=\"center\"\r\n          prop=\"nickName\"\r\n          width=\"150px\"\r\n          :show-overflow-tooltip=\"true\"\r\n        />\r\n        <el-table-column\r\n          label=\"部门\"\r\n          align=\"center\"\r\n          prop=\"deptName\"\r\n          width=\"200px\"\r\n          :show-overflow-tooltip=\"true\"\r\n        />\r\n        <el-table-column\r\n          label=\"权限\"\r\n          align=\"center\"\r\n          prop=\"roleNamesDesc\"\r\n          :show-overflow-tooltip=\"true\"\r\n        />\r\n        <el-table-column\r\n          label=\"操作\"\r\n          align=\"center\"\r\n          class-name=\"small-padding fixed-width\"\r\n        >\r\n          <template slot-scope=\"scope\">\r\n            <el-button\r\n              type=\"danger\"\r\n              icon=\"el-icon-delete\"\r\n              @click=\"handleDelete(scope.row)\"\r\n              >删除</el-button\r\n            >\r\n          </template>\r\n        </el-table-column>\r\n      </el-table>\r\n\r\n      <pagination\r\n        v-show=\"totalPermit > 0\"\r\n        :total=\"totalPermit\"\r\n        :page.sync=\"userQueryParams.pageNum\"\r\n        :limit.sync=\"userQueryParams.pageSize\"\r\n        @pagination=\"getHasPermitUsers\"\r\n      />\r\n    </el-card>\r\n\r\n    <!-- 添加或修改参数配置对话框 -->\r\n    <el-dialog :title=\"title\" :visible.sync=\"open\" width=\"600px\" append-to-body>\r\n      <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" label-width=\"80px\">\r\n        <el-row>\r\n          <el-form-item label=\"用户姓名\">\r\n            <el-input v-model=\"form.nickName\" readonly />\r\n          </el-form-item>\r\n        </el-row>\r\n        <el-row>\r\n          <el-form-item label=\"部门\" prop=\"deptId\">\r\n            <el-select\r\n              v-model=\"form.deptId\"\r\n              placeholder=\"请选择部门\"\r\n              filterable\r\n            >\r\n              <el-option\r\n                v-for=\"item in deptList\"\r\n                :key=\"item.id\"\r\n                :label=\"item.storeName\"\r\n                :value=\"item.id\"\r\n              ></el-option>\r\n            </el-select>\r\n          </el-form-item>\r\n        </el-row>\r\n        <el-row>\r\n          <el-form-item label=\"角色\" prop=\"roleId\">\r\n            <el-select\r\n              v-model=\"form.roleKeys\"\r\n              multiple\r\n              placeholder=\"请选择用户权限\"\r\n              filterable\r\n            >\r\n              <el-option\r\n                v-for=\"item in roleOptions\"\r\n                :key=\"item.index\"\r\n                :label=\"item.value\"\r\n                :value=\"item.code\"\r\n              ></el-option>\r\n            </el-select>\r\n          </el-form-item>\r\n        </el-row>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button type=\"primary\" @click=\"submitForm\">确 定</el-button>\r\n        <el-button @click=\"cancel\">取 消</el-button>\r\n      </div>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n\r\n<script>\r\nimport {\r\n  listDept,\r\n  listRole,\r\n  listAllUsers,\r\n  listHasPermitUsers,\r\n  getInfo,\r\n  updateInfo,\r\n  deleteInfo,\r\n} from \"@/api/leave/permit\";\r\n\r\nexport default {\r\n  name: \"exitPermitConfig\",\r\n  components: {},\r\n  data() {\r\n    return {\r\n      showSearch: true,\r\n      deptList: [],\r\n      roleOptions: [],\r\n      userList: [],\r\n      hasPermitUserList: [],\r\n      // 总条数\r\n      total: 0,\r\n      loading: false,\r\n      // 已分配\r\n      totalPermit: 0,\r\n      loadingPermit: false,\r\n      // 查询参数\r\n      queryParams: {\r\n        userName: null,\r\n        nickName: null,\r\n        pageNum: 1,\r\n        pageSize: 10,\r\n      },\r\n      userQueryParams: {\r\n        userName: null,\r\n        nickName: null,\r\n        deptId: null,\r\n        pageNum: 1,\r\n        pageSize: 10,\r\n      },\r\n      form: {},\r\n      // 弹出层标题\r\n      title: \"\",\r\n      // 是否显示弹出层\r\n      open: false,\r\n      rules: {\r\n        deptId: [{ required: true, message: \"请选择部门\", trigger: \"change\" }],\r\n      },\r\n    };\r\n  },\r\n  created() {\r\n    //初始化\r\n    this.init();\r\n  },\r\n  methods: {\r\n    //初始化\r\n    init() {\r\n      this.getHasPermitUsers();\r\n      this.getUserList();\r\n      this.getDeptList();\r\n      this.getRoleList();\r\n    },\r\n    //部门列表\r\n    getDeptList() {\r\n      listDept().then((res) => {\r\n        this.deptList = res.data;\r\n      });\r\n    },\r\n    //角色列表\r\n    getRoleList() {\r\n      listRole().then((res) => {\r\n        // let options = res.data;\r\n        // this.roleOptions = options.filter(\r\n        //   (x) =>\r\n        //     x.code != \"leave.centerApprover\" &&\r\n        //     x.code != \"leave.guard\" &&\r\n        //     x.code != \"leave.provider\"\r\n        // );\r\n        this.roleOptions = res.data\r\n      });\r\n    },\r\n    //待分配用户列表\r\n    getUserList() {\r\n      this.loading = true;\r\n      listAllUsers(this.queryParams).then((response) => {\r\n        this.userList = response.rows;\r\n        this.total = response.total;\r\n        this.loading = false;\r\n      });\r\n    },\r\n    //已分配用户列表\r\n    getHasPermitUsers() {\r\n      this.loadingPermit = true;\r\n      listHasPermitUsers(this.userQueryParams).then((res) => {\r\n        this.hasPermitUserList = res.rows;\r\n        this.totalPermit = res.total;\r\n        this.loadingPermit = false;\r\n      });\r\n    },\r\n    //搜索按钮操作\r\n    handleQuery() {\r\n      this.queryParams.pageNum = 1;\r\n      this.getUserList();\r\n    },\r\n    //重置按钮操作\r\n    resetQuery() {\r\n      this.resetForm(\"queryForm\");\r\n      this.handleQuery();\r\n    },\r\n    //搜索按钮操作\r\n    handleQuerySec() {\r\n      this.userQueryParams.pageNum = 1;\r\n      this.getHasPermitUsers();\r\n    },\r\n    //重置按钮操作\r\n    resetQuerySec() {\r\n      this.resetForm(\"userQueryForm\");\r\n      this.handleQuerySec();\r\n    },\r\n    // 表单重置\r\n    reset() {\r\n      this.form = {\r\n        userId: null,\r\n        userName: null,\r\n        nickName: null,\r\n        deptId: null,\r\n        roleKeys: [],\r\n      };\r\n      this.resetForm(\"form\");\r\n    },\r\n    //编辑用户\r\n    handleUpdate(row) {\r\n      this.reset();\r\n      const userId = row.userId;\r\n      getInfo({ userId: userId }).then((res) => {\r\n        this.open = true;\r\n        this.form.userId = userId;\r\n        this.form.nickName = row.nickName;\r\n        if (res.data != undefined) {\r\n          this.form.deptId = res.data.deptId;\r\n          this.form.roleKeys = res.data.roleKeys;\r\n        }\r\n      });\r\n    },\r\n    //删除用户\r\n    handleDelete(row) {\r\n      const userId = row.userId;\r\n      this.$modal\r\n        .confirm('是否删除\"' + row.nickName + '\"权限？')\r\n        .then(() => {\r\n          deleteInfo({ userId: userId });\r\n        })\r\n        .then(() => {\r\n          this.$modal.msgSuccess(\"删除成功\");\r\n          this.handleQuerySec();\r\n        })\r\n        .catch(() => {});\r\n    },\r\n    //提交表单\r\n    submitForm() {\r\n      this.$refs[\"form\"].validate((valid) => {\r\n        if (valid) {\r\n          updateInfo(this.form).then((response) => {\r\n            this.$modal.msgSuccess(\"修改成功\");\r\n            this.open = false;\r\n            this.handleQuerySec();\r\n          });\r\n        }\r\n      });\r\n    },\r\n    cancel() {\r\n      this.open = false;\r\n      this.reset();\r\n    },\r\n    tableRowClassName({ row, rowIndex }) {\r\n      if (row.status == \"1\") {\r\n        return \"warning-row\";\r\n      }\r\n      return \"\";\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n\r\n<style lang=\"scss\" scoped>\r\n.card-title {\r\n  margin-bottom: 10px;\r\n}\r\n.card-title-Sec {\r\n  margin-top: 10px;\r\n  margin-bottom: 10px;\r\n}\r\n.basic {\r\n  margin-top: 20px;\r\n  margin-bottom: 20px;\r\n}\r\n.card-footer {\r\n  display: flex;\r\n  justify-content: flex-end;\r\n}\r\n</style>"], "mappings": ";;;;;;AAqQA,IAAAA,OAAA,GAAAC,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCAUA;EACAC,IAAA;EACAC,UAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,UAAA;MACAC,QAAA;MACAC,WAAA;MACAC,QAAA;MACAC,iBAAA;MACA;MACAC,KAAA;MACAC,OAAA;MACA;MACAC,WAAA;MACAC,aAAA;MACA;MACAC,WAAA;QACAC,QAAA;QACAC,QAAA;QACAC,OAAA;QACAC,QAAA;MACA;MACAC,eAAA;QACAJ,QAAA;QACAC,QAAA;QACAI,MAAA;QACAH,OAAA;QACAC,QAAA;MACA;MACAG,IAAA;MACA;MACAC,KAAA;MACA;MACAC,IAAA;MACAC,KAAA;QACAJ,MAAA;UAAAK,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;MACA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA;IACA,KAAAC,IAAA;EACA;EACAC,OAAA;IACA;IACAD,IAAA,WAAAA,KAAA;MACA,KAAAE,iBAAA;MACA,KAAAC,WAAA;MACA,KAAAC,WAAA;MACA,KAAAC,WAAA;IACA;IACA;IACAD,WAAA,WAAAA,YAAA;MAAA,IAAAE,KAAA;MACA,IAAAC,gBAAA,IAAAC,IAAA,WAAAC,GAAA;QACAH,KAAA,CAAA7B,QAAA,GAAAgC,GAAA,CAAAlC,IAAA;MACA;IACA;IACA;IACA8B,WAAA,WAAAA,YAAA;MAAA,IAAAK,MAAA;MACA,IAAAC,gBAAA,IAAAH,IAAA,WAAAC,GAAA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACAC,MAAA,CAAAhC,WAAA,GAAA+B,GAAA,CAAAlC,IAAA;MACA;IACA;IACA;IACA4B,WAAA,WAAAA,YAAA;MAAA,IAAAS,MAAA;MACA,KAAA9B,OAAA;MACA,IAAA+B,oBAAA,OAAA5B,WAAA,EAAAuB,IAAA,WAAAM,QAAA;QACAF,MAAA,CAAAjC,QAAA,GAAAmC,QAAA,CAAAC,IAAA;QACAH,MAAA,CAAA/B,KAAA,GAAAiC,QAAA,CAAAjC,KAAA;QACA+B,MAAA,CAAA9B,OAAA;MACA;IACA;IACA;IACAoB,iBAAA,WAAAA,kBAAA;MAAA,IAAAc,MAAA;MACA,KAAAhC,aAAA;MACA,IAAAiC,0BAAA,OAAA3B,eAAA,EAAAkB,IAAA,WAAAC,GAAA;QACAO,MAAA,CAAApC,iBAAA,GAAA6B,GAAA,CAAAM,IAAA;QACAC,MAAA,CAAAjC,WAAA,GAAA0B,GAAA,CAAA5B,KAAA;QACAmC,MAAA,CAAAhC,aAAA;MACA;IACA;IACA;IACAkC,WAAA,WAAAA,YAAA;MACA,KAAAjC,WAAA,CAAAG,OAAA;MACA,KAAAe,WAAA;IACA;IACA;IACAgB,UAAA,WAAAA,WAAA;MACA,KAAAC,SAAA;MACA,KAAAF,WAAA;IACA;IACA;IACAG,cAAA,WAAAA,eAAA;MACA,KAAA/B,eAAA,CAAAF,OAAA;MACA,KAAAc,iBAAA;IACA;IACA;IACAoB,aAAA,WAAAA,cAAA;MACA,KAAAF,SAAA;MACA,KAAAC,cAAA;IACA;IACA;IACAE,KAAA,WAAAA,MAAA;MACA,KAAA/B,IAAA;QACAgC,MAAA;QACAtC,QAAA;QACAC,QAAA;QACAI,MAAA;QACAkC,QAAA;MACA;MACA,KAAAL,SAAA;IACA;IACA;IACAM,YAAA,WAAAA,aAAAC,GAAA;MAAA,IAAAC,MAAA;MACA,KAAAL,KAAA;MACA,IAAAC,MAAA,GAAAG,GAAA,CAAAH,MAAA;MACA,IAAAK,eAAA;QAAAL,MAAA,EAAAA;MAAA,GAAAhB,IAAA,WAAAC,GAAA;QACAmB,MAAA,CAAAlC,IAAA;QACAkC,MAAA,CAAApC,IAAA,CAAAgC,MAAA,GAAAA,MAAA;QACAI,MAAA,CAAApC,IAAA,CAAAL,QAAA,GAAAwC,GAAA,CAAAxC,QAAA;QACA,IAAAsB,GAAA,CAAAlC,IAAA,IAAAuD,SAAA;UACAF,MAAA,CAAApC,IAAA,CAAAD,MAAA,GAAAkB,GAAA,CAAAlC,IAAA,CAAAgB,MAAA;UACAqC,MAAA,CAAApC,IAAA,CAAAiC,QAAA,GAAAhB,GAAA,CAAAlC,IAAA,CAAAkD,QAAA;QACA;MACA;IACA;IACA;IACAM,YAAA,WAAAA,aAAAJ,GAAA;MAAA,IAAAK,MAAA;MACA,IAAAR,MAAA,GAAAG,GAAA,CAAAH,MAAA;MACA,KAAAS,MAAA,CACAC,OAAA,WAAAP,GAAA,CAAAxC,QAAA,WACAqB,IAAA;QACA,IAAA2B,kBAAA;UAAAX,MAAA,EAAAA;QAAA;MACA,GACAhB,IAAA;QACAwB,MAAA,CAAAC,MAAA,CAAAG,UAAA;QACAJ,MAAA,CAAAX,cAAA;MACA,GACAgB,KAAA;IACA;IACA;IACAC,UAAA,WAAAA,WAAA;MAAA,IAAAC,MAAA;MACA,KAAAC,KAAA,SAAAC,QAAA,WAAAC,KAAA;QACA,IAAAA,KAAA;UACA,IAAAC,kBAAA,EAAAJ,MAAA,CAAA/C,IAAA,EAAAgB,IAAA,WAAAM,QAAA;YACAyB,MAAA,CAAAN,MAAA,CAAAG,UAAA;YACAG,MAAA,CAAA7C,IAAA;YACA6C,MAAA,CAAAlB,cAAA;UACA;QACA;MACA;IACA;IACAuB,MAAA,WAAAA,OAAA;MACA,KAAAlD,IAAA;MACA,KAAA6B,KAAA;IACA;IACAsB,iBAAA,WAAAA,kBAAAC,IAAA;MAAA,IAAAnB,GAAA,GAAAmB,IAAA,CAAAnB,GAAA;QAAAoB,QAAA,GAAAD,IAAA,CAAAC,QAAA;MACA,IAAApB,GAAA,CAAAqB,MAAA;QACA;MACA;MACA;IACA;EACA;AACA", "ignoreList": []}]}