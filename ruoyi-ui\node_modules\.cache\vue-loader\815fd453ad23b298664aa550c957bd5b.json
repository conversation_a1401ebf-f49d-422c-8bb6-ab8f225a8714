{"remainingRequest": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\src\\views\\suppPunishment\\materialInfo-module.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\src\\views\\suppPunishment\\materialInfo-module.vue", "mtime": 1755499162058}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["materialInfo-module.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA0EA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "materialInfo-module.vue", "sourceRoot": "src/views/suppPunishment", "sourcesContent": ["<template>\r\n  <el-dialog\r\n    title=\"物料信息查询\"\r\n    :visible.sync=\"visible\"\r\n    width=\"900px\"\r\n    append-to-body\r\n    @close=\"handleClose\"\r\n  >\r\n    <!-- 查询条件 -->\r\n    <el-form :model=\"queryParams\" ref=\"queryForm\" :inline=\"true\" label-width=\"100px\">\r\n      <el-form-item label=\"物料小类代码\" prop=\"itemId\">\r\n        <el-input\r\n          v-model=\"queryParams.itemId\"\r\n          placeholder=\"请输入物料小类代码\"\r\n          clearable\r\n          size=\"small\"\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"物料小类名称\" prop=\"itemName\">\r\n        <el-input\r\n          v-model=\"queryParams.itemName\"\r\n          placeholder=\"请输入物料小类名称\"\r\n          clearable\r\n          size=\"small\"\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item >\r\n        <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleQuery\">搜索</el-button>\r\n        <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\r\n      </el-form-item>\r\n    </el-form>\r\n\r\n    <!-- 物料列表 -->\r\n    <el-table \r\n      v-loading=\"loading\" \r\n      :data=\"materialList\" \r\n      @row-click=\"handleRowClick\"\r\n      @row-dblclick=\"handleRowDoubleClick\"\r\n      highlight-current-row\r\n      style=\"cursor: pointer;\"\r\n    >\r\n      <el-table-column label=\"物料小类代码\" align=\"center\" prop=\"itemId\" width=\"150\" />\r\n      <el-table-column label=\"物料小类名称\" align=\"center\" prop=\"itemName\" />\r\n      <el-table-column label=\"操作\" align=\"center\" width=\"100\">\r\n        <template slot-scope=\"scope\">\r\n          <el-button\r\n            size=\"mini\"\r\n            type=\"text\"\r\n            icon=\"el-icon-check\"\r\n            @click=\"handleSelect(scope.row)\"\r\n          >选择</el-button>\r\n        </template>\r\n      </el-table-column>\r\n    </el-table>\r\n\r\n    <!-- 分页 -->\r\n    <pagination\r\n      v-show=\"total > 0\"\r\n      :total=\"total\"\r\n      :page.sync=\"queryParams.pageNum\"\r\n      :limit.sync=\"queryParams.pageSize\"\r\n      @pagination=\"getList\"\r\n    />\r\n\r\n    <!-- 底部按钮 -->\r\n    <div slot=\"footer\" class=\"dialog-footer\">\r\n      <el-button @click=\"handleClose\">取 消</el-button>\r\n    </div>\r\n  </el-dialog>\r\n</template>\r\n\r\n<script>\r\nimport { listMaterialInfo } from \"@/api/suppPunishment/materialInfo\";\r\n\r\nexport default {\r\n  name: \"MaterialInfoDialog\",\r\n  data() {\r\n    return {\r\n      // 是否显示弹出层\r\n      visible: false,\r\n      // 遮罩层\r\n      loading: true,\r\n      // 总条数\r\n      total: 0,\r\n      // 物料信息表格数据\r\n      materialList: [],\r\n      // 查询参数\r\n      queryParams: {\r\n        pageNum: 1,\r\n        pageSize: 10,\r\n        itemId: null,\r\n        itemName: null\r\n      }\r\n    };\r\n  },\r\n  methods: {\r\n    /** 显示弹窗 */\r\n    show() {\r\n      this.visible = true;\r\n      this.resetQuery();\r\n      this.getList();\r\n    },\r\n    \r\n    /** 隐藏弹窗 */\r\n    hide() {\r\n      this.visible = false;\r\n    },\r\n    \r\n    /** 关闭弹窗 */\r\n    handleClose() {\r\n      this.visible = false;\r\n      this.reset();\r\n    },\r\n    \r\n    /** 重置数据 */\r\n    reset() {\r\n      this.materialList = [];\r\n      this.total = 0;\r\n      this.queryParams = {\r\n        pageNum: 1,\r\n        pageSize: 10,\r\n        itemId: null,\r\n        itemName: null\r\n      };\r\n    },\r\n    \r\n    /** 查询物料信息列表 */\r\n    getList() {\r\n      this.loading = true;\r\n      listMaterialInfo(this.queryParams).then(response => {\r\n        this.materialList = response.rows;\r\n        this.total = response.total;\r\n        this.loading = false;\r\n      }).catch(() => {\r\n        this.loading = false;\r\n      });\r\n    },\r\n    \r\n    /** 搜索按钮操作 */\r\n    handleQuery() {\r\n      this.queryParams.pageNum = 1;\r\n      this.getList();\r\n    },\r\n    \r\n    /** 重置按钮操作 */\r\n    resetQuery() {\r\n      this.resetForm(\"queryForm\");\r\n      this.handleQuery();\r\n    },\r\n    \r\n    /** 行点击事件 */\r\n    handleRowClick(row) {\r\n      // 可以在这里添加行选中效果\r\n    },\r\n    \r\n    /** 行双击事件 */\r\n    handleRowDoubleClick(row) {\r\n      this.handleSelect(row);\r\n    },\r\n    \r\n    /** 选择物料 */\r\n    handleSelect(row) {\r\n      this.$emit('select', row);\r\n      this.handleClose();\r\n    }\r\n  }\r\n};\r\n</script>\r\n\r\n<style scoped>\r\n/* 表格行悬停效果 */\r\n::v-deep .el-table tbody tr:hover {\r\n  background-color: #f5f7fa;\r\n}\r\n\r\n/* 查询表单样式 - 输入框左对齐，按钮右对齐 */\r\n.el-form--inline {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 20px;\r\n}\r\n\r\n/* 输入框区域左对齐 */\r\n.el-form--inline .el-form-item:not(:last-child) {\r\n  margin-right: 15px;\r\n  margin-bottom: 0;\r\n}\r\n\r\n/* 按钮区域右对齐 */\r\n.el-form--inline .el-form-item:last-child {\r\n  margin-left: auto;\r\n  margin-right: 0;\r\n  margin-bottom: 0;\r\n}\r\n\r\n/* 统一输入框宽度 */\r\n.el-form--inline .el-form-item .el-input {\r\n  width: 200px;\r\n}\r\n\r\n/* 弹窗标题居中 */\r\n::v-deep .el-dialog__header {\r\n  text-align: center;\r\n}\r\n\r\n::v-deep .el-dialog__title {\r\n  text-align: center;\r\n  width: 100%;\r\n  display: block;\r\n}\r\n</style>\r\n"]}]}