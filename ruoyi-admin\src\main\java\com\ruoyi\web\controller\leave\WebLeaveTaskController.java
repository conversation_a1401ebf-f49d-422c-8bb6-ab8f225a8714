package com.ruoyi.web.controller.leave;

import com.ruoyi.app.leave.controller.LeaveTaskController;
import com.ruoyi.app.leave.domain.*;
import com.ruoyi.app.leave.dto.MeasureNotifyDto;
import com.ruoyi.app.leave.mapper.LeavePlanMapper;
import com.ruoyi.app.leave.service.*;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.GateLocationUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.system.mapper.SysUserMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.util.*;

/**
 * 出门证任务Controller
 * 
 * <AUTHOR>
 * @date 2025-03-13
 */
@RestController
@RequestMapping("/web/leaveTask/task")
public class WebLeaveTaskController extends BaseController
{
    @Autowired
    private ILeaveTaskService leaveTaskService;

    @Autowired
    private ILeavePlanService leavePlanService;

    @Autowired
    private ILeaveTaskMaterialService leaveTaskMaterialService;

    @Autowired
    private ILeaveLogService leaveLogService;

    @Autowired
    private ILeavePlanMaterialService leavePlanMaterialService;

    @Autowired
    private IStoreoutBfhCkService storeoutBfhCkService;

    @Autowired
    private IDicService dicService;

    @Autowired
    private SysUserMapper sysUserMapper;

    @Autowired
    private IStoreinWwjgService storeinWwjgService;

    @Autowired
    private IStoreinKqdbService storeinKqdbService;

    @Autowired
    private IStoreoutKqdbService storeoutKqdbService;

    @Autowired
    private IStoreoutWwjgService storeoutWwjgService;

    @Autowired
    private LeavePlanMapper leavePlanMapper;


    private static final Logger log = LoggerFactory.getLogger(LeaveTaskController.class);

    /**
     * 查询出门证任务列表
     */
    @GetMapping("/list")
    public TableDataInfo list(LeaveTask leaveTask)
    {
        startPage();
        List<LeaveTask> list = leaveTaskService.selectLeaveTaskList(leaveTask);
        return getDataTable(list);
    }

    /**
     * 导出出门证任务列表
     */
    @Log(title = "出门证任务", businessType = BusinessType.EXPORT)
    @GetMapping("/export")
    public AjaxResult export(LeaveTask leaveTask)
    {
        List<LeaveTask> list = leaveTaskService.selectLeaveTaskList(leaveTask);
        ExcelUtil<LeaveTask> util = new ExcelUtil<LeaveTask>(LeaveTask.class);
        return util.exportExcel(list, "task");
    }

    /**
     * 获取出门证任务详细信息
     */
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return AjaxResult.success(leaveTaskService.selectLeaveTaskById(id));
    }

    /**
     * 新增出门证任务
     */
    @Log(title = "出门证任务", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody LeaveTask leaveTask)
    {
        return toAjax(leaveTaskService.insertLeaveTask(leaveTask));
    }

    /**
     * 修改出门证任务
     */
    @Log(title = "出门证任务", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody LeaveTask leaveTask)
    {
        return toAjax(leaveTaskService.updateLeaveTask(leaveTask));
    }

    /**
     * 删除出门证任务
     */
    @Log(title = "出门证任务", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(leaveTaskService.deleteLeaveTaskByIds(ids));
    }

    /**监听计量推送的过磅信息，并修改任务状态*/
    @PostMapping("/listenMeasurePushInfo")
    public AjaxResult listenMeasurePushInfo(@RequestBody MeasureNotifyDto measureNotifyDto){
        leaveTaskService.listenMeasurePushInfo(measureNotifyDto);
        return AjaxResult.success();
    }

    @GetMapping(value = "/getDirectSupplyPlanAndTaskDetail")
    public TableDataInfo getDirectSupplyPlanAndTaskDetail(LeaveTask leaveTask0)
    {
        String directSupplyTaskNo = leaveTask0.getTaskNo();
        LeaveTask leaveTask = new LeaveTask();
        leaveTask.setTaskNo(directSupplyTaskNo);
        List<LeaveTask> list = leaveTaskService.selectLeaveTaskList(leaveTask);
        LeaveTask leaveTask1 = list.get(0);
        String applyNo = leaveTask1.getApplyNo();
        LeavePlan leavePlan = new LeavePlan();
        leavePlan.setApplyNo(applyNo);
        List<LeavePlan> leavePlans = leavePlanService.selectLeavePlanList(leavePlan);
        LeavePlan leavePlan1 = leavePlans.get(0);

        ArrayList<Object> objects = new ArrayList<>();
        objects.add(leaveTask1);
        objects.add(leavePlan1);

        return  getDataTable(objects);
    }

    @Log(title = "出门证任务", businessType = BusinessType.INSERT)
    @PostMapping("/taskAndMaterial")
    public AjaxResult addTaskAndMaterial(@RequestBody LeaveTask leaveTask)
    {

        String applyNo = leaveTask.getApplyNo();

        leavePlanService.updateIsSendCar(applyNo);
        String snowId = leaveTaskService.insertLeaveTaskAndMaterial(leaveTask);
        return new AjaxResult(200,"请求成功",snowId);
    }

    @Log(title = "出门证任务", businessType = BusinessType.INSERT)
    @PostMapping("/addTaskAndMaterialAndAddLeaveLog")
    public AjaxResult addTaskAndMaterialAndaddLeaveLog(@RequestBody AddTaskAndMaterialAndAddLeaveLogVo addTaskAndMaterialAndAddLeaveLogVo)
    {

        LeaveTask leaveTask = addTaskAndMaterialAndAddLeaveLogVo.getLeaveTask();
        String applyNo = leaveTask.getApplyNo();
        leaveTask.setCreateBy(SecurityUtils.getUsername());

        leavePlanService.updateIsSendCar(applyNo);
        String snowId = leaveTaskService.insertLeaveTaskAndMaterial(leaveTask);

        for (LeaveTaskMaterial leaveTaskMaterial : addTaskAndMaterialAndAddLeaveLogVo.getLeaveTaskMaterialList()) {
            leaveTaskMaterial.setTaskNo(snowId);
            leaveTaskMaterial.setCreateBy(SecurityUtils.getUsername());
            leaveTaskMaterial.setCreateTime(DateUtils.getNowDate());
            leaveTaskMaterialService.insertLeaveTaskMaterial(leaveTaskMaterial);
        }

        LeaveLog leaveLog = new LeaveLog();
        leaveLog.setLogType(2);
        leaveLog.setTaskNo(snowId);
        leaveLog.setApplyNo(leaveTask.getApplyNo());
        ;
        leaveLog.setInfo("派车任务创建，" + "创建人：" + SecurityUtils.getLoginUser().getUser().getNickName() + "，车牌号：" + leaveTask.getCarNum() + "，司机：" +  leaveTask.getDriverName());
        leaveLog.setCreateBy(SecurityUtils.getUsername());
        leaveLogService.insertLeaveLog(leaveLog);

//        leaveTaskMaterialService.insertLeaveTaskMaterial(leaveTaskMaterial)
        return new AjaxResult(200,"请求成功");
    }

    @Log(title = "出门证任务", businessType = BusinessType.INSERT)
    @PostMapping("/addLeaveLogAndEditTaskMaterialsAndUpdateTask")
    public AjaxResult addLeaveLogAndEditTaskMaterialsAndUpdateTask(@RequestBody AddLeaveLogAndEditTaskMaterialsAndUpdateTaskVo addLeaveLogAndEditTaskMaterialsAndUpdateTaskVo)
    {
        LeaveLog leaveLog = addLeaveLogAndEditTaskMaterialsAndUpdateTaskVo.getLeaveLog();
        leaveLog.setCreateBy(SecurityUtils.getUsername());
        leaveLogService.insertLeaveLog(leaveLog);

        for (LeaveTaskMaterial leaveTaskMaterial : addLeaveLogAndEditTaskMaterialsAndUpdateTaskVo.getTaskMaterialList()) {
            leaveTaskMaterialService.updateLeaveTaskMaterial(leaveTaskMaterial);
        }
        LeaveTask leaveTask = addLeaveLogAndEditTaskMaterialsAndUpdateTaskVo.getLeaveTask();

        //判断大门
        Optional<GateLocationUtils.GateLocation> gate = GateLocationUtils.getCurrentGate();
        if (leaveLog.getInfo().toLowerCase().contains("出厂".toLowerCase())) {
            //离厂大门
            leaveTask.setLeaveDoor(gate.get().getPostag());
            leaveTask.setLeaveTime(DateUtils.getNowDate());
        }
        if (leaveLog.getInfo().toLowerCase().contains("入厂".toLowerCase())) {
            //离厂大门
            leaveTask.setEnterDoor(gate.get().getPostag());
            leaveTask.setEnterTime(DateUtils.getNowDate());
        }


        leaveTaskService.updateLeaveTask(leaveTask);

        if (leaveTask.getTaskStatus() == 9) {
            LeaveLog leaveLog1 = new LeaveLog();
            leaveLog1.setLogType(2);
            leaveLog1.setTaskNo(leaveLog.getTaskNo());
            leaveLog1.setApplyNo(leaveLog.getApplyNo());
            leaveLog1.setInfo("任务完成");
            leaveLogService.insertLeaveLog(leaveLog1);
            //计划完成逻辑
            if (addLeaveLogAndEditTaskMaterialsAndUpdateTaskVo.getMeasureFlag() != null && addLeaveLogAndEditTaskMaterialsAndUpdateTaskVo.getMeasureFlag() == 0) {
                LeaveTask leaveTask1 = leaveTaskService.selectLeaveTaskById(leaveTask.getId());

                LeavePlan leavePlan = new LeavePlan();
                leavePlan.setApplyNo(leaveTask1.getApplyNo());

                List<LeavePlan> leavePlans = leavePlanService.selectLeavePlanList(leavePlan);
                LeavePlan leavePlan1 = leavePlans.get(0);

                //出厂不返回
                if (leavePlan1.getPlanType() == 1) {
                    LeavePlan leavePlan2 = new LeavePlan();
                    leavePlan2.setPlanStatus(7);
                    leavePlan2.setId(leavePlan1.getId());
                    leavePlan2.setUpdateBy(SecurityUtils.getUsername());
                    leavePlan2.setUpdateTime(DateUtils.getNowDate());
                    leavePlanMapper.updateLeavePlan(leavePlan2);
                    //计量交互
                    leavePlan1.setPlanStatus(7);
                    leavePlanService.handlePlan(leavePlan1);
                }
                //出厂返回
                if (leavePlan1.getPlanType() == 2) {
                    int flag = 0;

                    LeaveTask leaveTask2 = new LeaveTask();
                    leaveTask2.setApplyNo(leaveTask1.getApplyNo());
                    List<LeaveTask> list = leaveTaskService.selectLeaveTaskList(leaveTask2);
                    for (LeaveTask task : list) {
                        if (task.getTaskNo() != leaveTask1.getTaskNo()) {
                            Integer taskStatus = task.getTaskStatus();
                            if (taskStatus != 9) {
                                flag = -1;
                                break;
                            }
                        }
                    }

                    LeavePlanMaterial leavePlanMaterial = new LeavePlanMaterial();
                    leavePlanMaterial.setApplyNo(leaveTask1.getApplyNo());
                    List<LeavePlanMaterial> leavePlanMaterials = leavePlanMaterialService.selectLeavePlanMaterialList(leavePlanMaterial);

                    HashMap<Long, BigDecimal> hashMap = new HashMap<>();

                    for (LeaveTask task : list) {
                        if (task.getTaskType() == 2) {
                            LeaveTaskMaterial leaveTaskMaterial = new LeaveTaskMaterial();
                            leaveTaskMaterial.setTaskNo(task.getTaskNo());
                            List<LeaveTaskMaterial> leaveTaskMaterials = leaveTaskMaterialService.selectLeaveTaskMaterialList(leaveTaskMaterial);


                            for (LeaveTaskMaterial taskMaterial : leaveTaskMaterials) {
                                Long materialId = taskMaterial.getMaterialId();
                                BigDecimal planNum = taskMaterial.getPlanNum();
                                if (hashMap.containsKey(materialId)) {
                                    BigDecimal bigDecimal = hashMap.get(materialId);
                                    BigDecimal add = planNum.add(bigDecimal);
                                    hashMap.put(materialId, add);
                                } else {
                                    hashMap.put(materialId, planNum);
                                }
                            }
                        }
                    }


                    for (LeavePlanMaterial planMaterial : leavePlanMaterials) {
                        Long materialId = planMaterial.getMaterialId();
                        BigDecimal planNum = planMaterial.getPlanNum();

                        if (hashMap.containsKey(materialId)) {
                            BigDecimal bigDecimal = hashMap.get(materialId);
                            if (bigDecimal.compareTo(planNum) != 0) {
                                flag = -1;
                            }
                        } else {
                            flag = -1;
                        }
                        if (flag == -1) {
                            break;
                        }
                    }

                    if (flag == 0) {
                        LeavePlan leavePlan2 = new LeavePlan();
                        leavePlan2.setPlanStatus(7);
                        leavePlan2.setId(leavePlan1.getId());
                        leavePlan2.setUpdateBy(SecurityUtils.getUsername());
                        leavePlan2.setUpdateTime(DateUtils.getNowDate());
                        leavePlanMapper.updateLeavePlan(leavePlan2);

                        //计量交互
                        leavePlan1.setPlanStatus(7);
                        leavePlanService.handlePlan(leavePlan1);
                    }
                }

                //跨区调拨
                if (leavePlan1.getPlanType() == 3) {
                    LeavePlan leavePlan2 = new LeavePlan();
                    leavePlan2.setPlanStatus(7);
                    leavePlan2.setId(leavePlan1.getId());
                    leavePlan2.setUpdateBy(SecurityUtils.getUsername());
                    leavePlan2.setUpdateTime(DateUtils.getNowDate());
                    leavePlanMapper.updateLeavePlan(leavePlan2);

                    //计量交互
                    leavePlan1.setPlanStatus(7);
                    leavePlanService.handlePlan(leavePlan1);
                }

            }
        }

        return new AjaxResult(200,"请求成功");
    }

    @Log(title = "出门证任务", businessType = BusinessType.UPDATE)
    @PostMapping("/isAllowDispatch")
    public AjaxResult isAllowDispatch(@RequestBody LeaveTask leaveTask)
    {
        int row = leaveTaskService.isAllowDispatch(leaveTask);
        return AjaxResult.success(row);
    }

    @Log(title = "出门证任务", businessType = BusinessType.DELETE)
    @GetMapping("/getProcessList")
    public TableDataInfo getProcessList(DProcessTMeasure d_process_tMeasure)
    {
        List<DProcessTMeasure> list = leaveTaskService.selectProcessTypeListByValidFlag(d_process_tMeasure);
        return getDataTable(list);
    }

    @PostMapping("/getDirectSupplyPlans")
    public TableDataInfo getDirectSupplyPlans(@RequestBody LeavePlan leavePlan)
    {
        List<LeavePlan> list = leavePlanService.selectLeavePlanList(leavePlan);
        return getDataTable(list);
    }

    @Log(title = "出门证任务", businessType = BusinessType.INSERT)
    @PostMapping("/handleUnload")
    public AjaxResult handleUnload(@RequestBody HandleUnloadVo handleUnloadVo)
    {
        //获取当前用户
        String currentUserName = SecurityUtils.getUsername();
        SysUser currentUser = sysUserMapper.selectUserByUserName(currentUserName);

        int row = 1;
        LeaveTask leaveTask = handleUnloadVo.getLeaveTask();

        leaveTask.setUnloadingWorkNo(SecurityUtils.getUsername());
        leaveTask.setUnloadingTime(DateUtils.getNowDate());

        LeaveLog leaveLog = new LeaveLog();
        leaveLog.setLogType(2);
        leaveLog.setTaskNo(leaveTask.getTaskNo());
        leaveLog.setApplyNo(leaveTask.getApplyNo());
        leaveLog.setInfo("卸货人\""+ currentUser.getNickName()+ "\"确认入库");
        leaveLog.setCreateBy(SecurityUtils.getUsername());
        int i0 = leaveLogService.insertLeaveLog(leaveLog);
        if (i0 <= 0) {
            row = -1;
            return toAjax(row);
        }
        //初始传的是跨区调拨的applyNo
        String directSupplyApplyNo1 = leaveTask.getDirectSupplyTaskNo();
        String directSupplyPlanNo = null;
        LeavePlan leavePlan1 = new LeavePlan();

        String directSupplyTaskNo0 = null;

        Integer isDirectSupply = leaveTask.getIsDirectSupply();
        if (isDirectSupply == 1) {
            //查询直供计划号
            leavePlan1.setApplyNo(directSupplyApplyNo1);
            List<LeavePlan> leavePlans = leavePlanService.selectLeavePlanList(leavePlan1);
            if (leavePlans.size() == 1 && leavePlans.get(0) != null) {
                directSupplyPlanNo = leavePlans.get(0).getPlanNo();
            } else {
                log.error("查询直供计划号失败");
            }

            String directSupplyTaskNo = leaveTask.getTaskNo();

            LeaveTask directSupplyTask = handleUnloadVo.getDirectSupplyTask();

            directSupplyTask.setDirectSupplyTaskNo(directSupplyTaskNo);

            String applyNo = directSupplyTask.getApplyNo();

            leavePlanService.updateIsSendCar(applyNo);

            LeavePlanMaterial leavePlanMaterial = new LeavePlanMaterial();
            leavePlanMaterial.setApplyNo(applyNo);
            List<LeavePlanMaterial> leavePlanMaterials = leavePlanMaterialService.selectLeavePlanMaterialList(leavePlanMaterial);

            String snowId = leaveTaskService.insertLeaveTaskAndMaterial(directSupplyTask);

            directSupplyTaskNo0 = snowId;

            leaveTask.setDirectSupplyTaskNo(snowId);

            for (LeavePlanMaterial planMaterial : leavePlanMaterials) {
                LeaveTaskMaterial leaveTaskMaterial = new LeaveTaskMaterial();

                leaveTaskMaterial.setTaskNo(snowId);
                leaveTaskMaterial.setMaterialId(planMaterial.getMaterialId());
                leaveTaskMaterial.setMaterialNo(planMaterial.getMaterialNo());
                leaveTaskMaterial.setMaterialType(planMaterial.getMaterialType());
                leaveTaskMaterial.setMaterialName(planMaterial.getMaterialName());
                leaveTaskMaterial.setMaterialSpec(planMaterial.getMaterialSpec());
                leaveTaskMaterial.setMeasureUnit(planMaterial.getMeasureUnit());
                leaveTaskMaterial.setMeasureFlag(planMaterial.getMeasureFlag());
                leaveTaskMaterial.setPlanNum(planMaterial.getPlanNum());
                int i1 = leaveTaskMaterialService.insertLeaveTaskMaterial(leaveTaskMaterial);
                if (i1 <= 0) {
                    row = -1;
                    return toAjax(row);
                }
            }

            LeaveLog directSupplyTaskLog = new LeaveLog();
            directSupplyTaskLog.setLogType(2);
            directSupplyTaskLog.setTaskNo(snowId);
            directSupplyTaskLog.setApplyNo(directSupplyTask.getApplyNo());
            directSupplyTaskLog.setInfo("派车任务创建：" + directSupplyTask.getCarNum() + " " + directSupplyTask.getDriverName());
            directSupplyTaskLog.setCreateBy(SecurityUtils.getUsername());
            int i2 = leaveLogService.insertLeaveLog(directSupplyTaskLog);
            if (i2 <= 0) {
                row = -1;
                return toAjax(row);
            }

            leaveTask.setDirectSupplyTaskNo(directSupplyTaskNo0);
            int i = leaveTaskService.updateLeaveTask(leaveTask);
            if (i <= 0) {
                row = -1;
                return toAjax(row);
            }
        }


        int i = leaveTaskService.updateLeaveTask(leaveTask);
        if (i <= 0) {
            row = -1;
            return toAjax(row);
        }

        Date nowDate = DateUtils.getNowDate();
        LeavePlan leavePlan = handleUnloadVo.getLeavePlan();
        LeaveTaskMaterial leaveTaskMaterial = handleUnloadVo.getLeaveTaskMaterial();

        //计量系统交互
        if (leavePlan.getPlanType() == 2 && leaveTask.getTaskType() == 2) {
            storeinWwjgService.handleExternalProcessingStockIn(nowDate, leaveTask, leavePlan, leaveTaskMaterial,directSupplyPlanNo);
        }

        if (leavePlan.getPlanType() == 3) {
            storeinKqdbService.handleKqdbStockIn(nowDate, leaveTask, leavePlan, leaveTaskMaterial,directSupplyPlanNo);
        }
        return toAjax(row);
    }

    @Log(title = "出门证任务", businessType = BusinessType.INSERT)
    @PostMapping("/handleStockOut")
    public AjaxResult handleStockOut(@RequestBody HandleStockOutVo handleStockOutVo)
    {
        //获取当前用户
        String currentUserName = SecurityUtils.getUsername();
        SysUser currentUser = sysUserMapper.selectUserByUserName(currentUserName);


        int row = 1;

        Date nowDate = DateUtils.getNowDate();

        String username = SecurityUtils.getUsername();
        LeaveTask leaveTask = handleStockOutVo.getLeaveTask();
        leaveTask.setLoadingWorkNo(username);
        leaveTask.setLoadingTime(nowDate);
        int i = leaveTaskService.updateLeaveTask(leaveTask);
        if (i <= 0) {
            row = -1;
            return toAjax(row);
        }

        LeavePlan leavePlan = handleStockOutVo.getLeavePlan();
        LeaveTaskMaterial leaveTaskMaterial = handleStockOutVo.getLeaveTaskMaterial();
        Integer planType = leavePlan.getPlanType();

        LeaveLog leaveLog = new LeaveLog();
        leaveLog.setLogType(2);
        leaveLog.setTaskNo(leaveTask.getTaskNo());
        leaveLog.setApplyNo(leaveTask.getApplyNo());
        leaveLog.setInfo("确认出库，" + "装货人：" + currentUser.getNickName() + "，物资：" + leaveTaskMaterial.getMaterialName());
        leaveLog.setCreateBy(SecurityUtils.getUsername());
        int i0 = leaveLogService.insertLeaveLog(leaveLog);
        if (i0 <= 0) {
            row = -1;
            return toAjax(row);
        }

        //计量系统交互
        if (planType == 1) {
            storeoutBfhCkService.handleunReturnStockOut(nowDate, leaveTask, leavePlan, leaveTaskMaterial);
        }
        if (planType == 2) {
            storeoutWwjgService.handleExternalProcessingStockOut(nowDate, leaveTask, leavePlan, leaveTaskMaterial);
        }
        if (planType == 3) {
            storeoutKqdbService.handleKqdbStockOut(nowDate, leaveTask, leavePlan, leaveTaskMaterial);
        }

        return toAjax(row);
    }

    /**
     * 不返回出厂收货更改计量系统
     */
    @Log(title = "出门证任务", businessType = BusinessType.INSERT)
    @PostMapping("/unReturnStockOut")
    public AjaxResult UnReturnStockOut(@RequestBody UnReturnStockOutDTO unReturnStockOutDTO)
    {
        int row = 1;

        StoreoutBfhCkMeasure storeoutBfhCkMeasure = unReturnStockOutDTO.getStoreoutBfhCkMeasure();

        //不返回出库表 插入
        int i = storeoutBfhCkService.insertStoreoutBfhCk(storeoutBfhCkMeasure);

        //计量临时表 更新

        if (i <= 0) {
            row = -1;
            return toAjax(row);
        }

        DicMeasure dicMeasure = unReturnStockOutDTO.getDicMeasure();

        return null;
    }
}
