{"remainingRequest": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\src\\views\\leave\\permission\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\src\\views\\leave\\permission\\index.vue", "mtime": 1755499098416}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgew0KICBsaXN0RGVwdCwNCiAgbGlzdFJvbGUsDQogIGxpc3RBbGxVc2VycywNCiAgbGlzdEhhc1Blcm1pdFVzZXJzLA0KICBnZXRJbmZvLA0KICB1cGRhdGVJbmZvLA0KICBkZWxldGVJbmZvLA0KfSBmcm9tICJAL2FwaS9sZWF2ZS9wZXJtaXQiOw0KDQpleHBvcnQgZGVmYXVsdCB7DQogIG5hbWU6ICJleGl0UGVybWl0Q29uZmlnIiwNCiAgY29tcG9uZW50czoge30sDQogIGRhdGEoKSB7DQogICAgcmV0dXJuIHsNCiAgICAgIHNob3dTZWFyY2g6IHRydWUsDQogICAgICBkZXB0TGlzdDogW10sDQogICAgICByb2xlT3B0aW9uczogW10sDQogICAgICB1c2VyTGlzdDogW10sDQogICAgICBoYXNQZXJtaXRVc2VyTGlzdDogW10sDQogICAgICAvLyDmgLvmnaHmlbANCiAgICAgIHRvdGFsOiAwLA0KICAgICAgbG9hZGluZzogZmFsc2UsDQogICAgICAvLyDlt7LliIbphY0NCiAgICAgIHRvdGFsUGVybWl0OiAwLA0KICAgICAgbG9hZGluZ1Blcm1pdDogZmFsc2UsDQogICAgICAvLyDmn6Xor6Llj4LmlbANCiAgICAgIHF1ZXJ5UGFyYW1zOiB7DQogICAgICAgIHVzZXJOYW1lOiBudWxsLA0KICAgICAgICBuaWNrTmFtZTogbnVsbCwNCiAgICAgICAgcGFnZU51bTogMSwNCiAgICAgICAgcGFnZVNpemU6IDEwLA0KICAgICAgfSwNCiAgICAgIHVzZXJRdWVyeVBhcmFtczogew0KICAgICAgICB1c2VyTmFtZTogbnVsbCwNCiAgICAgICAgbmlja05hbWU6IG51bGwsDQogICAgICAgIGRlcHRJZDogbnVsbCwNCiAgICAgICAgcGFnZU51bTogMSwNCiAgICAgICAgcGFnZVNpemU6IDEwLA0KICAgICAgfSwNCiAgICAgIGZvcm06IHt9LA0KICAgICAgLy8g5by55Ye65bGC5qCH6aKYDQogICAgICB0aXRsZTogIiIsDQogICAgICAvLyDmmK/lkKbmmL7npLrlvLnlh7rlsYINCiAgICAgIG9wZW46IGZhbHNlLA0KICAgICAgcnVsZXM6IHsNCiAgICAgICAgZGVwdElkOiBbeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogIuivt+mAieaLqemDqOmXqCIsIHRyaWdnZXI6ICJjaGFuZ2UiIH1dLA0KICAgICAgfSwNCiAgICB9Ow0KICB9LA0KICBjcmVhdGVkKCkgew0KICAgIC8v5Yid5aeL5YyWDQogICAgdGhpcy5pbml0KCk7DQogIH0sDQogIG1ldGhvZHM6IHsNCiAgICAvL+WIneWni+WMlg0KICAgIGluaXQoKSB7DQogICAgICB0aGlzLmdldEhhc1Blcm1pdFVzZXJzKCk7DQogICAgICB0aGlzLmdldFVzZXJMaXN0KCk7DQogICAgICB0aGlzLmdldERlcHRMaXN0KCk7DQogICAgICB0aGlzLmdldFJvbGVMaXN0KCk7DQogICAgfSwNCiAgICAvL+mDqOmXqOWIl+ihqA0KICAgIGdldERlcHRMaXN0KCkgew0KICAgICAgbGlzdERlcHQoKS50aGVuKChyZXMpID0+IHsNCiAgICAgICAgdGhpcy5kZXB0TGlzdCA9IHJlcy5kYXRhOw0KICAgICAgfSk7DQogICAgfSwNCiAgICAvL+inkuiJsuWIl+ihqA0KICAgIGdldFJvbGVMaXN0KCkgew0KICAgICAgbGlzdFJvbGUoKS50aGVuKChyZXMpID0+IHsNCiAgICAgICAgLy8gbGV0IG9wdGlvbnMgPSByZXMuZGF0YTsNCiAgICAgICAgLy8gdGhpcy5yb2xlT3B0aW9ucyA9IG9wdGlvbnMuZmlsdGVyKA0KICAgICAgICAvLyAgICh4KSA9Pg0KICAgICAgICAvLyAgICAgeC5jb2RlICE9ICJsZWF2ZS5jZW50ZXJBcHByb3ZlciIgJiYNCiAgICAgICAgLy8gICAgIHguY29kZSAhPSAibGVhdmUuZ3VhcmQiICYmDQogICAgICAgIC8vICAgICB4LmNvZGUgIT0gImxlYXZlLnByb3ZpZGVyIg0KICAgICAgICAvLyApOw0KICAgICAgICB0aGlzLnJvbGVPcHRpb25zID0gcmVzLmRhdGENCiAgICAgIH0pOw0KICAgIH0sDQogICAgLy/lvoXliIbphY3nlKjmiLfliJfooagNCiAgICBnZXRVc2VyTGlzdCgpIHsNCiAgICAgIHRoaXMubG9hZGluZyA9IHRydWU7DQogICAgICBsaXN0QWxsVXNlcnModGhpcy5xdWVyeVBhcmFtcykudGhlbigocmVzcG9uc2UpID0+IHsNCiAgICAgICAgdGhpcy51c2VyTGlzdCA9IHJlc3BvbnNlLnJvd3M7DQogICAgICAgIHRoaXMudG90YWwgPSByZXNwb25zZS50b3RhbDsNCiAgICAgICAgdGhpcy5sb2FkaW5nID0gZmFsc2U7DQogICAgICB9KTsNCiAgICB9LA0KICAgIC8v5bey5YiG6YWN55So5oi35YiX6KGoDQogICAgZ2V0SGFzUGVybWl0VXNlcnMoKSB7DQogICAgICB0aGlzLmxvYWRpbmdQZXJtaXQgPSB0cnVlOw0KICAgICAgbGlzdEhhc1Blcm1pdFVzZXJzKHRoaXMudXNlclF1ZXJ5UGFyYW1zKS50aGVuKChyZXMpID0+IHsNCiAgICAgICAgdGhpcy5oYXNQZXJtaXRVc2VyTGlzdCA9IHJlcy5yb3dzOw0KICAgICAgICB0aGlzLnRvdGFsUGVybWl0ID0gcmVzLnRvdGFsOw0KICAgICAgICB0aGlzLmxvYWRpbmdQZXJtaXQgPSBmYWxzZTsNCiAgICAgIH0pOw0KICAgIH0sDQogICAgLy/mkJzntKLmjInpkq7mk43kvZwNCiAgICBoYW5kbGVRdWVyeSgpIHsNCiAgICAgIHRoaXMucXVlcnlQYXJhbXMucGFnZU51bSA9IDE7DQogICAgICB0aGlzLmdldFVzZXJMaXN0KCk7DQogICAgfSwNCiAgICAvL+mHjee9ruaMiemSruaTjeS9nA0KICAgIHJlc2V0UXVlcnkoKSB7DQogICAgICB0aGlzLnJlc2V0Rm9ybSgicXVlcnlGb3JtIik7DQogICAgICB0aGlzLmhhbmRsZVF1ZXJ5KCk7DQogICAgfSwNCiAgICAvL+aQnOe0ouaMiemSruaTjeS9nA0KICAgIGhhbmRsZVF1ZXJ5U2VjKCkgew0KICAgICAgdGhpcy51c2VyUXVlcnlQYXJhbXMucGFnZU51bSA9IDE7DQogICAgICB0aGlzLmdldEhhc1Blcm1pdFVzZXJzKCk7DQogICAgfSwNCiAgICAvL+mHjee9ruaMiemSruaTjeS9nA0KICAgIHJlc2V0UXVlcnlTZWMoKSB7DQogICAgICB0aGlzLnJlc2V0Rm9ybSgidXNlclF1ZXJ5Rm9ybSIpOw0KICAgICAgdGhpcy5oYW5kbGVRdWVyeVNlYygpOw0KICAgIH0sDQogICAgLy8g6KGo5Y2V6YeN572uDQogICAgcmVzZXQoKSB7DQogICAgICB0aGlzLmZvcm0gPSB7DQogICAgICAgIHVzZXJJZDogbnVsbCwNCiAgICAgICAgdXNlck5hbWU6IG51bGwsDQogICAgICAgIG5pY2tOYW1lOiBudWxsLA0KICAgICAgICBkZXB0SWQ6IG51bGwsDQogICAgICAgIHJvbGVLZXlzOiBbXSwNCiAgICAgIH07DQogICAgICB0aGlzLnJlc2V0Rm9ybSgiZm9ybSIpOw0KICAgIH0sDQogICAgLy/nvJbovpHnlKjmiLcNCiAgICBoYW5kbGVVcGRhdGUocm93KSB7DQogICAgICB0aGlzLnJlc2V0KCk7DQogICAgICBjb25zdCB1c2VySWQgPSByb3cudXNlcklkOw0KICAgICAgZ2V0SW5mbyh7IHVzZXJJZDogdXNlcklkIH0pLnRoZW4oKHJlcykgPT4gew0KICAgICAgICB0aGlzLm9wZW4gPSB0cnVlOw0KICAgICAgICB0aGlzLmZvcm0udXNlcklkID0gdXNlcklkOw0KICAgICAgICB0aGlzLmZvcm0ubmlja05hbWUgPSByb3cubmlja05hbWU7DQogICAgICAgIGlmIChyZXMuZGF0YSAhPSB1bmRlZmluZWQpIHsNCiAgICAgICAgICB0aGlzLmZvcm0uZGVwdElkID0gcmVzLmRhdGEuZGVwdElkOw0KICAgICAgICAgIHRoaXMuZm9ybS5yb2xlS2V5cyA9IHJlcy5kYXRhLnJvbGVLZXlzOw0KICAgICAgICB9DQogICAgICB9KTsNCiAgICB9LA0KICAgIC8v5Yig6Zmk55So5oi3DQogICAgaGFuZGxlRGVsZXRlKHJvdykgew0KICAgICAgY29uc3QgdXNlcklkID0gcm93LnVzZXJJZDsNCiAgICAgIHRoaXMuJG1vZGFsDQogICAgICAgIC5jb25maXJtKCfmmK/lkKbliKDpmaQiJyArIHJvdy5uaWNrTmFtZSArICci5p2D6ZmQ77yfJykNCiAgICAgICAgLnRoZW4oKCkgPT4gew0KICAgICAgICAgIGRlbGV0ZUluZm8oeyB1c2VySWQ6IHVzZXJJZCB9KTsNCiAgICAgICAgfSkNCiAgICAgICAgLnRoZW4oKCkgPT4gew0KICAgICAgICAgIHRoaXMuJG1vZGFsLm1zZ1N1Y2Nlc3MoIuWIoOmZpOaIkOWKnyIpOw0KICAgICAgICAgIHRoaXMuaGFuZGxlUXVlcnlTZWMoKTsNCiAgICAgICAgfSkNCiAgICAgICAgLmNhdGNoKCgpID0+IHt9KTsNCiAgICB9LA0KICAgIC8v5o+Q5Lqk6KGo5Y2VDQogICAgc3VibWl0Rm9ybSgpIHsNCiAgICAgIHRoaXMuJHJlZnNbImZvcm0iXS52YWxpZGF0ZSgodmFsaWQpID0+IHsNCiAgICAgICAgaWYgKHZhbGlkKSB7DQogICAgICAgICAgdXBkYXRlSW5mbyh0aGlzLmZvcm0pLnRoZW4oKHJlc3BvbnNlKSA9PiB7DQogICAgICAgICAgICB0aGlzLiRtb2RhbC5tc2dTdWNjZXNzKCLkv67mlLnmiJDlip8iKTsNCiAgICAgICAgICAgIHRoaXMub3BlbiA9IGZhbHNlOw0KICAgICAgICAgICAgdGhpcy5oYW5kbGVRdWVyeVNlYygpOw0KICAgICAgICAgIH0pOw0KICAgICAgICB9DQogICAgICB9KTsNCiAgICB9LA0KICAgIGNhbmNlbCgpIHsNCiAgICAgIHRoaXMub3BlbiA9IGZhbHNlOw0KICAgICAgdGhpcy5yZXNldCgpOw0KICAgIH0sDQogICAgdGFibGVSb3dDbGFzc05hbWUoeyByb3csIHJvd0luZGV4IH0pIHsNCiAgICAgIGlmIChyb3cuc3RhdHVzID09ICIxIikgew0KICAgICAgICByZXR1cm4gIndhcm5pbmctcm93IjsNCiAgICAgIH0NCiAgICAgIHJldHVybiAiIjsNCiAgICB9LA0KICB9LA0KfTsNCg=="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAqQA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/leave/permission", "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <div class=\"card-title\">\r\n      <el-tag>全体用户</el-tag>\r\n    </div>\r\n    <el-card>\r\n      <!--用户数据-->\r\n      <el-form\r\n        :model=\"queryParams\"\r\n        ref=\"queryForm\"\r\n        :inline=\"true\"\r\n        v-show=\"showSearch\"\r\n        label-width=\"auto\"\r\n      >\r\n        <el-form-item label=\"用户账号\" prop=\"userName\">\r\n          <el-input\r\n            v-model=\"queryParams.userName\"\r\n            placeholder=\"请输入用户账号\"\r\n            clearable\r\n            size=\"small\"\r\n            style=\"width: 240px\"\r\n            @keyup.enter.native=\"handleQuery\"\r\n          />\r\n        </el-form-item>\r\n        <el-form-item label=\"用户姓名\" prop=\"nickName\">\r\n          <el-input\r\n            v-model=\"queryParams.nickName\"\r\n            placeholder=\"请输入用户姓名\"\r\n            clearable\r\n            size=\"small\"\r\n            style=\"width: 240px\"\r\n            @keyup.enter.native=\"handleQuery\"\r\n          />\r\n        </el-form-item>\r\n        <el-form-item>\r\n          <el-button\r\n            type=\"cyan\"\r\n            icon=\"el-icon-search\"\r\n            size=\"mini\"\r\n            @click=\"handleQuery\"\r\n            >搜索</el-button\r\n          >\r\n          <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\"\r\n            >重置</el-button\r\n          >\r\n        </el-form-item>\r\n      </el-form>\r\n      <el-table\r\n        v-loading=\"loading\"\r\n        :data=\"userList\"\r\n        :row-class-name=\"tableRowClassName\"\r\n      >\r\n        <el-table-column\r\n          label=\"用户账号\"\r\n          align=\"center\"\r\n          prop=\"userName\"\r\n          :show-overflow-tooltip=\"true\"\r\n        />\r\n        <el-table-column\r\n          label=\"用户姓名\"\r\n          align=\"center\"\r\n          prop=\"nickName\"\r\n          :show-overflow-tooltip=\"true\"\r\n        />\r\n        <el-table-column\r\n          label=\"操作\"\r\n          align=\"center\"\r\n          class-name=\"small-padding fixed-width\"\r\n        >\r\n          <template slot-scope=\"scope\">\r\n            <el-button\r\n              type=\"primary\"\r\n              icon=\"el-icon-edit\"\r\n              @click=\"handleUpdate(scope.row)\"\r\n              >修改</el-button\r\n            >\r\n          </template>\r\n        </el-table-column>\r\n      </el-table>\r\n\r\n      <pagination\r\n        v-show=\"total > 0\"\r\n        :total=\"total\"\r\n        :page.sync=\"queryParams.pageNum\"\r\n        :limit.sync=\"queryParams.pageSize\"\r\n        @pagination=\"getUserList\"\r\n      />\r\n    </el-card>\r\n    <div class=\"card-title-Sec\">\r\n      <el-tag type=\"success\">已分配用户</el-tag>\r\n    </div>\r\n    <el-card>\r\n      <!--已分配用户数据-->\r\n      <el-form\r\n        :model=\"userQueryParams\"\r\n        ref=\"userQueryForm\"\r\n        :inline=\"true\"\r\n        v-show=\"showSearch\"\r\n        label-width=\"auto\"\r\n      >\r\n        <el-form-item label=\"用户账号\" prop=\"userName\">\r\n          <el-input\r\n            v-model=\"userQueryParams.userName\"\r\n            placeholder=\"请输入用户账号\"\r\n            clearable\r\n            size=\"small\"\r\n            style=\"width: 240px\"\r\n            @keyup.enter.native=\"handleQuerySec\"\r\n          />\r\n        </el-form-item>\r\n        <el-form-item label=\"用户姓名\" prop=\"nickName\">\r\n          <el-input\r\n            v-model=\"userQueryParams.nickName\"\r\n            placeholder=\"请输入用户姓名\"\r\n            clearable\r\n            size=\"small\"\r\n            style=\"width: 240px\"\r\n            @keyup.enter.native=\"handleQuerySec\"\r\n          />\r\n        </el-form-item>\r\n        <el-form-item label=\"部门\" prop=\"deptId\">\r\n          <el-select\r\n            v-model=\"userQueryParams.deptId\"\r\n            style=\"width: 200px\"\r\n            size=\"small\"\r\n            placeholder=\"请选择部门\"\r\n            clearable\r\n            filterable\r\n          >\r\n            <el-option\r\n              v-for=\"item in deptList\"\r\n              :key=\"item.id\"\r\n              :label=\"item.storeName\"\r\n              :value=\"item.id\"\r\n            >\r\n              <span style=\"float: left\">{{ item.storeName }}</span>\r\n            </el-option>\r\n          </el-select>\r\n        </el-form-item>\r\n        <el-form-item>\r\n          <el-button\r\n            type=\"cyan\"\r\n            icon=\"el-icon-search\"\r\n            size=\"mini\"\r\n            @click=\"handleQuerySec\"\r\n            >搜索</el-button\r\n          >\r\n          <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuerySec\"\r\n            >重置</el-button\r\n          >\r\n        </el-form-item>\r\n      </el-form>\r\n      <el-table\r\n        v-loading=\"loadingPermit\"\r\n        :data=\"hasPermitUserList\"\r\n        :row-class-name=\"tableRowClassName\"\r\n      >\r\n        <el-table-column\r\n          label=\"用户账号\"\r\n          align=\"center\"\r\n          prop=\"userName\"\r\n          width=\"150px\"\r\n          :show-overflow-tooltip=\"true\"\r\n        />\r\n        <el-table-column\r\n          label=\"用户姓名\"\r\n          align=\"center\"\r\n          prop=\"nickName\"\r\n          width=\"150px\"\r\n          :show-overflow-tooltip=\"true\"\r\n        />\r\n        <el-table-column\r\n          label=\"部门\"\r\n          align=\"center\"\r\n          prop=\"deptName\"\r\n          width=\"200px\"\r\n          :show-overflow-tooltip=\"true\"\r\n        />\r\n        <el-table-column\r\n          label=\"权限\"\r\n          align=\"center\"\r\n          prop=\"roleNamesDesc\"\r\n          :show-overflow-tooltip=\"true\"\r\n        />\r\n        <el-table-column\r\n          label=\"操作\"\r\n          align=\"center\"\r\n          class-name=\"small-padding fixed-width\"\r\n        >\r\n          <template slot-scope=\"scope\">\r\n            <el-button\r\n              type=\"danger\"\r\n              icon=\"el-icon-delete\"\r\n              @click=\"handleDelete(scope.row)\"\r\n              >删除</el-button\r\n            >\r\n          </template>\r\n        </el-table-column>\r\n      </el-table>\r\n\r\n      <pagination\r\n        v-show=\"totalPermit > 0\"\r\n        :total=\"totalPermit\"\r\n        :page.sync=\"userQueryParams.pageNum\"\r\n        :limit.sync=\"userQueryParams.pageSize\"\r\n        @pagination=\"getHasPermitUsers\"\r\n      />\r\n    </el-card>\r\n\r\n    <!-- 添加或修改参数配置对话框 -->\r\n    <el-dialog :title=\"title\" :visible.sync=\"open\" width=\"600px\" append-to-body>\r\n      <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" label-width=\"80px\">\r\n        <el-row>\r\n          <el-form-item label=\"用户姓名\">\r\n            <el-input v-model=\"form.nickName\" readonly />\r\n          </el-form-item>\r\n        </el-row>\r\n        <el-row>\r\n          <el-form-item label=\"部门\" prop=\"deptId\">\r\n            <el-select\r\n              v-model=\"form.deptId\"\r\n              placeholder=\"请选择部门\"\r\n              filterable\r\n            >\r\n              <el-option\r\n                v-for=\"item in deptList\"\r\n                :key=\"item.id\"\r\n                :label=\"item.storeName\"\r\n                :value=\"item.id\"\r\n              ></el-option>\r\n            </el-select>\r\n          </el-form-item>\r\n        </el-row>\r\n        <el-row>\r\n          <el-form-item label=\"角色\" prop=\"roleId\">\r\n            <el-select\r\n              v-model=\"form.roleKeys\"\r\n              multiple\r\n              placeholder=\"请选择用户权限\"\r\n              filterable\r\n            >\r\n              <el-option\r\n                v-for=\"item in roleOptions\"\r\n                :key=\"item.index\"\r\n                :label=\"item.value\"\r\n                :value=\"item.code\"\r\n              ></el-option>\r\n            </el-select>\r\n          </el-form-item>\r\n        </el-row>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button type=\"primary\" @click=\"submitForm\">确 定</el-button>\r\n        <el-button @click=\"cancel\">取 消</el-button>\r\n      </div>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n\r\n<script>\r\nimport {\r\n  listDept,\r\n  listRole,\r\n  listAllUsers,\r\n  listHasPermitUsers,\r\n  getInfo,\r\n  updateInfo,\r\n  deleteInfo,\r\n} from \"@/api/leave/permit\";\r\n\r\nexport default {\r\n  name: \"exitPermitConfig\",\r\n  components: {},\r\n  data() {\r\n    return {\r\n      showSearch: true,\r\n      deptList: [],\r\n      roleOptions: [],\r\n      userList: [],\r\n      hasPermitUserList: [],\r\n      // 总条数\r\n      total: 0,\r\n      loading: false,\r\n      // 已分配\r\n      totalPermit: 0,\r\n      loadingPermit: false,\r\n      // 查询参数\r\n      queryParams: {\r\n        userName: null,\r\n        nickName: null,\r\n        pageNum: 1,\r\n        pageSize: 10,\r\n      },\r\n      userQueryParams: {\r\n        userName: null,\r\n        nickName: null,\r\n        deptId: null,\r\n        pageNum: 1,\r\n        pageSize: 10,\r\n      },\r\n      form: {},\r\n      // 弹出层标题\r\n      title: \"\",\r\n      // 是否显示弹出层\r\n      open: false,\r\n      rules: {\r\n        deptId: [{ required: true, message: \"请选择部门\", trigger: \"change\" }],\r\n      },\r\n    };\r\n  },\r\n  created() {\r\n    //初始化\r\n    this.init();\r\n  },\r\n  methods: {\r\n    //初始化\r\n    init() {\r\n      this.getHasPermitUsers();\r\n      this.getUserList();\r\n      this.getDeptList();\r\n      this.getRoleList();\r\n    },\r\n    //部门列表\r\n    getDeptList() {\r\n      listDept().then((res) => {\r\n        this.deptList = res.data;\r\n      });\r\n    },\r\n    //角色列表\r\n    getRoleList() {\r\n      listRole().then((res) => {\r\n        // let options = res.data;\r\n        // this.roleOptions = options.filter(\r\n        //   (x) =>\r\n        //     x.code != \"leave.centerApprover\" &&\r\n        //     x.code != \"leave.guard\" &&\r\n        //     x.code != \"leave.provider\"\r\n        // );\r\n        this.roleOptions = res.data\r\n      });\r\n    },\r\n    //待分配用户列表\r\n    getUserList() {\r\n      this.loading = true;\r\n      listAllUsers(this.queryParams).then((response) => {\r\n        this.userList = response.rows;\r\n        this.total = response.total;\r\n        this.loading = false;\r\n      });\r\n    },\r\n    //已分配用户列表\r\n    getHasPermitUsers() {\r\n      this.loadingPermit = true;\r\n      listHasPermitUsers(this.userQueryParams).then((res) => {\r\n        this.hasPermitUserList = res.rows;\r\n        this.totalPermit = res.total;\r\n        this.loadingPermit = false;\r\n      });\r\n    },\r\n    //搜索按钮操作\r\n    handleQuery() {\r\n      this.queryParams.pageNum = 1;\r\n      this.getUserList();\r\n    },\r\n    //重置按钮操作\r\n    resetQuery() {\r\n      this.resetForm(\"queryForm\");\r\n      this.handleQuery();\r\n    },\r\n    //搜索按钮操作\r\n    handleQuerySec() {\r\n      this.userQueryParams.pageNum = 1;\r\n      this.getHasPermitUsers();\r\n    },\r\n    //重置按钮操作\r\n    resetQuerySec() {\r\n      this.resetForm(\"userQueryForm\");\r\n      this.handleQuerySec();\r\n    },\r\n    // 表单重置\r\n    reset() {\r\n      this.form = {\r\n        userId: null,\r\n        userName: null,\r\n        nickName: null,\r\n        deptId: null,\r\n        roleKeys: [],\r\n      };\r\n      this.resetForm(\"form\");\r\n    },\r\n    //编辑用户\r\n    handleUpdate(row) {\r\n      this.reset();\r\n      const userId = row.userId;\r\n      getInfo({ userId: userId }).then((res) => {\r\n        this.open = true;\r\n        this.form.userId = userId;\r\n        this.form.nickName = row.nickName;\r\n        if (res.data != undefined) {\r\n          this.form.deptId = res.data.deptId;\r\n          this.form.roleKeys = res.data.roleKeys;\r\n        }\r\n      });\r\n    },\r\n    //删除用户\r\n    handleDelete(row) {\r\n      const userId = row.userId;\r\n      this.$modal\r\n        .confirm('是否删除\"' + row.nickName + '\"权限？')\r\n        .then(() => {\r\n          deleteInfo({ userId: userId });\r\n        })\r\n        .then(() => {\r\n          this.$modal.msgSuccess(\"删除成功\");\r\n          this.handleQuerySec();\r\n        })\r\n        .catch(() => {});\r\n    },\r\n    //提交表单\r\n    submitForm() {\r\n      this.$refs[\"form\"].validate((valid) => {\r\n        if (valid) {\r\n          updateInfo(this.form).then((response) => {\r\n            this.$modal.msgSuccess(\"修改成功\");\r\n            this.open = false;\r\n            this.handleQuerySec();\r\n          });\r\n        }\r\n      });\r\n    },\r\n    cancel() {\r\n      this.open = false;\r\n      this.reset();\r\n    },\r\n    tableRowClassName({ row, rowIndex }) {\r\n      if (row.status == \"1\") {\r\n        return \"warning-row\";\r\n      }\r\n      return \"\";\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n\r\n<style lang=\"scss\" scoped>\r\n.card-title {\r\n  margin-bottom: 10px;\r\n}\r\n.card-title-Sec {\r\n  margin-top: 10px;\r\n  margin-bottom: 10px;\r\n}\r\n.basic {\r\n  margin-top: 20px;\r\n  margin-bottom: 20px;\r\n}\r\n.card-footer {\r\n  display: flex;\r\n  justify-content: flex-end;\r\n}\r\n</style>"]}]}