{"remainingRequest": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\src\\views\\suppPunishment\\punishmentBasis-module.vue?vue&type=style&index=0&id=5e61e69e&scoped=true&lang=css", "dependencies": [{"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\src\\views\\suppPunishment\\punishmentBasis-module.vue", "mtime": 1755499162061}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\@vue\\cli-service\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["punishmentBasis-module.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAyTA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA", "file": "punishmentBasis-module.vue", "sourceRoot": "src/views/suppPunishment", "sourcesContent": ["<template>\r\n  <el-dialog\r\n    title=\"处罚依据选择\"\r\n    :visible.sync=\"visible\"\r\n    width=\"500px\"\r\n    top=\"5vh\"\r\n    append-to-body\r\n    @close=\"handleClose\"\r\n    :close-on-click-modal=\"false\"\r\n    custom-class=\"basis-dialog\"\r\n  >\r\n    <div class=\"basis-dialog-content\">\r\n      <!-- 处罚依据选项 -->\r\n      <div class=\"basis-options\">\r\n        <h4 class=\"section-title\"><span class=\"required-mark\">*</span>选择依据类型：</h4>\r\n        <el-radio-group v-model=\"selectedBasisType\" @change=\"handleBasisTypeChange\">\r\n          <!-- 质量异议单号 -->\r\n          <div class=\"basis-item\">\r\n            <div class=\"basis-row\">\r\n              <div class=\"radio-wrapper\">\r\n                <el-radio label=\"quality\" @change=\"handleBasisTypeChange\">质量异议单号</el-radio>\r\n              </div>\r\n              <div class=\"input-wrapper\">\r\n                <el-input\r\n                  v-model=\"qualityNumber\"\r\n                  placeholder=\"请输入质量异议单号\"\r\n                  class=\"aligned-input\"\r\n                  @input=\"updateBasisText\"\r\n                  @focus=\"checkQuality\"\r\n                />\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- 制度名称 -->\r\n          <div class=\"basis-item\">\r\n            <div class=\"basis-row\">\r\n              <div class=\"radio-wrapper\">\r\n                <el-radio label=\"system\" @change=\"handleBasisTypeChange\">制度名称</el-radio>\r\n              </div>\r\n              <div class=\"input-wrapper\">\r\n                <el-input\r\n                  v-model=\"systemName\"\r\n                  placeholder=\"请输入制度名称\"\r\n                  class=\"aligned-input\"\r\n                  @input=\"updateBasisText\"\r\n                  @focus=\"checkSystem\"\r\n                />\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- 报告 -->\r\n          <div class=\"basis-item\">\r\n            <div class=\"basis-row\">\r\n              <div class=\"radio-wrapper\">\r\n                <el-radio label=\"report\" @change=\"handleBasisTypeChange\">报告</el-radio>\r\n              </div>\r\n              <div class=\"input-wrapper\">\r\n                <el-input\r\n                  v-model=\"reportName\"\r\n                  placeholder=\"请输入文件报批单号\"\r\n                  class=\"aligned-input\"\r\n                  @input=\"updateBasisText\"\r\n                  @focus=\"checkReport\"\r\n                />\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </el-radio-group>\r\n      </div>\r\n\r\n      <!-- 依据内容 -->\r\n      <div class=\"basis-content\">\r\n        <h4 class=\"section-title\"><span class=\"required-mark\">*</span>依据内容：</h4>\r\n        <div class=\"content-wrapper\">\r\n          <el-input\r\n            v-model=\"basisContent\"\r\n            type=\"textarea\"\r\n            :rows=\"4\"\r\n            placeholder=\"请输入依据内容\"\r\n            @input=\"updateBasisText\"\r\n            class=\"content-textarea\"\r\n          />\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 预览区域 -->\r\n      <div class=\"preview-area\">\r\n        <h4 class=\"section-title\"> 预览结果：</h4>\r\n        <div class=\"preview-wrapper\">\r\n          <el-input\r\n            v-model=\"previewText\"\r\n            type=\"textarea\"\r\n            :rows=\"4\"\r\n            readonly\r\n            placeholder=\"选择处罚依据后将在此显示预览\"\r\n            class=\"preview-textarea\"\r\n          />\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 底部按钮 -->\r\n    <div slot=\"footer\" class=\"dialog-footer\">\r\n      <div class=\"footer-buttons\">\r\n        <el-button type=\"primary\" @click=\"handleConfirm\">确 定</el-button>\r\n        <el-button @click=\"handleClose\">取 消</el-button>\r\n      </div>\r\n    </div>\r\n  </el-dialog>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  name: \"PunishmentBasisDialog\",\r\n  data() {\r\n    return {\r\n      // 是否显示弹出层\r\n      visible: false,\r\n      // 选中的依据类型\r\n      selectedBasisType: '',\r\n      // 质量异议单号\r\n      qualityNumber: '',\r\n      // 制度名称\r\n      systemName: '',\r\n      // 报告名称\r\n      reportName: '',\r\n      // 依据内容\r\n      basisContent: '',\r\n      // 预览文本\r\n      previewText: ''\r\n    };\r\n  },\r\n  methods: {\r\n    /** 显示弹窗 */\r\n    show(currentValue = '') {\r\n      this.visible = true;\r\n      this.parseCurrentValue(currentValue);\r\n      this.updatePreview();\r\n      // 确保弹窗完全打开后再进行其他操作\r\n      this.$nextTick(() => {\r\n        console.log('弹窗已显示，当前数据：', {\r\n          selectedBasisType: this.selectedBasisType,\r\n          qualityNumber: this.qualityNumber,\r\n          systemName: this.systemName,\r\n          reportName: this.reportName,\r\n          basisContent: this.basisContent\r\n        });\r\n      });\r\n    },\r\n    \r\n    /** 隐藏弹窗 */\r\n    hide() {\r\n      this.visible = false;\r\n    },\r\n    \r\n    /** 关闭弹窗 */\r\n    handleClose() {\r\n      this.visible = false;\r\n      this.reset();\r\n    },\r\n    \r\n    /** 重置数据 */\r\n    reset() {\r\n      this.selectedBasisType = '';\r\n      this.qualityNumber = '';\r\n      this.systemName = '';\r\n      this.reportName = '';\r\n      this.basisContent = '';\r\n      this.previewText = '';\r\n    },\r\n    \r\n    /** 解析当前值 */\r\n    parseCurrentValue(value) {\r\n      if (!value) {\r\n        this.reset();\r\n        return;\r\n      }\r\n      \r\n      // 尝试解析现有的依据内容\r\n      if (value.includes('质量异议单号：')) {\r\n        this.selectedBasisType = 'quality';\r\n        const match = value.match(/质量异议单号：([^；\\n]*)/);\r\n        if (match) {\r\n          this.qualityNumber = match[1].trim();\r\n        }\r\n      } else if (value.includes('制度名称：')) {\r\n        this.selectedBasisType = 'system';\r\n        const match = value.match(/制度名称：([^；\\n]*)/);\r\n        if (match) {\r\n          this.systemName = match[1].trim();\r\n        }\r\n      } else if (value.includes('报告：')) {\r\n        this.selectedBasisType = 'report';\r\n        const match = value.match(/报告：([^；\\n]*)/);\r\n        if (match) {\r\n          this.reportName = match[1].trim();\r\n        }\r\n      }\r\n      \r\n      // 解析依据内容\r\n      const contentMatch = value.match(/依据内容：([^]*)/);\r\n      if (contentMatch) {\r\n        this.basisContent = contentMatch[1].trim();\r\n      } else {\r\n        // 如果没有找到依据内容标识，将整个内容作为依据内容\r\n        this.basisContent = value;\r\n      }\r\n    },\r\n    \r\n    /** 依据类型变化 */\r\n    handleBasisTypeChange() {\r\n      this.updatePreview();\r\n    },\r\n    \r\n    /** 更新依据文本 */\r\n    updateBasisText() {\r\n      this.updatePreview();\r\n    },\r\n\r\n    /** 点击质量异议单号输入框时自动选中 */\r\n    checkQuality() {\r\n      if (this.selectedBasisType !== 'quality') {\r\n        this.selectedBasisType = 'quality';\r\n        this.updatePreview();\r\n      }\r\n    },\r\n\r\n    /** 点击制度名称输入框时自动选中 */\r\n    checkSystem() {\r\n      if (this.selectedBasisType !== 'system') {\r\n        this.selectedBasisType = 'system';\r\n        this.updatePreview();\r\n      }\r\n    },\r\n\r\n    /** 点击报告输入框时自动选中 */\r\n    checkReport() {\r\n      if (this.selectedBasisType !== 'report') {\r\n        this.selectedBasisType = 'report';\r\n        this.updatePreview();\r\n      }\r\n    },\r\n    \r\n    /** 更新预览 */\r\n    updatePreview() {\r\n      const parts = [];\r\n\r\n      // 添加选中的依据类型信息\r\n      if (this.selectedBasisType === 'quality' && this.qualityNumber) {\r\n        parts.push(`质量异议单号：${this.qualityNumber}`);\r\n      } else if (this.selectedBasisType === 'system' && this.systemName) {\r\n        parts.push(`制度名称：${this.systemName}`);\r\n      } else if (this.selectedBasisType === 'report' && this.reportName) {\r\n        parts.push(`报告：${this.reportName}`);\r\n      }\r\n\r\n      // 添加依据内容\r\n      if (this.basisContent) {\r\n        parts.push(`依据内容：${this.basisContent}`);\r\n      }\r\n\r\n      this.previewText = parts.join('；');\r\n\r\n      console.log('预览更新：', {\r\n        selectedBasisType: this.selectedBasisType,\r\n        qualityNumber: this.qualityNumber,\r\n        systemName: this.systemName,\r\n        reportName: this.reportName,\r\n        basisContent: this.basisContent,\r\n        previewText: this.previewText\r\n      });\r\n    },\r\n    \r\n    /** 确认选择 */\r\n    handleConfirm() {\r\n      this.updatePreview();\r\n\r\n      // 验证是否填写了必要信息\r\n      if (!this.selectedBasisType) {\r\n        this.$message.warning('请选择依据类型');\r\n        return;\r\n      }\r\n\r\n      if (this.selectedBasisType === 'quality' && !this.qualityNumber) {\r\n        this.$message.warning('请输入质量异议单号');\r\n        return;\r\n      }\r\n\r\n      if (this.selectedBasisType === 'system' && !this.systemName) {\r\n        this.$message.warning('请输入制度名称');\r\n        return;\r\n      }\r\n\r\n      if (this.selectedBasisType === 'report' && !this.reportName) {\r\n        this.$message.warning('请输入文件报批单号');\r\n        return;\r\n      }\r\n\r\n      if (!this.basisContent) {\r\n        this.$message.warning('请输入依据内容');\r\n        return;\r\n      }\r\n\r\n      this.$emit('select', this.previewText);\r\n      this.handleClose();\r\n    }\r\n  }\r\n};\r\n</script>\r\n\r\n<style scoped>\r\n/* 弹窗内容容器 */\r\n.basis-dialog-content {\r\n  padding: 10px 0;\r\n}\r\n\r\n/* 章节标题样式 */\r\n.section-title {\r\n  font-size: 16px;\r\n  color: #303133;\r\n  margin: 0 0 15px 0;\r\n  font-weight: 600;\r\n}\r\n\r\n/* 顶级标题样式（选择依据类型） */\r\n.basis-options .section-title {\r\n  margin-bottom: 15px;\r\n}\r\n\r\n/* 处罚依据选项样式 */\r\n.basis-options {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.basis-item {\r\n  margin-bottom: 15px;\r\n  padding: 0;\r\n}\r\n\r\n/* 新的行布局 */\r\n.basis-row {\r\n  display: flex;\r\n  align-items: center;\r\n  width: 100%;\r\n  min-height: 36px;\r\n}\r\n\r\n.radio-wrapper {\r\n  width: 120px;\r\n  flex-shrink: 0;\r\n  display: flex;\r\n  align-items: center;\r\n  height: 36px;\r\n}\r\n\r\n.input-wrapper {\r\n  width: calc(100% - 135px);\r\n  margin-left: 14px;\r\n}\r\n\r\n.aligned-input {\r\n  width: 100%;\r\n}\r\n\r\n.aligned-input ::v-deep .el-input__inner {\r\n  height: 36px;\r\n  line-height: 36px;\r\n  border-radius: 4px;\r\n  font-size: 14px;\r\n}\r\n\r\n/* 单选框对齐样式 */\r\n.radio-wrapper ::v-deep .el-radio {\r\n  height: 36px;\r\n  display: flex;\r\n  align-items: center;\r\n  margin: 0;\r\n}\r\n\r\n.radio-wrapper ::v-deep .el-radio__input {\r\n  display: flex;\r\n  align-items: center;\r\n  margin-right: 8px;\r\n}\r\n\r\n.radio-wrapper ::v-deep .el-radio__inner {\r\n  width: 16px;\r\n  height: 16px;\r\n}\r\n\r\n.radio-wrapper ::v-deep .el-radio__label {\r\n  font-size: 14px;\r\n  line-height: 36px;\r\n  padding-left: 8px;\r\n  color: #606266;\r\n}\r\n\r\n/* 依据内容区域样式 */\r\n.basis-content {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.basis-content .section-title {\r\n  margin-bottom: 15px;\r\n}\r\n\r\n.content-wrapper {\r\n  padding: 0;\r\n}\r\n\r\n.content-textarea {\r\n  width: 100%;\r\n}\r\n\r\n.content-textarea ::v-deep .el-textarea__inner {\r\n  border-radius: 4px;\r\n  font-family: inherit;\r\n  font-size: 14px;\r\n  line-height: 1.5;\r\n  min-height: 120px;\r\n}\r\n\r\n/* 预览区域样式 */\r\n.preview-area {\r\n  margin-bottom: 0;\r\n}\r\n\r\n.preview-area .section-title {\r\n  margin-bottom: 15px;\r\n}\r\n\r\n.preview-wrapper {\r\n  padding: 0;\r\n}\r\n\r\n.preview-textarea {\r\n  width: 100%;\r\n}\r\n\r\n.preview-textarea ::v-deep .el-textarea__inner {\r\n  border-radius: 4px;\r\n  background-color: #ffffff;\r\n  font-family: inherit;\r\n  font-size: 14px;\r\n  line-height: 1.5;\r\n  min-height: 120px;\r\n}\r\n\r\n/* 弹窗标题居中 */\r\n::v-deep .el-dialog__header {\r\n  text-align: center;\r\n  background: #f8f9fa;\r\n  border-bottom: 1px solid #e9ecef;\r\n}\r\n\r\n::v-deep .el-dialog__title {\r\n  text-align: center;\r\n  width: 100%;\r\n  display: block;\r\n  font-weight: 600;\r\n  color: #303133;\r\n}\r\n\r\n/* 专门的弹窗样式 */\r\n::v-deep .basis-dialog {\r\n  margin-top: 5vh !important;\r\n}\r\n\r\n::v-deep .basis-dialog .el-dialog__body {\r\n  padding: 25px;\r\n  max-height: 75vh;\r\n  overflow-y: auto;\r\n  background-color: #ffffff;\r\n}\r\n\r\n::v-deep .basis-dialog .el-dialog__header {\r\n  padding: 20px 25px 15px;\r\n}\r\n\r\n::v-deep .basis-dialog .el-dialog__footer {\r\n  padding: 15px 25px 20px;\r\n  text-align: right;\r\n}\r\n\r\n/* 底部按钮样式 */\r\n.footer-buttons {\r\n  display: flex;\r\n  justify-content: flex-end;\r\n  gap: 10px;\r\n}\r\n\r\n/* 单选框组样式 */\r\n::v-deep .el-radio-group {\r\n  width: 100%;\r\n}\r\n\r\n::v-deep .el-radio {\r\n  margin-right: 0;\r\n  margin-bottom: 0;\r\n}\r\n\r\n::v-deep .el-radio__label {\r\n  display: flex;\r\n  align-items: center;\r\n  width: 100%;\r\n  font-size: 14px;\r\n  color: #606266;\r\n}\r\n\r\n::v-deep .el-radio__input.is-checked + .el-radio__label {\r\n  color: #409EFF;\r\n}\r\n\r\n/* 必填标识符样式 */\r\n.required-mark {\r\n  color: #F56C6C;\r\n  margin-right: 4px;\r\n  font-weight: bold;\r\n}\r\n</style>\r\n"]}]}