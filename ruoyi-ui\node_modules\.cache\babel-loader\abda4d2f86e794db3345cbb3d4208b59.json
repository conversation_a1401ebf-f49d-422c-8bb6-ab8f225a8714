{"remainingRequest": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\src\\views\\truck\\alloy\\detail.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\src\\views\\truck\\alloy\\detail.vue", "mtime": 1755499162067}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\babel.config.js", "mtime": 1688548084091}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_reservation", "require", "name", "data", "orderInfo", "logList", "driverImg", "photoTemp", "vehicleLicenseList", "vehicleLicenseListTemp", "driverLicenseList", "driverLicenseListTemp", "labelStyle", "width", "editStatusDialogVisible", "editStatusForm", "reservationNo", "status", "statusOptions", "value", "label", "created", "getDetail", "methods", "_this", "$route", "params", "getAlloyOrderDetail", "then", "response", "code", "matMgmtAlloyLogs", "driverFaceImg", "JSON", "parse", "_unused", "driverLicenseImgs", "_unused2", "drivingLicenseImg", "_unused3", "map", "el", "url", "formatTime", "time", "date", "Date", "isNaN", "getTime", "y", "getFullYear", "m", "getMonth", "toString", "padStart", "d", "getDate", "h", "getHours", "i", "getMinutes", "s", "getSeconds", "concat", "getBusinessDept", "val", "getEntranceGate", "getElectrodeDesc", "getStatusLabel", "statusMap", "getStatusTagType", "statusTypeMap", "openEditStatusDialog", "submitEditStatus", "_this2", "updateAlloyOrderStatus", "$message", "success", "error", "msg"], "sources": ["src/views/truck/alloy/detail.vue"], "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <h3>基本信息</h3>\r\n    <el-descriptions class=\"margin-top\" :column=\"2\" border :labelStyle=\"labelStyle\">\r\n      <el-descriptions-item>\r\n        <template slot=\"label\">\r\n          <i class=\"el-icon-document\"></i>\r\n          预约编号\r\n        </template>\r\n        {{ orderInfo.reservationNo }}\r\n      </el-descriptions-item>\r\n      <el-descriptions-item>\r\n        <template slot=\"label\">\r\n          <i class=\"el-icon-document\"></i>\r\n          业务部门\r\n        </template>\r\n        {{ getBusinessDept(orderInfo.approvalDept) }}\r\n      </el-descriptions-item>\r\n      <el-descriptions-item>\r\n        <template slot=\"label\">\r\n          <i class=\"el-icon-document\"></i>\r\n          供方业务员姓名\r\n        </template>\r\n        {{ orderInfo.supplierSalesName }}\r\n      </el-descriptions-item>\r\n      <el-descriptions-item>\r\n        <template slot=\"label\">\r\n          <i class=\"el-icon-document\"></i>\r\n          供方业务员手机号\r\n        </template>\r\n        {{ orderInfo.supplierSalesPhone }}\r\n      </el-descriptions-item>\r\n      <el-descriptions-item>\r\n        <template slot=\"label\">\r\n          <i class=\"el-icon-document\"></i>\r\n          申请单位名称\r\n        </template>\r\n        {{ orderInfo.applyCompanyName }}\r\n      </el-descriptions-item>\r\n      <el-descriptions-item>\r\n        <template slot=\"label\">\r\n          <i class=\"el-icon-document\"></i>\r\n          合金类型\r\n        </template>\r\n        {{ orderInfo.alloyType }}\r\n      </el-descriptions-item>\r\n      <el-descriptions-item>\r\n        <template slot=\"label\">\r\n          <i class=\"el-icon-document\"></i>\r\n          合金\r\n        </template>\r\n        {{ orderInfo.alloyLabel }}\r\n      </el-descriptions-item>\r\n      <el-descriptions-item>\r\n        <template slot=\"label\">\r\n          <i class=\"el-icon-document\"></i>\r\n          电极规格\r\n        </template>\r\n        {{ getElectrodeDesc(orderInfo.electrodeType) }}\r\n      </el-descriptions-item>\r\n      <el-descriptions-item>\r\n        <template slot=\"label\">\r\n          <i class=\"el-icon-document\"></i>\r\n          合金吨数\r\n        </template>\r\n        {{ orderInfo.estimatedWeight }}\r\n      </el-descriptions-item>\r\n      <el-descriptions-item>\r\n        <template slot=\"label\">\r\n          <i class=\"el-icon-document\"></i>\r\n          预计送货日期\r\n        </template>\r\n        {{ formatTime(orderInfo.expectedDeliveryTime) }}\r\n      </el-descriptions-item>\r\n      <el-descriptions-item>\r\n        <template slot=\"label\">\r\n          <i class=\"el-icon-time\"></i>\r\n          有效开始时间\r\n        </template>\r\n        {{ formatTime(orderInfo.effectiveStartTime) }}\r\n      </el-descriptions-item>\r\n      <el-descriptions-item>\r\n        <template slot=\"label\">\r\n          <i class=\"el-icon-time\"></i>\r\n          有效结束时间\r\n        </template>\r\n        {{ formatTime(orderInfo.effectiveEndTime) }}\r\n      </el-descriptions-item>\r\n      <el-descriptions-item>\r\n        <template slot=\"label\">\r\n          <i class=\"el-icon-document\"></i>\r\n          车牌号\r\n        </template>\r\n        {{ orderInfo.carNo }}\r\n      </el-descriptions-item>\r\n      <el-descriptions-item>\r\n        <template slot=\"label\">\r\n          <i class=\"el-icon-document\"></i>\r\n          司机名\r\n        </template>\r\n        {{ orderInfo.driverName }}\r\n      </el-descriptions-item>\r\n      <el-descriptions-item>\r\n        <template slot=\"label\">\r\n          <i class=\"el-icon-document\"></i>\r\n          司机身份证\r\n        </template>\r\n        {{ orderInfo.driverCardNo }}\r\n      </el-descriptions-item>\r\n      <el-descriptions-item>\r\n        <template slot=\"label\">\r\n          <i class=\"el-icon-document\"></i>\r\n          司机手机号\r\n        </template>\r\n        {{ orderInfo.driverMobile }}\r\n      </el-descriptions-item>\r\n      <el-descriptions-item>\r\n        <template slot=\"label\">\r\n          <i class=\"el-icon-document\"></i>\r\n          入厂大门\r\n        </template>\r\n        {{ getEntranceGate(orderInfo.enterDoor) }}\r\n      </el-descriptions-item>\r\n      <el-descriptions-item>\r\n        <template slot=\"label\">\r\n          <i class=\"el-icon-document\"></i>\r\n          状态\r\n        </template>\r\n        <el-tag :type=\"getStatusTagType(orderInfo.status)\" size=\"small\">\r\n          {{ getStatusLabel(orderInfo.status) }}\r\n        </el-tag>\r\n      </el-descriptions-item>\r\n      <el-descriptions-item>\r\n        <template slot=\"label\">\r\n          <i class=\"el-icon-document\"></i>\r\n          创建时间\r\n        </template>\r\n        {{ formatTime(orderInfo.createTime) }}\r\n      </el-descriptions-item>\r\n      <el-descriptions-item>\r\n        <template slot=\"label\"><i class=\"el-icon-truck\"></i> 驾驶证 </template>\r\n        <el-image v-for=\"(item, index) in driverLicenseListTemp\" :key=\"index\" style=\"width: 100px; height: 100px\"\r\n          :src=\"item.url\" fit=\"fit\" :preview-src-list=\"driverLicenseList\">\r\n        </el-image>\r\n      </el-descriptions-item>\r\n      <el-descriptions-item>\r\n        <template slot=\"label\"><i class=\"el-icon-truck\"></i> 行驶证 </template>\r\n        <el-image v-for=\"(item, index) in vehicleLicenseListTemp\" :key=\"index\" style=\"width: 100px; height: 100px\"\r\n          :src=\"item.url\" fit=\"fit\" :preview-src-list=\"vehicleLicenseList\">\r\n        </el-image>\r\n      </el-descriptions-item>\r\n      <el-descriptions-item>\r\n        <template slot=\"label\"><i class=\"el-icon-truck\"></i> 司机照片 </template>\r\n        <el-image v-for=\"(item, index) in photoTemp\" :key=\"index\" style=\"width: 100px; height: 100px\" :src=\"item.url\"\r\n          fit=\"fit\" :preview-src-list=\"driverImg\">\r\n        </el-image>\r\n      </el-descriptions-item>\r\n    </el-descriptions>\r\n    <h3>操作</h3>\r\n    <el-button type=\"primary\" size=\"mini\" @click=\"openEditStatusDialog\">修改状态</el-button>\r\n    <el-dialog :title=\"'修改预约单状态'\" :visible.sync=\"editStatusDialogVisible\" width=\"400px\">\r\n      <el-form :model=\"editStatusForm\" label-width=\"80px\">\r\n        <el-form-item label=\"状态\">\r\n          <el-select v-model=\"editStatusForm.status\" placeholder=\"请选择状态\">\r\n            <el-option v-for=\"item in statusOptions\" :key=\"item.value\" :label=\"item.label\" :value=\"item.value\" />\r\n          </el-select>\r\n        </el-form-item>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"editStatusDialogVisible = false\">取消</el-button>\r\n        <el-button type=\"primary\" @click=\"submitEditStatus\">确定</el-button>\r\n      </div>\r\n    </el-dialog>\r\n    <h3>预约单日志</h3>\r\n    <el-table ref=\"statusTable\" :data=\"logList\" style=\"width: 100%\">\r\n      <el-table-column prop=\"createTime\" label=\"时间\" min-width=\"200\">\r\n        <template slot-scope=\"scope\">\r\n          {{ formatTime(scope.row.createTime) }}\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column prop=\"info\" label=\"描述\" min-width=\"500\">\r\n      </el-table-column>\r\n    </el-table>\r\n  </div>\r\n</template>\r\n<script>\r\nimport { getAlloyOrderDetail, updateAlloyOrderStatus } from '@/api/truck/alloy/reservation';\r\n\r\nexport default {\r\n  name: 'AlloyOrderDetail',\r\n  data() {\r\n    return {\r\n      orderInfo: {},\r\n      logList: [],\r\n      driverImg: [],\r\n      photoTemp: [],\r\n      vehicleLicenseList: [],\r\n      vehicleLicenseListTemp: [],\r\n      driverLicenseList: [],\r\n      driverLicenseListTemp: [],\r\n      labelStyle: {\r\n        width: '200px',\r\n      },\r\n      editStatusDialogVisible: false,\r\n      editStatusForm: {\r\n        reservationNo: '',\r\n        status: ''\r\n      },\r\n      statusOptions: [\r\n        { value: '1', label: '待审核' },\r\n        { value: '2', label: '待签到' },\r\n        { value: '3', label: '物管分配' },\r\n        { value: '4', label: '待入厂' },\r\n        { value: '5', label: '已入厂' },\r\n        { value: '6', label: '物管确认' },\r\n        { value: '7', label: '已出厂' },\r\n        { value: '21', label: '驳回' },\r\n        { value: '22', label: '待取消' },\r\n        { value: '23', label: '已取消' }\r\n      ],\r\n    };\r\n  },\r\n  created() {\r\n    this.getDetail();\r\n  },\r\n  methods: {\r\n    getDetail() {\r\n      const reservationNo = this.$route.params.reservationNo;\r\n      getAlloyOrderDetail({ reservationNo }).then((response) => {\r\n        if (response.code === 200) {\r\n          this.orderInfo = response.data;\r\n          this.logList = response.data.matMgmtAlloyLogs || [];\r\n          // 处理图片字段\r\n          try {\r\n            this.photoTemp = this.orderInfo.driverFaceImg ? JSON.parse(this.orderInfo.driverFaceImg) : [];\r\n          } catch { this.photoTemp = []; }\r\n          try {\r\n            this.driverLicenseListTemp = this.orderInfo.driverLicenseImgs ? JSON.parse(this.orderInfo.driverLicenseImgs) : [];\r\n          } catch { this.driverLicenseListTemp = []; }\r\n          try {\r\n            this.vehicleLicenseListTemp = this.orderInfo.drivingLicenseImg ? JSON.parse(this.orderInfo.drivingLicenseImg) : [];\r\n          } catch { this.vehicleLicenseListTemp = []; }\r\n          this.driverImg = this.photoTemp.map(el => el.url);\r\n          this.driverLicenseList = this.driverLicenseListTemp.map(el => el.url);\r\n          this.vehicleLicenseList = this.vehicleLicenseListTemp.map(el => el.url);\r\n        }\r\n      });\r\n    },\r\n    formatTime(time) {\r\n      if (!time) return '';\r\n      const date = new Date(time);\r\n      if (isNaN(date.getTime())) return time;\r\n\r\n      const y = date.getFullYear();\r\n      const m = (date.getMonth() + 1).toString().padStart(2, '0');\r\n      const d = date.getDate().toString().padStart(2, '0');\r\n      const h = date.getHours().toString().padStart(2, '0');\r\n      const i = date.getMinutes().toString().padStart(2, '0');\r\n      const s = date.getSeconds().toString().padStart(2, '0');\r\n\r\n      return `${y}-${m}-${d} ${h}:${i}:${s}`;\r\n    },\r\n    getBusinessDept(val) {\r\n      if (val == '1') return '采购中心';\r\n      if (val == '2') return '商务部';\r\n      return '';\r\n    },\r\n    getEntranceGate(val) {\r\n      if (val === 1 || val === '1') return '安全村';\r\n      if (val === 2 || val === '2') return '三号门';\r\n      return '';\r\n    },\r\n    getElectrodeDesc(val) {\r\n      if (val == '1') return '400';\r\n      if (val == '2') return '450';\r\n      if (val == '3') return '700';\r\n      return '';\r\n    },\r\n    getStatusLabel(val) {\r\n      const statusMap = {\r\n        1: '待审核',\r\n        2: '待签到',\r\n        3: '物管分配',\r\n        4: '待入厂',\r\n        5: '已入厂',\r\n        6: '物管确认',\r\n        7: '已出厂',\r\n        21: '驳回',\r\n        22: '待取消',\r\n        23: '已取消'\r\n      };\r\n      return statusMap[val] || '未知';\r\n    },\r\n    getStatusTagType(val) {\r\n      const statusTypeMap = {\r\n        1: 'warning',    // 待审核\r\n        2: 'info',       // 待签到\r\n        3: 'primary',    // 物管分配\r\n        4: 'warning',    // 待入厂\r\n        5: 'success',    // 已入厂\r\n        6: 'primary',    // 物管确认\r\n        7: 'success',    // 已出厂\r\n        21: 'danger',    // 驳回\r\n        22: 'warning',   // 待取消\r\n        23: 'info'       // 已取消\r\n      };\r\n      return statusTypeMap[val] || 'info';\r\n    },\r\n    openEditStatusDialog() {\r\n      this.editStatusForm.reservationNo = this.orderInfo.reservationNo;\r\n      this.editStatusForm.status = this.orderInfo.status;\r\n      this.editStatusDialogVisible = true;\r\n    },\r\n    submitEditStatus() {\r\n      updateAlloyOrderStatus(this.editStatusForm).then(response => {\r\n        if (response.code === 200) {\r\n          this.$message.success('状态修改成功');\r\n          this.editStatusDialogVisible = false;\r\n          this.getDetail();\r\n        } else {\r\n          this.$message.error(response.msg || '状态修改失败');\r\n        }\r\n      });\r\n    }\r\n  }\r\n};\r\n</script>\r\n<style scoped lang=\"scss\">\r\n.title-div {\r\n  display: flex;\r\n  flex-direction: row;\r\n  align-items: center;\r\n  justify-content: space-between;\r\n}\r\n\r\n.url-div {\r\n  display: flex;\r\n  flex-direction: row;\r\n  align-items: center;\r\n  justify-content: flex-start;\r\n}\r\n\r\n/*去除页眉页脚*/\r\n@page {\r\n  size: auto;\r\n  /* auto is the initial value */\r\n  margin: 3mm;\r\n  /* this affects the margin in the printer settings */\r\n}\r\n\r\nhtml {\r\n  background-color: #ffffff;\r\n  margin: 0;\r\n  /* this affects the margin on the html before sending to printer */\r\n}\r\n\r\nbody {\r\n  border: solid 1px blue;\r\n  margin: 10mm 15mm 10mm 15mm;\r\n  /* margin you want for the content */\r\n}\r\n\r\n/*去除页眉页脚*/\r\n</style>\r\n"], "mappings": ";;;;;;;;;;;;;;AA0LA,IAAAA,YAAA,GAAAC,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCAEA;EACAC,IAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,SAAA;MACAC,OAAA;MACAC,SAAA;MACAC,SAAA;MACAC,kBAAA;MACAC,sBAAA;MACAC,iBAAA;MACAC,qBAAA;MACAC,UAAA;QACAC,KAAA;MACA;MACAC,uBAAA;MACAC,cAAA;QACAC,aAAA;QACAC,MAAA;MACA;MACAC,aAAA,GACA;QAAAC,KAAA;QAAAC,KAAA;MAAA,GACA;QAAAD,KAAA;QAAAC,KAAA;MAAA,GACA;QAAAD,KAAA;QAAAC,KAAA;MAAA,GACA;QAAAD,KAAA;QAAAC,KAAA;MAAA,GACA;QAAAD,KAAA;QAAAC,KAAA;MAAA,GACA;QAAAD,KAAA;QAAAC,KAAA;MAAA,GACA;QAAAD,KAAA;QAAAC,KAAA;MAAA,GACA;QAAAD,KAAA;QAAAC,KAAA;MAAA,GACA;QAAAD,KAAA;QAAAC,KAAA;MAAA,GACA;QAAAD,KAAA;QAAAC,KAAA;MAAA;IAEA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA,KAAAC,SAAA;EACA;EACAC,OAAA;IACAD,SAAA,WAAAA,UAAA;MAAA,IAAAE,KAAA;MACA,IAAAR,aAAA,QAAAS,MAAA,CAAAC,MAAA,CAAAV,aAAA;MACA,IAAAW,gCAAA;QAAAX,aAAA,EAAAA;MAAA,GAAAY,IAAA,WAAAC,QAAA;QACA,IAAAA,QAAA,CAAAC,IAAA;UACAN,KAAA,CAAApB,SAAA,GAAAyB,QAAA,CAAA1B,IAAA;UACAqB,KAAA,CAAAnB,OAAA,GAAAwB,QAAA,CAAA1B,IAAA,CAAA4B,gBAAA;UACA;UACA;YACAP,KAAA,CAAAjB,SAAA,GAAAiB,KAAA,CAAApB,SAAA,CAAA4B,aAAA,GAAAC,IAAA,CAAAC,KAAA,CAAAV,KAAA,CAAApB,SAAA,CAAA4B,aAAA;UACA,SAAAG,OAAA;YAAAX,KAAA,CAAAjB,SAAA;UAAA;UACA;YACAiB,KAAA,CAAAb,qBAAA,GAAAa,KAAA,CAAApB,SAAA,CAAAgC,iBAAA,GAAAH,IAAA,CAAAC,KAAA,CAAAV,KAAA,CAAApB,SAAA,CAAAgC,iBAAA;UACA,SAAAC,QAAA;YAAAb,KAAA,CAAAb,qBAAA;UAAA;UACA;YACAa,KAAA,CAAAf,sBAAA,GAAAe,KAAA,CAAApB,SAAA,CAAAkC,iBAAA,GAAAL,IAAA,CAAAC,KAAA,CAAAV,KAAA,CAAApB,SAAA,CAAAkC,iBAAA;UACA,SAAAC,QAAA;YAAAf,KAAA,CAAAf,sBAAA;UAAA;UACAe,KAAA,CAAAlB,SAAA,GAAAkB,KAAA,CAAAjB,SAAA,CAAAiC,GAAA,WAAAC,EAAA;YAAA,OAAAA,EAAA,CAAAC,GAAA;UAAA;UACAlB,KAAA,CAAAd,iBAAA,GAAAc,KAAA,CAAAb,qBAAA,CAAA6B,GAAA,WAAAC,EAAA;YAAA,OAAAA,EAAA,CAAAC,GAAA;UAAA;UACAlB,KAAA,CAAAhB,kBAAA,GAAAgB,KAAA,CAAAf,sBAAA,CAAA+B,GAAA,WAAAC,EAAA;YAAA,OAAAA,EAAA,CAAAC,GAAA;UAAA;QACA;MACA;IACA;IACAC,UAAA,WAAAA,WAAAC,IAAA;MACA,KAAAA,IAAA;MACA,IAAAC,IAAA,OAAAC,IAAA,CAAAF,IAAA;MACA,IAAAG,KAAA,CAAAF,IAAA,CAAAG,OAAA,YAAAJ,IAAA;MAEA,IAAAK,CAAA,GAAAJ,IAAA,CAAAK,WAAA;MACA,IAAAC,CAAA,IAAAN,IAAA,CAAAO,QAAA,QAAAC,QAAA,GAAAC,QAAA;MACA,IAAAC,CAAA,GAAAV,IAAA,CAAAW,OAAA,GAAAH,QAAA,GAAAC,QAAA;MACA,IAAAG,CAAA,GAAAZ,IAAA,CAAAa,QAAA,GAAAL,QAAA,GAAAC,QAAA;MACA,IAAAK,CAAA,GAAAd,IAAA,CAAAe,UAAA,GAAAP,QAAA,GAAAC,QAAA;MACA,IAAAO,CAAA,GAAAhB,IAAA,CAAAiB,UAAA,GAAAT,QAAA,GAAAC,QAAA;MAEA,UAAAS,MAAA,CAAAd,CAAA,OAAAc,MAAA,CAAAZ,CAAA,OAAAY,MAAA,CAAAR,CAAA,OAAAQ,MAAA,CAAAN,CAAA,OAAAM,MAAA,CAAAJ,CAAA,OAAAI,MAAA,CAAAF,CAAA;IACA;IACAG,eAAA,WAAAA,gBAAAC,GAAA;MACA,IAAAA,GAAA;MACA,IAAAA,GAAA;MACA;IACA;IACAC,eAAA,WAAAA,gBAAAD,GAAA;MACA,IAAAA,GAAA,UAAAA,GAAA;MACA,IAAAA,GAAA,UAAAA,GAAA;MACA;IACA;IACAE,gBAAA,WAAAA,iBAAAF,GAAA;MACA,IAAAA,GAAA;MACA,IAAAA,GAAA;MACA,IAAAA,GAAA;MACA;IACA;IACAG,cAAA,WAAAA,eAAAH,GAAA;MACA,IAAAI,SAAA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;MACA;MACA,OAAAA,SAAA,CAAAJ,GAAA;IACA;IACAK,gBAAA,WAAAA,iBAAAL,GAAA;MACA,IAAAM,aAAA;QACA;QAAA;QACA;QAAA;QACA;QAAA;QACA;QAAA;QACA;QAAA;QACA;QAAA;QACA;QAAA;QACA;QAAA;QACA;QAAA;QACA;MACA;MACA,OAAAA,aAAA,CAAAN,GAAA;IACA;IACAO,oBAAA,WAAAA,qBAAA;MACA,KAAAzD,cAAA,CAAAC,aAAA,QAAAZ,SAAA,CAAAY,aAAA;MACA,KAAAD,cAAA,CAAAE,MAAA,QAAAb,SAAA,CAAAa,MAAA;MACA,KAAAH,uBAAA;IACA;IACA2D,gBAAA,WAAAA,iBAAA;MAAA,IAAAC,MAAA;MACA,IAAAC,mCAAA,OAAA5D,cAAA,EAAAa,IAAA,WAAAC,QAAA;QACA,IAAAA,QAAA,CAAAC,IAAA;UACA4C,MAAA,CAAAE,QAAA,CAAAC,OAAA;UACAH,MAAA,CAAA5D,uBAAA;UACA4D,MAAA,CAAApD,SAAA;QACA;UACAoD,MAAA,CAAAE,QAAA,CAAAE,KAAA,CAAAjD,QAAA,CAAAkD,GAAA;QACA;MACA;IACA;EACA;AACA", "ignoreList": []}]}