{"remainingRequest": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\src\\views\\drawing\\technicalDetail\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\src\\views\\drawing\\technicalDetail\\index.vue", "mtime": 1755499162049}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\babel.config.js", "mtime": 1688548084091}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_drawing", "require", "_role", "name", "components", "data", "loading", "desLabelStyle", "width", "orderInfo", "total", "userTotal", "selectedUserId", "selectedUserName", "form", "logList", "drawingList", "tblList", "userList", "technicalAgreementList", "upload", "headers", "url", "process", "env", "VUE_APP_BASE_API", "isUploading", "showSearch", "rejectDrawing", "rejectReason", "passOpen", "passForm", "id", "factoryAapproveReason", "equipmentApproveReason", "equipmentSearch", "isEquipmentApprove", "approveReason", "attachmentUuid", "logQueryParams", "ticketNo", "pageNum", "pageSize", "queryParams", "userName", "undefined", "created", "$route", "params", "approveId", "console", "log", "getInfo", "getLogs", "$message", "message", "type", "methods", "handleTechnical", "_this", "Number", "$confirm", "confirmButtonText", "cancelButtonText", "then", "downloadTechnicalAgreement", "response", "download", "msg", "handleApproverRowClick", "row", "handleClearSelection", "_this2", "getTechnicalAgreement", "JSON", "parse", "technicalAgreement", "handlePictureCardPreview", "file", "localUrl", "window", "location", "host", "replace", "open", "tmpUrl", "formatTime", "isoTimeString", "date", "Date", "year", "getFullYear", "month", "String", "getMonth", "padStart", "day", "getDate", "hours", "getHours", "minutes", "getMinutes", "seconds", "getSeconds", "concat", "formatStringTime", "time", "substring", "_this3", "getLogList", "rows"], "sources": ["src/views/drawing/technicalDetail/index.vue"], "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <!-- 信息确认 -->\r\n    <el-descriptions title=\"基础信息\" :column=\"4\" border>\r\n      <el-descriptions-item>\r\n        <template slot=\"label\"> 物料编码 </template>\r\n        {{ this.form.materialsCode }}\r\n      </el-descriptions-item>\r\n      <el-descriptions-item :span=\"2\">\r\n        <template slot=\"label\">技术协议</template>\r\n        <div v-if=\"!technicalAgreementList\" class=\"attachment-empty\">无技术协议</div>\r\n        <div v-if=\"technicalAgreementList\" class=\"attachment-empty\">\r\n          <a v-for=\"(item, index) in technicalAgreementList\" :key=\"index\" style=\"display: block; margin-bottom: 10px;\">\r\n            <el-button icon=\"el-icon-paperclip\" type=\"primary\" size=\"small\" @click=\"handleTechnical\">下载技术协议{{ index + 1\r\n              }}</el-button>\r\n          </a>\r\n        </div>\r\n      </el-descriptions-item>\r\n    </el-descriptions>\r\n\r\n    <h3>日志</h3>\r\n    <el-table v-loading=\"loading\" ref=\"logTable\" :data=\"logList\" style=\"width: 100%\">\r\n      <el-table-column prop=\"text\" label=\"时间\" width=\"200\">\r\n        <template slot-scope=\"scopes\">\r\n          {{ scopes.row.createTime }}\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column prop=\"info\" label=\"描述\" min-width=\"600\">\r\n      </el-table-column>\r\n    </el-table>\r\n  </div>\r\n</template>\r\n\r\n<pagination v-show=\"total >0\" :total=\"total\" :page.sync=\"logQuerParams.pageNum\" :limit.sync=\"logQueryParams.pageSize\"\r\n  @pagination=\"getLogs\"></pagination>\r\n\r\n<style scoped>\r\n.btn-footer {\r\n  padding: 18px 0;\r\n  text-align: center;\r\n}\r\n\r\n.margin-top {\r\n  margin-top: 20px;\r\n}\r\n\r\n.dialog-footer {\r\n  margin-top: 30px;\r\n}\r\n\r\n.title-div {\r\n  display: flex;\r\n  flex-direction: row;\r\n  align-items: center;\r\n  justify-content: space-between;\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.url-div {\r\n  display: flex;\r\n  flex-direction: row;\r\n  align-items: center;\r\n  justify-content: flex-start;\r\n}\r\n\r\n.qrcode-div {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: flex-start;\r\n  justify-content: flex-start;\r\n}\r\n</style>\r\n\r\n<script>\r\nimport { getLogList, getDrawing,  downloadTechnicalAgreement,getTechnicalAgreement } from '@/api/drawing/drawing';\r\nimport { getListByRoleKey } from '@/api/system/role';\r\n\r\nexport default {\r\n  name: \"TechnicalDetail\",\r\n  components: {},\r\n  data() {\r\n    return {\r\n      // 遮罩层\r\n      loading: true,\r\n      desLabelStyle: { width: '160px' },\r\n      // 总条数\r\n      orderInfo: {},\r\n      total: 0,\r\n      userTotal: 0,\r\n      // drawingNo: null,\r\n      selectedUserId: null,\r\n      selectedUserName: null,\r\n      form: {\r\n        // fileUrl: '[]',\r\n      },\r\n      logList: [],\r\n      drawingList: [],\r\n      tblList: [],\r\n      userList: [],\r\n      // fileList: [],\r\n      technicalAgreementList: [],\r\n      upload: {\r\n        // 设置上传的请求头部\r\n        headers: {},\r\n        // 上传的地址\r\n        url: process.env.VUE_APP_BASE_API + \"/app/common/uploadMinio\",\r\n        isUploading: false,\r\n      },\r\n      // fileList: [],\r\n      // rejectOpen: false,\r\n      // showDialog: false,\r\n      showSearch: true,\r\n      rejectDrawing: {\r\n        rejectReason: ''\r\n      },\r\n      passOpen: false,\r\n      passForm: {\r\n        id: '',\r\n        factoryAapproveReason: '',\r\n        equipmentApproveReason: '',\r\n        equipmentSearch: ''\r\n      },\r\n      isEquipmentApprove: false,\r\n      approveReason: '',\r\n      attachmentUuid: [],\r\n      // fileUrl: [],\r\n      logQueryParams: {\r\n        ticketNo: null,\r\n        pageNum: 1,\r\n        pageSize: 10,\r\n      },\r\n      queryParams: {\r\n        pageNum: 1,\r\n        pageSize: 10,\r\n        userName: undefined,\r\n      },\r\n    };\r\n  },\r\n\r\n  created() {\r\n    const id = this.$route.params && this.$route.params.id;\r\n    this.logQueryParams.approveId = id;\r\n    console.log(\"图纸号\", id);\r\n    // this.getUserList();\r\n    if (id) {\r\n      this.id = id;\r\n      this.getInfo();\r\n      this.getLogs();\r\n    } else {\r\n      this.$message({ message: \"未获取图纸号\", type: \"warning\" });\r\n    }\r\n  },\r\n  methods: {\r\n  \r\n    handleTechnical() {\r\n      const queryParams = { id: Number(this.id) };\r\n      this.$confirm('是否确认下载对应技术协议附件？', \"警告\", {\r\n        confirmButtonText: \"确定\",\r\n        cancelButtonText: \"取消\",\r\n        type: \"warning\"\r\n      }).then(function () {\r\n        return downloadTechnicalAgreement(queryParams);\r\n      }).then(response => {\r\n        this.download(response.msg);\r\n      })\r\n      console.log()\r\n    },\r\n\r\n    handleApproverRowClick(row) {\r\n      // 单击行时触发\r\n      this.selectedUserId = row.userName;\r\n    },\r\n\r\n    //清空选择状态\r\n    handleClearSelection() {\r\n      this.selectedUserId = null;\r\n    },\r\n\r\n    // 获取图纸详情\r\n    getInfo() {\r\n      const id = this.id;\r\n      getTechnicalAgreement(id).then((response) => {\r\n        this.form = response.data;\r\n        console.log(\"获取\", this.form);\r\n        this.technicalAgreementList = JSON.parse(this.form.technicalAgreement);\r\n      });\r\n    },\r\n\r\n    // 预览文件\r\n    handlePictureCardPreview(file) {\r\n      let localUrl = window.location.host;\r\n      if (file.url != null) {\r\n        if (localUrl === \"************:8099\") {\r\n          file.url = file.url.replace(\"ydxt.citicsteel.com:8099\", \"************:8099\");\r\n        }\r\n        window.open(file.url);\r\n      }\r\n      if (file.response && file.response.url) {\r\n        if (localUrl === \"************:8099\") {\r\n          let tmpUrl = file.response.url.replace(\"ydxt.citicsteel.com:8099\", \"************:8099\");\r\n          window.open(tmpUrl);\r\n        } else {\r\n          window.open(file.response.url);\r\n        }\r\n      }\r\n    },\r\n\r\n\r\n    //时间格式化\r\n    formatTime(isoTimeString) {\r\n      if (!isoTimeString) return '';\r\n\r\n      // 解析 ISO 8601 时间字符串\r\n      const date = new Date(isoTimeString);\r\n\r\n      // 提取年月日时分秒\r\n      const year = date.getFullYear();\r\n      const month = String(date.getMonth() + 1).padStart(2, '0'); // 月份是从0开始的，所以加1\r\n      const day = String(date.getDate()).padStart(2, '0');\r\n      const hours = String(date.getHours()).padStart(2, '0');\r\n      const minutes = String(date.getMinutes()).padStart(2, '0');\r\n      const seconds = String(date.getSeconds()).padStart(2, '0');\r\n\r\n      // 返回格式化的字符串\r\n      return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;\r\n\r\n    },\r\n    // 字符串时间格式化\r\n    formatStringTime(time) {\r\n      if (typeof time === \"string\") {\r\n        return (\r\n          time.substring(0, 4) +\r\n          \"-\" +\r\n          time.substring(4, 6) +\r\n          \"-\" +\r\n          time.substring(6, 8) +\r\n          \" \" +\r\n          time.substring(8, 10) +\r\n          \":\" +\r\n          time.substring(10, 12) +\r\n          \":\" +\r\n          time.substring(12, 14)\r\n        );\r\n      } else {\r\n        return time;\r\n      }\r\n    },\r\n\r\n    // 获取日志\r\n    getLogs() {\r\n      getLogList(this.logQueryParams).then((response) => {\r\n        console.log(\"日志\", response);\r\n        this.logList = response.rows;\r\n        this.total = response.total;\r\n        this.loading = false;\r\n      });\r\n    },\r\n\r\n  },\r\n};\r\n</script>"], "mappings": ";;;;;;;;;;;;AA0EA,IAAAA,QAAA,GAAAC,OAAA;AACA,IAAAC,KAAA,GAAAD,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCAEA;EACAE,IAAA;EACAC,UAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACA;MACAC,OAAA;MACAC,aAAA;QAAAC,KAAA;MAAA;MACA;MACAC,SAAA;MACAC,KAAA;MACAC,SAAA;MACA;MACAC,cAAA;MACAC,gBAAA;MACAC,IAAA;QACA;MAAA,CACA;MACAC,OAAA;MACAC,WAAA;MACAC,OAAA;MACAC,QAAA;MACA;MACAC,sBAAA;MACAC,MAAA;QACA;QACAC,OAAA;QACA;QACAC,GAAA,EAAAC,OAAA,CAAAC,GAAA,CAAAC,gBAAA;QACAC,WAAA;MACA;MACA;MACA;MACA;MACAC,UAAA;MACAC,aAAA;QACAC,YAAA;MACA;MACAC,QAAA;MACAC,QAAA;QACAC,EAAA;QACAC,qBAAA;QACAC,sBAAA;QACAC,eAAA;MACA;MACAC,kBAAA;MACAC,aAAA;MACAC,cAAA;MACA;MACAC,cAAA;QACAC,QAAA;QACAC,OAAA;QACAC,QAAA;MACA;MACAC,WAAA;QACAF,OAAA;QACAC,QAAA;QACAE,QAAA,EAAAC;MACA;IACA;EACA;EAEAC,OAAA,WAAAA,QAAA;IACA,IAAAd,EAAA,QAAAe,MAAA,CAAAC,MAAA,SAAAD,MAAA,CAAAC,MAAA,CAAAhB,EAAA;IACA,KAAAO,cAAA,CAAAU,SAAA,GAAAjB,EAAA;IACAkB,OAAA,CAAAC,GAAA,QAAAnB,EAAA;IACA;IACA,IAAAA,EAAA;MACA,KAAAA,EAAA,GAAAA,EAAA;MACA,KAAAoB,OAAA;MACA,KAAAC,OAAA;IACA;MACA,KAAAC,QAAA;QAAAC,OAAA;QAAAC,IAAA;MAAA;IACA;EACA;EACAC,OAAA;IAEAC,eAAA,WAAAA,gBAAA;MAAA,IAAAC,KAAA;MACA,IAAAhB,WAAA;QAAAX,EAAA,EAAA4B,MAAA,MAAA5B,EAAA;MAAA;MACA,KAAA6B,QAAA;QACAC,iBAAA;QACAC,gBAAA;QACAP,IAAA;MACA,GAAAQ,IAAA;QACA,WAAAC,mCAAA,EAAAtB,WAAA;MACA,GAAAqB,IAAA,WAAAE,QAAA;QACAP,KAAA,CAAAQ,QAAA,CAAAD,QAAA,CAAAE,GAAA;MACA;MACAlB,OAAA,CAAAC,GAAA;IACA;IAEAkB,sBAAA,WAAAA,uBAAAC,GAAA;MACA;MACA,KAAA1D,cAAA,GAAA0D,GAAA,CAAA1B,QAAA;IACA;IAEA;IACA2B,oBAAA,WAAAA,qBAAA;MACA,KAAA3D,cAAA;IACA;IAEA;IACAwC,OAAA,WAAAA,QAAA;MAAA,IAAAoB,MAAA;MACA,IAAAxC,EAAA,QAAAA,EAAA;MACA,IAAAyC,8BAAA,EAAAzC,EAAA,EAAAgC,IAAA,WAAAE,QAAA;QACAM,MAAA,CAAA1D,IAAA,GAAAoD,QAAA,CAAA7D,IAAA;QACA6C,OAAA,CAAAC,GAAA,OAAAqB,MAAA,CAAA1D,IAAA;QACA0D,MAAA,CAAArD,sBAAA,GAAAuD,IAAA,CAAAC,KAAA,CAAAH,MAAA,CAAA1D,IAAA,CAAA8D,kBAAA;MACA;IACA;IAEA;IACAC,wBAAA,WAAAA,yBAAAC,IAAA;MACA,IAAAC,QAAA,GAAAC,MAAA,CAAAC,QAAA,CAAAC,IAAA;MACA,IAAAJ,IAAA,CAAAxD,GAAA;QACA,IAAAyD,QAAA;UACAD,IAAA,CAAAxD,GAAA,GAAAwD,IAAA,CAAAxD,GAAA,CAAA6D,OAAA;QACA;QACAH,MAAA,CAAAI,IAAA,CAAAN,IAAA,CAAAxD,GAAA;MACA;MACA,IAAAwD,IAAA,CAAAZ,QAAA,IAAAY,IAAA,CAAAZ,QAAA,CAAA5C,GAAA;QACA,IAAAyD,QAAA;UACA,IAAAM,MAAA,GAAAP,IAAA,CAAAZ,QAAA,CAAA5C,GAAA,CAAA6D,OAAA;UACAH,MAAA,CAAAI,IAAA,CAAAC,MAAA;QACA;UACAL,MAAA,CAAAI,IAAA,CAAAN,IAAA,CAAAZ,QAAA,CAAA5C,GAAA;QACA;MACA;IACA;IAGA;IACAgE,UAAA,WAAAA,WAAAC,aAAA;MACA,KAAAA,aAAA;;MAEA;MACA,IAAAC,IAAA,OAAAC,IAAA,CAAAF,aAAA;;MAEA;MACA,IAAAG,IAAA,GAAAF,IAAA,CAAAG,WAAA;MACA,IAAAC,KAAA,GAAAC,MAAA,CAAAL,IAAA,CAAAM,QAAA,QAAAC,QAAA;MACA,IAAAC,GAAA,GAAAH,MAAA,CAAAL,IAAA,CAAAS,OAAA,IAAAF,QAAA;MACA,IAAAG,KAAA,GAAAL,MAAA,CAAAL,IAAA,CAAAW,QAAA,IAAAJ,QAAA;MACA,IAAAK,OAAA,GAAAP,MAAA,CAAAL,IAAA,CAAAa,UAAA,IAAAN,QAAA;MACA,IAAAO,OAAA,GAAAT,MAAA,CAAAL,IAAA,CAAAe,UAAA,IAAAR,QAAA;;MAEA;MACA,UAAAS,MAAA,CAAAd,IAAA,OAAAc,MAAA,CAAAZ,KAAA,OAAAY,MAAA,CAAAR,GAAA,OAAAQ,MAAA,CAAAN,KAAA,OAAAM,MAAA,CAAAJ,OAAA,OAAAI,MAAA,CAAAF,OAAA;IAEA;IACA;IACAG,gBAAA,WAAAA,iBAAAC,IAAA;MACA,WAAAA,IAAA;QACA,OACAA,IAAA,CAAAC,SAAA,SACA,MACAD,IAAA,CAAAC,SAAA,SACA,MACAD,IAAA,CAAAC,SAAA,SACA,MACAD,IAAA,CAAAC,SAAA,UACA,MACAD,IAAA,CAAAC,SAAA,WACA,MACAD,IAAA,CAAAC,SAAA;MAEA;QACA,OAAAD,IAAA;MACA;IACA;IAEA;IACArD,OAAA,WAAAA,QAAA;MAAA,IAAAuD,MAAA;MACA,IAAAC,mBAAA,OAAAtE,cAAA,EAAAyB,IAAA,WAAAE,QAAA;QACAhB,OAAA,CAAAC,GAAA,OAAAe,QAAA;QACA0C,MAAA,CAAA7F,OAAA,GAAAmD,QAAA,CAAA4C,IAAA;QACAF,MAAA,CAAAlG,KAAA,GAAAwD,QAAA,CAAAxD,KAAA;QACAkG,MAAA,CAAAtG,OAAA;MACA;IACA;EAEA;AACA", "ignoreList": []}]}