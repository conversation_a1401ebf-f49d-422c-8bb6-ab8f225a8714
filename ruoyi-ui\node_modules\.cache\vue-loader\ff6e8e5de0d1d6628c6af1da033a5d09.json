{"remainingRequest": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\src\\views\\leave\\plan\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\src\\views\\leave\\plan\\index.vue", "mtime": 1755499098437}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgeyBsaXN0UGxhbiwgZ2V0UGxhbiwgZGVsUGxhbiwgYWRkUGxhbiwgdXBkYXRlUGxhbiwgZXhwb3J0UGxhbiB9IGZyb20gIkAvYXBpL2xlYXZlL3BsYW4iOw0KaW1wb3J0IEVkaXRvciBmcm9tICdAL2NvbXBvbmVudHMvRWRpdG9yJzsNCg0KZXhwb3J0IGRlZmF1bHQgew0KICBuYW1lOiAiUGxhbiIsDQogIGNvbXBvbmVudHM6IHsNCiAgICBFZGl0b3IsDQogIH0sDQogIGRhdGEoKSB7DQogICAgcmV0dXJuIHsNCiAgICAgIC8vIOmBrue9qeWxgg0KICAgICAgbG9hZGluZzogdHJ1ZSwNCiAgICAgIC8vIOmAieS4reaVsOe7hA0KICAgICAgYXBwbHlOb3M6IFtdLA0KICAgICAgLy8g6Z2e5Y2V5Liq56aB55SoDQogICAgICBzaW5nbGU6IHRydWUsDQogICAgICAvLyDpnZ7lpJrkuKrnpoHnlKgNCiAgICAgIG11bHRpcGxlOiB0cnVlLA0KICAgICAgLy8g5pi+56S65pCc57Si5p2h5Lu2DQogICAgICBzaG93U2VhcmNoOiB0cnVlLA0KICAgICAgLy8g5oC75p2h5pWwDQogICAgICB0b3RhbDogMCwNCiAgICAgIC8vIOWHuumXqOivgeiuoeWIkueUs+ivt+ihqOagvOaVsOaNrg0KICAgICAgcGxhbkxpc3Q6IFtdLA0KICAgICAgLy8g5pel5pyf6IyD5Zu0DQogICAgICBkYXRlcmFuZ2VQbGFuUmV0dXJuOiBbXSwNCiAgICAgIGRhdGVyYW5nZUV4cGlyZTogW10sDQogICAgICBkYXRlcmFuZ2VBcHBseTogW10sDQogICAgICAvLyDlvLnlh7rlsYLmoIfpopgNCiAgICAgIHRpdGxlOiAiIiwNCiAgICAgIC8vIOaYr+WQpuaYvuekuuW8ueWHuuWxgg0KICAgICAgb3BlbjogZmFsc2UsDQogICAgICAvL+aYr+WQpuaYvuekuuaWsOWinuaMiemSrg0KICAgICAgc2hvd0FkZEJ0bjpmYWxzZSwNCiAgICAgIC8v5piv5ZCm5pi+56S65bqf5byD5oyJ6ZKuDQogICAgICBzaG93Q2FuY2VsQnRuOmZhbHNlLA0KICAgICAgLy8g5p+l6K+i5Y+C5pWwDQogICAgICBxdWVyeVBhcmFtczogew0KICAgICAgICBwYWdlTnVtOiAxLA0KICAgICAgICBwYWdlU2l6ZTogMTAsDQogICAgICAgIHBsYW5ObzogbnVsbCwNCiAgICAgICAgcGxhblR5cGU6IG51bGwsDQogICAgICAgIGJ1c2luZXNzQ2F0ZWdvcnk6IG51bGwsDQogICAgICAgIG1lYXN1cmVGbGFnOiBudWxsLA0KICAgICAgICBwbGFubmVkQW1vdW50OiBudWxsLA0KICAgICAgICByZWNlaXZlQ29tcGFueTogbnVsbCwNCiAgICAgICAgcmVjZWl2ZUNvbXBhbnlDb2RlOiBudWxsLA0KICAgICAgICB0YXJnZXRDb21wYW55OiBudWxsLA0KICAgICAgICB0YXJnZXRDb21wYW55Q29kZTogbnVsbCwNCiAgICAgICAgc291cmNlQ29tcGFueTogbnVsbCwNCiAgICAgICAgc291cmNlQ29tcGFueUNvZGU6IG51bGwsDQogICAgICAgIHBsYW5SZXR1cm5TdGFydFRpbWU6IG51bGwsDQogICAgICAgIHBsYW5SZXR1cm5FbmRUaW1lOiBudWxsLA0KICAgICAgICByZWFsUmV0dXJuVGltZTogbnVsbCwNCiAgICAgICAgbW9uaXRvcjogbnVsbCwNCiAgICAgICAgc3BlY2lhbE1hbmFnZXI6IG51bGwsDQogICAgICAgIGV4cGlyZVN0YXJ0VGltZTogbnVsbCwNCiAgICAgICAgZXhwaXJlRW5kVGltZTogbnVsbCwNCiAgICAgICAgcmVhc29uOiBudWxsLA0KICAgICAgICBpdGVtVHlwZTogbnVsbCwNCiAgICAgICAgcGxhblN0YXR1czogbnVsbCwNCiAgICAgICAgYXBwbHlTdGFydFRpbWU6IG51bGwsDQogICAgICAgIGFwcGx5RW5kVGltZTogbnVsbCwNCiAgICAgICAgYXBwbHlXb3JrTm86IG51bGwsDQogICAgICAgIGZhY3RvcnlBcHByb3ZlVGltZTogbnVsbCwNCiAgICAgICAgZmFjdG9yeUFwcHJvdmVXb3JrTm86IG51bGwsDQogICAgICAgIGZhY3RvcnlBcHByb3ZlRmxhZzogbnVsbCwNCiAgICAgICAgZmFjdG9yeUFwcHJvdmVDb250ZW50OiBudWxsLA0KICAgICAgICBmYWN0b3J5U2VjQXBwcm92ZUZsYWc6IG51bGwsDQogICAgICAgIGZhY3RvcnlTZWNBcHByb3ZlVGltZTogbnVsbCwNCiAgICAgICAgZmFjdG9yeVNlY0FwcHJvdmVXb3JrTm86IG51bGwsDQogICAgICAgIGZhY3RvcnlTZWNBcHByb3ZlQ29udGVudDogbnVsbCwNCiAgICAgICAgY2VudGVyQXBwcm92ZVRpbWU6IG51bGwsDQogICAgICAgIGNlbnRlckFwcHJvdmVXb3JrTm86IG51bGwsDQogICAgICAgIGNlbnRlckFwcHJvdmVGbGFnOiBudWxsLA0KICAgICAgICBjZW50ZXJBcHByb3ZlQ29udGVudDogbnVsbCwNCiAgICAgICAgYXBwbHlGaWxlVXJsOiBudWxsLA0KICAgICAgfSwNCiAgICAgIC8vIOihqOWNleWPguaVsA0KICAgICAgZm9ybToge30sDQogICAgICAvLyDooajljZXmoKHpqowNCiAgICAgIHJ1bGVzOiB7DQogICAgICB9DQogICAgfTsNCiAgfSwNCiAgY3JlYXRlZCgpIHsNCiAgICB0aGlzLmdldExpc3QoKTsNCiAgfSwNCiAgd2F0Y2g6IHsNCiAgJyRyb3V0ZS5xdWVyeS5yZWZyZXNoJzogew0KICAgIGltbWVkaWF0ZTogdHJ1ZSwNCiAgICBoYW5kbGVyKG5ld1ZhbCkgew0KICAgICAgaWYgKG5ld1ZhbCkgew0KICAgICAgICB0aGlzLmdldExpc3QoKTsgLy8g5Yi35paw5YiX6KGo5pWw5o2u55qE5pa55rOVDQogICAgICAgICAgfQ0KICAgICAgICB9DQogICAgICB9DQogICAgfSwNCiAgbWV0aG9kczogew0KICAgIC8qKiDmn6Xor6Llh7rpl6jor4HorqHliJLnlLPor7fliJfooaggKi8NCiAgICBnZXRMaXN0KCkgew0KICAgICAgdGhpcy5sb2FkaW5nID0gdHJ1ZTsNCiAgICAgIGxpc3RQbGFuKHRoaXMucXVlcnlQYXJhbXMpLnRoZW4ocmVzcG9uc2UgPT4gew0KICAgICAgICB0aGlzLnBsYW5MaXN0ID0gcmVzcG9uc2Uucm93czsNCiAgICAgICAgY29uc29sZS5sb2codGhpcy5wbGFuTGlzdCk7DQogICAgICAgIHRoaXMuc2hvd0FkZEJ0biA9dGhpcy5wbGFuTGlzdFswXS5zaG93QWRkQnRuOw0KICAgICAgICB0aGlzLnNob3dDYW5jZWxCdG4gPXRoaXMucGxhbkxpc3RbMF0uc2hvd0NhbmNlbEJ0bjsNCiAgICAgICAgdGhpcy50b3RhbCA9IHJlc3BvbnNlLnRvdGFsOw0KICAgICAgICB0aGlzLmxvYWRpbmcgPSBmYWxzZTsNCiAgICAgIH0pOw0KICAgIH0sDQogICAgLy8g5Y+W5raI5oyJ6ZKuDQogICAgY2FuY2VsKCkgew0KICAgICAgdGhpcy5vcGVuID0gZmFsc2U7DQogICAgICB0aGlzLnJlc2V0KCk7DQogICAgfSwNCiAgICANCiAgICAvKiog5pCc57Si5oyJ6ZKu5pON5L2cICovDQogICAgaGFuZGxlUXVlcnkoKSB7DQogICAgICB0aGlzLnF1ZXJ5UGFyYW1zLnBhZ2VOdW0gPSAxOw0KICAgICAgdGhpcy5nZXRMaXN0KCk7DQogICAgfSwNCiAgICAvKiog6YeN572u5oyJ6ZKu5pON5L2cICovDQogICAgcmVzZXRRdWVyeSgpIHsNCiAgICAgIHRoaXMucmVzZXRGb3JtKCJxdWVyeUZvcm0iKTsNCiAgICAgIHRoaXMuaGFuZGxlUXVlcnkoKTsNCiAgICB9LA0KICAgIC8vIOWkmumAieahhumAieS4reaVsOaNrg0KICAgIGhhbmRsZVNlbGVjdGlvbkNoYW5nZShzZWxlY3Rpb24pIHsNCiAgICAgIHRoaXMuYXBwbHlOb3MgPSBzZWxlY3Rpb24ubWFwKGl0ZW0gPT4gaXRlbS5hcHBseU5vKQ0KICAgICAgdGhpcy5zaW5nbGUgPSBzZWxlY3Rpb24ubGVuZ3RoIT09MQ0KICAgICAgdGhpcy5tdWx0aXBsZSA9ICFzZWxlY3Rpb24ubGVuZ3RoDQogICAgfSwNCiAgICAvKiog5paw5aKe5oyJ6ZKu5pON5L2cICovDQogICAgaGFuZGxlQWRkKCkgew0KICAgICAgdGhpcy4kcm91dGVyLnB1c2goIi9sZWF2ZS9wbGFuL2VkaXQiKTsNCiAgICB9LA0KICAgIA0KICAgIC8qKiDkv67mlLnmjInpkq7mk43kvZwgKi8NCiAgICBoYW5kbGVVcGRhdGUocm93KSB7DQogICAgICBpZiAodGhpcy5hcHBseU5vcy5sZW5ndGggPiAwKSB7DQogICAgICAgIHRoaXMuJHJvdXRlci5wdXNoKGAvbGVhdmUvcGxhbi9lZGl0LyR7dGhpcy5hcHBseU5vc1swXX1gKTsNCiAgICAgIH0gZWxzZSB7DQogICAgICAgIHRoaXMuJHJvdXRlci5wdXNoKGAvbGVhdmUvcGxhbi9lZGl0LyR7cm93LmFwcGx5Tm99YCk7DQogICAgICB9DQogICAgfSwNCiAgICANCiAgICAvKiog5Yig6Zmk5oyJ6ZKu5pON5L2cICovDQogICAgaGFuZGxlRGVsZXRlKHJvdykgew0KICAgICAgY29uc3QgaWRzID0gcm93LmlkIHx8IHRoaXMuaWRzOw0KICAgICAgdGhpcy4kY29uZmlybSgn5piv5ZCm56Gu6K6k5Yig6Zmk5Ye66Zeo6K+B6K6h5YiS55Sz6K+357yW5Y+35Li6IicgKyBpZHMgKyAnIueahOaVsOaNrumhuT8nLCAi6K2m5ZGKIiwgew0KICAgICAgICAgIGNvbmZpcm1CdXR0b25UZXh0OiAi56Gu5a6aIiwNCiAgICAgICAgICBjYW5jZWxCdXR0b25UZXh0OiAi5Y+W5raIIiwNCiAgICAgICAgICB0eXBlOiAid2FybmluZyINCiAgICAgICAgfSkudGhlbihmdW5jdGlvbigpIHsNCiAgICAgICAgICByZXR1cm4gZGVsUGxhbihpZHMpOw0KICAgICAgICB9KS50aGVuKCgpID0+IHsNCiAgICAgICAgICB0aGlzLmdldExpc3QoKTsNCiAgICAgICAgICB0aGlzLm1zZ1N1Y2Nlc3MoIuWIoOmZpOaIkOWKnyIpOw0KICAgICAgICB9KQ0KICAgIH0sDQogICAgLyoqIOWvvOWHuuaMiemSruaTjeS9nCAqLw0KICAgIGhhbmRsZUV4cG9ydCgpIHsNCiAgICAgIGNvbnN0IHF1ZXJ5UGFyYW1zID0gdGhpcy5xdWVyeVBhcmFtczsNCiAgICAgIHRoaXMuJGNvbmZpcm0oJ+aYr+WQpuehruiupOWvvOWHuuaJgOacieWHuumXqOivgeiuoeWIkueUs+ivt+aVsOaNrumhuT8nLCAi6K2m5ZGKIiwgew0KICAgICAgICAgIGNvbmZpcm1CdXR0b25UZXh0OiAi56Gu5a6aIiwNCiAgICAgICAgICBjYW5jZWxCdXR0b25UZXh0OiAi5Y+W5raIIiwNCiAgICAgICAgICB0eXBlOiAid2FybmluZyINCiAgICAgICAgfSkudGhlbihmdW5jdGlvbigpIHsNCiAgICAgICAgICByZXR1cm4gZXhwb3J0UGxhbihxdWVyeVBhcmFtcyk7DQogICAgICAgIH0pLnRoZW4ocmVzcG9uc2UgPT4gew0KICAgICAgICAgIHRoaXMuZG93bmxvYWQocmVzcG9uc2UubXNnKTsNCiAgICAgICAgfSkNCiAgICB9LA0KICAgIGhhbmRsZURldGFpbChyb3cpIHsNCiAgICAgIC8vIOi3s+i9rOWIsOivpuaDhemhte+8jOW5tuS8oOmAkklE5Y+C5pWwDQogICAgICB0aGlzLiRyb3V0ZXIucHVzaCh7DQogICAgICAgIHBhdGg6IGAvbGVhdmUvcGxhbi9kZXRhaWwvJHtyb3cuYXBwbHlOb31gDQogICAgICB9KTsNCiAgICB9LA0KICAgIC8vIOiuoeWIkui/lOWbnuaXtumXtOiMg+WbtOWPkeeUn+WPmOWMlg0KICAgIGhhbmRsZVBsYW5SZXR1cm5UaW1lQ2hhbmdlKHZhbCkgew0KICAgICAgaWYgKHZhbCkgew0KICAgICAgICB0aGlzLnF1ZXJ5UGFyYW1zLnBsYW5SZXR1cm5TdGFydFRpbWUgPSB2YWxbMF07DQogICAgICAgIHRoaXMucXVlcnlQYXJhbXMucGxhblJldHVybkVuZFRpbWUgPSB2YWxbMV07DQogICAgICB9IGVsc2Ugew0KICAgICAgICB0aGlzLnF1ZXJ5UGFyYW1zLnBsYW5SZXR1cm5TdGFydFRpbWUgPSBudWxsOw0KICAgICAgICB0aGlzLnF1ZXJ5UGFyYW1zLnBsYW5SZXR1cm5FbmRUaW1lID0gbnVsbDsNCiAgICAgIH0NCiAgICB9LA0KICAgIA0KICAgIC8vIOeUs+ivt+acieaViOacn+iMg+WbtOWPkeeUn+WPmOWMlg0KICAgIGhhbmRsZUV4cGlyZVRpbWVDaGFuZ2UodmFsKSB7DQogICAgICBpZiAodmFsKSB7DQogICAgICAgIHRoaXMucXVlcnlQYXJhbXMuZXhwaXJlU3RhcnRUaW1lID0gdmFsWzBdOw0KICAgICAgICB0aGlzLnF1ZXJ5UGFyYW1zLmV4cGlyZUVuZFRpbWUgPSB2YWxbMV07DQogICAgICB9IGVsc2Ugew0KICAgICAgICB0aGlzLnF1ZXJ5UGFyYW1zLmV4cGlyZVN0YXJ0VGltZSA9IG51bGw7DQogICAgICAgIHRoaXMucXVlcnlQYXJhbXMuZXhwaXJlRW5kVGltZSA9IG51bGw7DQogICAgICB9DQogICAgfSwNCiAgICANCiAgICAvLyDnlLPor7fml7bpl7TojIPlm7Tlj5HnlJ/lj5jljJYNCiAgICBoYW5kbGVBcHBseVRpbWVDaGFuZ2UodmFsKSB7DQogICAgICBpZiAodmFsKSB7DQogICAgICAgIHRoaXMucXVlcnlQYXJhbXMuYXBwbHlTdGFydFRpbWUgPSB2YWxbMF07DQogICAgICAgIHRoaXMucXVlcnlQYXJhbXMuYXBwbHlFbmRUaW1lID0gdmFsWzFdOw0KICAgICAgfSBlbHNlIHsNCiAgICAgICAgdGhpcy5xdWVyeVBhcmFtcy5hcHBseVN0YXJ0VGltZSA9IG51bGw7DQogICAgICAgIHRoaXMucXVlcnlQYXJhbXMuYXBwbHlFbmRUaW1lID0gbnVsbDsNCiAgICAgIH0NCiAgICB9LA0KICAgIA0KICAgIC8qKiDkvZzlup/mjInpkq7mk43kvZwgKi8NCiAgICBoYW5kbGVJbnZhbGlkYXRlKHJvdykgew0KICAgICAgLy8gVE9ETzog5a6e546w5L2c5bqf5Yqf6IO9DQogICAgfSwNCiAgICANCiAgICAvKiog5omT5Y2w55Sz6K+35Y2V5oyJ6ZKu5pON5L2cICovDQogICAgaGFuZGxlUHJpbnRBcHBsaWNhdGlvbihyb3cpIHsNCiAgICAgIC8vIFRPRE86IOWunueOsOaJk+WNsOeUs+ivt+WNleWKn+iDvQ0KICAgIH0sDQogICAgDQogICAgLyoqIOaJk+WNsOeJqeaWmea4heWNleaMiemSruaTjeS9nCAqLw0KICAgIGhhbmRsZVByaW50TWF0ZXJpYWxMaXN0KHJvdykgew0KICAgICAgLy8gVE9ETzog5a6e546w5omT5Y2w54mp5paZ5riF5Y2V5Yqf6IO9DQogICAgfSwNCiAgICANCiAgICAvKiog5omT5Y2w5Ye66Zeo6K+B5oyJ6ZKu5pON5L2cICovDQogICAgaGFuZGxlUHJpbnRQZXJtaXQocm93KSB7DQogICAgICAvLyBUT0RPOiDlrp7njrDmiZPljbDlh7rpl6jor4Hlip/og70NCiAgICB9LA0KICB9DQp9Ow0K"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA0VA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/leave/plan", "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <el-form :model=\"queryParams\" ref=\"queryForm\" :inline=\"true\" v-show=\"showSearch\" label-width=\"80px\">\r\n      <el-row :gutter=\"30\">\r\n        <el-col :span=\"6\">\r\n          <el-form-item label=\"计划号\" prop=\"planNo\">\r\n            <el-input\r\n              v-model=\"queryParams.planNo\"\r\n              placeholder=\"请输入计划号\"\r\n              clearable\r\n              size=\"small\"\r\n              @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n        </el-col>\r\n        <el-col :span=\"6\">\r\n          <el-form-item label=\"计划类型\" prop=\"planType\">\r\n            <el-select v-model=\"queryParams.planType\" placeholder=\"请选择计划类型\" clearable size=\"small\" style=\"width: 100%\">\r\n              <el-option label=\"出厂不返回\" value=\"1\" />\r\n              <el-option label=\"出厂返回\" value=\"2\" />\r\n              <el-option label=\"跨区调拨\" value=\"3\" />\r\n              <el-option label=\"退货申请\" value=\"4\" />\r\n            </el-select>\r\n          </el-form-item>\r\n        </el-col>\r\n        \r\n        <el-col :span=\"6\">\r\n          <el-form-item label=\"是否计量\" prop=\"measureFlag\">\r\n            <el-select v-model=\"queryParams.measureFlag\" placeholder=\"请选择是否计量\" clearable size=\"small\" style=\"width: 100%\">\r\n              <el-option label=\"计量\" value=\"1\" />\r\n              <el-option label=\"不计量\" value=\"0\" />\r\n            </el-select>\r\n          </el-form-item>\r\n        </el-col>\r\n\r\n        <el-col :span=\"6\">\r\n          <el-form-item label=\"申请单位\" prop=\"sourceCompany\">\r\n            <el-input\r\n              v-model=\"queryParams.sourceCompany\"\r\n              placeholder=\"请输入申请单位\"\r\n              clearable\r\n              size=\"small\"\r\n              @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n        </el-col>\r\n\r\n        <el-col :span=\"6\">\r\n          <el-form-item label=\"收货单位\" prop=\"receiveCompany\">\r\n            <el-input\r\n              v-model=\"queryParams.receiveCompany\"\r\n              placeholder=\"请输入收货单位\"\r\n              clearable\r\n              size=\"small\"\r\n              @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n        </el-col>\r\n\r\n        <el-col :span=\"6\">\r\n          <el-form-item label=\"返回单位\" prop=\"targetCompany\">\r\n            <el-input\r\n              v-model=\"queryParams.targetCompany\"\r\n              placeholder=\"请输入返回单位\"\r\n              clearable\r\n              size=\"small\"\r\n              @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n        </el-col>\r\n\r\n        <!-- <el-col :span=\"6\">\r\n          <el-form-item label=\"物资专管员\" prop=\"specialManager\">\r\n            <el-input\r\n              v-model=\"queryParams.specialManager\"\r\n              placeholder=\"请输入物资专管员\"\r\n              clearable\r\n              size=\"small\"\r\n              @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n        </el-col> -->\r\n\r\n        <el-col :span=\"6\">\r\n          <el-form-item label=\"计划状态\" prop=\"planStatus\">\r\n            <el-select v-model=\"queryParams.planStatus\" placeholder=\"请选择计划状态\" clearable size=\"small\" style=\"width: 100%\">\r\n              <el-option label=\"待分厂审批\" value=\"1\" />\r\n              <el-option label=\"待分厂复审\" value=\"2\" />\r\n              <el-option label=\"待生产指挥中心审批\" value=\"3\" />\r\n              <el-option label=\"审批完成\" value=\"4\" />\r\n              <el-option label=\"已出厂\" value=\"5\" />\r\n              <el-option label=\"部分收货\" value=\"6\" />\r\n              <el-option label=\"已完成\" value=\"7\" />\r\n              <el-option label=\"驳回\" value=\"11\" />\r\n              <el-option label=\"废弃\" value=\"12\" />\r\n              <el-option label=\"过期\" value=\"13\" />\r\n            </el-select>\r\n          </el-form-item>\r\n        </el-col>\r\n\r\n        <el-col :span=\"12\">\r\n          <el-form-item label=\"返回时间\" prop=\"planReturnTime\">\r\n            <el-date-picker \r\n              clearable \r\n              size=\"small\" \r\n              style=\"width: 100%\"\r\n              v-model=\"daterangePlanReturn\"\r\n              type=\"daterange\"\r\n              value-format=\"yyyy-MM-dd\"\r\n              range-separator=\"至\"\r\n              start-placeholder=\"开始日期\"\r\n              end-placeholder=\"结束日期\"\r\n              @change=\"handlePlanReturnTimeChange\">\r\n            </el-date-picker>\r\n          </el-form-item>\r\n        </el-col>\r\n\r\n        <el-col :span=\"12\">\r\n          <el-form-item label=\"有效期\" prop=\"expireTime\">\r\n            <el-date-picker \r\n              clearable \r\n              size=\"small\" \r\n              style=\"width: 100%\"\r\n              v-model=\"daterangeExpire\"\r\n              type=\"daterange\"\r\n              value-format=\"yyyy-MM-dd\"\r\n              range-separator=\"至\"\r\n              start-placeholder=\"开始日期\"\r\n              end-placeholder=\"结束日期\"\r\n              @change=\"handleExpireTimeChange\">\r\n            </el-date-picker>\r\n          </el-form-item>\r\n        </el-col>\r\n        \r\n        <el-col :span=\"12\">\r\n          <el-form-item label=\"申请时间\" prop=\"applyTime\">\r\n            <el-date-picker \r\n              clearable \r\n              size=\"small\" \r\n              style=\"width: 100%\"\r\n              v-model=\"daterangeApply\"\r\n              type=\"daterange\"\r\n              value-format=\"yyyy-MM-dd\"\r\n              range-separator=\"至\"\r\n              start-placeholder=\"开始日期\"\r\n              end-placeholder=\"结束日期\"\r\n              @change=\"handleApplyTimeChange\">\r\n            </el-date-picker>\r\n          </el-form-item>\r\n        </el-col>\r\n        \r\n        <el-col :span=\"24\" style=\"text-align: center; margin-top: 10px\">\r\n          <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleQuery\">搜索</el-button>\r\n          <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\r\n        </el-col>\r\n      </el-row>\r\n    </el-form>\r\n\r\n    <el-row :gutter=\"10\" class=\"mb8\">\r\n      <el-col :span=\"1.5\">\r\n        <el-button v-if=\"this.showAddBtn\"\r\n          type=\"primary\"\r\n          icon=\"el-icon-plus\"\r\n          size=\"mini\"\r\n          @click=\"handleAdd\"\r\n        >新增</el-button>\r\n      </el-col>\r\n      <!-- <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"success\"\r\n          icon=\"el-icon-edit\"\r\n          size=\"mini\"\r\n          :disabled=\"single\"\r\n          @click=\"handleUpdate\"\r\n        >修改</el-button>\r\n      </el-col> -->\r\n      <el-col :span=\"1.5\">\r\n        <el-button v-if=\"this.showCancelBtn\"\r\n          type=\"danger\"\r\n          icon=\"el-icon-close\"\r\n          size=\"mini\"\r\n          :disabled=\"single\"\r\n          @click=\"handleInvalidate\"\r\n        >作废</el-button>\r\n      </el-col>\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"warning\"\r\n          icon=\"el-icon-printer\"\r\n          size=\"mini\"\r\n          :disabled=\"single\"\r\n          @click=\"handlePrintApplication\"\r\n        >打印申请单</el-button>\r\n      </el-col>\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"info\"\r\n          icon=\"el-icon-printer\"\r\n          size=\"mini\"\r\n          :disabled=\"single\"\r\n          @click=\"handlePrintMaterialList\"\r\n        >打印物料清单</el-button>\r\n      </el-col>\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"primary\"\r\n          plain\r\n          icon=\"el-icon-printer\"\r\n          size=\"mini\"\r\n          :disabled=\"single\"\r\n          @click=\"handlePrintPermit\"\r\n        >打印出门证</el-button>\r\n      </el-col>\r\n      <!-- <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"danger\"\r\n          icon=\"el-icon-delete\"\r\n          size=\"mini\"\r\n          :disabled=\"multiple\"\r\n          @click=\"handleDelete\"\r\n        >删除</el-button>\r\n      </el-col>\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"warning\"\r\n          icon=\"el-icon-download\"\r\n          size=\"mini\"\r\n          @click=\"handleExport\"\r\n        >导出</el-button>\r\n      </el-col> -->\r\n\t  <right-toolbar :showSearch.sync=\"showSearch\" @queryTable=\"getList\"></right-toolbar>\r\n    </el-row>\r\n\r\n    <el-table v-loading=\"loading\" :data=\"planList\" @selection-change=\"handleSelectionChange\" class=\"plan-table\">\r\n      <el-table-column type=\"selection\" width=\"55\" align=\"center\" />\r\n      <el-table-column label=\"申请编号\" align=\"center\" prop=\"applyNo\" width=\"180\" />\r\n      <el-table-column label=\"计划号\" align=\"center\" prop=\"planNo\" />\r\n      <el-table-column label=\"计划类型\" align=\"center\" prop=\"planType\" width=\"120\" >\r\n      <!-- tag标签 -->\r\n      <template slot-scope=\"scope\">\r\n        <el-tag v-if=\"scope.row.planType == '1'\" type=\"success\">出厂不返回</el-tag>\r\n        <el-tag v-if=\"scope.row.planType == '2'\" type=\"warning\">出厂返回</el-tag>\r\n        <el-tag v-if=\"scope.row.planType == '3'\" type=\"info\">跨区调拨</el-tag>\r\n        <el-tag v-if=\"scope.row.planType == '4'\" type=\"danger\">退货申请</el-tag>\r\n      </template>\r\n\r\n      </el-table-column>\r\n      <el-table-column label=\"申请人\" align=\"center\" prop=\"applyUserName\" />\r\n      <el-table-column label=\"申请单位\" align=\"center\" prop=\"sourceCompany\" width=\"200\" />\r\n      <el-table-column label=\"是否计量\" align=\"center\" prop=\"measureFlag\" >\r\n        <!-- tag标签 -->\r\n        <template slot-scope=\"scope\">\r\n          <el-tag v-if=\"scope.row.measureFlag == '1'\" type=\"success\">计量</el-tag>\r\n          <el-tag v-if=\"scope.row.measureFlag == '0'\" type=\"danger\">不计量</el-tag>\r\n        </template>\r\n      </el-table-column>\r\n      <!-- <el-table-column label=\"计划量\" align=\"center\" prop=\"plannedAmount\" /> -->\r\n      <el-table-column label=\"收货单位\" align=\"center\" prop=\"receiveCompany\" width=\"200\" />\r\n      <el-table-column label=\"返回单位\" align=\"center\" prop=\"targetCompany\" />\r\n      \r\n      <el-table-column label=\"申请有效期\" align=\"center\" prop=\"expireTime\" width=\"180\">\r\n        <template slot-scope=\"scope\">\r\n          <span>{{ parseTime(scope.row.expireTime, '{y}-{m}-{d}') }}</span>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"出厂原因\" align=\"center\" prop=\"reason\" />\r\n      <el-table-column label=\"计划状态\" align=\"center\" prop=\"planStatus\" >\r\n        <!-- tag标签 -->\r\n        <template slot-scope=\"scope\">\r\n          <el-tag v-if=\"scope.row.planStatus == '1'\" type=\"warning\">待分厂审批</el-tag>\r\n          <el-tag v-if=\"scope.row.planStatus == '2'\" type=\"warning\">待分厂复审</el-tag>\r\n          <el-tag v-if=\"scope.row.planStatus == '3'\" type=\"warning\">待生产指挥中心审批</el-tag>\r\n          <el-tag v-if=\"scope.row.planStatus == '4'\" type=\"success\">审批完成</el-tag>\r\n          <el-tag v-if=\"scope.row.planStatus == '5'\" type=\"success\">已出厂</el-tag>\r\n          <el-tag v-if=\"scope.row.planStatus == '6'\" type=\"info\">部分收货</el-tag>\r\n          <el-tag v-if=\"scope.row.planStatus == '7'\" type=\"success\">已完成</el-tag>\r\n          <el-tag v-if=\"scope.row.planStatus == '11'\" type=\"danger\">驳回</el-tag>\r\n          <el-tag v-if=\"scope.row.planStatus == '12'\" type=\"danger\">废弃</el-tag>\r\n          <el-tag v-if=\"scope.row.planStatus == '13'\" type=\"danger\">过期</el-tag>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"申请时间\" align=\"center\" prop=\"applyTime\" width=\"180\">\r\n        <template slot-scope=\"scope\">\r\n          <span>{{ parseTime(scope.row.applyTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>\r\n        </template>\r\n      </el-table-column>\r\n      \r\n      <el-table-column label=\"分厂审核\" align=\"center\" prop=\"factoryApproveFlag\" >\r\n        <template slot-scope=\"scope\">\r\n          <el-tag v-if=\"scope.row.factoryApproveFlag === '1'\" type=\"success\">同意</el-tag>\r\n          <el-tag v-else-if=\"scope.row.factoryApproveFlag === '0'\" type=\"danger\">拒绝</el-tag>\r\n          <span v-else>-</span>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"分厂复审\" align=\"center\" prop=\"factorySecApproveFlag\" >\r\n        <template slot-scope=\"scope\">\r\n          <el-tag v-if=\"scope.row.factorySecApproveFlag === '1'\" type=\"success\">同意</el-tag>\r\n          <el-tag v-else-if=\"scope.row.factorySecApproveFlag === '0'\" type=\"danger\">拒绝</el-tag>\r\n          <span v-else>-</span>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"生产指挥中心审核\" align=\"center\" prop=\"centerApproveFlag\" >\r\n        <template slot-scope=\"scope\">\r\n          <el-tag v-if=\"scope.row.centerApproveFlag === '1'\" type=\"success\">同意</el-tag>\r\n          <el-tag v-else-if=\"scope.row.centerApproveFlag === '0'\" type=\"danger\">拒绝</el-tag>\r\n          <span v-else>-</span>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"操作\" align=\"center\" class-name=\"small-padding fixed-width\" fixed=\"right\">\r\n        <template slot-scope=\"scope\">\r\n          <el-button\r\n            v-if=\"scope.row.showModifyBtn\"\r\n            size=\"mini\"\r\n            type=\"text\"\r\n            icon=\"el-icon-edit\"\r\n            @click=\"handleUpdate(scope.row)\"\r\n          >修改</el-button>\r\n          <!-- <el-button\r\n            size=\"mini\"\r\n            type=\"text\"\r\n            icon=\"el-icon-delete\"\r\n            @click=\"handleDelete(scope.row)\"\r\n          >删除</el-button> -->\r\n          <el-button\r\n            size=\"mini\"\r\n            type=\"text\"\r\n            icon=\"el-icon-view\"\r\n            @click=\"handleDetail(scope.row)\"\r\n          >详情</el-button>\r\n        </template>\r\n      </el-table-column>\r\n    </el-table>\r\n\r\n    <pagination\r\n      v-show=\"total>0\"\r\n      :total=\"total\"\r\n      :page.sync=\"queryParams.pageNum\"\r\n      :limit.sync=\"queryParams.pageSize\"\r\n      @pagination=\"getList\"\r\n    />\r\n\r\n    \r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { listPlan, getPlan, delPlan, addPlan, updatePlan, exportPlan } from \"@/api/leave/plan\";\r\nimport Editor from '@/components/Editor';\r\n\r\nexport default {\r\n  name: \"Plan\",\r\n  components: {\r\n    Editor,\r\n  },\r\n  data() {\r\n    return {\r\n      // 遮罩层\r\n      loading: true,\r\n      // 选中数组\r\n      applyNos: [],\r\n      // 非单个禁用\r\n      single: true,\r\n      // 非多个禁用\r\n      multiple: true,\r\n      // 显示搜索条件\r\n      showSearch: true,\r\n      // 总条数\r\n      total: 0,\r\n      // 出门证计划申请表格数据\r\n      planList: [],\r\n      // 日期范围\r\n      daterangePlanReturn: [],\r\n      daterangeExpire: [],\r\n      daterangeApply: [],\r\n      // 弹出层标题\r\n      title: \"\",\r\n      // 是否显示弹出层\r\n      open: false,\r\n      //是否显示新增按钮\r\n      showAddBtn:false,\r\n      //是否显示废弃按钮\r\n      showCancelBtn:false,\r\n      // 查询参数\r\n      queryParams: {\r\n        pageNum: 1,\r\n        pageSize: 10,\r\n        planNo: null,\r\n        planType: null,\r\n        businessCategory: null,\r\n        measureFlag: null,\r\n        plannedAmount: null,\r\n        receiveCompany: null,\r\n        receiveCompanyCode: null,\r\n        targetCompany: null,\r\n        targetCompanyCode: null,\r\n        sourceCompany: null,\r\n        sourceCompanyCode: null,\r\n        planReturnStartTime: null,\r\n        planReturnEndTime: null,\r\n        realReturnTime: null,\r\n        monitor: null,\r\n        specialManager: null,\r\n        expireStartTime: null,\r\n        expireEndTime: null,\r\n        reason: null,\r\n        itemType: null,\r\n        planStatus: null,\r\n        applyStartTime: null,\r\n        applyEndTime: null,\r\n        applyWorkNo: null,\r\n        factoryApproveTime: null,\r\n        factoryApproveWorkNo: null,\r\n        factoryApproveFlag: null,\r\n        factoryApproveContent: null,\r\n        factorySecApproveFlag: null,\r\n        factorySecApproveTime: null,\r\n        factorySecApproveWorkNo: null,\r\n        factorySecApproveContent: null,\r\n        centerApproveTime: null,\r\n        centerApproveWorkNo: null,\r\n        centerApproveFlag: null,\r\n        centerApproveContent: null,\r\n        applyFileUrl: null,\r\n      },\r\n      // 表单参数\r\n      form: {},\r\n      // 表单校验\r\n      rules: {\r\n      }\r\n    };\r\n  },\r\n  created() {\r\n    this.getList();\r\n  },\r\n  watch: {\r\n  '$route.query.refresh': {\r\n    immediate: true,\r\n    handler(newVal) {\r\n      if (newVal) {\r\n        this.getList(); // 刷新列表数据的方法\r\n          }\r\n        }\r\n      }\r\n    },\r\n  methods: {\r\n    /** 查询出门证计划申请列表 */\r\n    getList() {\r\n      this.loading = true;\r\n      listPlan(this.queryParams).then(response => {\r\n        this.planList = response.rows;\r\n        console.log(this.planList);\r\n        this.showAddBtn =this.planList[0].showAddBtn;\r\n        this.showCancelBtn =this.planList[0].showCancelBtn;\r\n        this.total = response.total;\r\n        this.loading = false;\r\n      });\r\n    },\r\n    // 取消按钮\r\n    cancel() {\r\n      this.open = false;\r\n      this.reset();\r\n    },\r\n    \r\n    /** 搜索按钮操作 */\r\n    handleQuery() {\r\n      this.queryParams.pageNum = 1;\r\n      this.getList();\r\n    },\r\n    /** 重置按钮操作 */\r\n    resetQuery() {\r\n      this.resetForm(\"queryForm\");\r\n      this.handleQuery();\r\n    },\r\n    // 多选框选中数据\r\n    handleSelectionChange(selection) {\r\n      this.applyNos = selection.map(item => item.applyNo)\r\n      this.single = selection.length!==1\r\n      this.multiple = !selection.length\r\n    },\r\n    /** 新增按钮操作 */\r\n    handleAdd() {\r\n      this.$router.push(\"/leave/plan/edit\");\r\n    },\r\n    \r\n    /** 修改按钮操作 */\r\n    handleUpdate(row) {\r\n      if (this.applyNos.length > 0) {\r\n        this.$router.push(`/leave/plan/edit/${this.applyNos[0]}`);\r\n      } else {\r\n        this.$router.push(`/leave/plan/edit/${row.applyNo}`);\r\n      }\r\n    },\r\n    \r\n    /** 删除按钮操作 */\r\n    handleDelete(row) {\r\n      const ids = row.id || this.ids;\r\n      this.$confirm('是否确认删除出门证计划申请编号为\"' + ids + '\"的数据项?', \"警告\", {\r\n          confirmButtonText: \"确定\",\r\n          cancelButtonText: \"取消\",\r\n          type: \"warning\"\r\n        }).then(function() {\r\n          return delPlan(ids);\r\n        }).then(() => {\r\n          this.getList();\r\n          this.msgSuccess(\"删除成功\");\r\n        })\r\n    },\r\n    /** 导出按钮操作 */\r\n    handleExport() {\r\n      const queryParams = this.queryParams;\r\n      this.$confirm('是否确认导出所有出门证计划申请数据项?', \"警告\", {\r\n          confirmButtonText: \"确定\",\r\n          cancelButtonText: \"取消\",\r\n          type: \"warning\"\r\n        }).then(function() {\r\n          return exportPlan(queryParams);\r\n        }).then(response => {\r\n          this.download(response.msg);\r\n        })\r\n    },\r\n    handleDetail(row) {\r\n      // 跳转到详情页，并传递ID参数\r\n      this.$router.push({\r\n        path: `/leave/plan/detail/${row.applyNo}`\r\n      });\r\n    },\r\n    // 计划返回时间范围发生变化\r\n    handlePlanReturnTimeChange(val) {\r\n      if (val) {\r\n        this.queryParams.planReturnStartTime = val[0];\r\n        this.queryParams.planReturnEndTime = val[1];\r\n      } else {\r\n        this.queryParams.planReturnStartTime = null;\r\n        this.queryParams.planReturnEndTime = null;\r\n      }\r\n    },\r\n    \r\n    // 申请有效期范围发生变化\r\n    handleExpireTimeChange(val) {\r\n      if (val) {\r\n        this.queryParams.expireStartTime = val[0];\r\n        this.queryParams.expireEndTime = val[1];\r\n      } else {\r\n        this.queryParams.expireStartTime = null;\r\n        this.queryParams.expireEndTime = null;\r\n      }\r\n    },\r\n    \r\n    // 申请时间范围发生变化\r\n    handleApplyTimeChange(val) {\r\n      if (val) {\r\n        this.queryParams.applyStartTime = val[0];\r\n        this.queryParams.applyEndTime = val[1];\r\n      } else {\r\n        this.queryParams.applyStartTime = null;\r\n        this.queryParams.applyEndTime = null;\r\n      }\r\n    },\r\n    \r\n    /** 作废按钮操作 */\r\n    handleInvalidate(row) {\r\n      // TODO: 实现作废功能\r\n    },\r\n    \r\n    /** 打印申请单按钮操作 */\r\n    handlePrintApplication(row) {\r\n      // TODO: 实现打印申请单功能\r\n    },\r\n    \r\n    /** 打印物料清单按钮操作 */\r\n    handlePrintMaterialList(row) {\r\n      // TODO: 实现打印物料清单功能\r\n    },\r\n    \r\n    /** 打印出门证按钮操作 */\r\n    handlePrintPermit(row) {\r\n      // TODO: 实现打印出门证功能\r\n    },\r\n  }\r\n};\r\n</script>\r\n\r\n<style>\r\n.mb8 {\r\n  margin-bottom: 8px;\r\n}\r\n\r\n.plan-table {\r\n  width: 100%;\r\n}\r\n\r\n.plan-table th {\r\n  background-color: #f5f7fa;\r\n  color: #606266;\r\n  font-weight: 600;\r\n  text-align: center !important;\r\n}\r\n\r\n.plan-table td {\r\n  text-align: center !important;\r\n}\r\n\r\n/* 表单样式调整 */\r\n.el-form-item {\r\n  margin-bottom: 15px;\r\n  width: 100%;\r\n}\r\n\r\n.el-form-item__content {\r\n  width: calc(100% - 80px);\r\n}\r\n\r\n.el-date-editor.el-input {\r\n  width: 100%;\r\n}\r\n\r\n.el-date-editor--daterange.el-input__inner {\r\n  width: 100% !important;\r\n}\r\n</style>\r\n"]}]}