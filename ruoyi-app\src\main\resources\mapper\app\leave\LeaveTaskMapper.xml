<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.app.leave.mapper.LeaveTaskMapper">
    
    <resultMap type="LeaveTask" id="LeaveTaskResult">
        <result property="id"    column="id"    />
        <result property="taskNo"    column="task_no"    />
        <result property="applyNo"    column="apply_no"    />
        <result property="taskType"    column="task_type"    />
        <result property="planNo"    column="plan_no"    />
        <result property="taskStatus"    column="task_status"    />
        <result property="leaveDoor"    column="leave_door"    />
        <result property="leaveTime"    column="leave_time"    />
        <result property="enterDoor"    column="enter_door"    />
        <result property="enterTime"    column="enter_time"    />
        <result property="loadingWorkNo"    column="loading_work_no"    />
        <result property="loadingTime"    column="loading_time"    />
        <result property="unloadingWorkNo"    column="unloading_work_no"    />
        <result property="unloadingTime"    column="unloading_time"    />
        <result property="driverName"    column="driver_name"    />
        <result property="sex"    column="sex"    />
        <result property="mobilePhone"    column="mobile_phone"    />
        <result property="idCardNo"    column="id_card_no"    />
        <result property="carNum"    column="car_num"    />
        <result property="vehicleEmissionStandards"    column="vehicle_emission_standards"    />
        <result property="faceImg"    column="face_img"    />
        <result property="drivingLicenseImg"    column="driving_license_img"    />
        <result property="driverLicenseImg"    column="driver_license_img"    />
        <result property="companyName"    column="company_name"    />
        <result property="gross"    column="gross"    />
        <result property="tare"    column="tare"    />
        <result property="netWeight"    column="net_weight"    />
        <result property="netWeightTime"    column="net_weight_time"    />
        <result property="grossTime"    column="gross_time"    />
        <result property="tareTime"    column="tare_time"    />
        <result property="secGross"    column="sec_gross"    />
        <result property="secTare"    column="sec_tare"    />
        <result property="secNetWeight"    column="sec_net_weight"    />
        <result property="secNetWeightTime"    column="sec_net_weight_time"    />
        <result property="secTareTime"    column="sec_tare_time"    />
        <result property="secGrossTime"    column="sec_gross_time"    />
        <result property="createTime"    column="create_time"    />
        <result property="createBy"    column="create_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="trailerNumber"    column="trailer_number"    />
        <result property="trailerId"    column="trailer_id"    />
        <result property="axisType"    column="axis_type"    />
        <result property="driverWeight"    column="driver_weight"    />
        <result property="maxWeight"    column="max_weight"    />
        <result property="engineNumber"    column="engine_number"    />
        <result property="vinNumber"    column="vin_number"    />
        <result property="licensePlateColor"    column="license_plate_color"    />
        <result property="carId"    column="car_id"    />
        <result property="spec1Length"    column="spec1_length"    />
        <result property="spec2Width"    column="spec2_width"    />
        <result property="totals"    column="totals"    />
        <result property="processType"    column="process_type"    />
        <result property="heatNo"    column="heat_no"    />
        <result property="steelGrade"    column="steel_grade"    />
        <result property="axles"    column="axles"    />
        <result property="remark"    column="remark"    />
        <result property="isDirectSupply"    column="is_direct_supply"    />
        <result property="stockOutSpec1Length"    column="stock_out_spec1_length"    />
        <result property="stockOutSpec2Width"    column="stock_out_spec2_width"    />
        <result property="stockOutTotals"    column="stock_out_totals"    />
        <result property="stockOutProcessType"    column="stock_out_process_type"    />
        <result property="stockOutHeatNo"    column="stock_out_heat_no"    />
        <result property="stockOutSteelGrade"    column="stock_out_steel_grade"    />
        <result property="stockOutAxles"    column="stock_out_axles"    />
        <result property="stockOutRemark"    column="stock_out_remark"    />
        <result property="directSupplyTaskNo"    column="direct_supply_task_no"    />
        <result property="deductWeight"    column="deduct_weight"    />
    </resultMap>

    <!-- 字段映射 -->
    <resultMap id="D_PROCESS_T_MAP" type="DProcessTMeasure">
        <id property="ID" column="ID"/>
        <result property="VALIDFLAG" column="VALIDFLAG"/>
        <result property="PROCESSNAME" column="PROCESSNAME"/>
        <result property="CREATEDATE" column="CREATEDATE"/>
        <result property="CREATEMAN" column="CREATEMAN"/>
        <result property="UPDATEDATE" column="UPDATEDATE"/>
        <result property="UPDATEMAN" column="UPDATEMAN"/>
    </resultMap>

    <select id="selectProcessTypeListByValidFlag" resultMap="D_PROCESS_T_MAP" parameterType="DProcessTMeasure">
        SELECT
            ID,
            VALIDFLAG,
            PROCESSNAME,
            CREATEDATE,
            CREATEMAN,
            UPDATEDATE,
            UPDATEMAN
        FROM D_PROCESS_T
        <where>
            <if test="searchValue != null and searchValue != ''"> and PROCESSNAME like '%' || #{searchValue} || '%'</if>
            and VALIDFLAG = 1
        </where>
    </select>

    <sql id="selectLeaveTaskVo">
        select id, task_no, apply_no, task_type, plan_no, task_status, leave_door, leave_time, enter_door, enter_time,
        loading_work_no, loading_time, unloading_work_no, unloading_time, driver_name, sex, mobile_phone, id_card_no,
        car_num, vehicle_emission_standards, face_img, driving_license_img, driver_license_img, company_name, gross,
        tare, net_weight, net_weight_time, gross_time, tare_time, sec_gross, sec_tare, sec_net_weight, sec_net_weight_time,
        sec_tare_time, sec_gross_time, create_time, create_by, update_time, update_by, trailer_number, trailer_id, axis_type,
        driver_weight, max_weight, engine_number, vin_number, license_plate_color, car_id, spec1_length, spec2_width, totals,
        process_type, heat_no, steel_grade, axles, deduct_weight, remark, is_direct_supply, stock_out_spec1_length,
        stock_out_spec2_width, stock_out_totals, stock_out_process_type, stock_out_heat_no, stock_out_steel_grade, stock_out_axles,
        stock_out_remark, direct_supply_task_no
        from leave_task
    </sql>

    <select id="selectByPlanNo" parameterType="String" resultMap="LeaveTaskResult">
        <include refid="selectLeaveTaskVo"/>
        where plan_no = #{planNo}
    </select>

    <select id="selectLeaveTaskList" parameterType="LeaveTask" resultMap="LeaveTaskResult">
        <include refid="selectLeaveTaskVo"/>
        <where>  
            <if test="taskNo != null  and taskNo != ''"> and task_no = #{taskNo}</if>
            <if test="applyNo != null  and applyNo != ''"> and apply_no = #{applyNo}</if>
            <if test="taskType != null"> and task_type = #{taskType}</if>
            <if test="planNo != null  and planNo != ''"> and plan_no = #{planNo}</if>
            <if test="taskStatus != null"> and task_status = #{taskStatus}</if>
            <if test="leaveDoor != null"> and leave_door = #{leaveDoor}</if>
            <if test="leaveTime != null"> and leave_time = #{leaveTime}</if>
            <if test="enterDoor != null"> and enter_door = #{enterDoor}</if>
            <if test="enterTime != null"> and enter_time = #{enterTime}</if>
            <if test="loadingWorkNo != null  and loadingWorkNo != ''"> and loading_work_no = #{loadingWorkNo}</if>
            <if test="loadingTime != null"> and loading_time = #{loadingTime}</if>
            <if test="unloadingWorkNo != null  and unloadingWorkNo != ''"> and unloading_work_no = #{unloadingWorkNo}</if>
            <if test="unloadingTime != null"> and unloading_time = #{unloadingTime}</if>
            <if test="driverName != null  and driverName != ''"> and driver_name like concat('%', #{driverName}, '%')</if>
            <if test="sex != null"> and sex = #{sex}</if>
            <if test="mobilePhone != null  and mobilePhone != ''"> and mobile_phone = #{mobilePhone}</if>
            <if test="idCardNo != null  and idCardNo != ''"> and id_card_no = #{idCardNo}</if>
            <if test="carNum != null  and carNum != ''"> and car_num = #{carNum}</if>
            <if test="vehicleEmissionStandards != null"> and vehicle_emission_standards = #{vehicleEmissionStandards}</if>
            <if test="faceImg != null  and faceImg != ''"> and face_img = #{faceImg}</if>
            <if test="drivingLicenseImg != null  and drivingLicenseImg != ''"> and driving_license_img = #{drivingLicenseImg}</if>
            <if test="driverLicenseImg != null  and driverLicenseImg != ''"> and driver_license_img = #{driverLicenseImg}</if>
            <if test="companyName != null  and companyName != ''"> and company_name like concat('%', #{companyName}, '%')</if>
            <if test="gross != null"> and gross = #{gross}</if>
            <if test="tare != null"> and tare = #{tare}</if>
            <if test="netWeight != null"> and net_weight = #{netWeight}</if>
            <if test="grossTime != null"> and gross_time = #{grossTime}</if>
            <if test="tareTime != null"> and tare_time = #{tareTime}</if>
            <if test="secGross != null"> and sec_gross = #{secGross}</if>
            <if test="secTare != null"> and sec_tare = #{secTare}</if>
            <if test="secNetWeight != null"> and sec_net_weight = #{secNetWeight}</if>
            <if test="secTareTime != null"> and sec_tare_time = #{secTareTime}</if>
            <if test="secGrossTime != null"> and sec_gross_time = #{secGrossTime}</if>
            <if test="trailerNumber != null  and trailerNumber != ''"> and trailer_number = #{trailerNumber}</if>
            <if test="trailerId != null  and trailerId != ''"> and trailer_id = #{trailerId}</if>
            <if test="axisType != null  and axisType != ''"> and axis_type = #{axisType}</if>
            <if test="driverWeight != null"> and driver_weight = #{driverWeight}</if>
            <if test="maxWeight != null"> and max_weight = #{maxWeight}</if>
            <if test="engineNumber != null  and engineNumber != ''"> and engine_number = #{engineNumber}</if>
            <if test="vinNumber != null  and vinNumber != ''"> and vin_number = #{vinNumber}</if>
            <if test="licensePlateColor != null  and licensePlateColor != ''"> and license_plate_color = #{licensePlateColor}</if>
            <if test="carId != null  and carId != ''"> and car_id = #{carId}</if>
            <if test="spec1Length != null"> and spec1_length = #{spec1Length}</if>
            <if test="spec2Width != null"> and spec2_width = #{spec2Width}</if>
            <if test="totals != null  and totals != ''"> and totals = #{totals}</if>
            <if test="processType != null  and processType != ''"> and process_type = #{processType}</if>
            <if test="heatNo != null  and heatNo != ''"> and heat_no = #{heatNo}</if>
            <if test="steelGrade != null  and steelGrade != ''"> and steel_grade = #{steelGrade}</if>
            <if test="axles != null  and axles != ''"> and axles = #{axles}</if>
            <if test="remark != null  and remark != ''"> and remark like concat('%', #{remark}, '%')</if>
            <if test="isDirectSupply != null"> and is_direct_supply = #{isDirectSupply}</if>
            <if test="stockOutSpec1Length != null"> and stock_out_spec1_length = #{stockOutSpec1Length}</if>
            <if test="stockOutSpec2Width != null"> and stock_out_spec2_width = #{stockOutSpec2Width}</if>
            <if test="stockOutTotals != null  and stockOutTotals != ''"> and stock_out_totals = #{stockOutTotals}</if>
            <if test="stockOutProcessType != null  and stockOutProcessType != ''"> and stock_out_process_type = #{stockOutProcessType}</if>
            <if test="stockOutHeatNo != null  and stockOutHeatNo != ''"> and stock_out_heat_no = #{stockOutHeatNo}</if>
            <if test="stockOutSteelGrade != null  and stockOutSteelGrade != ''"> and stock_out_steel_grade = #{stockOutSteelGrade}</if>
            <if test="stockOutAxles != null  and stockOutAxles != ''"> and stock_out_axles = #{stockOutAxles}</if>
            <if test="stockOutRemark != null  and stockOutRemark != ''"> and stock_out_remark like concat('%', #{stockOutRemark}, '%')</if>
            <if test="directSupplyTaskNo != null  and directSupplyTaskNo != ''"> and direct_supply_task_no = #{directSupplyTaskNo}</if>
        </where>
    </select>

    <select id="isAllowDispatch" parameterType="LeaveTask" resultMap="LeaveTaskResult">
        <include refid="selectLeaveTaskVo"/>
        <where>
            <if test="taskNo != null  and taskNo != ''"> and task_no = #{taskNo}</if>
            <if test="applyNo != null  and applyNo != ''"> and apply_no = #{applyNo}</if>
            <if test="taskType != null "> and task_type = #{taskType}</if>
            <if test="planNo != null  and planNo != ''"> and plan_no = #{planNo}</if>
            <if test="taskStatus != null "> and task_status != 9 </if>
            <if test="heatNo != null  and heatNo != ''"> and heat_no = #{heatNo}</if>
            <if test="steelGrade != null  and steelGrade != ''"> and steel_grade = #{steelGrade}</if>
            <if test="leaveDoor != null "> and leave_door = #{leaveDoor}</if>
            <if test="leaveTime != null "> and leave_time = #{leaveTime}</if>
            <if test="enterDoor != null "> and enter_door = #{enterDoor}</if>
            <if test="enterTime != null "> and enter_time = #{enterTime}</if>
            <if test="loadingWorkNo != null  and loadingWorkNo != ''"> and loading_work_no = #{loadingWorkNo}</if>
            <if test="loadingTime != null "> and loading_time = #{loadingTime}</if>
            <if test="unloadingWorkNo != null  and unloadingWorkNo != ''"> and unloading_work_no = #{unloadingWorkNo}</if>
            <if test="unloadingTime != null "> and unloading_time = #{unloadingTime}</if>
            <if test="driverName != null  and driverName != ''"> and driver_name like concat('%', #{driverName}, '%')</if>
            <if test="sex != null "> and sex = #{sex}</if>
            <if test="mobilePhone != null  and mobilePhone != ''"> and mobile_phone = #{mobilePhone}</if>
            <if test="idCardNo != null  and idCardNo != ''"> and id_card_no = #{idCardNo}</if>
            <if test="carNum != null  and carNum != ''"> and car_num = #{carNum}</if>
            <if test="vehicleEmissionStandards != null "> and vehicle_emission_standards = #{vehicleEmissionStandards}</if>
            <if test="faceImg != null  and faceImg != ''"> and face_img = #{faceImg}</if>
            <if test="drivingLicenseImg != null  and drivingLicenseImg != ''"> and driving_license_img = #{drivingLicenseImg}</if>
            <if test="driverLicenseImg != null  and driverLicenseImg != ''"> and driver_license_img = #{driverLicenseImg}</if>
            <if test="companyName != null  and companyName != ''"> and company_name like concat('%', #{companyName}, '%')</if>
            <if test="gross != null "> and gross = #{gross}</if>
            <if test="tare != null "> and tare = #{tare}</if>
            <if test="netWeight != null "> and net_weight = #{netWeight}</if>
            <if test="grossTime != null "> and gross_time = #{grossTime}</if>
            <if test="tareTime != null "> and tare_time = #{tareTime}</if>
            <if test="secGross != null "> and sec_gross = #{secGross}</if>
            <if test="secTare != null "> and sec_tare = #{secTare}</if>
            <if test="secNetWeight != null "> and sec_net_weight = #{secNetWeight}</if>
            <if test="secTareTime != null "> and sec_tare_time = #{secTareTime}</if>
            <if test="secGrossTime != null "> and sec_gross_time = #{secGrossTime}</if>
            <if test="licensePlateColor != null  and licensePlateColor != ''"> and license_plate_color = #{licensePlateColor}</if>
            <if test="carId != null  and carId != ''"> and car_id = #{carId}</if>
            <if test="trailerNumber != null  and trailerNumber != ''"> and trailer_number = #{trailerNumber}</if>
            <if test="trailerId != null  and trailerId != ''"> and trailer_id = #{trailerId}</if>
            <if test="axisType != null  and axisType != ''"> and axis_type = #{axisType}</if>
            <if test="driverWeight != null "> and driver_weight = #{driverWeight}</if>
            <if test="maxWeight != null "> and max_weight = #{maxWeight}</if>
            <if test="engineNumber != null  and engineNumber != ''"> and engine_number = #{engineNumber}</if>
            <if test="vinNumber != null  and vinNumber != ''"> and vin_number = #{vinNumber}</if>
            <if test="isDirectSupply != null"> and is_direct_supply = #{isDirectSupply}</if>
            <if test="stockOutSpec1Length != null"> and stock_out_spec1_length = #{stockOutSpec1Length}</if>
            <if test="stockOutSpec2Width != null"> and stock_out_spec2_width = #{stockOutSpec2Width}</if>
            <if test="stockOutTotals != null  and stockOutTotals != ''"> and stock_out_totals = #{stockOutTotals}</if>
            <if test="stockOutProcessType != null  and stockOutProcessType != ''"> and stock_out_process_type = #{stockOutProcessType}</if>
            <if test="stockOutHeatNo != null  and stockOutHeatNo != ''"> and stock_out_heat_no = #{stockOutHeatNo}</if>
            <if test="stockOutSteelGrade != null  and stockOutSteelGrade != ''"> and stock_out_steel_grade = #{stockOutSteelGrade}</if>
            <if test="stockOutAxles != null  and stockOutAxles != ''"> and stock_out_axles = #{stockOutAxles}</if>
            <if test="stockOutRemark != null  and stockOutRemark != ''"> and stock_out_remark like concat('%', #{stockOutRemark}, '%')</if>
            <if test="directSupplyTaskNo != null  and directSupplyTaskNo != ''"> and direct_supply_task_no = #{directSupplyTaskNo}</if>
        </where>
    </select>
    
    <select id="selectLeaveTaskById" parameterType="Long" resultMap="LeaveTaskResult">
        <include refid="selectLeaveTaskVo"/>
        where id = #{id}
    </select>

    <insert id="insertLeaveTask" parameterType="LeaveTask" useGeneratedKeys="true" keyProperty="id">
        insert into leave_task
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="taskNo != null">task_no,</if>
            <if test="applyNo != null">apply_no,</if>
            <if test="taskType != null">task_type,</if>
            <if test="planNo != null">plan_no,</if>
            <if test="taskStatus != null">task_status,</if>
            <if test="leaveDoor != null">leave_door,</if>
            <if test="leaveTime != null">leave_time,</if>
            <if test="enterDoor != null">enter_door,</if>
            <if test="enterTime != null">enter_time,</if>
            <if test="loadingWorkNo != null">loading_work_no,</if>
            <if test="loadingTime != null">loading_time,</if>
            <if test="unloadingWorkNo != null">unloading_work_no,</if>
            <if test="unloadingTime != null">unloading_time,</if>
            <if test="driverName != null">driver_name,</if>
            <if test="sex != null">sex,</if>
            <if test="mobilePhone != null">mobile_phone,</if>
            <if test="idCardNo != null">id_card_no,</if>
            <if test="carNum != null">car_num,</if>
            <if test="vehicleEmissionStandards != null">vehicle_emission_standards,</if>
            <if test="faceImg != null">face_img,</if>
            <if test="drivingLicenseImg != null">driving_license_img,</if>
            <if test="driverLicenseImg != null">driver_license_img,</if>
            <if test="companyName != null">company_name,</if>
            <if test="gross != null">gross,</if>
            <if test="tare != null">tare,</if>
            <if test="netWeight != null">net_weight,</if>
            <if test="netWeightTime != null">net_weight_time,</if>
            <if test="grossTime != null">gross_time,</if>
            <if test="tareTime != null">tare_time,</if>
            <if test="secGross != null">sec_gross,</if>
            <if test="secTare != null">sec_tare,</if>
            <if test="secNetWeight != null">sec_net_weight,</if>
            <if test="secNetWeightTime != null">sec_net_weight_time,</if>
            <if test="secTareTime != null">sec_tare_time,</if>
            <if test="secGrossTime != null">sec_gross_time,</if>
            <if test="createTime != null">create_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="trailerNumber != null">trailer_number,</if>
            <if test="trailerId != null">trailer_id,</if>
            <if test="axisType != null">axis_type,</if>
            <if test="driverWeight != null">driver_weight,</if>
            <if test="maxWeight != null">max_weight,</if>
            <if test="engineNumber != null">engine_number,</if>
            <if test="vinNumber != null">vin_number,</if>
            <if test="licensePlateColor != null">license_plate_color,</if>
            <if test="carId != null">car_id,</if>
            <if test="spec1Length != null">spec1_length,</if>
            <if test="spec2Width != null">spec2_width,</if>
            <if test="totals != null">totals,</if>
            <if test="processType != null">process_type,</if>
            <if test="heatNo != null">heat_no,</if>
            <if test="steelGrade != null">steel_grade,</if>
            <if test="axles != null">axles,</if>
            <if test="deductWeight != null">deduct_weight,</if>
            <if test="remark != null">remark,</if>
            <if test="isDirectSupply != null">is_direct_supply,</if>
            <if test="stockOutSpec1Length != null">stock_out_spec1_length,</if>
            <if test="stockOutSpec2Width != null">stock_out_spec2_width,</if>
            <if test="stockOutTotals != null">stock_out_totals,</if>
            <if test="stockOutProcessType != null">stock_out_process_type,</if>
            <if test="stockOutHeatNo != null">stock_out_heat_no,</if>
            <if test="stockOutSteelGrade != null">stock_out_steel_grade,</if>
            <if test="stockOutAxles != null">stock_out_axles,</if>
            <if test="stockOutRemark != null">stock_out_remark,</if>
            <if test="directSupplyTaskNo != null">direct_supply_task_no,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="taskNo != null">#{taskNo},</if>
            <if test="applyNo != null">#{applyNo},</if>
            <if test="taskType != null">#{taskType},</if>
            <if test="planNo != null">#{planNo},</if>
            <if test="taskStatus != null">#{taskStatus},</if>
            <if test="leaveDoor != null">#{leaveDoor},</if>
            <if test="leaveTime != null">#{leaveTime},</if>
            <if test="enterDoor != null">#{enterDoor},</if>
            <if test="enterTime != null">#{enterTime},</if>
            <if test="loadingWorkNo != null">#{loadingWorkNo},</if>
            <if test="loadingTime != null">#{loadingTime},</if>
            <if test="unloadingWorkNo != null">#{unloadingWorkNo},</if>
            <if test="unloadingTime != null">#{unloadingTime},</if>
            <if test="driverName != null">#{driverName},</if>
            <if test="sex != null">#{sex},</if>
            <if test="mobilePhone != null">#{mobilePhone},</if>
            <if test="idCardNo != null">#{idCardNo},</if>
            <if test="carNum != null">#{carNum},</if>
            <if test="vehicleEmissionStandards != null">#{vehicleEmissionStandards},</if>
            <if test="faceImg != null">#{faceImg},</if>
            <if test="drivingLicenseImg != null">#{drivingLicenseImg},</if>
            <if test="driverLicenseImg != null">#{driverLicenseImg},</if>
            <if test="companyName != null">#{companyName},</if>
            <if test="gross != null">#{gross},</if>
            <if test="tare != null">#{tare},</if>
            <if test="netWeight != null">#{netWeight},</if>
            <if test="netWeightTime != null">#{netWeightTime},</if>
            <if test="grossTime != null">#{grossTime},</if>
            <if test="tareTime != null">#{tareTime},</if>
            <if test="secGross != null">#{secGross},</if>
            <if test="secTare != null">#{secTare},</if>
            <if test="secNetWeight != null">#{secNetWeight},</if>
            <if test="secNetWeightTime != null">#{secNetWeightTime},</if>
            <if test="secTareTime != null">#{secTareTime},</if>
            <if test="secGrossTime != null">#{secGrossTime},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="trailerNumber != null">#{trailerNumber},</if>
            <if test="trailerId != null">#{trailerId},</if>
            <if test="axisType != null">#{axisType},</if>
            <if test="driverWeight != null">#{driverWeight},</if>
            <if test="maxWeight != null">#{maxWeight},</if>
            <if test="engineNumber != null">#{engineNumber},</if>
            <if test="vinNumber != null">#{vinNumber},</if>
            <if test="licensePlateColor != null">#{licensePlateColor},</if>
            <if test="carId != null">#{carId},</if>
            <if test="spec1Length != null">#{spec1Length},</if>
            <if test="spec2Width != null">#{spec2Width},</if>
            <if test="totals != null">#{totals},</if>
            <if test="processType != null">#{processType},</if>
            <if test="heatNo != null">#{heatNo},</if>
            <if test="steelGrade != null">#{steelGrade},</if>
            <if test="axles != null">#{axles},</if>
            <if test="deductWeight != null">#{deductWeight},</if>
            <if test="remark != null">#{remark},</if>
            <if test="isDirectSupply != null">#{isDirectSupply},</if>
            <if test="stockOutSpec1Length != null">#{stockOutSpec1Length},</if>
            <if test="stockOutSpec2Width != null">#{stockOutSpec2Width},</if>
            <if test="stockOutTotals != null">#{stockOutTotals},</if>
            <if test="stockOutProcessType != null">#{stockOutProcessType},</if>
            <if test="stockOutHeatNo != null">#{stockOutHeatNo},</if>
            <if test="stockOutSteelGrade != null">#{stockOutSteelGrade},</if>
            <if test="stockOutAxles != null">#{stockOutAxles},</if>
            <if test="stockOutRemark != null">#{stockOutRemark},</if>
            <if test="directSupplyTaskNo != null">#{directSupplyTaskNo},</if>
         </trim>
    </insert>

    <update id="updateLeaveTask" parameterType="LeaveTask">
        update leave_task
        <trim prefix="SET" suffixOverrides=",">
            <if test="taskNo != null">task_no = #{taskNo},</if>
            <if test="applyNo != null">apply_no = #{applyNo},</if>
            <if test="taskType != null">task_type = #{taskType},</if>
            <if test="planNo != null">plan_no = #{planNo},</if>
            <if test="taskStatus != null">task_status = #{taskStatus},</if>
            <if test="leaveDoor != null">leave_door = #{leaveDoor},</if>
            <if test="leaveTime != null">leave_time = #{leaveTime},</if>
            <if test="enterDoor != null">enter_door = #{enterDoor},</if>
            <if test="enterTime != null">enter_time = #{enterTime},</if>
            <if test="loadingWorkNo != null">loading_work_no = #{loadingWorkNo},</if>
            <if test="loadingTime != null">loading_time = #{loadingTime},</if>
            <if test="unloadingWorkNo != null">unloading_work_no = #{unloadingWorkNo},</if>
            <if test="unloadingTime != null">unloading_time = #{unloadingTime},</if>
            <if test="driverName != null">driver_name = #{driverName},</if>
            <if test="sex != null">sex = #{sex},</if>
            <if test="mobilePhone != null">mobile_phone = #{mobilePhone},</if>
            <if test="idCardNo != null">id_card_no = #{idCardNo},</if>
            <if test="carNum != null">car_num = #{carNum},</if>
            <if test="vehicleEmissionStandards != null">vehicle_emission_standards = #{vehicleEmissionStandards},</if>
            <if test="faceImg != null">face_img = #{faceImg},</if>
            <if test="drivingLicenseImg != null">driving_license_img = #{drivingLicenseImg},</if>
            <if test="driverLicenseImg != null">driver_license_img = #{driverLicenseImg},</if>
            <if test="companyName != null">company_name = #{companyName},</if>
            <if test="gross != null">gross = #{gross},</if>
            <if test="tare != null">tare = #{tare},</if>
            <if test="netWeight != null">net_weight = #{netWeight},</if>
            <if test="netWeightTime != null">net_weight_time = #{netWeightTime},</if>
            <if test="grossTime != null">gross_time = #{grossTime},</if>
            <if test="tareTime != null">tare_time = #{tareTime},</if>
            <if test="secGross != null">sec_gross = #{secGross},</if>
            <if test="secTare != null">sec_tare = #{secTare},</if>
            <if test="secNetWeight != null">sec_net_weight = #{secNetWeight},</if>
            <if test="secNetWeightTime != null">sec_net_weight_time = #{secNetWeightTime},</if>
            <if test="secTareTime != null">sec_tare_time = #{secTareTime},</if>
            <if test="secGrossTime != null">sec_gross_time = #{secGrossTime},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="trailerNumber != null">trailer_number = #{trailerNumber},</if>
            <if test="trailerId != null">trailer_id = #{trailerId},</if>
            <if test="axisType != null">axis_type = #{axisType},</if>
            <if test="driverWeight != null">driver_weight = #{driverWeight},</if>
            <if test="maxWeight != null">max_weight = #{maxWeight},</if>
            <if test="engineNumber != null">engine_number = #{engineNumber},</if>
            <if test="vinNumber != null">vin_number = #{vinNumber},</if>
            <if test="licensePlateColor != null">license_plate_color = #{licensePlateColor},</if>
            <if test="carId != null">car_id = #{carId},</if>
            <if test="spec1Length != null">spec1_length = #{spec1Length},</if>
            <if test="spec2Width != null">spec2_width = #{spec2Width},</if>
            <if test="totals != null">totals = #{totals},</if>
            <if test="processType != null">process_type = #{processType},</if>
            <if test="heatNo != null">heat_no = #{heatNo},</if>
            <if test="steelGrade != null">steel_grade = #{steelGrade},</if>
            <if test="axles != null">axles = #{axles},</if>
            <if test="deductWeight != null">deduct_weight = #{deductWeight},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="isDirectSupply != null">is_direct_supply = #{isDirectSupply},</if>
            <if test="stockOutSpec1Length != null">stock_out_spec1_length = #{stockOutSpec1Length},</if>
            <if test="stockOutSpec2Width != null">stock_out_spec2_width = #{stockOutSpec2Width},</if>
            <if test="stockOutTotals != null">stock_out_totals = #{stockOutTotals},</if>
            <if test="stockOutProcessType != null">stock_out_process_type = #{stockOutProcessType},</if>
            <if test="stockOutHeatNo != null">stock_out_heat_no = #{stockOutHeatNo},</if>
            <if test="stockOutSteelGrade != null">stock_out_steel_grade = #{stockOutSteelGrade},</if>
            <if test="stockOutAxles != null">stock_out_axles = #{stockOutAxles},</if>
            <if test="stockOutRemark != null">stock_out_remark = #{stockOutRemark},</if>
            <if test="directSupplyTaskNo != null">direct_supply_task_no = #{directSupplyTaskNo},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteLeaveTaskById" parameterType="Long">
        delete from leave_task where id = #{id}
    </delete>

    <delete id="deleteLeaveTaskByIds" parameterType="String">
        delete from leave_task where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
    
</mapper>