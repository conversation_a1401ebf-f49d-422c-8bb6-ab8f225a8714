{"remainingRequest": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\src\\views\\supply\\tec\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\src\\views\\supply\\tec\\index.vue", "mtime": 1755499162066}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgeyBsaXN0VXNlclRlYywgZ2V0VXNlclRlYywgdXBkYXRlVXNlclRlYywgZGVsVXNlclRlYyB9IGZyb20gJ0AvYXBpL3N1cHBseS91c2VydGVjJw0KDQpleHBvcnQgZGVmYXVsdCB7DQogIG5hbWU6ICdTdXBwbHlVc2VyVGVjJywNCiAgZGF0YSgpIHsNCiAgICByZXR1cm4gew0KICAgICAgLy8g6YGu572p5bGCDQogICAgICBsb2FkaW5nOiB0cnVlLA0KICAgICAgLy8g6YCJ5Lit5pWw57uEDQogICAgICBpZHM6IFtdLA0KICAgICAgLy8g6Z2e5Y2V5Liq56aB55SoDQogICAgICBzaW5nbGU6IHRydWUsDQogICAgICAvLyDpnZ7lpJrkuKrnpoHnlKgNCiAgICAgIG11bHRpcGxlOiB0cnVlLA0KICAgICAgLy8g5pi+56S65pCc57Si5p2h5Lu2DQogICAgICBzaG93U2VhcmNoOiB0cnVlLA0KICAgICAgLy8g5oC75p2h5pWwDQogICAgICB0b3RhbDogMCwNCiAgICAgIC8vIOS4iee6p+aVmeiCsuWNoeihqOagvOaVsOaNrg0KICAgICAgdXNlclRlY0xpc3Q6IFtdLA0KICAgICAgLy8g5by55Ye65bGC5qCH6aKYDQogICAgICB0aXRsZTogJycsDQogICAgICAvLyDmmK/lkKbmmL7npLrlvLnlh7rlsYINCiAgICAgIG9wZW46IGZhbHNlLA0KICAgICAgLy8g5p+l6K+i5Y+C5pWwDQogICAgICBxdWVyeVBhcmFtczogew0KICAgICAgICBwYWdlTnVtOiAxLA0KICAgICAgICBwYWdlU2l6ZTogMTAsDQogICAgICAgIHVzZXJOYW1lOiBudWxsLA0KICAgICAgICBpZGNhcmQ6IG51bGwsDQogICAgICAgIHN1cHBseU5hbWU6IG51bGwsDQogICAgICAgIHN0YXRlOiBudWxsDQogICAgICB9LA0KICAgICAgLy8g6KGo5Y2V5Y+C5pWwDQogICAgICBmb3JtOiB7fSwNCiAgICAgIC8vIOafpeeci+ivpuaDheWvueivneahhg0KICAgICAgdmlld0RpYWxvZ1Zpc2libGU6IGZhbHNlLA0KICAgICAgLy8g6ICD6K+V57uT5p6c5a+56K+d5qGGDQogICAgICBleGFtUmVzdWx0RGlhbG9nVmlzaWJsZTogZmFsc2UsDQogICAgICAvLyDogIPor5Xnu5PmnpzooajljZUNCiAgICAgIGV4YW1SZXN1bHRGb3JtOiB7DQogICAgICAgIGlkOiBudWxsLA0KICAgICAgICB1c2VyTmFtZTogJycsDQogICAgICAgIGV4YW1SZXM6ICcnLA0KICAgICAgICBzdGF0ZTogbnVsbA0KICAgICAgfSwNCiAgICAgIC8vIOiAg+ivlee7k+aenOihqOWNlemqjOivgeinhOWImQ0KICAgICAgZXhhbVJlc3VsdFJ1bGVzOiB7DQogICAgICAgIGV4YW1SZXM6IFsNCiAgICAgICAgICB7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAn6K+36YCJ5oup6ICD6K+V57uT5p6cJywgdHJpZ2dlcjogJ2NoYW5nZScgfQ0KICAgICAgICBdDQogICAgICB9DQogICAgfQ0KICB9LA0KICBjcmVhdGVkKCkgew0KICAgIHRoaXMuZ2V0TGlzdCgpDQogIH0sDQogIG1ldGhvZHM6IHsNCiAgICAvKiog5p+l6K+i5LiJ57qn5pWZ6IKy5Y2h5YiX6KGoICovDQogICAgZ2V0TGlzdCgpIHsNCiAgICAgIHRoaXMubG9hZGluZyA9IHRydWUNCiAgICAgIGxpc3RVc2VyVGVjKHRoaXMucXVlcnlQYXJhbXMpLnRoZW4ocmVzcG9uc2UgPT4gew0KICAgICAgICB0aGlzLnVzZXJUZWNMaXN0ID0gcmVzcG9uc2Uucm93cw0KICAgICAgICB0aGlzLnRvdGFsID0gcmVzcG9uc2UudG90YWwNCiAgICAgICAgdGhpcy5sb2FkaW5nID0gZmFsc2UNCiAgICAgIH0pDQogICAgfSwNCiAgICAvLyDlj5bmtojmjInpkq4NCiAgICBjYW5jZWwoKSB7DQogICAgICB0aGlzLm9wZW4gPSBmYWxzZQ0KICAgICAgdGhpcy5yZXNldCgpDQogICAgfSwNCiAgICAvLyDooajljZXph43nva4NCiAgICByZXNldCgpIHsNCiAgICAgIHRoaXMuZm9ybSA9IHsNCiAgICAgICAgaWQ6IG51bGwsDQogICAgICAgIHVzZXJOYW1lOiBudWxsLA0KICAgICAgICBpZGNhcmQ6IG51bGwsDQogICAgICAgIHN1cHBseUNvZGU6IG51bGwsDQogICAgICAgIHN1cHBseU5hbWU6IG51bGwsDQogICAgICAgIHVzZXJQb3N0OiBudWxsLA0KICAgICAgICB1c2VyRGVwdEM6IG51bGwsDQogICAgICAgIHVzZXJEZXB0TmFtZTogbnVsbCwNCiAgICAgICAgc2V4OiBudWxsLA0KICAgICAgICBiaXJ0aDogbnVsbCwNCiAgICAgICAgZWR1Y2F0aW9uTGV2ZWw6IG51bGwsDQogICAgICAgIHNhZmV0eVRyYWluaW5nOiBudWxsLA0KICAgICAgICBleGFtUmVzOiBudWxsLA0KICAgICAgICBhZGRyZXNzOiBudWxsLA0KICAgICAgICBlbXBsb3ltZW50RGF0ZTogbnVsbCwNCiAgICAgICAgZmlyc3RBcnRUaW1lOiBudWxsLA0KICAgICAgICBmaXJFbmRUaW1lOiBudWxsLA0KICAgICAgICBmaXJIb3VyczogbnVsbCwNCiAgICAgICAgZmlyRWR1Y2F0ZWRVcmw6IG51bGwsDQogICAgICAgIGZpckVkdWNhdG9yTm86IG51bGwsDQogICAgICAgIGZpckVkdWNhdG9yTmFtZTogbnVsbCwNCiAgICAgICAgZmlyRWR1Y2F0b3JVcmw6IG51bGwsDQogICAgICAgIHNlY1N0YXJ0VGltZTogbnVsbCwNCiAgICAgICAgc2VjRW5kVGltZTogbnVsbCwNCiAgICAgICAgc2VjSG91cnM6IG51bGwsDQogICAgICAgIHNlY0VkdWNhdGVkVXJsOiBudWxsLA0KICAgICAgICBzZWNFZHVjYXRvck5vOiBudWxsLA0KICAgICAgICBzZWNFZHVjYXRvck5hbWU6IG51bGwsDQogICAgICAgIHNlY0VkdWNhdG9yVXJsOiBudWxsLA0KICAgICAgICB0aGlTdGFydFRpbWU6IG51bGwsDQogICAgICAgIHRoaUVuZFRpbWU6IG51bGwsDQogICAgICAgIHRoaUhvdXJzOiBudWxsLA0KICAgICAgICB0aGlFZHVjYXRlZFVybDogbnVsbCwNCiAgICAgICAgdGhpRWR1Y2F0b3JObzogbnVsbCwNCiAgICAgICAgdGhpRWR1Y2F0b3JOYW1lOiBudWxsLA0KICAgICAgICB0aGlFZHVjYXRvclVybDogbnVsbCwNCiAgICAgICAgc3RhdGU6IG51bGwNCiAgICAgIH0NCiAgICAgIHRoaXMucmVzZXRGb3JtKCdmb3JtJykNCiAgICB9LA0KICAgIC8qKiDmkJzntKLmjInpkq7mk43kvZwgKi8NCiAgICBoYW5kbGVRdWVyeSgpIHsNCiAgICAgIHRoaXMucXVlcnlQYXJhbXMucGFnZU51bSA9IDENCiAgICAgIHRoaXMuZ2V0TGlzdCgpDQogICAgfSwNCiAgICAvKiog6YeN572u5oyJ6ZKu5pON5L2cICovDQogICAgcmVzZXRRdWVyeSgpIHsNCiAgICAgIHRoaXMucmVzZXRGb3JtKCdxdWVyeUZvcm0nKQ0KICAgICAgdGhpcy5oYW5kbGVRdWVyeSgpDQogICAgfSwNCiAgICAvLyDlpJrpgInmoYbpgInkuK3mlbDmja4NCiAgICBoYW5kbGVTZWxlY3Rpb25DaGFuZ2Uoc2VsZWN0aW9uKSB7DQogICAgICB0aGlzLmlkcyA9IHNlbGVjdGlvbi5tYXAoaXRlbSA9PiBpdGVtLmlkKQ0KICAgICAgdGhpcy5zaW5nbGUgPSBzZWxlY3Rpb24ubGVuZ3RoICE9PSAxDQogICAgICB0aGlzLm11bHRpcGxlID0gIXNlbGVjdGlvbi5sZW5ndGgNCiAgICB9LA0KICAgIC8qKiDmn6XnnIvmjInpkq7mk43kvZwgKi8NCiAgICBoYW5kbGVWaWV3KHJvdykgew0KICAgICAgdGhpcy5yZXNldCgpDQogICAgICBjb25zdCBpZCA9IHJvdy5pZCB8fCB0aGlzLmlkcw0KICAgICAgZ2V0VXNlclRlYyhpZCkudGhlbihyZXNwb25zZSA9PiB7DQogICAgICAgIHRoaXMuZm9ybSA9IHJlc3BvbnNlLmRhdGENCiAgICAgICAgdGhpcy52aWV3RGlhbG9nVmlzaWJsZSA9IHRydWUNCiAgICAgIH0pDQogICAgfSwNCiAgICAvKiog5aGr5YaZ6ICD6K+V57uT5p6c5oyJ6ZKu5pON5L2cICovDQogICAgaGFuZGxlRXhhbVJlc3VsdChyb3cpIHsNCiAgICAgIHRoaXMuZXhhbVJlc3VsdEZvcm0gPSB7DQogICAgICAgIGlkOiByb3cuaWQsDQogICAgICAgIHVzZXJOYW1lOiByb3cudXNlck5hbWUsDQogICAgICAgIGV4YW1SZXM6ICcnLA0KICAgICAgICBzdGF0ZTogbnVsbA0KICAgICAgfQ0KICAgICAgdGhpcy5leGFtUmVzdWx0RGlhbG9nVmlzaWJsZSA9IHRydWUNCiAgICB9LA0KICAgIC8qKiDmj5DkuqTogIPor5Xnu5PmnpwgKi8NCiAgICBzdWJtaXRFeGFtUmVzdWx0KCkgew0KICAgICAgdGhpcy4kcmVmc1snZXhhbVJlc3VsdEZvcm0nXS52YWxpZGF0ZSh2YWxpZCA9PiB7DQogICAgICAgIGlmICh2YWxpZCkgew0KICAgICAgICAgIC8vIOagueaNruiAg+ivlee7k+aenOiHquWKqOiuvue9rueKtuaAgQ0KICAgICAgICAgIGNvbnN0IHN0YXRlID0gdGhpcy5leGFtUmVzdWx0Rm9ybS5leGFtUmVzID09PSAn5ZCI5qC8JyA/IDEgOiAyDQogICAgICAgICAgY29uc3QgdXBkYXRlRGF0YSA9IHsNCiAgICAgICAgICAgIC4uLnRoaXMuZXhhbVJlc3VsdEZvcm0sDQogICAgICAgICAgICBzdGF0ZTogc3RhdGUNCiAgICAgICAgICB9DQoNCiAgICAgICAgICB1cGRhdGVVc2VyVGVjKHVwZGF0ZURhdGEpLnRoZW4ocmVzcG9uc2UgPT4gew0KICAgICAgICAgICAgdGhpcy4kbW9kYWwubXNnU3VjY2Vzcygn5L+u5pS55oiQ5YqfJykNCiAgICAgICAgICAgIHRoaXMuZXhhbVJlc3VsdERpYWxvZ1Zpc2libGUgPSBmYWxzZQ0KICAgICAgICAgICAgdGhpcy5nZXRMaXN0KCkNCiAgICAgICAgICB9KQ0KICAgICAgICB9DQogICAgICB9KQ0KICAgIH0sDQogICAgLyoqIOWIoOmZpOaMiemSruaTjeS9nCAqLw0KICAgIGhhbmRsZURlbGV0ZShyb3cpIHsNCiAgICAgIGNvbnN0IGlkcyA9IHJvdy5pZCB8fCB0aGlzLmlkcw0KICAgICAgdGhpcy4kbW9kYWwuY29uZmlybSgn5piv5ZCm56Gu6K6k5Yig6Zmk5LiJ57qn5pWZ6IKy5Y2h57yW5Y+35Li6IicgKyBpZHMgKyAnIueahOaVsOaNrumhue+8nycpLnRoZW4oZnVuY3Rpb24oKSB7DQogICAgICAgIHJldHVybiBkZWxVc2VyVGVjKGlkcykNCiAgICAgIH0pLnRoZW4oKCkgPT4gew0KICAgICAgICB0aGlzLmdldExpc3QoKQ0KICAgICAgICB0aGlzLiRtb2RhbC5tc2dTdWNjZXNzKCfliKDpmaTmiJDlip8nKQ0KICAgICAgfSkuY2F0Y2goKCkgPT4ge30pDQogICAgfSwNCiAgICAvKiog5a+85Ye65oyJ6ZKu5pON5L2cICovDQogICAgaGFuZGxlRXhwb3J0KCkgew0KICAgICAgdGhpcy5kb3dubG9hZCgnc3VwcGx5L3VzZXJ0ZWMvZXhwb3J0Jywgew0KICAgICAgICAuLi50aGlzLnF1ZXJ5UGFyYW1zDQogICAgICB9LCBg5LiJ57qn5pWZ6IKy5Y2hXyR7bmV3IERhdGUoKS5nZXRUaW1lKCl9Lnhsc3hgKQ0KICAgIH0sDQogICAgLyoqIOWIpOaWreaJgOacieetvuWQjeaYr+WQpuWujOaIkCAqLw0KICAgIGlzQWxsU2lnbmF0dXJlc0NvbXBsZXRlKHJvdykgew0KICAgICAgcmV0dXJuIHJvdy5maXJFZHVjYXRlZFVybCAmJg0KICAgICAgICAgICAgIHJvdy5maXJFZHVjYXRvclVybCAmJg0KICAgICAgICAgICAgIHJvdy5zZWNFZHVjYXRlZFVybCAmJg0KICAgICAgICAgICAgIHJvdy5zZWNFZHVjYXRvclVybCAmJg0KICAgICAgICAgICAgIHJvdy50aGlFZHVjYXRlZFVybCAmJg0KICAgICAgICAgICAgIHJvdy50aGlFZHVjYXRvclVybA0KICAgIH0NCiAgfQ0KfQ0K"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAuRA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/supply/tec", "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <!-- 搜索表单 -->\r\n    <el-form :model=\"queryParams\" ref=\"queryForm\" size=\"small\" :inline=\"true\" v-show=\"showSearch\" label-width=\"80px\">\r\n      <el-form-item label=\"用户姓名\" prop=\"userName\">\r\n        <el-input\r\n          v-model=\"queryParams.userName\"\r\n          placeholder=\"请输入用户姓名\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"身份证\" prop=\"idcard\">\r\n        <el-input\r\n          v-model=\"queryParams.idcard\"\r\n          placeholder=\"请输入身份证号\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"供应商名称\" prop=\"supplyName\">\r\n        <el-input\r\n          v-model=\"queryParams.supplyName\"\r\n          placeholder=\"请输入供应商名称\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"状态\" prop=\"state\">\r\n        <el-select v-model=\"queryParams.state\" placeholder=\"请选择状态\" clearable>\r\n          <el-option label=\"未完成\" :value=\"0\" />\r\n          <el-option label=\"合格\" :value=\"1\" />\r\n          <el-option label=\"不合格\" :value=\"2\" />\r\n        </el-select>\r\n      </el-form-item>\r\n      <el-form-item>\r\n        <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleQuery\">搜索</el-button>\r\n        <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\r\n      </el-form-item>\r\n    </el-form>\r\n\r\n    <!-- 操作按钮 -->\r\n    <el-row :gutter=\"10\" class=\"mb8\">\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"danger\"\r\n          plain\r\n          icon=\"el-icon-delete\"\r\n          size=\"mini\"\r\n          :disabled=\"multiple\"\r\n          @click=\"handleDelete\"\r\n          v-hasPermi=\"['supply:usertec:remove']\"\r\n        >删除</el-button>\r\n      </el-col>\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"warning\"\r\n          plain\r\n          icon=\"el-icon-download\"\r\n          size=\"mini\"\r\n          @click=\"handleExport\"\r\n          v-hasPermi=\"['supply:usertec:export']\"\r\n        >导出</el-button>\r\n      </el-col>\r\n      <right-toolbar :showSearch.sync=\"showSearch\" @queryTable=\"getList\"></right-toolbar>\r\n    </el-row>\r\n\r\n    <!-- 数据表格 -->\r\n    <el-table v-loading=\"loading\" :data=\"userTecList\" @selection-change=\"handleSelectionChange\">\r\n      <el-table-column type=\"selection\" width=\"55\" align=\"center\" />\r\n      <el-table-column label=\"ID\" align=\"center\" prop=\"id\" width=\"80\" />\r\n      <el-table-column label=\"用户姓名\" align=\"center\" prop=\"userName\" />\r\n      <el-table-column label=\"身份证\" align=\"center\" prop=\"idcard\" width=\"180\" />\r\n      <el-table-column label=\"供应商名称\" align=\"center\" prop=\"supplyName\" />\r\n      <el-table-column label=\"岗位\" align=\"center\" prop=\"userPost\" />\r\n      <el-table-column label=\"服务分厂\" align=\"center\" prop=\"userDeptName\" />\r\n      <el-table-column label=\"考试结果\" align=\"center\" prop=\"examRes\" />\r\n      <el-table-column label=\"状态\" align=\"center\" prop=\"state\" width=\"100\">\r\n        <template slot-scope=\"scope\">\r\n          <el-tag v-if=\"scope.row.state === '0'\" type=\"info\">未完成</el-tag>\r\n          <el-tag v-else-if=\"scope.row.state === '1'\" type=\"success\">合格</el-tag>\r\n          <el-tag v-else-if=\"scope.row.state === '2'\" type=\"danger\">不合格</el-tag>\r\n          <el-tag v-else type=\"info\">未知</el-tag>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"签名状态\" align=\"center\" width=\"120\">\r\n        <template slot-scope=\"scope\">\r\n          <el-tag v-if=\"isAllSignaturesComplete(scope.row)\" type=\"success\">已完成</el-tag>\r\n          <el-tag v-else type=\"warning\">未完成</el-tag>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"操作\" align=\"center\" class-name=\"small-padding fixed-width\" width=\"200\">\r\n        <template slot-scope=\"scope\">\r\n          <el-button\r\n            size=\"mini\"\r\n            type=\"text\"\r\n            icon=\"el-icon-view\"\r\n            @click=\"handleView(scope.row)\"\r\n          >查看</el-button>\r\n          <el-button\r\n            v-if=\"isAllSignaturesComplete(scope.row) && scope.row.state === '0'\"\r\n            size=\"mini\"\r\n            type=\"text\"\r\n            icon=\"el-icon-edit\"\r\n            @click=\"handleExamResult(scope.row)\"\r\n          >填写考试结果</el-button>\r\n          <el-button\r\n            size=\"mini\"\r\n            type=\"text\"\r\n            icon=\"el-icon-delete\"\r\n            @click=\"handleDelete(scope.row)\"\r\n            v-hasPermi=\"['supply:usertec:remove']\"\r\n          >删除</el-button>\r\n        </template>\r\n      </el-table-column>\r\n    </el-table>\r\n\r\n    <!-- 分页组件 -->\r\n    <pagination\r\n      v-show=\"total>0\"\r\n      :total=\"total\"\r\n      :page.sync=\"queryParams.pageNum\"\r\n      :limit.sync=\"queryParams.pageSize\"\r\n      @pagination=\"getList\"\r\n    />\r\n\r\n    <!-- 查看详情对话框 -->\r\n    <el-dialog title=\"三级教育卡详情\" :visible.sync=\"viewDialogVisible\" width=\"80%\" append-to-body>\r\n      <el-descriptions :column=\"2\" border>\r\n        <el-descriptions-item label=\"用户姓名\">{{ form.userName }}</el-descriptions-item>\r\n        <el-descriptions-item label=\"身份证\">{{ form.idcard }}</el-descriptions-item>\r\n        <el-descriptions-item label=\"供应商名称\">{{ form.supplyName }}</el-descriptions-item>\r\n        <el-descriptions-item label=\"岗位\">{{ form.userPost }}</el-descriptions-item>\r\n        <el-descriptions-item label=\"服务分厂\">{{ form.userDeptName }}</el-descriptions-item>\r\n        <el-descriptions-item label=\"性别\">{{ form.sex }}</el-descriptions-item>\r\n        <el-descriptions-item label=\"文化水平\">{{ form.educationLevel }}</el-descriptions-item>\r\n        <el-descriptions-item label=\"考试结果\">{{ form.examRes }}</el-descriptions-item>\r\n        <el-descriptions-item label=\"状态\">\r\n          <el-tag v-if=\"form.state === '0'\" type=\"info\">未完成</el-tag>\r\n          <el-tag v-else-if=\"form.state === '1'\" type=\"success\">合格</el-tag>\r\n          <el-tag v-else-if=\"form.state === '2'\" type=\"danger\">不合格</el-tag>\r\n          <el-tag v-else type=\"info\">未知</el-tag>\r\n        </el-descriptions-item>\r\n        <el-descriptions-item label=\"上岗时间\">{{ parseTime(form.employmentDate, '{y}-{m}-{d}') }}</el-descriptions-item>\r\n      </el-descriptions>\r\n\r\n      <!-- 三级培训信息 -->\r\n      <el-divider content-position=\"left\">培训信息</el-divider>\r\n\r\n      <!-- 公司级培训 -->\r\n      <el-card class=\"box-card\" style=\"margin-bottom: 20px;\">\r\n        <div slot=\"header\" class=\"clearfix\">\r\n          <span>公司级培训</span>\r\n        </div>\r\n        <el-row :gutter=\"20\">\r\n          <el-col :span=\"8\">\r\n            <p><strong>开始时间：</strong>{{ parseTime(form.firstArtTime, '{y}-{m}-{d}') }}</p>\r\n            <p><strong>结束时间：</strong>{{ parseTime(form.firEndTime, '{y}-{m}-{d}') }}</p>\r\n            <p><strong>培训学时：</strong>{{ form.firHours }}</p>\r\n          </el-col>\r\n          <el-col :span=\"8\">\r\n            <p><strong>受教育者签名：</strong></p>\r\n            <el-image\r\n              v-if=\"form.firEducatedUrl\"\r\n              :src=\"form.firEducatedUrl\"\r\n              style=\"width: 100px; height: 50px;\"\r\n              :preview-src-list=\"[form.firEducatedUrl]\">\r\n            </el-image>\r\n            <span v-else style=\"color: #999;\">未签名</span>\r\n          </el-col>\r\n          <el-col :span=\"8\">\r\n            <p><strong>教育者签名：</strong></p>\r\n            <el-image\r\n              v-if=\"form.firEducatorUrl\"\r\n              :src=\"form.firEducatorUrl\"\r\n              style=\"width: 100px; height: 50px;\"\r\n              :preview-src-list=\"[form.firEducatorUrl]\">\r\n            </el-image>\r\n            <span v-else style=\"color: #999;\">未签名</span>\r\n          </el-col>\r\n        </el-row>\r\n      </el-card>\r\n\r\n      <!-- 厂部级培训 -->\r\n      <el-card class=\"box-card\" style=\"margin-bottom: 20px;\">\r\n        <div slot=\"header\" class=\"clearfix\">\r\n          <span>厂部级培训</span>\r\n        </div>\r\n        <el-row :gutter=\"20\">\r\n          <el-col :span=\"8\">\r\n            <p><strong>开始时间：</strong>{{ parseTime(form.secStartTime, '{y}-{m}-{d}') }}</p>\r\n            <p><strong>结束时间：</strong>{{ parseTime(form.secEndTime, '{y}-{m}-{d}') }}</p>\r\n            <p><strong>培训学时：</strong>{{ form.secHours }}</p>\r\n          </el-col>\r\n          <el-col :span=\"8\">\r\n            <p><strong>受教育者签名：</strong></p>\r\n            <el-image\r\n              v-if=\"form.secEducatedUrl\"\r\n              :src=\"form.secEducatedUrl\"\r\n              style=\"width: 100px; height: 50px;\"\r\n              :preview-src-list=\"[form.secEducatedUrl]\">\r\n            </el-image>\r\n            <span v-else style=\"color: #999;\">未签名</span>\r\n          </el-col>\r\n          <el-col :span=\"8\">\r\n            <p><strong>教育者签名：</strong></p>\r\n            <el-image\r\n              v-if=\"form.secEducatorUrl\"\r\n              :src=\"form.secEducatorUrl\"\r\n              style=\"width: 100px; height: 50px;\"\r\n              :preview-src-list=\"[form.secEducatorUrl]\">\r\n            </el-image>\r\n            <span v-else style=\"color: #999;\">未签名</span>\r\n          </el-col>\r\n        </el-row>\r\n      </el-card>\r\n\r\n      <!-- 班组级培训 -->\r\n      <el-card class=\"box-card\">\r\n        <div slot=\"header\" class=\"clearfix\">\r\n          <span>班组级培训</span>\r\n        </div>\r\n        <el-row :gutter=\"20\">\r\n          <el-col :span=\"8\">\r\n            <p><strong>开始时间：</strong>{{ parseTime(form.thiStartTime, '{y}-{m}-{d}') }}</p>\r\n            <p><strong>结束时间：</strong>{{ parseTime(form.thiEndTime, '{y}-{m}-{d}') }}</p>\r\n            <p><strong>培训学时：</strong>{{ form.thiHours }}</p>\r\n          </el-col>\r\n          <el-col :span=\"8\">\r\n            <p><strong>受教育者签名：</strong></p>\r\n            <el-image\r\n              v-if=\"form.thiEducatedUrl\"\r\n              :src=\"form.thiEducatedUrl\"\r\n              style=\"width: 100px; height: 50px;\"\r\n              :preview-src-list=\"[form.thiEducatedUrl]\">\r\n            </el-image>\r\n            <span v-else style=\"color: #999;\">未签名</span>\r\n          </el-col>\r\n          <el-col :span=\"8\">\r\n            <p><strong>教育者签名：</strong></p>\r\n            <el-image\r\n              v-if=\"form.thiEducatorUrl\"\r\n              :src=\"form.thiEducatorUrl\"\r\n              style=\"width: 100px; height: 50px;\"\r\n              :preview-src-list=\"[form.thiEducatorUrl]\">\r\n            </el-image>\r\n            <span v-else style=\"color: #999;\">未签名</span>\r\n          </el-col>\r\n        </el-row>\r\n      </el-card>\r\n    </el-dialog>\r\n\r\n    <!-- 填写考试结果对话框 -->\r\n    <el-dialog title=\"填写考试结果\" :visible.sync=\"examResultDialogVisible\" width=\"500px\" append-to-body>\r\n      <el-form ref=\"examResultForm\" :model=\"examResultForm\" :rules=\"examResultRules\" label-width=\"100px\">\r\n        <el-form-item label=\"用户姓名\">\r\n          <el-input v-model=\"examResultForm.userName\" disabled />\r\n        </el-form-item>\r\n        <el-form-item label=\"考试结果\" prop=\"examRes\">\r\n          <el-select v-model=\"examResultForm.examRes\" placeholder=\"请选择考试结果\" style=\"width: 100%;\">\r\n            <el-option label=\"合格\" value=\"合格\" />\r\n            <el-option label=\"不合格\" value=\"不合格\" />\r\n          </el-select>\r\n        </el-form-item>\r\n        <el-form-item label=\"状态\">\r\n          <el-tag v-if=\"examResultForm.examRes === '合格'\" type=\"success\">合格</el-tag>\r\n          <el-tag v-else-if=\"examResultForm.examRes === '不合格'\" type=\"danger\">不合格</el-tag>\r\n          <el-tag v-else type=\"info\">未选择</el-tag>\r\n        </el-form-item>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"examResultDialogVisible = false\">取 消</el-button>\r\n        <el-button type=\"primary\" @click=\"submitExamResult\">确 定</el-button>\r\n      </div>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { listUserTec, getUserTec, updateUserTec, delUserTec } from '@/api/supply/usertec'\r\n\r\nexport default {\r\n  name: 'SupplyUserTec',\r\n  data() {\r\n    return {\r\n      // 遮罩层\r\n      loading: true,\r\n      // 选中数组\r\n      ids: [],\r\n      // 非单个禁用\r\n      single: true,\r\n      // 非多个禁用\r\n      multiple: true,\r\n      // 显示搜索条件\r\n      showSearch: true,\r\n      // 总条数\r\n      total: 0,\r\n      // 三级教育卡表格数据\r\n      userTecList: [],\r\n      // 弹出层标题\r\n      title: '',\r\n      // 是否显示弹出层\r\n      open: false,\r\n      // 查询参数\r\n      queryParams: {\r\n        pageNum: 1,\r\n        pageSize: 10,\r\n        userName: null,\r\n        idcard: null,\r\n        supplyName: null,\r\n        state: null\r\n      },\r\n      // 表单参数\r\n      form: {},\r\n      // 查看详情对话框\r\n      viewDialogVisible: false,\r\n      // 考试结果对话框\r\n      examResultDialogVisible: false,\r\n      // 考试结果表单\r\n      examResultForm: {\r\n        id: null,\r\n        userName: '',\r\n        examRes: '',\r\n        state: null\r\n      },\r\n      // 考试结果表单验证规则\r\n      examResultRules: {\r\n        examRes: [\r\n          { required: true, message: '请选择考试结果', trigger: 'change' }\r\n        ]\r\n      }\r\n    }\r\n  },\r\n  created() {\r\n    this.getList()\r\n  },\r\n  methods: {\r\n    /** 查询三级教育卡列表 */\r\n    getList() {\r\n      this.loading = true\r\n      listUserTec(this.queryParams).then(response => {\r\n        this.userTecList = response.rows\r\n        this.total = response.total\r\n        this.loading = false\r\n      })\r\n    },\r\n    // 取消按钮\r\n    cancel() {\r\n      this.open = false\r\n      this.reset()\r\n    },\r\n    // 表单重置\r\n    reset() {\r\n      this.form = {\r\n        id: null,\r\n        userName: null,\r\n        idcard: null,\r\n        supplyCode: null,\r\n        supplyName: null,\r\n        userPost: null,\r\n        userDeptC: null,\r\n        userDeptName: null,\r\n        sex: null,\r\n        birth: null,\r\n        educationLevel: null,\r\n        safetyTraining: null,\r\n        examRes: null,\r\n        address: null,\r\n        employmentDate: null,\r\n        firstArtTime: null,\r\n        firEndTime: null,\r\n        firHours: null,\r\n        firEducatedUrl: null,\r\n        firEducatorNo: null,\r\n        firEducatorName: null,\r\n        firEducatorUrl: null,\r\n        secStartTime: null,\r\n        secEndTime: null,\r\n        secHours: null,\r\n        secEducatedUrl: null,\r\n        secEducatorNo: null,\r\n        secEducatorName: null,\r\n        secEducatorUrl: null,\r\n        thiStartTime: null,\r\n        thiEndTime: null,\r\n        thiHours: null,\r\n        thiEducatedUrl: null,\r\n        thiEducatorNo: null,\r\n        thiEducatorName: null,\r\n        thiEducatorUrl: null,\r\n        state: null\r\n      }\r\n      this.resetForm('form')\r\n    },\r\n    /** 搜索按钮操作 */\r\n    handleQuery() {\r\n      this.queryParams.pageNum = 1\r\n      this.getList()\r\n    },\r\n    /** 重置按钮操作 */\r\n    resetQuery() {\r\n      this.resetForm('queryForm')\r\n      this.handleQuery()\r\n    },\r\n    // 多选框选中数据\r\n    handleSelectionChange(selection) {\r\n      this.ids = selection.map(item => item.id)\r\n      this.single = selection.length !== 1\r\n      this.multiple = !selection.length\r\n    },\r\n    /** 查看按钮操作 */\r\n    handleView(row) {\r\n      this.reset()\r\n      const id = row.id || this.ids\r\n      getUserTec(id).then(response => {\r\n        this.form = response.data\r\n        this.viewDialogVisible = true\r\n      })\r\n    },\r\n    /** 填写考试结果按钮操作 */\r\n    handleExamResult(row) {\r\n      this.examResultForm = {\r\n        id: row.id,\r\n        userName: row.userName,\r\n        examRes: '',\r\n        state: null\r\n      }\r\n      this.examResultDialogVisible = true\r\n    },\r\n    /** 提交考试结果 */\r\n    submitExamResult() {\r\n      this.$refs['examResultForm'].validate(valid => {\r\n        if (valid) {\r\n          // 根据考试结果自动设置状态\r\n          const state = this.examResultForm.examRes === '合格' ? 1 : 2\r\n          const updateData = {\r\n            ...this.examResultForm,\r\n            state: state\r\n          }\r\n\r\n          updateUserTec(updateData).then(response => {\r\n            this.$modal.msgSuccess('修改成功')\r\n            this.examResultDialogVisible = false\r\n            this.getList()\r\n          })\r\n        }\r\n      })\r\n    },\r\n    /** 删除按钮操作 */\r\n    handleDelete(row) {\r\n      const ids = row.id || this.ids\r\n      this.$modal.confirm('是否确认删除三级教育卡编号为\"' + ids + '\"的数据项？').then(function() {\r\n        return delUserTec(ids)\r\n      }).then(() => {\r\n        this.getList()\r\n        this.$modal.msgSuccess('删除成功')\r\n      }).catch(() => {})\r\n    },\r\n    /** 导出按钮操作 */\r\n    handleExport() {\r\n      this.download('supply/usertec/export', {\r\n        ...this.queryParams\r\n      }, `三级教育卡_${new Date().getTime()}.xlsx`)\r\n    },\r\n    /** 判断所有签名是否完成 */\r\n    isAllSignaturesComplete(row) {\r\n      return row.firEducatedUrl &&\r\n             row.firEducatorUrl &&\r\n             row.secEducatedUrl &&\r\n             row.secEducatorUrl &&\r\n             row.thiEducatedUrl &&\r\n             row.thiEducatorUrl\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.box-card {\r\n  margin-bottom: 20px;\r\n}\r\n.box-card .el-card__header {\r\n  background-color: #f5f7fa;\r\n  font-weight: bold;\r\n}\r\n.box-card p {\r\n  margin: 8px 0;\r\n  line-height: 1.5;\r\n}\r\n</style>\r\n\r\n"]}]}