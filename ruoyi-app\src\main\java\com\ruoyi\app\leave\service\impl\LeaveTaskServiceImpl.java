package com.ruoyi.app.leave.service.impl;

import java.util.Date;
import java.util.List;

import java.util.Objects;
import java.util.stream.Collectors;

import com.ruoyi.app.leave.domain.DProcessTMeasure;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.SnowFlakeUtil;

import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.app.leave.mapper.LeaveTaskMapper;
import com.ruoyi.app.leave.domain.LeaveTask;
import com.ruoyi.app.leave.dto.MeasureNotifyDto;
import com.ruoyi.app.leave.enums.LeaveTaskStatusEnum;
import com.ruoyi.app.leave.service.ILeaveTaskService;
import com.ruoyi.app.leave.util.MeasureDesUtil;

/**
 * 出门证任务Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-03-13
 */
@Service
public class LeaveTaskServiceImpl implements ILeaveTaskService 
{
    private static final Logger log = LoggerFactory.getLogger(LeaveTaskServiceImpl.class);
    @Autowired
    private LeaveTaskMapper leaveTaskMapper;

    /**
     * 查询出门证任务
     * 
     * @param id 出门证任务ID
     * @return 出门证任务
     */
    @Override
    public LeaveTask selectLeaveTaskById(Long id)
    {
        LeaveTask leaveTask = leaveTaskMapper.selectLeaveTaskById(id);
        //生成二维码内容
        leaveTask.setQrCodeContent(generateQrCodeContent(leaveTask));
        return leaveTask;
    }

    private String generateQrCodeContent(LeaveTask leaveTask){
        String qrCodeContent = leaveTask.getPlanNo() + "|" + leaveTask.getCarNum();
        //加密
        return MeasureDesUtil.encrypt(qrCodeContent);
    }

    /**
     * 查询出门证任务列表
     * 
     * @param leaveTask 出门证任务
     * @return 出门证任务
     */
    @Override
    public List<LeaveTask> selectLeaveTaskList(LeaveTask leaveTask)
    {
        return leaveTaskMapper.selectLeaveTaskList(leaveTask);
    }

    /**
     * 新增出门证任务
     * 
     * @param leaveTask 出门证任务
     * @return 结果
     */
    @Override
    public int insertLeaveTask(LeaveTask leaveTask)
    {
        leaveTask.setCreateTime(DateUtils.getNowDate());
        return leaveTaskMapper.insertLeaveTask(leaveTask);
    }

    /**
     * 修改出门证任务
     * 
     * @param leaveTask 出门证任务
     * @return 结果
     */
    @Override
    public int updateLeaveTask(LeaveTask leaveTask)
    {
//        卸货人占位符
        if (leaveTask.getUnloadingWorkNo() != null) {
            if (leaveTask.getUnloadingWorkNo().equals("卸货人占位符")){
                leaveTask.setUnloadingWorkNo(SecurityUtils.getUsername());
            }
        }
        leaveTask.setUpdateTime(DateUtils.getNowDate());
        return leaveTaskMapper.updateLeaveTask(leaveTask);
    }

    /**
     * 批量删除出门证任务
     * 
     * @param ids 需要删除的出门证任务ID
     * @return 结果
     */
    @Override
    public int deleteLeaveTaskByIds(Long[] ids)
    {
        return leaveTaskMapper.deleteLeaveTaskByIds(ids);
    }

    /**
     * 删除出门证任务信息
     * 
     * @param id 出门证任务ID
     * @return 结果
     */
    @Override
    public int deleteLeaveTaskById(Long id)
    {
        return leaveTaskMapper.deleteLeaveTaskById(id);
    }

    @Override
    public String insertLeaveTaskAndMaterial(LeaveTask leaveTask) {
        String leaveTaskNoSnowFlakeId = SnowFlakeUtil.getLeaveTaskNoSnowFlakeId();
        leaveTask.setTaskNo(leaveTaskNoSnowFlakeId);
        leaveTask.setCreateTime(DateUtils.getNowDate());
        leaveTaskMapper.insertLeaveTask(leaveTask);
        return leaveTaskNoSnowFlakeId;
    }

    /**
     * 监听计量推送的过磅信息，并修改任务状态
     */
    @Override
    public void listenMeasurePushInfo(MeasureNotifyDto measureNotifyDto) {
        //通过计划号和车牌号查询对应的任务
        LeaveTask condition = new LeaveTask();
        condition.setPlanNo(measureNotifyDto.getPlanId());
        condition.setCarNum(measureNotifyDto.getCarNo());
        List<LeaveTask> leaveTasks = leaveTaskMapper.selectLeaveTaskList(condition);
        if(CollectionUtils.isEmpty(leaveTasks)){
            log.info("不存在出门证任务，计划号：{}，车牌号：{}", measureNotifyDto.getPlanId(), measureNotifyDto.getCarNo());
            return;
        }
        //1-待过皮重 2-待装货 3-待过毛重 4-待出厂 5-待返厂 6-待过毛重(复磅) 7-待卸货 8-待过皮重(复磅) 9-完成
        //筛选适合的状态
        leaveTasks = leaveTasks.stream().filter(leaveTask -> {
            return isStatusMatch(leaveTask);
        }).collect(Collectors.toList());
        if(CollectionUtils.isEmpty(leaveTasks)){
            log.info("不存在适合状态的出门证任务，计划号：{}，车牌号：{}", measureNotifyDto.getPlanId(), measureNotifyDto.getCarNo());
            return;
        }

        if(leaveTasks.size() > 1){
            // 按isDirectSupply排序：1在前，3在后
            leaveTasks.sort((task1, task2) -> {
                Integer supply1 = task1.getIsDirectSupply();
                Integer supply2 = task2.getIsDirectSupply();
                
                // 如果第一个是1，第二个是3，则第一个排在前面
                if(supply1 == 1 && supply2 == 3) {
                    return -1;
                }
                // 如果第一个是3，第二个是1，则第二个排在前面
                if(supply1 == 3 && supply2 == 1) {
                    return 1;
                }
                // 其他情况保持原有顺序
                return 0;
            });
            
            // 检查排序后是否还有多个任务
            if(leaveTasks.size() > 2) {
                log.error("存在多个任务且无法确定优先级，计划号：{}，车牌号：{}", measureNotifyDto.getPlanId(), measureNotifyDto.getCarNo());
                return;
            }
            

        }

        LeaveTask leaveTask = leaveTasks.get(0);
        if("1".equals(measureNotifyDto.getMeasureType())){
            //毛重
            if(leaveTask.getTaskStatus() == LeaveTaskStatusEnum.WAIT_UNLOADING.getCode()){
                //待过毛重
                leaveTask.setGross(measureNotifyDto.getGross());
                leaveTask.setGrossTime(DateUtils.dateTime(measureNotifyDto.getGrossTime()));
                leaveTask.setUpdateTime(DateUtils.getNowDate());
                leaveTask.setUpdateBy("admin");
                if(Objects.nonNull(leaveTask.getTare())){
                    //计算净重
                    leaveTask.setNetWeight(leaveTask.getGross().subtract(leaveTask.getTare()));
                    leaveTask.setNetWeightTime(DateUtils.getNowDate());
                }
                leaveTask.setTaskStatus(LeaveTaskStatusEnum.WAIT_OUT_FACTORY.getCode());
                leaveTaskMapper.updateLeaveTask(leaveTask);
            }else if(leaveTask.getTaskStatus() == LeaveTaskStatusEnum.WAIT_RECHECK_UNLOADING.getCode()){
                //待过毛重(复磅)
                leaveTask.setSecGross(measureNotifyDto.getGross());
                leaveTask.setSecGrossTime(DateUtils.dateTime(measureNotifyDto.getGrossTime()));
                leaveTask.setUpdateTime(DateUtils.getNowDate());
                leaveTask.setUpdateBy("admin");
                leaveTask.setTaskStatus(LeaveTaskStatusEnum.WAIT_UNLOADING_RECHECK.getCode());
                leaveTaskMapper.updateLeaveTask(leaveTask);
            }else{
                log.error("任务状态不匹配，计划号：{}，车牌号：{}", measureNotifyDto.getPlanId(), measureNotifyDto.getCarNo());
                return;
            }
        }else if("2".equals(measureNotifyDto.getMeasureType())){
            //皮重
            if(leaveTask.getTaskStatus() == LeaveTaskStatusEnum.WAIT_PICKUP.getCode()){
                //待过皮重
                leaveTask.setTare(measureNotifyDto.getTare());
                leaveTask.setTareTime(DateUtils.dateTime(measureNotifyDto.getTareTime()));
                leaveTask.setUpdateTime(DateUtils.getNowDate());
                leaveTask.setUpdateBy("admin");
                leaveTask.setTaskStatus(LeaveTaskStatusEnum.WAIT_LOADING.getCode());
                leaveTaskMapper.updateLeaveTask(leaveTask);
            }else if(leaveTask.getTaskStatus() == LeaveTaskStatusEnum.WAIT_RECHECK_PICKUP.getCode()){
                //待过皮重(复磅)
                if (leaveTask.getIsDirectSupply() == 1) {
                    //直供计划处理
                    leaveTask.setSecTare(measureNotifyDto.getTare());
                    leaveTask.setSecTareTime(DateUtils.dateTime(measureNotifyDto.getTareTime()));
                    leaveTask.setUpdateTime(DateUtils.getNowDate());
                    leaveTask.setUpdateBy("admin");
                    if(Objects.nonNull(leaveTask.getSecGross())){
                        //计算净重
                        leaveTask.setSecNetWeight(leaveTask.getSecGross().subtract(leaveTask.getSecTare()));
                        leaveTask.setSecNetWeightTime(DateUtils.getNowDate());
                    }
                    leaveTask.setTaskStatus(LeaveTaskStatusEnum.COMPLETE.getCode());
                    leaveTaskMapper.updateLeaveTask(leaveTask);
                    //跨区调拨同步
                    LeaveTask leaveTask1 = leaveTasks.get(1);
                    leaveTask1.setSecTare(measureNotifyDto.getTare());
                    leaveTask1.setSecTareTime(DateUtils.dateTime(measureNotifyDto.getTareTime()));
                    leaveTask1.setUpdateTime(DateUtils.getNowDate());
                    leaveTask1.setUpdateBy("admin");
                    if(Objects.nonNull(leaveTask1.getSecGross())){
                        //计算净重
                        leaveTask1.setSecNetWeight(leaveTask1.getSecGross().subtract(leaveTask1.getSecTare()));
                        leaveTask1.setSecNetWeightTime(DateUtils.getNowDate());
                    }
                    leaveTask1.setTaskStatus(LeaveTaskStatusEnum.COMPLETE.getCode());
                    leaveTaskMapper.updateLeaveTask(leaveTask1);

                } else {
                    leaveTask.setSecTare(measureNotifyDto.getTare());
                    leaveTask.setSecTareTime(DateUtils.dateTime(measureNotifyDto.getTareTime()));
                    leaveTask.setUpdateTime(DateUtils.getNowDate());
                    leaveTask.setUpdateBy("admin");
                    if(Objects.nonNull(leaveTask.getSecGross())){
                        //计算净重
                        leaveTask.setSecNetWeight(leaveTask.getSecGross().subtract(leaveTask.getSecTare()));
                        leaveTask.setSecNetWeightTime(DateUtils.getNowDate());
                    }
                    leaveTask.setTaskStatus(LeaveTaskStatusEnum.COMPLETE.getCode());
                    leaveTaskMapper.updateLeaveTask(leaveTask);
                }
            }else{
                log.error("任务状态不匹配，计划号：{}，车牌号：{}", measureNotifyDto.getPlanId(), measureNotifyDto.getCarNo());
                return;
            }
        }
    }

    @Override
    public int updateLeaveTaskUnload(LeaveTask leaveTask) {
        leaveTask.setUnloadingTime(DateUtils.getNowDate());
        int i = leaveTaskMapper.updateLeaveTask(leaveTask);
        return i;

    }

    @Override
    public int isAllowDispatch(LeaveTask leaveTask) {
        int row = 0;
        List<LeaveTask> leaveTasks = leaveTaskMapper.isAllowDispatch(leaveTask);
        if (!CollectionUtils.isEmpty(leaveTasks)) {
            for (LeaveTask task : leaveTasks) {
                Integer taskStatus = task.getTaskStatus();
                if (taskStatus != 9) {
                    row = row + 1;
                }
            }

        }
        return row;
    }

    @Override
    public List<DProcessTMeasure> selectProcessTypeListByValidFlag(DProcessTMeasure d_process_tMeasure) {
        List<DProcessTMeasure> list = leaveTaskMapper.selectProcessTypeListByValidFlag(d_process_tMeasure);
        return list;
    }

    private boolean isStatusMatch(LeaveTask leaveTask){
        LeaveTaskStatusEnum statusEnum = LeaveTaskStatusEnum.getByCode(leaveTask.getTaskStatus());
        switch (statusEnum) {
            case WAIT_PICKUP:
            case WAIT_UNLOADING:
            case WAIT_RECHECK_UNLOADING:
            case WAIT_RECHECK_PICKUP:
                return true;
            default:
                return false;
        }
    }

}
