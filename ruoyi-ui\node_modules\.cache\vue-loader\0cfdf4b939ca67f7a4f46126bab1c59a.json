{"remainingRequest": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\src\\views\\supply\\info\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\src\\views\\supply\\info\\index.vue", "mtime": 1755499162065}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgeyBsaXN0SW5mbywgYWRkSW5mbywgdXBkYXRlSW5mbywgZGVsSW5mbywgZXhwb3J0SW5mbyB9IGZyb20gJ0AvYXBpL3N1cHBseS9pbmZvJw0KaW1wb3J0IHsgZ2V0RmFjLCBhZGRGYWMsIHVwZGF0ZUZhYyB9IGZyb20gJ0AvYXBpL3N1cHBseS9mYWMnDQppbXBvcnQgeyBnZXRIZWFsdGgsIGFkZEhlYWx0aCwgdXBkYXRlSGVhbHRoIH0gZnJvbSAnQC9hcGkvc3VwcGx5L2hlYWx0aCcNCmltcG9ydCB7IGxpc3RGaWxlLCBkZWxGaWxlIH0gZnJvbSAnQC9hcGkvc3VwcGx5L2ZpbGUnDQppbXBvcnQgcmVxdWVzdCBmcm9tICdAL3V0aWxzL3JlcXVlc3QnDQppbXBvcnQgeyBnZXRUb2tlbiB9IGZyb20gJ0AvdXRpbHMvYXV0aCcNCg0KZXhwb3J0IGRlZmF1bHQgew0KICBuYW1lOiAnU3VwcGx5VXNlckluZm8nLA0KICBkYXRhKCkgew0KICAgIHJldHVybiB7DQogICAgICBxdWVyeVBhcmFtczogew0KICAgICAgICBwYWdlTnVtOiAxLA0KICAgICAgICBwYWdlU2l6ZTogMTAsDQogICAgICAgIHN1cHBseUNvZGU6ICcnDQogICAgICB9LA0KICAgICAgdXNlckxpc3Q6IFtdLA0KICAgICAgdG90YWw6IDAsDQogICAgICAvLyDlspfkvY3or4bliKvljaENCiAgICAgIGZhY0RpYWxvZ1Zpc2libGU6IGZhbHNlLA0KICAgICAgZmFjRm9ybToge30sDQogICAgICBmYWNGb3JtSXRlbXM6IFsNCiAgICAgICAgeyBmaWVsZDogJ3VzZXJQb3N0JywgdGl0bGU6ICflspfkvY3lkI3np7AnLCBzcGFuOiAyNCwgaXRlbVJlbmRlcjogeyBuYW1lOiAnVnhlVGV4dGFyZWEnLCBwcm9wczogeyBwbGFjZWhvbGRlcjogJ+ivt+i+k+WFpeWyl+S9jeWQjeensCcsIHJvd3M6IDIgfSB9IH0sDQogICAgICAgIHsgZmllbGQ6ICd1c2VyRmFjQ2xhc3MnLCB0aXRsZTogJ+Wyl+S9jeePree7hCcsIHNwYW46IDEyLCBpdGVtUmVuZGVyOiB7IG5hbWU6ICdWeGVJbnB1dCcsIHByb3BzOiB7IHBsYWNlaG9sZGVyOiAn6K+36L6T5YWl5bKX5L2N54+t57uEJyB9IH0gfSwNCiAgICAgICAgeyBmaWVsZDogJ3VzZXJEZXB0TmFtZScsIHRpdGxlOiAn5omA5bGe6YOo6ZeoJywgc3BhbjogMTIsIGl0ZW1SZW5kZXI6IHsgbmFtZTogJ1Z4ZUlucHV0JywgcHJvcHM6IHsgcGxhY2Vob2xkZXI6ICfor7fovpPlhaXmiYDlsZ7pg6jpl6gnIH0gfSB9LA0KICAgICAgICB7IGZpZWxkOiAndXNlckZhY1dvcmsnLCB0aXRsZTogJ+Wyl+S9jeaPj+i/sCcsIHNwYW46IDEyLCBpdGVtUmVuZGVyOiB7IG5hbWU6ICdWeGVJbnB1dCcsIHByb3BzOiB7IHBsYWNlaG9sZGVyOiAn6K+36L6T5YWl5bKX5L2N5o+P6L+wJyB9IH0gfSwNCiAgICAgICAgeyBmaWVsZDogJ3VzZXJUaW1lQmVnaW4nLCB0aXRsZTogJ+WFpeWOguaXtumXtCcsIHNwYW46IDEyLCBpdGVtUmVuZGVyOiB7IG5hbWU6ICdWeGVJbnB1dCcsIHByb3BzOiB7IHR5cGU6ICdkYXRlJywgcGxhY2Vob2xkZXI6ICfpgInmi6nml6XmnJ8nIH0gfSB9LA0KICAgICAgICB7IGZpZWxkOiAndXNlclRpbWVFbmQnLCB0aXRsZTogJ+emu+WOguaXtumXtCcsIHNwYW46IDEyLCBpdGVtUmVuZGVyOiB7IG5hbWU6ICdWeGVJbnB1dCcsIHByb3BzOiB7IHR5cGU6ICdkYXRlJywgcGxhY2Vob2xkZXI6ICfpgInmi6nml6XmnJ8nIH0gfSB9LA0KICAgICAgICB7DQogICAgICAgICAgZmllbGQ6ICdzdGF0ZScsDQogICAgICAgICAgdGl0bGU6ICfnirbmgIEnLA0KICAgICAgICAgIHNwYW46IDI0LA0KICAgICAgICAgIGl0ZW1SZW5kZXI6IHsNCiAgICAgICAgICAgIG5hbWU6ICdWeGVTZWxlY3QnLA0KICAgICAgICAgICAgb3B0aW9uczogWw0KICAgICAgICAgICAgICB7IGxhYmVsOiAn6LW36I2JJywgdmFsdWU6IDAgfSwNCiAgICAgICAgICAgICAgeyBsYWJlbDogJ+WIhuWOguWuoeaguOS6uicsIHZhbHVlOiAxIH0sDQogICAgICAgICAgICAgIHsgbGFiZWw6ICfkurrlipvotYTmupDpg6gnLCB2YWx1ZTogMiB9LA0KICAgICAgICAgICAgICB7IGxhYmVsOiAn6YCA5ZueJywgdmFsdWU6IC0xIH0sDQogICAgICAgICAgICAgIHsgbGFiZWw6ICfnpoHnlKgnLCB2YWx1ZTogMTAxIH0sDQogICAgICAgICAgICAgIHsgbGFiZWw6ICflrqHmoLjpgJrov4cnLCB2YWx1ZTogOTkgfSwNCiAgICAgICAgICAgICAgeyBsYWJlbDogJ+WIoOmZpCcsIHZhbHVlOiAxMDIgfQ0KICAgICAgICAgICAgXSwNCiAgICAgICAgICAgIHByb3BzOiB7IHBsYWNlaG9sZGVyOiAn6K+36YCJ5oupJyB9DQogICAgICAgICAgfQ0KICAgICAgICB9DQogICAgICBdLA0KICAgICAgLy8g5YGl5bq35L+h5oGvDQogICAgICBoZWFsdGhEaWFsb2dWaXNpYmxlOiBmYWxzZSwNCiAgICAgIGhlYWx0aEZvcm06IHt9LA0KICAgICAgaGVhbHRoRm9ybUl0ZW1zOiBbDQogICAgICAgIHsgZmllbGQ6ICdoZWFsZGF0ZScsIHRpdGxlOiAn5L2T5qOA5pel5pyfJywgc3BhbjogMTIsIGl0ZW1SZW5kZXI6IHsgbmFtZTogJ1Z4ZUlucHV0JywgcHJvcHM6IHsgdHlwZTogJ2RhdGUnLCBwbGFjZWhvbGRlcjogJ+mAieaLqeaXpeacnycgfSB9IH0sDQogICAgICAgIHsgZmllbGQ6ICdob3MnLCB0aXRsZTogJ+WMu+mZoicsIHNwYW46IDEyLCBpdGVtUmVuZGVyOiB7IG5hbWU6ICdWeGVJbnB1dCcsIHByb3BzOiB7IHBsYWNlaG9sZGVyOiAn6K+36L6T5YWl5Yy76ZmiJyB9IH0gfSwNCiAgICAgICAgeyBmaWVsZDogJ2hlYWx0eicsIHRpdGxlOiAn5L2T6YeNJywgc3BhbjogMTIsIGl0ZW1SZW5kZXI6IHsgbmFtZTogJ1Z4ZUlucHV0JywgcHJvcHM6IHsgcGxhY2Vob2xkZXI6ICfor7fovpPlhaXkvZPph40nIH0gfSB9LA0KICAgICAgICB7IGZpZWxkOiAnaGVhbHR6enMnLCB0aXRsZTogJ+S9k+mHjeaMh+aVsCcsIHNwYW46IDEyLCBpdGVtUmVuZGVyOiB7IG5hbWU6ICdWeGVJbnB1dCcsIHByb3BzOiB7IHBsYWNlaG9sZGVyOiAn6K+36L6T5YWl5L2T6YeN5oyH5pWwJyB9IH0gfSwNCiAgICAgICAgeyBmaWVsZDogJ2hlYWxwdHQnLCB0aXRsZTogJ+ihgOezlicsIHNwYW46IDEyLCBpdGVtUmVuZGVyOiB7IG5hbWU6ICdWeGVJbnB1dCcsIHByb3BzOiB7IHBsYWNlaG9sZGVyOiAn6K+36L6T5YWl6KGA57OWJyB9IH0gfSwNCiAgICAgICAgeyBmaWVsZDogJ2hlYWxzc3knLCB0aXRsZTogJ+aUtue8qeWOiycsIHNwYW46IDEyLCBpdGVtUmVuZGVyOiB7IG5hbWU6ICdWeGVJbnB1dCcsIHByb3BzOiB7IHBsYWNlaG9sZGVyOiAn6K+36L6T5YWl5pS257yp5Y6LJyB9IH0gfSwNCiAgICAgICAgeyBmaWVsZDogJ2hlYWxzenknLCB0aXRsZTogJ+iIkuW8oOWOiycsIHNwYW46IDEyLCBpdGVtUmVuZGVyOiB7IG5hbWU6ICdWeGVJbnB1dCcsIHByb3BzOiB7IHBsYWNlaG9sZGVyOiAn6K+36L6T5YWl6IiS5byg5Y6LJyB9IH0gfSwNCiAgICAgICAgeyBmaWVsZDogJ2hlYWx6ZGdjJywgdGl0bGU6ICfmgLvog4blm7rphocnLCBzcGFuOiAxMiwgaXRlbVJlbmRlcjogeyBuYW1lOiAnVnhlSW5wdXQnLCBwcm9wczogeyBwbGFjZWhvbGRlcjogJ+ivt+i+k+WFpeaAu+iDhuWbuumGhycgfSB9IH0sDQogICAgICAgIHsgZmllbGQ6ICdoZWFsZ3lzeicsIHRpdGxlOiAn55SY5rK55LiJ6YWvJywgc3BhbjogMTIsIGl0ZW1SZW5kZXI6IHsgbmFtZTogJ1Z4ZUlucHV0JywgcHJvcHM6IHsgcGxhY2Vob2xkZXI6ICfor7fovpPlhaXnlJjmsrnkuInpha8nIH0gfSB9LA0KICAgICAgICB7IGZpZWxkOiAnaGVhbGdhJywgdGl0bGU6ICfosLfmsKjphbDovazogr3phbYnLCBzcGFuOiAxMiwgaXRlbVJlbmRlcjogeyBuYW1lOiAnVnhlSW5wdXQnLCBwcm9wczogeyBwbGFjZWhvbGRlcjogJ+ivt+i+k+WFpeiwt+awqOmFsOi9rOiCvemFticgfSB9IH0sDQogICAgICAgIHsgZmllbGQ6ICdoZWFsZ2InLCB0aXRsZTogJ+iwt+S4mei9rOawqOmFticsIHNwYW46IDEyLCBpdGVtUmVuZGVyOiB7IG5hbWU6ICdWeGVJbnB1dCcsIHByb3BzOiB7IHBsYWNlaG9sZGVyOiAn6K+36L6T5YWl6LC35LiZ6L2s5rCo6YW2JyB9IH0gfSwNCiAgICAgICAgeyBmaWVsZDogJ2hlYWxnYycsIHRpdGxlOiAn6LC36I2J6L2s5rCo6YW2Jywgc3BhbjogMTIsIGl0ZW1SZW5kZXI6IHsgbmFtZTogJ1Z4ZUlucHV0JywgcHJvcHM6IHsgcGxhY2Vob2xkZXI6ICfor7fovpPlhaXosLfojYnovazmsKjphbYnIH0gfSB9LA0KICAgICAgICB7IGZpZWxkOiAnaGVhbG5zZCcsIHRpdGxlOiAn5bC/57Sg5rCuJywgc3BhbjogMTIsIGl0ZW1SZW5kZXI6IHsgbmFtZTogJ1Z4ZUlucHV0JywgcHJvcHM6IHsgcGxhY2Vob2xkZXI6ICfor7fovpPlhaXlsL/ntKDmsK4nIH0gfSB9LA0KICAgICAgICB7IGZpZWxkOiAnaGVhbGpnJywgdGl0bGU6ICfogozphZAnLCBzcGFuOiAxMiwgaXRlbVJlbmRlcjogeyBuYW1lOiAnVnhlSW5wdXQnLCBwcm9wczogeyBwbGFjZWhvbGRlcjogJ+ivt+i+k+WFpeiCjOmFkCcgfSB9IH0sDQogICAgICAgIHsgZmllbGQ6ICdoZWFseGQnLCB0aXRsZTogJ+W/g+eUteWbvicsIHNwYW46IDEyLCBpdGVtUmVuZGVyOiB7IG5hbWU6ICdWeGVJbnB1dCcsIHByb3BzOiB7IHBsYWNlaG9sZGVyOiAn6K+36L6T5YWl5b+D55S15Zu+JyB9IH0gfSwNCiAgICAgICAgeyBmaWVsZDogJ2hlYWx4aicsIHRpdGxlOiAn5bCP57uTJywgc3BhbjogMjQsIGl0ZW1SZW5kZXI6IHsgbmFtZTogJ1Z4ZVRleHRhcmVhJywgcHJvcHM6IHsgcGxhY2Vob2xkZXI6ICfor7fovpPlhaXlsI/nu5MnLCByb3dzOiAyIH0gfSB9LA0KICAgICAgICB7IGZpZWxkOiAnaGVhbGp5JywgdGl0bGU6ICflu7rorq4nLCBzcGFuOiAyNCwgaXRlbVJlbmRlcjogeyBuYW1lOiAnVnhlVGV4dGFyZWEnLCBwcm9wczogeyBwbGFjZWhvbGRlcjogJ+ivt+i+k+WFpeW7uuiuricsIHJvd3M6IDIgfSB9IH0sDQogICAgICAgIHsNCiAgICAgICAgICBmaWVsZDogJ3N0YXRlJywNCiAgICAgICAgICB0aXRsZTogJ+eKtuaAgScsDQogICAgICAgICAgc3BhbjogMTIsDQogICAgICAgICAgaXRlbVJlbmRlcjogew0KICAgICAgICAgICAgbmFtZTogJ1Z4ZVNlbGVjdCcsDQogICAgICAgICAgICBvcHRpb25zOiBbDQogICAgICAgICAgICAgIHsgbGFiZWw6ICfmraPluLgnLCB2YWx1ZTogMSB9LA0KICAgICAgICAgICAgICB7IGxhYmVsOiAn5Yig6ZmkJywgdmFsdWU6IDEwMSB9DQogICAgICAgICAgICBdLA0KICAgICAgICAgICAgcHJvcHM6IHsgcGxhY2Vob2xkZXI6ICfor7fpgInmi6knIH0NCiAgICAgICAgICB9DQogICAgICAgIH0NCiAgICAgIF0sDQogICAgICAvLyDpmYTku7YNCiAgICAgIGZpbGVEaWFsb2dWaXNpYmxlOiBmYWxzZSwNCiAgICAgIGZpbGVMaXN0OiBbXSwNCiAgICAgIHVwbG9hZFVybDogcHJvY2Vzcy5lbnYuVlVFX0FQUF9CQVNFX0FQSSArICcvd2ViL3N1cHBseS91c2VyZmlsZS91cGxvYWQnLCAvLyDmlrDnmoQgU0ZUUCDkuIrkvKDmjqXlj6MNCiAgICAgIGN1cnJlbnRVc2VySWQ6IG51bGwsDQogICAgICBjdXJyZW50VXNlckluZm86IHt9LCAvLyDmlrDlop7vvJrkv53lrZjlvZPliY3nlKjmiLfkv6Hmga8NCiAgICAgIC8vIOS4iuS8oOmFjee9rg0KICAgICAgdXBsb2FkOiB7DQogICAgICAgIGhlYWRlcnM6IHsgQXV0aG9yaXphdGlvbjogJ0JlYXJlciAnICsgZ2V0VG9rZW4oKSB9DQogICAgICB9LA0KICAgICAgLy8g5paw5aKeL+e8lui+keS4u+ihqA0KICAgICAgZGlhbG9nVmlzaWJsZTogZmFsc2UsDQogICAgICBkaWFsb2dUaXRsZTogJycsDQogICAgICBmb3JtOiB7fSwNCiAgICAgIGltcG9ydERpYWxvZ1Zpc2libGU6IGZhbHNlLA0KICAgICAgaW1wb3J0VXJsOiAnL3dlYi9zdXBwbHkvdXNlcmluZm8vaW1wb3J0Jw0KICAgIH0NCiAgfSwNCiAgY29tcHV0ZWQ6IHsNCiAgICB1cGxvYWREYXRhKCkgew0KICAgICAgcmV0dXJuIHsNCiAgICAgICAgdXNlcmlkOiB0aGlzLmN1cnJlbnRVc2VySWQsDQogICAgICAgIHVzZXJjb2RlOiB0aGlzLmN1cnJlbnRVc2VySW5mby51c2VyQ29kZSwNCiAgICAgICAgdXNlcm5hbWU6IHRoaXMuY3VycmVudFVzZXJJbmZvLnVzZXJOYW1lLA0KICAgICAgICBzdXBwbHljb2RlOiB0aGlzLmN1cnJlbnRVc2VySW5mby5zdXBwbHlDb2RlLA0KICAgICAgICBzdXBwbHluYW1lOiB0aGlzLmN1cnJlbnRVc2VySW5mby5zdXBwbHlOYW1lLA0KICAgICAgICBpZGNhcmQ6IHRoaXMuY3VycmVudFVzZXJJbmZvLmlkY2FyZCwNCiAgICAgICAgdXNlcmRlcHRuYW1lOiB0aGlzLmN1cnJlbnRVc2VySW5mby51c2VyRGVwdE5hbWUNCiAgICAgIH0NCiAgICB9DQogIH0sDQogIG1ldGhvZHM6IHsNCiAgICAvLyDmn6Xor6LnlKjmiLfliJfooagNCiAgICBoYW5kbGVRdWVyeSgpIHsNCiAgICAgIGxpc3RJbmZvKHRoaXMucXVlcnlQYXJhbXMpLnRoZW4ocmVzID0+IHsNCiAgICAgICAgdGhpcy51c2VyTGlzdCA9IHJlcy5yb3dzDQogICAgICAgIHRoaXMudG90YWwgPSByZXMudG90YWwNCiAgICAgIH0pDQogICAgfSwNCiAgICByZXNldFF1ZXJ5KCkgew0KICAgICAgdGhpcy5xdWVyeVBhcmFtcy5zdXBwbHlDb2RlID0gJycNCiAgICAgIHRoaXMuaGFuZGxlUXVlcnkoKQ0KICAgIH0sDQogICAgLy8g5paw5aKeDQogICAgaGFuZGxlQWRkKCkgew0KICAgICAgdGhpcy5kaWFsb2dUaXRsZSA9ICfmlrDlop7nm7jlhbPmlrnkurrlkZgnDQogICAgICB0aGlzLmZvcm0gPSB7fQ0KICAgICAgdGhpcy5kaWFsb2dWaXNpYmxlID0gdHJ1ZQ0KICAgIH0sDQogICAgLy8g57yW6L6RDQogICAgaGFuZGxlRWRpdChyb3cpIHsNCiAgICAgIHRoaXMuZGlhbG9nVGl0bGUgPSAn57yW6L6R55u45YWz5pa55Lq65ZGYJw0KICAgICAgdGhpcy5mb3JtID0gT2JqZWN0LmFzc2lnbih7fSwgcm93KQ0KICAgICAgdGhpcy5kaWFsb2dWaXNpYmxlID0gdHJ1ZQ0KICAgIH0sDQogICAgLy8g5Yig6ZmkDQogICAgaGFuZGxlRGVsZXRlKHJvdykgew0KICAgICAgdGhpcy4kY29uZmlybSgn56Gu5a6a5Yig6Zmk6K+l5p2h5pWw5o2u5ZCX77yfJywgJ+aPkOekuicsIHsgdHlwZTogJ3dhcm5pbmcnIH0pLnRoZW4oKCkgPT4gew0KICAgICAgICBkZWxJbmZvKHJvdy5pZCkudGhlbigoKSA9PiB7DQogICAgICAgICAgdGhpcy4kbWVzc2FnZS5zdWNjZXNzKCfliKDpmaTmiJDlip8nKQ0KICAgICAgICAgIHRoaXMuaGFuZGxlUXVlcnkoKQ0KICAgICAgICB9KQ0KICAgICAgfSkNCiAgICB9LA0KICAgIC8vIOaPkOS6pOS4u+ihqA0KICAgIHN1Ym1pdEZvcm0oKSB7DQogICAgICBpZiAodGhpcy5mb3JtLmlkKSB7DQogICAgICAgIHVwZGF0ZUluZm8odGhpcy5mb3JtKS50aGVuKCgpID0+IHsNCiAgICAgICAgICB0aGlzLiRtZXNzYWdlLnN1Y2Nlc3MoJ+S/ruaUueaIkOWKnycpDQogICAgICAgICAgdGhpcy5kaWFsb2dWaXNpYmxlID0gZmFsc2UNCiAgICAgICAgICB0aGlzLmhhbmRsZVF1ZXJ5KCkNCiAgICAgICAgfSkNCiAgICAgIH0gZWxzZSB7DQogICAgICAgIGFkZEluZm8odGhpcy5mb3JtKS50aGVuKCgpID0+IHsNCiAgICAgICAgICB0aGlzLiRtZXNzYWdlLnN1Y2Nlc3MoJ+aWsOWinuaIkOWKnycpDQogICAgICAgICAgdGhpcy5kaWFsb2dWaXNpYmxlID0gZmFsc2UNCiAgICAgICAgICB0aGlzLmhhbmRsZVF1ZXJ5KCkNCiAgICAgICAgfSkNCiAgICAgIH0NCiAgICB9LA0KICAgIC8vIOWvvOWHug0KICAgIGhhbmRsZUV4cG9ydCgpIHsNCiAgICAgIGV4cG9ydEluZm8odGhpcy5xdWVyeVBhcmFtcykudGhlbihyZXMgPT4gew0KICAgICAgICBjb25zdCBibG9iID0gbmV3IEJsb2IoW3Jlc10sIHsgdHlwZTogJ2FwcGxpY2F0aW9uL3ZuZC5tcy1leGNlbCcgfSkNCiAgICAgICAgY29uc3QgdXJsID0gd2luZG93LlVSTC5jcmVhdGVPYmplY3RVUkwoYmxvYikNCiAgICAgICAgY29uc3QgbGluayA9IGRvY3VtZW50LmNyZWF0ZUVsZW1lbnQoJ2EnKQ0KICAgICAgICBsaW5rLnN0eWxlLmRpc3BsYXkgPSAnbm9uZScNCiAgICAgICAgbGluay5ocmVmID0gdXJsDQogICAgICAgIGxpbmsuc2V0QXR0cmlidXRlKCdkb3dubG9hZCcsICfnm7jlhbPmlrnkurrlkZjmlbDmja4ueGxzeCcpDQogICAgICAgIGRvY3VtZW50LmJvZHkuYXBwZW5kQ2hpbGQobGluaykNCiAgICAgICAgbGluay5jbGljaygpDQogICAgICAgIGRvY3VtZW50LmJvZHkucmVtb3ZlQ2hpbGQobGluaykNCiAgICAgICAgd2luZG93LlVSTC5yZXZva2VPYmplY3RVUkwodXJsKQ0KICAgICAgfSkNCiAgICB9LA0KICAgIC8vIOWvvOWFpQ0KICAgIGhhbmRsZUltcG9ydCgpIHsNCiAgICAgIHRoaXMuaW1wb3J0RGlhbG9nVmlzaWJsZSA9IHRydWUNCiAgICB9LA0KICAgIGhhbmRsZUltcG9ydFN1Y2Nlc3MocmVzcG9uc2UpIHsNCiAgICAgIGlmIChyZXNwb25zZS5jb2RlID09PSAyMDApIHsNCiAgICAgICAgdGhpcy4kbWVzc2FnZS5zdWNjZXNzKCflr7zlhaXmiJDlip8nKQ0KICAgICAgICB0aGlzLmltcG9ydERpYWxvZ1Zpc2libGUgPSBmYWxzZQ0KICAgICAgICB0aGlzLmhhbmRsZVF1ZXJ5KCkNCiAgICAgIH0gZWxzZSB7DQogICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IocmVzcG9uc2UubXNnIHx8ICflr7zlhaXlpLHotKUnKQ0KICAgICAgfQ0KICAgIH0sDQogICAgLy8g5a+85YWl5YmN5qOA5p+l5paH5Lu257G75Z6LDQogICAgYmVmb3JlSW1wb3J0VXBsb2FkKGZpbGUpIHsNCiAgICAgIGNvbnN0IGlzRXhjZWwgPSBmaWxlLnR5cGUgPT09ICdhcHBsaWNhdGlvbi92bmQubXMtZXhjZWwnIHx8IGZpbGUudHlwZSA9PT0gJ2FwcGxpY2F0aW9uL3ZuZC5vcGVueG1sZm9ybWF0cy1vZmZpY2Vkb2N1bWVudC5zcHJlYWRzaGVldG1sLnNoZWV0Jw0KICAgICAgaWYgKCFpc0V4Y2VsKSB7DQogICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoJ+WPquiDveS4iuS8oEV4Y2Vs5paH5Lu277yBJykNCiAgICAgIH0NCiAgICAgIHJldHVybiBpc0V4Y2VsDQogICAgfSwNCiAgICAvLyDpmYTku7bkuIrkvKDliY3mo4Dmn6Xmlofku7bnsbvlnosNCiAgICBiZWZvcmVGaWxlVXBsb2FkKGZpbGUpIHsNCiAgICAgIC8vIOajgOafpeaWh+S7tuexu+Wei+aYr+WQpuS4ulBERg0KICAgICAgY29uc3QgaXNQREYgPSBmaWxlLnR5cGUgPT09ICdhcHBsaWNhdGlvbi9wZGYnIHx8IGZpbGUubmFtZS50b0xvd2VyQ2FzZSgpLmVuZHNXaXRoKCcucGRmJykNCiAgICAgIGlmICghaXNQREYpIHsNCiAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcign5Y+q6IO95LiK5LygUERG5qC85byP5paH5Lu277yBJykNCiAgICAgICAgcmV0dXJuIGZhbHNlDQogICAgICB9DQogICAgICANCiAgICAgIC8vIOajgOafpeaWh+S7tuWkp+Wwj+mZkOWItg0KICAgICAgY29uc3QgbWF4U2l6ZSA9IDUwICogMTAyNCAqIDEwMjQgLy8gNTBNQg0KICAgICAgaWYgKGZpbGUuc2l6ZSA+IG1heFNpemUpIHsNCiAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcign5paH5Lu25aSn5bCP5LiN6IO96LaF6L+HNTBNQu+8gScpDQogICAgICAgIHJldHVybiBmYWxzZQ0KICAgICAgfQ0KICAgICAgDQogICAgICByZXR1cm4gdHJ1ZQ0KICAgIH0sDQogICAgLy8g5bKX5L2N6K+G5Yir5Y2hDQogICAgb3BlbkZhY0RpYWxvZyhyb3cpIHsNCiAgICAgIGdldEZhYyhyb3cuaWQpLnRoZW4ocmVzID0+IHsNCiAgICAgICAgdGhpcy5mYWNGb3JtID0gcmVzLmRhdGEgfHwgeyB1c2VySWQ6IHJvdy5pZCB9DQogICAgICAgIHRoaXMuZmFjRGlhbG9nVmlzaWJsZSA9IHRydWUNCiAgICAgIH0pDQogICAgfSwNCiAgICBzdWJtaXRGYWMoKSB7DQogICAgICBjb25zdCBhcGkgPSB0aGlzLmZhY0Zvcm0uaWQgPyB1cGRhdGVGYWMgOiBhZGRGYWMNCiAgICAgIGFwaSh0aGlzLmZhY0Zvcm0pLnRoZW4oKCkgPT4gew0KICAgICAgICB0aGlzLiRtZXNzYWdlLnN1Y2Nlc3MoJ+S/neWtmOaIkOWKnycpDQogICAgICAgIHRoaXMuZmFjRGlhbG9nVmlzaWJsZSA9IGZhbHNlDQogICAgICAgIHRoaXMuaGFuZGxlUXVlcnkoKQ0KICAgICAgfSkNCiAgICB9LA0KICAgIC8vIOWBpeW6t+S/oeaBrw0KICAgIG9wZW5IZWFsdGhEaWFsb2cocm93KSB7DQogICAgICBnZXRIZWFsdGgocm93LmlkKS50aGVuKHJlcyA9PiB7DQogICAgICAgIHRoaXMuaGVhbHRoRm9ybSA9IHJlcy5kYXRhIHx8IHsgdXNlcmlkOiByb3cuaWQgfQ0KICAgICAgICB0aGlzLmhlYWx0aERpYWxvZ1Zpc2libGUgPSB0cnVlDQogICAgICB9KQ0KICAgIH0sDQogICAgc3VibWl0SGVhbHRoKCkgew0KICAgICAgY29uc3QgYXBpID0gdGhpcy5oZWFsdGhGb3JtLmlkID8gdXBkYXRlSGVhbHRoIDogYWRkSGVhbHRoDQogICAgICBhcGkodGhpcy5oZWFsdGhGb3JtKS50aGVuKCgpID0+IHsNCiAgICAgICAgdGhpcy4kbWVzc2FnZS5zdWNjZXNzKCfkv53lrZjmiJDlip8nKQ0KICAgICAgICB0aGlzLmhlYWx0aERpYWxvZ1Zpc2libGUgPSBmYWxzZQ0KICAgICAgICB0aGlzLmhhbmRsZVF1ZXJ5KCkNCiAgICAgIH0pDQogICAgfSwNCiAgICAvLyDpmYTku7bnrqHnkIYNCiAgICBvcGVuRmlsZURpYWxvZyhyb3cpIHsNCiAgICAgIHRoaXMuY3VycmVudFVzZXJJZCA9IHJvdy5pZA0KICAgICAgdGhpcy5jdXJyZW50VXNlckluZm8gPSByb3cgLy8g5L+d5a2Y5b2T5YmN55So5oi35L+h5oGvDQogICAgICB0aGlzLmdldEZpbGVMaXN0KHJvdy5pZCkNCiAgICAgIHRoaXMuZmlsZURpYWxvZ1Zpc2libGUgPSB0cnVlDQogICAgfSwNCiAgICBnZXRGaWxlTGlzdCh1c2VyaWQpIHsNCiAgICAgIGxpc3RGaWxlKHsgdXNlcmlkIH0pLnRoZW4ocmVzID0+IHsNCiAgICAgICAgdGhpcy5maWxlTGlzdCA9IHJlcy5yb3dzDQogICAgICB9KQ0KICAgIH0sDQogICAgaGFuZGxlRmlsZVVwbG9hZFN1Y2Nlc3MocmVzcG9uc2UpIHsNCiAgICAgIGlmIChyZXNwb25zZS5jb2RlID09PSAyMDApIHsNCiAgICAgICAgdGhpcy4kbWVzc2FnZS5zdWNjZXNzKCfmlofku7bkuIrkvKDmiJDlip8nKQ0KICAgICAgICB0aGlzLmdldEZpbGVMaXN0KHRoaXMuY3VycmVudFVzZXJJZCkNCiAgICAgIH0gZWxzZSB7DQogICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IocmVzcG9uc2UubXNnIHx8ICfmlofku7bkuIrkvKDlpLHotKUnKQ0KICAgICAgfQ0KICAgIH0sDQogICAgaGFuZGxlRmlsZVVwbG9hZEVycm9yKGVycikgew0KICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcign5paH5Lu25LiK5Lyg5aSx6LSlOiAnICsgKGVyci5tZXNzYWdlIHx8ICfmnKrnn6XplJnor68nKSkNCiAgICB9LA0KICAgIGRlbGV0ZUZpbGUocm93KSB7DQogICAgICB0aGlzLiRjb25maXJtKCfnoa7lrprliKDpmaTor6XpmYTku7blkJfvvJ8nLCAn5o+Q56S6JywgeyB0eXBlOiAnd2FybmluZycgfSkudGhlbigoKSA9PiB7DQogICAgICAgIGRlbEZpbGUocm93LmlkKS50aGVuKCgpID0+IHsNCiAgICAgICAgICB0aGlzLiRtZXNzYWdlLnN1Y2Nlc3MoJ+WIoOmZpOaIkOWKnycpDQogICAgICAgICAgdGhpcy5nZXRGaWxlTGlzdCh0aGlzLmN1cnJlbnRVc2VySWQpDQogICAgICAgIH0pDQogICAgICB9KQ0KICAgIH0sDQogICAgZG93bmxvYWRGaWxlKHJvdykgew0KICAgICAgLy8g6LCD55So5LiL6L295o6l5Y+j6I635Y+W5paH5Lu2VVJMDQogICAgICByZXF1ZXN0LmdldChgL3dlYi9zdXBwbHkvdXNlcmZpbGUvZG93bmxvYWQvJHtyb3cuaWR9YCkudGhlbihyZXNwb25zZSA9PiB7DQogICAgICAgIGlmIChyZXNwb25zZS5jb2RlID09PSAyMDApIHsNCiAgICAgICAgICAvLyDojrflj5bliLDmlofku7ZVUkzlkI7vvIzlnKjmlrDnqpflj6PkuK3miZPlvIDkuIvovb0NCiAgICAgICAgICBjb25zdCBmaWxlVXJsID0gcmVzcG9uc2UuZGF0YQ0KICAgICAgICAgIHdpbmRvdy5vcGVuKGZpbGVVcmwsICdfYmxhbmsnKQ0KICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IocmVzcG9uc2UubXNnIHx8ICfkuIvovb3lpLHotKUnKQ0KICAgICAgICB9DQogICAgICB9KS5jYXRjaChlcnJvciA9PiB7DQogICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoJ+S4i+i9veWksei0pTogJyArIGVycm9yLm1lc3NhZ2UpDQogICAgICB9KQ0KICAgIH0sDQogICAgdGFibGVSb3dDbGFzc05hbWUoeyByb3csIHJvd0luZGV4IH0pIHsNCiAgICAgIGlmIChyb3cuc3RhdGUgPT09IDEpIHsNCiAgICAgICAgcmV0dXJuICdzdWNjZXNzLXJvdycNCiAgICAgIH0gZWxzZSB7DQogICAgICAgIHJldHVybiAnZGFuZ2VyLXJvdycNCiAgICAgIH0NCiAgICB9DQogIH0sDQogIG1vdW50ZWQoKSB7DQogICAgdGhpcy5oYW5kbGVRdWVyeSgpDQogIH0NCn0NCg=="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA8RA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/supply/info", "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <!-- 查询表单 -->\r\n    <el-form :inline=\"true\" :model=\"queryParams\" class=\"demo-form-inline\" @submit.native.prevent>\r\n      <el-form-item label=\"供应商代码\">\r\n        <el-input v-model=\"queryParams.supplyCode\" placeholder=\"请输入供应商代码\" clearable />\r\n      </el-form-item>\r\n      <el-form-item>\r\n        <el-button type=\"primary\" icon=\"el-icon-search\" @click=\"handleQuery\">查询</el-button>\r\n        <el-button icon=\"el-icon-refresh\" @click=\"resetQuery\">重置</el-button>\r\n      </el-form-item>\r\n    </el-form>\r\n\r\n    <!-- 工具栏 -->\r\n    <div style=\"margin-bottom: 10px;\">\r\n      <el-button type=\"primary\" icon=\"el-icon-plus\" @click=\"handleAdd\">新增</el-button>\r\n      <el-button type=\"success\" icon=\"el-icon-upload\" @click=\"handleImport\">导入</el-button>\r\n      <el-button type=\"warning\" icon=\"el-icon-download\" @click=\"handleExport\">导出</el-button>\r\n    </div>\r\n\r\n    <!-- 用户列表 -->\r\n    <el-table :data=\"userList\" border stripe style=\"width: 100%\">\r\n      <el-table-column prop=\"id\" label=\"ID\" width=\"60\" />\r\n      <el-table-column prop=\"supplyCode\" label=\"供应商代码\" />\r\n      <el-table-column prop=\"userName\" label=\"用户姓名\" />\r\n      <el-table-column prop=\"idcard\" label=\"身份证\" />\r\n      <el-table-column label=\"岗位识别卡\">\r\n        <template slot-scope=\"scope\">\r\n          <el-button size=\"mini\" @click=\"openFacDialog(scope.row)\">补充/编辑</el-button>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"健康信息\">\r\n        <template slot-scope=\"scope\">\r\n          <el-button size=\"mini\" @click=\"openHealthDialog(scope.row)\">补充/编辑</el-button>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"附件\">\r\n        <template slot-scope=\"scope\">\r\n          <el-button size=\"mini\" @click=\"openFileDialog(scope.row)\">管理</el-button>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"操作\" width=\"200\" align=\"center\">\r\n        <template slot-scope=\"scope\">\r\n          <div class=\"operation-buttons\">\r\n            <el-button\r\n              size=\"mini\"\r\n              type=\"primary\"\r\n              icon=\"el-icon-download\"\r\n              @click=\"downloadFile(scope.row)\"\r\n            >\r\n              下载\r\n            </el-button>\r\n            <el-button\r\n              size=\"mini\"\r\n              type=\"danger\"\r\n              icon=\"el-icon-delete\"\r\n              @click=\"deleteFile(scope.row)\"\r\n            >\r\n              删除\r\n            </el-button>\r\n          </div>\r\n        </template>\r\n      </el-table-column>\r\n    </el-table>\r\n\r\n    <!-- 分页 -->\r\n    <el-pagination\r\n      style=\"margin-top: 10px;\"\r\n      background\r\n      layout=\"total, prev, pager, next, jumper\"\r\n      :total=\"total\"\r\n      :page-size=\"queryParams.pageSize\"\r\n      :current-page.sync=\"queryParams.pageNum\"\r\n      @current-change=\"handleQuery\"\r\n    />\r\n\r\n    <!-- 新增/编辑主表弹窗 -->\r\n    <el-dialog :title=\"dialogTitle\" :visible.sync=\"dialogVisible\">\r\n      <el-form :model=\"form\" label-width=\"100px\">\r\n        <el-form-item label=\"供应商代码\">\r\n          <el-input v-model=\"form.supplyCode\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"供应商名称\">\r\n          <el-input v-model=\"form.supplyName\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"用户编号\">\r\n          <el-input v-model=\"form.userCode\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"用户姓名\">\r\n          <el-input v-model=\"form.userName\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"身份证\">\r\n          <el-input v-model=\"form.idcard\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"状态\">\r\n          <el-select v-model=\"form.state\" placeholder=\"请选择\">\r\n            <el-option label=\"正常\" :value=\"1\" />\r\n            <el-option label=\"删除\" :value=\"101\" />\r\n          </el-select>\r\n        </el-form-item>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"dialogVisible = false\">取 消</el-button>\r\n        <el-button type=\"primary\" @click=\"submitForm\">确 定</el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <!-- 导入弹窗 -->\r\n    <el-dialog title=\"导入相关方人员\" :visible.sync=\"importDialogVisible\">\r\n      <el-upload\r\n        :action=\"importUrl\"\r\n        :show-file-list=\"false\"\r\n        :on-success=\"handleImportSuccess\"\r\n        :before-upload=\"beforeImportUpload\"\r\n        :headers=\"uploadHeaders\"\r\n      >\r\n        <el-button type=\"primary\">选择文件上传</el-button>\r\n      </el-upload>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"importDialogVisible = false\">关 闭</el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <!-- 岗位识别卡弹窗 -->\r\n    <vxe-modal v-model=\"facDialogVisible\" title=\"岗位识别卡\" width=\"700\" show-footer>\r\n      <vxe-form\r\n        :data=\"facForm\"\r\n        :items=\"facFormItems\"\r\n        title-align=\"left\"\r\n        title-width=\"90\"\r\n        title-colon\r\n        border\r\n        size=\"small\"\r\n      />\r\n      <template #footer>\r\n        <vxe-button @click=\"facDialogVisible = false\">取消</vxe-button>\r\n        <vxe-button status=\"primary\" @click=\"submitFac\">保存</vxe-button>\r\n      </template>\r\n    </vxe-modal>\r\n\r\n    <!-- 健康信息弹窗 -->\r\n    <vxe-modal v-model=\"healthDialogVisible\" title=\"健康信息\" width=\"800\" show-footer>\r\n      <vxe-form\r\n        :data=\"healthForm\"\r\n        :items=\"healthFormItems\"\r\n        title-align=\"left\"\r\n        title-width=\"90\"\r\n        title-colon\r\n        border\r\n        size=\"small\"\r\n      />\r\n      <template #footer>\r\n        <vxe-button @click=\"healthDialogVisible = false\">取消</vxe-button>\r\n        <vxe-button status=\"primary\" @click=\"submitHealth\">保存</vxe-button>\r\n      </template>\r\n    </vxe-modal>\r\n\r\n    <!-- 附件管理弹窗 -->\r\n    <el-dialog \r\n      :visible.sync=\"fileDialogVisible\" \r\n      title=\"附件管理\" \r\n      width=\"800px\"\r\n      :close-on-click-modal=\"false\"\r\n      :close-on-press-escape=\"false\"\r\n    >\r\n      <!-- 上传区域 -->\r\n      <div class=\"upload-section\">\r\n        <div class=\"upload-header\">\r\n          <i class=\"el-icon-upload\"></i>\r\n          <span class=\"upload-title\">文件上传</span>\r\n        </div>\r\n        <div class=\"upload-content\">\r\n          <el-upload\r\n            ref=\"fileUpload\"\r\n            :action=\"uploadUrl\"\r\n            :headers=\"upload.headers\"\r\n            :data=\"uploadData\"\r\n            :on-success=\"handleFileUploadSuccess\"\r\n            :on-error=\"handleFileUploadError\"\r\n            :before-upload=\"beforeFileUpload\"\r\n            :show-file-list=\"false\"\r\n            accept=\".pdf\"\r\n            drag\r\n            class=\"upload-dragger\"\r\n          >\r\n            <div class=\"upload-area\">\r\n              <i class=\"el-icon-upload upload-icon\"></i>\r\n              <div class=\"upload-text\">\r\n                <span class=\"upload-main-text\">将PDF文件拖到此处，或</span>\r\n                <em class=\"upload-click-text\">点击上传</em>\r\n              </div>\r\n              <div class=\"upload-tip\">\r\n                <i class=\"el-icon-info\"></i>\r\n                <span>仅支持PDF格式文件，单个文件不超过50MB</span>\r\n              </div>\r\n              <div class=\"upload-limits\">\r\n                <div class=\"limit-item\">\r\n                  <i class=\"el-icon-document\"></i>\r\n                  <span>文件格式：PDF</span>\r\n                </div>\r\n                <div class=\"limit-item\">\r\n                  <i class=\"el-icon-files\"></i>\r\n                  <span>文件大小：≤ 50MB</span>\r\n                </div>\r\n                <div class=\"limit-item\">\r\n                  <i class=\"el-icon-upload2\"></i>\r\n                  <span>支持拖拽上传</span>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </el-upload>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 文件列表 -->\r\n      <div class=\"file-list-section\">\r\n        <div class=\"file-list-header\">\r\n          <i class=\"el-icon-document\"></i>\r\n          <span class=\"file-list-title\">已上传文件</span>\r\n          <span class=\"file-count\">(共 {{fileList.length}} 个文件)</span>\r\n        </div>\r\n        <div class=\"file-list-content\">\r\n          <el-table \r\n            :data=\"fileList\" \r\n            style=\"width: 100%\"\r\n            :header-cell-style=\"{background:'#f5f7fa',color:'#606266'}\"\r\n            :row-class-name=\"tableRowClassName\"\r\n          >\r\n            <el-table-column prop=\"filename\" label=\"文件名\" min-width=\"200\">\r\n              <template slot-scope=\"scope\">\r\n                <div class=\"file-info\">\r\n                  <i class=\"el-icon-document\"></i>\r\n                  <span class=\"file-name\">{{scope.row.filename}}</span>\r\n                </div>\r\n              </template>\r\n            </el-table-column>\r\n            <el-table-column prop=\"format\" label=\"格式\" width=\"80\" align=\"center\">\r\n              <template slot-scope=\"scope\">\r\n                <el-tag size=\"mini\" type=\"info\">{{scope.row.format}}</el-tag>\r\n              </template>\r\n            </el-table-column>\r\n            <el-table-column prop=\"state\" label=\"状态\" width=\"80\" align=\"center\">\r\n              <template slot-scope=\"scope\">\r\n                <el-tag \r\n                  size=\"mini\" \r\n                  :type=\"scope.row.state === 1 ? 'success' : 'danger'\"\r\n                >\r\n                  {{scope.row.state === 1 ? '正常' : '异常'}}\r\n                </el-tag>\r\n              </template>\r\n            </el-table-column>\r\n            <el-table-column label=\"操作\" width=\"200\" align=\"center\">\r\n              <template slot-scope=\"scope\">\r\n                <div class=\"operation-buttons\">\r\n                  <el-button\r\n                    size=\"mini\"\r\n                    type=\"primary\"\r\n                    icon=\"el-icon-download\"\r\n                    @click=\"downloadFile(scope.row)\"\r\n                  >\r\n                    下载\r\n                  </el-button>\r\n                  <el-button\r\n                    size=\"mini\"\r\n                    type=\"danger\"\r\n                    icon=\"el-icon-delete\"\r\n                    @click=\"deleteFile(scope.row)\"\r\n                  >\r\n                    删除\r\n                  </el-button>\r\n                </div>\r\n              </template>\r\n            </el-table-column>\r\n          </el-table>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 弹窗底部 -->\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"fileDialogVisible = false\" icon=\"el-icon-close\">关闭</el-button>\r\n      </div>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { listInfo, addInfo, updateInfo, delInfo, exportInfo } from '@/api/supply/info'\r\nimport { getFac, addFac, updateFac } from '@/api/supply/fac'\r\nimport { getHealth, addHealth, updateHealth } from '@/api/supply/health'\r\nimport { listFile, delFile } from '@/api/supply/file'\r\nimport request from '@/utils/request'\r\nimport { getToken } from '@/utils/auth'\r\n\r\nexport default {\r\n  name: 'SupplyUserInfo',\r\n  data() {\r\n    return {\r\n      queryParams: {\r\n        pageNum: 1,\r\n        pageSize: 10,\r\n        supplyCode: ''\r\n      },\r\n      userList: [],\r\n      total: 0,\r\n      // 岗位识别卡\r\n      facDialogVisible: false,\r\n      facForm: {},\r\n      facFormItems: [\r\n        { field: 'userPost', title: '岗位名称', span: 24, itemRender: { name: 'VxeTextarea', props: { placeholder: '请输入岗位名称', rows: 2 } } },\r\n        { field: 'userFacClass', title: '岗位班组', span: 12, itemRender: { name: 'VxeInput', props: { placeholder: '请输入岗位班组' } } },\r\n        { field: 'userDeptName', title: '所属部门', span: 12, itemRender: { name: 'VxeInput', props: { placeholder: '请输入所属部门' } } },\r\n        { field: 'userFacWork', title: '岗位描述', span: 12, itemRender: { name: 'VxeInput', props: { placeholder: '请输入岗位描述' } } },\r\n        { field: 'userTimeBegin', title: '入厂时间', span: 12, itemRender: { name: 'VxeInput', props: { type: 'date', placeholder: '选择日期' } } },\r\n        { field: 'userTimeEnd', title: '离厂时间', span: 12, itemRender: { name: 'VxeInput', props: { type: 'date', placeholder: '选择日期' } } },\r\n        {\r\n          field: 'state',\r\n          title: '状态',\r\n          span: 24,\r\n          itemRender: {\r\n            name: 'VxeSelect',\r\n            options: [\r\n              { label: '起草', value: 0 },\r\n              { label: '分厂审核人', value: 1 },\r\n              { label: '人力资源部', value: 2 },\r\n              { label: '退回', value: -1 },\r\n              { label: '禁用', value: 101 },\r\n              { label: '审核通过', value: 99 },\r\n              { label: '删除', value: 102 }\r\n            ],\r\n            props: { placeholder: '请选择' }\r\n          }\r\n        }\r\n      ],\r\n      // 健康信息\r\n      healthDialogVisible: false,\r\n      healthForm: {},\r\n      healthFormItems: [\r\n        { field: 'healdate', title: '体检日期', span: 12, itemRender: { name: 'VxeInput', props: { type: 'date', placeholder: '选择日期' } } },\r\n        { field: 'hos', title: '医院', span: 12, itemRender: { name: 'VxeInput', props: { placeholder: '请输入医院' } } },\r\n        { field: 'healtz', title: '体重', span: 12, itemRender: { name: 'VxeInput', props: { placeholder: '请输入体重' } } },\r\n        { field: 'healtzzs', title: '体重指数', span: 12, itemRender: { name: 'VxeInput', props: { placeholder: '请输入体重指数' } } },\r\n        { field: 'healptt', title: '血糖', span: 12, itemRender: { name: 'VxeInput', props: { placeholder: '请输入血糖' } } },\r\n        { field: 'healssy', title: '收缩压', span: 12, itemRender: { name: 'VxeInput', props: { placeholder: '请输入收缩压' } } },\r\n        { field: 'healszy', title: '舒张压', span: 12, itemRender: { name: 'VxeInput', props: { placeholder: '请输入舒张压' } } },\r\n        { field: 'healzdgc', title: '总胆固醇', span: 12, itemRender: { name: 'VxeInput', props: { placeholder: '请输入总胆固醇' } } },\r\n        { field: 'healgysz', title: '甘油三酯', span: 12, itemRender: { name: 'VxeInput', props: { placeholder: '请输入甘油三酯' } } },\r\n        { field: 'healga', title: '谷氨酰转肽酶', span: 12, itemRender: { name: 'VxeInput', props: { placeholder: '请输入谷氨酰转肽酶' } } },\r\n        { field: 'healgb', title: '谷丙转氨酶', span: 12, itemRender: { name: 'VxeInput', props: { placeholder: '请输入谷丙转氨酶' } } },\r\n        { field: 'healgc', title: '谷草转氨酶', span: 12, itemRender: { name: 'VxeInput', props: { placeholder: '请输入谷草转氨酶' } } },\r\n        { field: 'healnsd', title: '尿素氮', span: 12, itemRender: { name: 'VxeInput', props: { placeholder: '请输入尿素氮' } } },\r\n        { field: 'healjg', title: '肌酐', span: 12, itemRender: { name: 'VxeInput', props: { placeholder: '请输入肌酐' } } },\r\n        { field: 'healxd', title: '心电图', span: 12, itemRender: { name: 'VxeInput', props: { placeholder: '请输入心电图' } } },\r\n        { field: 'healxj', title: '小结', span: 24, itemRender: { name: 'VxeTextarea', props: { placeholder: '请输入小结', rows: 2 } } },\r\n        { field: 'healjy', title: '建议', span: 24, itemRender: { name: 'VxeTextarea', props: { placeholder: '请输入建议', rows: 2 } } },\r\n        {\r\n          field: 'state',\r\n          title: '状态',\r\n          span: 12,\r\n          itemRender: {\r\n            name: 'VxeSelect',\r\n            options: [\r\n              { label: '正常', value: 1 },\r\n              { label: '删除', value: 101 }\r\n            ],\r\n            props: { placeholder: '请选择' }\r\n          }\r\n        }\r\n      ],\r\n      // 附件\r\n      fileDialogVisible: false,\r\n      fileList: [],\r\n      uploadUrl: process.env.VUE_APP_BASE_API + '/web/supply/userfile/upload', // 新的 SFTP 上传接口\r\n      currentUserId: null,\r\n      currentUserInfo: {}, // 新增：保存当前用户信息\r\n      // 上传配置\r\n      upload: {\r\n        headers: { Authorization: 'Bearer ' + getToken() }\r\n      },\r\n      // 新增/编辑主表\r\n      dialogVisible: false,\r\n      dialogTitle: '',\r\n      form: {},\r\n      importDialogVisible: false,\r\n      importUrl: '/web/supply/userinfo/import'\r\n    }\r\n  },\r\n  computed: {\r\n    uploadData() {\r\n      return {\r\n        userid: this.currentUserId,\r\n        usercode: this.currentUserInfo.userCode,\r\n        username: this.currentUserInfo.userName,\r\n        supplycode: this.currentUserInfo.supplyCode,\r\n        supplyname: this.currentUserInfo.supplyName,\r\n        idcard: this.currentUserInfo.idcard,\r\n        userdeptname: this.currentUserInfo.userDeptName\r\n      }\r\n    }\r\n  },\r\n  methods: {\r\n    // 查询用户列表\r\n    handleQuery() {\r\n      listInfo(this.queryParams).then(res => {\r\n        this.userList = res.rows\r\n        this.total = res.total\r\n      })\r\n    },\r\n    resetQuery() {\r\n      this.queryParams.supplyCode = ''\r\n      this.handleQuery()\r\n    },\r\n    // 新增\r\n    handleAdd() {\r\n      this.dialogTitle = '新增相关方人员'\r\n      this.form = {}\r\n      this.dialogVisible = true\r\n    },\r\n    // 编辑\r\n    handleEdit(row) {\r\n      this.dialogTitle = '编辑相关方人员'\r\n      this.form = Object.assign({}, row)\r\n      this.dialogVisible = true\r\n    },\r\n    // 删除\r\n    handleDelete(row) {\r\n      this.$confirm('确定删除该条数据吗？', '提示', { type: 'warning' }).then(() => {\r\n        delInfo(row.id).then(() => {\r\n          this.$message.success('删除成功')\r\n          this.handleQuery()\r\n        })\r\n      })\r\n    },\r\n    // 提交主表\r\n    submitForm() {\r\n      if (this.form.id) {\r\n        updateInfo(this.form).then(() => {\r\n          this.$message.success('修改成功')\r\n          this.dialogVisible = false\r\n          this.handleQuery()\r\n        })\r\n      } else {\r\n        addInfo(this.form).then(() => {\r\n          this.$message.success('新增成功')\r\n          this.dialogVisible = false\r\n          this.handleQuery()\r\n        })\r\n      }\r\n    },\r\n    // 导出\r\n    handleExport() {\r\n      exportInfo(this.queryParams).then(res => {\r\n        const blob = new Blob([res], { type: 'application/vnd.ms-excel' })\r\n        const url = window.URL.createObjectURL(blob)\r\n        const link = document.createElement('a')\r\n        link.style.display = 'none'\r\n        link.href = url\r\n        link.setAttribute('download', '相关方人员数据.xlsx')\r\n        document.body.appendChild(link)\r\n        link.click()\r\n        document.body.removeChild(link)\r\n        window.URL.revokeObjectURL(url)\r\n      })\r\n    },\r\n    // 导入\r\n    handleImport() {\r\n      this.importDialogVisible = true\r\n    },\r\n    handleImportSuccess(response) {\r\n      if (response.code === 200) {\r\n        this.$message.success('导入成功')\r\n        this.importDialogVisible = false\r\n        this.handleQuery()\r\n      } else {\r\n        this.$message.error(response.msg || '导入失败')\r\n      }\r\n    },\r\n    // 导入前检查文件类型\r\n    beforeImportUpload(file) {\r\n      const isExcel = file.type === 'application/vnd.ms-excel' || file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'\r\n      if (!isExcel) {\r\n        this.$message.error('只能上传Excel文件！')\r\n      }\r\n      return isExcel\r\n    },\r\n    // 附件上传前检查文件类型\r\n    beforeFileUpload(file) {\r\n      // 检查文件类型是否为PDF\r\n      const isPDF = file.type === 'application/pdf' || file.name.toLowerCase().endsWith('.pdf')\r\n      if (!isPDF) {\r\n        this.$message.error('只能上传PDF格式文件！')\r\n        return false\r\n      }\r\n      \r\n      // 检查文件大小限制\r\n      const maxSize = 50 * 1024 * 1024 // 50MB\r\n      if (file.size > maxSize) {\r\n        this.$message.error('文件大小不能超过50MB！')\r\n        return false\r\n      }\r\n      \r\n      return true\r\n    },\r\n    // 岗位识别卡\r\n    openFacDialog(row) {\r\n      getFac(row.id).then(res => {\r\n        this.facForm = res.data || { userId: row.id }\r\n        this.facDialogVisible = true\r\n      })\r\n    },\r\n    submitFac() {\r\n      const api = this.facForm.id ? updateFac : addFac\r\n      api(this.facForm).then(() => {\r\n        this.$message.success('保存成功')\r\n        this.facDialogVisible = false\r\n        this.handleQuery()\r\n      })\r\n    },\r\n    // 健康信息\r\n    openHealthDialog(row) {\r\n      getHealth(row.id).then(res => {\r\n        this.healthForm = res.data || { userid: row.id }\r\n        this.healthDialogVisible = true\r\n      })\r\n    },\r\n    submitHealth() {\r\n      const api = this.healthForm.id ? updateHealth : addHealth\r\n      api(this.healthForm).then(() => {\r\n        this.$message.success('保存成功')\r\n        this.healthDialogVisible = false\r\n        this.handleQuery()\r\n      })\r\n    },\r\n    // 附件管理\r\n    openFileDialog(row) {\r\n      this.currentUserId = row.id\r\n      this.currentUserInfo = row // 保存当前用户信息\r\n      this.getFileList(row.id)\r\n      this.fileDialogVisible = true\r\n    },\r\n    getFileList(userid) {\r\n      listFile({ userid }).then(res => {\r\n        this.fileList = res.rows\r\n      })\r\n    },\r\n    handleFileUploadSuccess(response) {\r\n      if (response.code === 200) {\r\n        this.$message.success('文件上传成功')\r\n        this.getFileList(this.currentUserId)\r\n      } else {\r\n        this.$message.error(response.msg || '文件上传失败')\r\n      }\r\n    },\r\n    handleFileUploadError(err) {\r\n      this.$message.error('文件上传失败: ' + (err.message || '未知错误'))\r\n    },\r\n    deleteFile(row) {\r\n      this.$confirm('确定删除该附件吗？', '提示', { type: 'warning' }).then(() => {\r\n        delFile(row.id).then(() => {\r\n          this.$message.success('删除成功')\r\n          this.getFileList(this.currentUserId)\r\n        })\r\n      })\r\n    },\r\n    downloadFile(row) {\r\n      // 调用下载接口获取文件URL\r\n      request.get(`/web/supply/userfile/download/${row.id}`).then(response => {\r\n        if (response.code === 200) {\r\n          // 获取到文件URL后，在新窗口中打开下载\r\n          const fileUrl = response.data\r\n          window.open(fileUrl, '_blank')\r\n        } else {\r\n          this.$message.error(response.msg || '下载失败')\r\n        }\r\n      }).catch(error => {\r\n        this.$message.error('下载失败: ' + error.message)\r\n      })\r\n    },\r\n    tableRowClassName({ row, rowIndex }) {\r\n      if (row.state === 1) {\r\n        return 'success-row'\r\n      } else {\r\n        return 'danger-row'\r\n      }\r\n    }\r\n  },\r\n  mounted() {\r\n    this.handleQuery()\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.app-container {\r\n  padding: 20px;\r\n}\r\n\r\n.upload-section {\r\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n  padding: 25px;\r\n  border-radius: 8px;\r\n  margin-bottom: 25px;\r\n  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.upload-header {\r\n  display: flex;\r\n  align-items: center;\r\n  margin-bottom: 20px;\r\n  color: #fff;\r\n}\r\n\r\n.upload-header i {\r\n  font-size: 20px;\r\n  margin-right: 10px;\r\n}\r\n\r\n.upload-title {\r\n  font-size: 18px;\r\n  font-weight: 600;\r\n}\r\n\r\n.upload-content {\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  min-height: 180px;\r\n  border: 2px dashed rgba(255, 255, 255, 0.3);\r\n  border-radius: 8px;\r\n  background-color: rgba(255, 255, 255, 0.1);\r\n  cursor: pointer;\r\n  transition: all 0.3s ease;\r\n  backdrop-filter: blur(10px);\r\n}\r\n\r\n.upload-content:hover {\r\n  border-color: rgba(255, 255, 255, 0.6);\r\n  background-color: rgba(255, 255, 255, 0.15);\r\n  transform: translateY(-2px);\r\n  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);\r\n}\r\n\r\n.upload-dragger {\r\n  width: 100%;\r\n  height: 100%;\r\n}\r\n\r\n.upload-area {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  justify-content: center;\r\n  padding: 20px;\r\n  text-align: center;\r\n}\r\n\r\n.upload-icon {\r\n  font-size: 48px;\r\n  color: rgba(255, 255, 255, 0.8);\r\n  margin-bottom: 15px;\r\n}\r\n\r\n.upload-text {\r\n  margin-bottom: 15px;\r\n}\r\n\r\n.upload-main-text {\r\n  color: rgba(255, 255, 255, 0.9);\r\n  font-size: 16px;\r\n}\r\n\r\n.upload-click-text {\r\n  color: #fff;\r\n  font-style: normal;\r\n  font-weight: 600;\r\n  text-decoration: underline;\r\n  cursor: pointer;\r\n}\r\n\r\n.upload-tip {\r\n  display: flex;\r\n  align-items: center;\r\n  color: rgba(255, 255, 255, 0.7);\r\n  font-size: 14px;\r\n}\r\n\r\n.upload-tip i {\r\n  margin-right: 5px;\r\n  font-size: 12px;\r\n}\r\n\r\n.upload-limits {\r\n  margin-top: 20px;\r\n  display: flex;\r\n  justify-content: space-around;\r\n  width: 100%;\r\n  color: rgba(255, 255, 255, 0.8);\r\n  font-size: 13px;\r\n  flex-wrap: wrap;\r\n  gap: 15px;\r\n}\r\n\r\n.limit-item {\r\n  display: flex;\r\n  align-items: center;\r\n  background: rgba(255, 255, 255, 0.1);\r\n  padding: 8px 12px;\r\n  border-radius: 20px;\r\n  backdrop-filter: blur(5px);\r\n  border: 1px solid rgba(255, 255, 255, 0.2);\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.limit-item:hover {\r\n  background: rgba(255, 255, 255, 0.2);\r\n  transform: translateY(-1px);\r\n}\r\n\r\n.limit-item i {\r\n  margin-right: 6px;\r\n  font-size: 14px;\r\n  color: rgba(255, 255, 255, 0.9);\r\n}\r\n\r\n.file-list-section {\r\n  background-color: #f5f7fa;\r\n  padding: 20px;\r\n  border-radius: 4px;\r\n  margin-top: 20px;\r\n}\r\n\r\n.file-list-header {\r\n  display: flex;\r\n  align-items: center;\r\n  margin-bottom: 15px;\r\n  color: #606266;\r\n}\r\n\r\n.file-list-title {\r\n  margin-left: 8px;\r\n  font-size: 16px;\r\n}\r\n\r\n.file-count {\r\n  margin-left: 10px;\r\n  font-size: 14px;\r\n  color: #909399;\r\n}\r\n\r\n.file-list-content {\r\n  background-color: #fff;\r\n  border-radius: 4px;\r\n  padding: 10px;\r\n  border: 1px solid #ebeef5;\r\n}\r\n\r\n.file-info {\r\n  display: flex;\r\n  align-items: center;\r\n  margin-bottom: 5px;\r\n}\r\n\r\n.file-name {\r\n  margin-left: 8px;\r\n  font-size: 14px;\r\n  color: #303133;\r\n  overflow: hidden;\r\n  text-overflow: ellipsis;\r\n  white-space: nowrap;\r\n  max-width: 150px;\r\n}\r\n\r\n.el-table .success-row {\r\n  background-color: #f0f9eb;\r\n}\r\n\r\n.el-table .danger-row {\r\n  background-color: #fef0f0;\r\n}\r\n\r\n.operation-buttons {\r\n  display: flex;\r\n  justify-content: center;\r\n  gap: 8px;\r\n}\r\n\r\n.operation-buttons .el-button {\r\n  margin: 0;\r\n}\r\n</style>\r\n"]}]}