{"remainingRequest": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\src\\views\\leave\\plan\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\src\\views\\leave\\plan\\index.vue", "mtime": 1755499098437}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\babel.config.js", "mtime": 1688548084091}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_plan", "require", "_Editor", "_interopRequireDefault", "name", "components", "Editor", "data", "loading", "applyNos", "single", "multiple", "showSearch", "total", "planList", "daterangePlanReturn", "daterangeExpire", "daterangeApply", "title", "open", "showAddBtn", "showCancelBtn", "queryParams", "pageNum", "pageSize", "planNo", "planType", "businessCategory", "measureFlag", "plannedAmount", "receiveCompany", "receiveCompanyCode", "targetCompany", "targetCompanyCode", "sourceCompany", "sourceCompanyCode", "planReturnStartTime", "planReturnEndTime", "realReturnTime", "monitor", "specialManager", "expireStartTime", "expireEndTime", "reason", "itemType", "planStatus", "applyStartTime", "applyEndTime", "applyWorkNo", "factoryApproveTime", "factoryApproveWorkNo", "factoryApproveFlag", "factoryApproveContent", "factorySecApproveFlag", "factorySecApproveTime", "factorySecApproveWorkNo", "factorySecApproveContent", "centerApproveTime", "centerApproveWorkNo", "centerApproveFlag", "centerApproveContent", "applyFileUrl", "form", "rules", "created", "getList", "watch", "immediate", "handler", "newVal", "methods", "_this", "listPlan", "then", "response", "rows", "console", "log", "cancel", "reset", "handleQuery", "reset<PERSON><PERSON>y", "resetForm", "handleSelectionChange", "selection", "map", "item", "applyNo", "length", "handleAdd", "$router", "push", "handleUpdate", "row", "concat", "handleDelete", "_this2", "ids", "id", "$confirm", "confirmButtonText", "cancelButtonText", "type", "delPlan", "msgSuccess", "handleExport", "_this3", "exportPlan", "download", "msg", "handleDetail", "path", "handlePlanReturnTimeChange", "val", "handleExpireTimeChange", "handleApplyTimeChange", "handleInvalidate", "handlePrintApplication", "handlePrintMaterialList", "handlePrintPermit"], "sources": ["src/views/leave/plan/index.vue"], "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <el-form :model=\"queryParams\" ref=\"queryForm\" :inline=\"true\" v-show=\"showSearch\" label-width=\"80px\">\r\n      <el-row :gutter=\"30\">\r\n        <el-col :span=\"6\">\r\n          <el-form-item label=\"计划号\" prop=\"planNo\">\r\n            <el-input\r\n              v-model=\"queryParams.planNo\"\r\n              placeholder=\"请输入计划号\"\r\n              clearable\r\n              size=\"small\"\r\n              @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n        </el-col>\r\n        <el-col :span=\"6\">\r\n          <el-form-item label=\"计划类型\" prop=\"planType\">\r\n            <el-select v-model=\"queryParams.planType\" placeholder=\"请选择计划类型\" clearable size=\"small\" style=\"width: 100%\">\r\n              <el-option label=\"出厂不返回\" value=\"1\" />\r\n              <el-option label=\"出厂返回\" value=\"2\" />\r\n              <el-option label=\"跨区调拨\" value=\"3\" />\r\n              <el-option label=\"退货申请\" value=\"4\" />\r\n            </el-select>\r\n          </el-form-item>\r\n        </el-col>\r\n        \r\n        <el-col :span=\"6\">\r\n          <el-form-item label=\"是否计量\" prop=\"measureFlag\">\r\n            <el-select v-model=\"queryParams.measureFlag\" placeholder=\"请选择是否计量\" clearable size=\"small\" style=\"width: 100%\">\r\n              <el-option label=\"计量\" value=\"1\" />\r\n              <el-option label=\"不计量\" value=\"0\" />\r\n            </el-select>\r\n          </el-form-item>\r\n        </el-col>\r\n\r\n        <el-col :span=\"6\">\r\n          <el-form-item label=\"申请单位\" prop=\"sourceCompany\">\r\n            <el-input\r\n              v-model=\"queryParams.sourceCompany\"\r\n              placeholder=\"请输入申请单位\"\r\n              clearable\r\n              size=\"small\"\r\n              @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n        </el-col>\r\n\r\n        <el-col :span=\"6\">\r\n          <el-form-item label=\"收货单位\" prop=\"receiveCompany\">\r\n            <el-input\r\n              v-model=\"queryParams.receiveCompany\"\r\n              placeholder=\"请输入收货单位\"\r\n              clearable\r\n              size=\"small\"\r\n              @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n        </el-col>\r\n\r\n        <el-col :span=\"6\">\r\n          <el-form-item label=\"返回单位\" prop=\"targetCompany\">\r\n            <el-input\r\n              v-model=\"queryParams.targetCompany\"\r\n              placeholder=\"请输入返回单位\"\r\n              clearable\r\n              size=\"small\"\r\n              @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n        </el-col>\r\n\r\n        <!-- <el-col :span=\"6\">\r\n          <el-form-item label=\"物资专管员\" prop=\"specialManager\">\r\n            <el-input\r\n              v-model=\"queryParams.specialManager\"\r\n              placeholder=\"请输入物资专管员\"\r\n              clearable\r\n              size=\"small\"\r\n              @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n        </el-col> -->\r\n\r\n        <el-col :span=\"6\">\r\n          <el-form-item label=\"计划状态\" prop=\"planStatus\">\r\n            <el-select v-model=\"queryParams.planStatus\" placeholder=\"请选择计划状态\" clearable size=\"small\" style=\"width: 100%\">\r\n              <el-option label=\"待分厂审批\" value=\"1\" />\r\n              <el-option label=\"待分厂复审\" value=\"2\" />\r\n              <el-option label=\"待生产指挥中心审批\" value=\"3\" />\r\n              <el-option label=\"审批完成\" value=\"4\" />\r\n              <el-option label=\"已出厂\" value=\"5\" />\r\n              <el-option label=\"部分收货\" value=\"6\" />\r\n              <el-option label=\"已完成\" value=\"7\" />\r\n              <el-option label=\"驳回\" value=\"11\" />\r\n              <el-option label=\"废弃\" value=\"12\" />\r\n              <el-option label=\"过期\" value=\"13\" />\r\n            </el-select>\r\n          </el-form-item>\r\n        </el-col>\r\n\r\n        <el-col :span=\"12\">\r\n          <el-form-item label=\"返回时间\" prop=\"planReturnTime\">\r\n            <el-date-picker \r\n              clearable \r\n              size=\"small\" \r\n              style=\"width: 100%\"\r\n              v-model=\"daterangePlanReturn\"\r\n              type=\"daterange\"\r\n              value-format=\"yyyy-MM-dd\"\r\n              range-separator=\"至\"\r\n              start-placeholder=\"开始日期\"\r\n              end-placeholder=\"结束日期\"\r\n              @change=\"handlePlanReturnTimeChange\">\r\n            </el-date-picker>\r\n          </el-form-item>\r\n        </el-col>\r\n\r\n        <el-col :span=\"12\">\r\n          <el-form-item label=\"有效期\" prop=\"expireTime\">\r\n            <el-date-picker \r\n              clearable \r\n              size=\"small\" \r\n              style=\"width: 100%\"\r\n              v-model=\"daterangeExpire\"\r\n              type=\"daterange\"\r\n              value-format=\"yyyy-MM-dd\"\r\n              range-separator=\"至\"\r\n              start-placeholder=\"开始日期\"\r\n              end-placeholder=\"结束日期\"\r\n              @change=\"handleExpireTimeChange\">\r\n            </el-date-picker>\r\n          </el-form-item>\r\n        </el-col>\r\n        \r\n        <el-col :span=\"12\">\r\n          <el-form-item label=\"申请时间\" prop=\"applyTime\">\r\n            <el-date-picker \r\n              clearable \r\n              size=\"small\" \r\n              style=\"width: 100%\"\r\n              v-model=\"daterangeApply\"\r\n              type=\"daterange\"\r\n              value-format=\"yyyy-MM-dd\"\r\n              range-separator=\"至\"\r\n              start-placeholder=\"开始日期\"\r\n              end-placeholder=\"结束日期\"\r\n              @change=\"handleApplyTimeChange\">\r\n            </el-date-picker>\r\n          </el-form-item>\r\n        </el-col>\r\n        \r\n        <el-col :span=\"24\" style=\"text-align: center; margin-top: 10px\">\r\n          <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleQuery\">搜索</el-button>\r\n          <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\r\n        </el-col>\r\n      </el-row>\r\n    </el-form>\r\n\r\n    <el-row :gutter=\"10\" class=\"mb8\">\r\n      <el-col :span=\"1.5\">\r\n        <el-button v-if=\"this.showAddBtn\"\r\n          type=\"primary\"\r\n          icon=\"el-icon-plus\"\r\n          size=\"mini\"\r\n          @click=\"handleAdd\"\r\n        >新增</el-button>\r\n      </el-col>\r\n      <!-- <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"success\"\r\n          icon=\"el-icon-edit\"\r\n          size=\"mini\"\r\n          :disabled=\"single\"\r\n          @click=\"handleUpdate\"\r\n        >修改</el-button>\r\n      </el-col> -->\r\n      <el-col :span=\"1.5\">\r\n        <el-button v-if=\"this.showCancelBtn\"\r\n          type=\"danger\"\r\n          icon=\"el-icon-close\"\r\n          size=\"mini\"\r\n          :disabled=\"single\"\r\n          @click=\"handleInvalidate\"\r\n        >作废</el-button>\r\n      </el-col>\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"warning\"\r\n          icon=\"el-icon-printer\"\r\n          size=\"mini\"\r\n          :disabled=\"single\"\r\n          @click=\"handlePrintApplication\"\r\n        >打印申请单</el-button>\r\n      </el-col>\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"info\"\r\n          icon=\"el-icon-printer\"\r\n          size=\"mini\"\r\n          :disabled=\"single\"\r\n          @click=\"handlePrintMaterialList\"\r\n        >打印物料清单</el-button>\r\n      </el-col>\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"primary\"\r\n          plain\r\n          icon=\"el-icon-printer\"\r\n          size=\"mini\"\r\n          :disabled=\"single\"\r\n          @click=\"handlePrintPermit\"\r\n        >打印出门证</el-button>\r\n      </el-col>\r\n      <!-- <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"danger\"\r\n          icon=\"el-icon-delete\"\r\n          size=\"mini\"\r\n          :disabled=\"multiple\"\r\n          @click=\"handleDelete\"\r\n        >删除</el-button>\r\n      </el-col>\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"warning\"\r\n          icon=\"el-icon-download\"\r\n          size=\"mini\"\r\n          @click=\"handleExport\"\r\n        >导出</el-button>\r\n      </el-col> -->\r\n\t  <right-toolbar :showSearch.sync=\"showSearch\" @queryTable=\"getList\"></right-toolbar>\r\n    </el-row>\r\n\r\n    <el-table v-loading=\"loading\" :data=\"planList\" @selection-change=\"handleSelectionChange\" class=\"plan-table\">\r\n      <el-table-column type=\"selection\" width=\"55\" align=\"center\" />\r\n      <el-table-column label=\"申请编号\" align=\"center\" prop=\"applyNo\" width=\"180\" />\r\n      <el-table-column label=\"计划号\" align=\"center\" prop=\"planNo\" />\r\n      <el-table-column label=\"计划类型\" align=\"center\" prop=\"planType\" width=\"120\" >\r\n      <!-- tag标签 -->\r\n      <template slot-scope=\"scope\">\r\n        <el-tag v-if=\"scope.row.planType == '1'\" type=\"success\">出厂不返回</el-tag>\r\n        <el-tag v-if=\"scope.row.planType == '2'\" type=\"warning\">出厂返回</el-tag>\r\n        <el-tag v-if=\"scope.row.planType == '3'\" type=\"info\">跨区调拨</el-tag>\r\n        <el-tag v-if=\"scope.row.planType == '4'\" type=\"danger\">退货申请</el-tag>\r\n      </template>\r\n\r\n      </el-table-column>\r\n      <el-table-column label=\"申请人\" align=\"center\" prop=\"applyUserName\" />\r\n      <el-table-column label=\"申请单位\" align=\"center\" prop=\"sourceCompany\" width=\"200\" />\r\n      <el-table-column label=\"是否计量\" align=\"center\" prop=\"measureFlag\" >\r\n        <!-- tag标签 -->\r\n        <template slot-scope=\"scope\">\r\n          <el-tag v-if=\"scope.row.measureFlag == '1'\" type=\"success\">计量</el-tag>\r\n          <el-tag v-if=\"scope.row.measureFlag == '0'\" type=\"danger\">不计量</el-tag>\r\n        </template>\r\n      </el-table-column>\r\n      <!-- <el-table-column label=\"计划量\" align=\"center\" prop=\"plannedAmount\" /> -->\r\n      <el-table-column label=\"收货单位\" align=\"center\" prop=\"receiveCompany\" width=\"200\" />\r\n      <el-table-column label=\"返回单位\" align=\"center\" prop=\"targetCompany\" />\r\n      \r\n      <el-table-column label=\"申请有效期\" align=\"center\" prop=\"expireTime\" width=\"180\">\r\n        <template slot-scope=\"scope\">\r\n          <span>{{ parseTime(scope.row.expireTime, '{y}-{m}-{d}') }}</span>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"出厂原因\" align=\"center\" prop=\"reason\" />\r\n      <el-table-column label=\"计划状态\" align=\"center\" prop=\"planStatus\" >\r\n        <!-- tag标签 -->\r\n        <template slot-scope=\"scope\">\r\n          <el-tag v-if=\"scope.row.planStatus == '1'\" type=\"warning\">待分厂审批</el-tag>\r\n          <el-tag v-if=\"scope.row.planStatus == '2'\" type=\"warning\">待分厂复审</el-tag>\r\n          <el-tag v-if=\"scope.row.planStatus == '3'\" type=\"warning\">待生产指挥中心审批</el-tag>\r\n          <el-tag v-if=\"scope.row.planStatus == '4'\" type=\"success\">审批完成</el-tag>\r\n          <el-tag v-if=\"scope.row.planStatus == '5'\" type=\"success\">已出厂</el-tag>\r\n          <el-tag v-if=\"scope.row.planStatus == '6'\" type=\"info\">部分收货</el-tag>\r\n          <el-tag v-if=\"scope.row.planStatus == '7'\" type=\"success\">已完成</el-tag>\r\n          <el-tag v-if=\"scope.row.planStatus == '11'\" type=\"danger\">驳回</el-tag>\r\n          <el-tag v-if=\"scope.row.planStatus == '12'\" type=\"danger\">废弃</el-tag>\r\n          <el-tag v-if=\"scope.row.planStatus == '13'\" type=\"danger\">过期</el-tag>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"申请时间\" align=\"center\" prop=\"applyTime\" width=\"180\">\r\n        <template slot-scope=\"scope\">\r\n          <span>{{ parseTime(scope.row.applyTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>\r\n        </template>\r\n      </el-table-column>\r\n      \r\n      <el-table-column label=\"分厂审核\" align=\"center\" prop=\"factoryApproveFlag\" >\r\n        <template slot-scope=\"scope\">\r\n          <el-tag v-if=\"scope.row.factoryApproveFlag === '1'\" type=\"success\">同意</el-tag>\r\n          <el-tag v-else-if=\"scope.row.factoryApproveFlag === '0'\" type=\"danger\">拒绝</el-tag>\r\n          <span v-else>-</span>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"分厂复审\" align=\"center\" prop=\"factorySecApproveFlag\" >\r\n        <template slot-scope=\"scope\">\r\n          <el-tag v-if=\"scope.row.factorySecApproveFlag === '1'\" type=\"success\">同意</el-tag>\r\n          <el-tag v-else-if=\"scope.row.factorySecApproveFlag === '0'\" type=\"danger\">拒绝</el-tag>\r\n          <span v-else>-</span>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"生产指挥中心审核\" align=\"center\" prop=\"centerApproveFlag\" >\r\n        <template slot-scope=\"scope\">\r\n          <el-tag v-if=\"scope.row.centerApproveFlag === '1'\" type=\"success\">同意</el-tag>\r\n          <el-tag v-else-if=\"scope.row.centerApproveFlag === '0'\" type=\"danger\">拒绝</el-tag>\r\n          <span v-else>-</span>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"操作\" align=\"center\" class-name=\"small-padding fixed-width\" fixed=\"right\">\r\n        <template slot-scope=\"scope\">\r\n          <el-button\r\n            v-if=\"scope.row.showModifyBtn\"\r\n            size=\"mini\"\r\n            type=\"text\"\r\n            icon=\"el-icon-edit\"\r\n            @click=\"handleUpdate(scope.row)\"\r\n          >修改</el-button>\r\n          <!-- <el-button\r\n            size=\"mini\"\r\n            type=\"text\"\r\n            icon=\"el-icon-delete\"\r\n            @click=\"handleDelete(scope.row)\"\r\n          >删除</el-button> -->\r\n          <el-button\r\n            size=\"mini\"\r\n            type=\"text\"\r\n            icon=\"el-icon-view\"\r\n            @click=\"handleDetail(scope.row)\"\r\n          >详情</el-button>\r\n        </template>\r\n      </el-table-column>\r\n    </el-table>\r\n\r\n    <pagination\r\n      v-show=\"total>0\"\r\n      :total=\"total\"\r\n      :page.sync=\"queryParams.pageNum\"\r\n      :limit.sync=\"queryParams.pageSize\"\r\n      @pagination=\"getList\"\r\n    />\r\n\r\n    \r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { listPlan, getPlan, delPlan, addPlan, updatePlan, exportPlan } from \"@/api/leave/plan\";\r\nimport Editor from '@/components/Editor';\r\n\r\nexport default {\r\n  name: \"Plan\",\r\n  components: {\r\n    Editor,\r\n  },\r\n  data() {\r\n    return {\r\n      // 遮罩层\r\n      loading: true,\r\n      // 选中数组\r\n      applyNos: [],\r\n      // 非单个禁用\r\n      single: true,\r\n      // 非多个禁用\r\n      multiple: true,\r\n      // 显示搜索条件\r\n      showSearch: true,\r\n      // 总条数\r\n      total: 0,\r\n      // 出门证计划申请表格数据\r\n      planList: [],\r\n      // 日期范围\r\n      daterangePlanReturn: [],\r\n      daterangeExpire: [],\r\n      daterangeApply: [],\r\n      // 弹出层标题\r\n      title: \"\",\r\n      // 是否显示弹出层\r\n      open: false,\r\n      //是否显示新增按钮\r\n      showAddBtn:false,\r\n      //是否显示废弃按钮\r\n      showCancelBtn:false,\r\n      // 查询参数\r\n      queryParams: {\r\n        pageNum: 1,\r\n        pageSize: 10,\r\n        planNo: null,\r\n        planType: null,\r\n        businessCategory: null,\r\n        measureFlag: null,\r\n        plannedAmount: null,\r\n        receiveCompany: null,\r\n        receiveCompanyCode: null,\r\n        targetCompany: null,\r\n        targetCompanyCode: null,\r\n        sourceCompany: null,\r\n        sourceCompanyCode: null,\r\n        planReturnStartTime: null,\r\n        planReturnEndTime: null,\r\n        realReturnTime: null,\r\n        monitor: null,\r\n        specialManager: null,\r\n        expireStartTime: null,\r\n        expireEndTime: null,\r\n        reason: null,\r\n        itemType: null,\r\n        planStatus: null,\r\n        applyStartTime: null,\r\n        applyEndTime: null,\r\n        applyWorkNo: null,\r\n        factoryApproveTime: null,\r\n        factoryApproveWorkNo: null,\r\n        factoryApproveFlag: null,\r\n        factoryApproveContent: null,\r\n        factorySecApproveFlag: null,\r\n        factorySecApproveTime: null,\r\n        factorySecApproveWorkNo: null,\r\n        factorySecApproveContent: null,\r\n        centerApproveTime: null,\r\n        centerApproveWorkNo: null,\r\n        centerApproveFlag: null,\r\n        centerApproveContent: null,\r\n        applyFileUrl: null,\r\n      },\r\n      // 表单参数\r\n      form: {},\r\n      // 表单校验\r\n      rules: {\r\n      }\r\n    };\r\n  },\r\n  created() {\r\n    this.getList();\r\n  },\r\n  watch: {\r\n  '$route.query.refresh': {\r\n    immediate: true,\r\n    handler(newVal) {\r\n      if (newVal) {\r\n        this.getList(); // 刷新列表数据的方法\r\n          }\r\n        }\r\n      }\r\n    },\r\n  methods: {\r\n    /** 查询出门证计划申请列表 */\r\n    getList() {\r\n      this.loading = true;\r\n      listPlan(this.queryParams).then(response => {\r\n        this.planList = response.rows;\r\n        console.log(this.planList);\r\n        this.showAddBtn =this.planList[0].showAddBtn;\r\n        this.showCancelBtn =this.planList[0].showCancelBtn;\r\n        this.total = response.total;\r\n        this.loading = false;\r\n      });\r\n    },\r\n    // 取消按钮\r\n    cancel() {\r\n      this.open = false;\r\n      this.reset();\r\n    },\r\n    \r\n    /** 搜索按钮操作 */\r\n    handleQuery() {\r\n      this.queryParams.pageNum = 1;\r\n      this.getList();\r\n    },\r\n    /** 重置按钮操作 */\r\n    resetQuery() {\r\n      this.resetForm(\"queryForm\");\r\n      this.handleQuery();\r\n    },\r\n    // 多选框选中数据\r\n    handleSelectionChange(selection) {\r\n      this.applyNos = selection.map(item => item.applyNo)\r\n      this.single = selection.length!==1\r\n      this.multiple = !selection.length\r\n    },\r\n    /** 新增按钮操作 */\r\n    handleAdd() {\r\n      this.$router.push(\"/leave/plan/edit\");\r\n    },\r\n    \r\n    /** 修改按钮操作 */\r\n    handleUpdate(row) {\r\n      if (this.applyNos.length > 0) {\r\n        this.$router.push(`/leave/plan/edit/${this.applyNos[0]}`);\r\n      } else {\r\n        this.$router.push(`/leave/plan/edit/${row.applyNo}`);\r\n      }\r\n    },\r\n    \r\n    /** 删除按钮操作 */\r\n    handleDelete(row) {\r\n      const ids = row.id || this.ids;\r\n      this.$confirm('是否确认删除出门证计划申请编号为\"' + ids + '\"的数据项?', \"警告\", {\r\n          confirmButtonText: \"确定\",\r\n          cancelButtonText: \"取消\",\r\n          type: \"warning\"\r\n        }).then(function() {\r\n          return delPlan(ids);\r\n        }).then(() => {\r\n          this.getList();\r\n          this.msgSuccess(\"删除成功\");\r\n        })\r\n    },\r\n    /** 导出按钮操作 */\r\n    handleExport() {\r\n      const queryParams = this.queryParams;\r\n      this.$confirm('是否确认导出所有出门证计划申请数据项?', \"警告\", {\r\n          confirmButtonText: \"确定\",\r\n          cancelButtonText: \"取消\",\r\n          type: \"warning\"\r\n        }).then(function() {\r\n          return exportPlan(queryParams);\r\n        }).then(response => {\r\n          this.download(response.msg);\r\n        })\r\n    },\r\n    handleDetail(row) {\r\n      // 跳转到详情页，并传递ID参数\r\n      this.$router.push({\r\n        path: `/leave/plan/detail/${row.applyNo}`\r\n      });\r\n    },\r\n    // 计划返回时间范围发生变化\r\n    handlePlanReturnTimeChange(val) {\r\n      if (val) {\r\n        this.queryParams.planReturnStartTime = val[0];\r\n        this.queryParams.planReturnEndTime = val[1];\r\n      } else {\r\n        this.queryParams.planReturnStartTime = null;\r\n        this.queryParams.planReturnEndTime = null;\r\n      }\r\n    },\r\n    \r\n    // 申请有效期范围发生变化\r\n    handleExpireTimeChange(val) {\r\n      if (val) {\r\n        this.queryParams.expireStartTime = val[0];\r\n        this.queryParams.expireEndTime = val[1];\r\n      } else {\r\n        this.queryParams.expireStartTime = null;\r\n        this.queryParams.expireEndTime = null;\r\n      }\r\n    },\r\n    \r\n    // 申请时间范围发生变化\r\n    handleApplyTimeChange(val) {\r\n      if (val) {\r\n        this.queryParams.applyStartTime = val[0];\r\n        this.queryParams.applyEndTime = val[1];\r\n      } else {\r\n        this.queryParams.applyStartTime = null;\r\n        this.queryParams.applyEndTime = null;\r\n      }\r\n    },\r\n    \r\n    /** 作废按钮操作 */\r\n    handleInvalidate(row) {\r\n      // TODO: 实现作废功能\r\n    },\r\n    \r\n    /** 打印申请单按钮操作 */\r\n    handlePrintApplication(row) {\r\n      // TODO: 实现打印申请单功能\r\n    },\r\n    \r\n    /** 打印物料清单按钮操作 */\r\n    handlePrintMaterialList(row) {\r\n      // TODO: 实现打印物料清单功能\r\n    },\r\n    \r\n    /** 打印出门证按钮操作 */\r\n    handlePrintPermit(row) {\r\n      // TODO: 实现打印出门证功能\r\n    },\r\n  }\r\n};\r\n</script>\r\n\r\n<style>\r\n.mb8 {\r\n  margin-bottom: 8px;\r\n}\r\n\r\n.plan-table {\r\n  width: 100%;\r\n}\r\n\r\n.plan-table th {\r\n  background-color: #f5f7fa;\r\n  color: #606266;\r\n  font-weight: 600;\r\n  text-align: center !important;\r\n}\r\n\r\n.plan-table td {\r\n  text-align: center !important;\r\n}\r\n\r\n/* 表单样式调整 */\r\n.el-form-item {\r\n  margin-bottom: 15px;\r\n  width: 100%;\r\n}\r\n\r\n.el-form-item__content {\r\n  width: calc(100% - 80px);\r\n}\r\n\r\n.el-date-editor.el-input {\r\n  width: 100%;\r\n}\r\n\r\n.el-date-editor--daterange.el-input__inner {\r\n  width: 100% !important;\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;;;;AA0VA,IAAAA,KAAA,GAAAC,OAAA;AACA,IAAAC,OAAA,GAAAC,sBAAA,CAAAF,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCAEA;EACAG,IAAA;EACAC,UAAA;IACAC,MAAA,EAAAA;EACA;EACAC,IAAA,WAAAA,KAAA;IACA;MACA;MACAC,OAAA;MACA;MACAC,QAAA;MACA;MACAC,MAAA;MACA;MACAC,QAAA;MACA;MACAC,UAAA;MACA;MACAC,KAAA;MACA;MACAC,QAAA;MACA;MACAC,mBAAA;MACAC,eAAA;MACAC,cAAA;MACA;MACAC,KAAA;MACA;MACAC,IAAA;MACA;MACAC,UAAA;MACA;MACAC,aAAA;MACA;MACAC,WAAA;QACAC,OAAA;QACAC,QAAA;QACAC,MAAA;QACAC,QAAA;QACAC,gBAAA;QACAC,WAAA;QACAC,aAAA;QACAC,cAAA;QACAC,kBAAA;QACAC,aAAA;QACAC,iBAAA;QACAC,aAAA;QACAC,iBAAA;QACAC,mBAAA;QACAC,iBAAA;QACAC,cAAA;QACAC,OAAA;QACAC,cAAA;QACAC,eAAA;QACAC,aAAA;QACAC,MAAA;QACAC,QAAA;QACAC,UAAA;QACAC,cAAA;QACAC,YAAA;QACAC,WAAA;QACAC,kBAAA;QACAC,oBAAA;QACAC,kBAAA;QACAC,qBAAA;QACAC,qBAAA;QACAC,qBAAA;QACAC,uBAAA;QACAC,wBAAA;QACAC,iBAAA;QACAC,mBAAA;QACAC,iBAAA;QACAC,oBAAA;QACAC,YAAA;MACA;MACA;MACAC,IAAA;MACA;MACAC,KAAA,GACA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA,KAAAC,OAAA;EACA;EACAC,KAAA;IACA;MACAC,SAAA;MACAC,OAAA,WAAAA,QAAAC,MAAA;QACA,IAAAA,MAAA;UACA,KAAAJ,OAAA;QACA;MACA;IACA;EACA;EACAK,OAAA;IACA,kBACAL,OAAA,WAAAA,QAAA;MAAA,IAAAM,KAAA;MACA,KAAA/D,OAAA;MACA,IAAAgE,cAAA,OAAAlD,WAAA,EAAAmD,IAAA,WAAAC,QAAA;QACAH,KAAA,CAAAzD,QAAA,GAAA4D,QAAA,CAAAC,IAAA;QACAC,OAAA,CAAAC,GAAA,CAAAN,KAAA,CAAAzD,QAAA;QACAyD,KAAA,CAAAnD,UAAA,GAAAmD,KAAA,CAAAzD,QAAA,IAAAM,UAAA;QACAmD,KAAA,CAAAlD,aAAA,GAAAkD,KAAA,CAAAzD,QAAA,IAAAO,aAAA;QACAkD,KAAA,CAAA1D,KAAA,GAAA6D,QAAA,CAAA7D,KAAA;QACA0D,KAAA,CAAA/D,OAAA;MACA;IACA;IACA;IACAsE,MAAA,WAAAA,OAAA;MACA,KAAA3D,IAAA;MACA,KAAA4D,KAAA;IACA;IAEA,aACAC,WAAA,WAAAA,YAAA;MACA,KAAA1D,WAAA,CAAAC,OAAA;MACA,KAAA0C,OAAA;IACA;IACA,aACAgB,UAAA,WAAAA,WAAA;MACA,KAAAC,SAAA;MACA,KAAAF,WAAA;IACA;IACA;IACAG,qBAAA,WAAAA,sBAAAC,SAAA;MACA,KAAA3E,QAAA,GAAA2E,SAAA,CAAAC,GAAA,WAAAC,IAAA;QAAA,OAAAA,IAAA,CAAAC,OAAA;MAAA;MACA,KAAA7E,MAAA,GAAA0E,SAAA,CAAAI,MAAA;MACA,KAAA7E,QAAA,IAAAyE,SAAA,CAAAI,MAAA;IACA;IACA,aACAC,SAAA,WAAAA,UAAA;MACA,KAAAC,OAAA,CAAAC,IAAA;IACA;IAEA,aACAC,YAAA,WAAAA,aAAAC,GAAA;MACA,SAAApF,QAAA,CAAA+E,MAAA;QACA,KAAAE,OAAA,CAAAC,IAAA,qBAAAG,MAAA,MAAArF,QAAA;MACA;QACA,KAAAiF,OAAA,CAAAC,IAAA,qBAAAG,MAAA,CAAAD,GAAA,CAAAN,OAAA;MACA;IACA;IAEA,aACAQ,YAAA,WAAAA,aAAAF,GAAA;MAAA,IAAAG,MAAA;MACA,IAAAC,GAAA,GAAAJ,GAAA,CAAAK,EAAA,SAAAD,GAAA;MACA,KAAAE,QAAA,uBAAAF,GAAA;QACAG,iBAAA;QACAC,gBAAA;QACAC,IAAA;MACA,GAAA7B,IAAA;QACA,WAAA8B,aAAA,EAAAN,GAAA;MACA,GAAAxB,IAAA;QACAuB,MAAA,CAAA/B,OAAA;QACA+B,MAAA,CAAAQ,UAAA;MACA;IACA;IACA,aACAC,YAAA,WAAAA,aAAA;MAAA,IAAAC,MAAA;MACA,IAAApF,WAAA,QAAAA,WAAA;MACA,KAAA6E,QAAA;QACAC,iBAAA;QACAC,gBAAA;QACAC,IAAA;MACA,GAAA7B,IAAA;QACA,WAAAkC,gBAAA,EAAArF,WAAA;MACA,GAAAmD,IAAA,WAAAC,QAAA;QACAgC,MAAA,CAAAE,QAAA,CAAAlC,QAAA,CAAAmC,GAAA;MACA;IACA;IACAC,YAAA,WAAAA,aAAAjB,GAAA;MACA;MACA,KAAAH,OAAA,CAAAC,IAAA;QACAoB,IAAA,wBAAAjB,MAAA,CAAAD,GAAA,CAAAN,OAAA;MACA;IACA;IACA;IACAyB,0BAAA,WAAAA,2BAAAC,GAAA;MACA,IAAAA,GAAA;QACA,KAAA3F,WAAA,CAAAc,mBAAA,GAAA6E,GAAA;QACA,KAAA3F,WAAA,CAAAe,iBAAA,GAAA4E,GAAA;MACA;QACA,KAAA3F,WAAA,CAAAc,mBAAA;QACA,KAAAd,WAAA,CAAAe,iBAAA;MACA;IACA;IAEA;IACA6E,sBAAA,WAAAA,uBAAAD,GAAA;MACA,IAAAA,GAAA;QACA,KAAA3F,WAAA,CAAAmB,eAAA,GAAAwE,GAAA;QACA,KAAA3F,WAAA,CAAAoB,aAAA,GAAAuE,GAAA;MACA;QACA,KAAA3F,WAAA,CAAAmB,eAAA;QACA,KAAAnB,WAAA,CAAAoB,aAAA;MACA;IACA;IAEA;IACAyE,qBAAA,WAAAA,sBAAAF,GAAA;MACA,IAAAA,GAAA;QACA,KAAA3F,WAAA,CAAAwB,cAAA,GAAAmE,GAAA;QACA,KAAA3F,WAAA,CAAAyB,YAAA,GAAAkE,GAAA;MACA;QACA,KAAA3F,WAAA,CAAAwB,cAAA;QACA,KAAAxB,WAAA,CAAAyB,YAAA;MACA;IACA;IAEA,aACAqE,gBAAA,WAAAA,iBAAAvB,GAAA;MACA;IAAA,CACA;IAEA,gBACAwB,sBAAA,WAAAA,uBAAAxB,GAAA;MACA;IAAA,CACA;IAEA,iBACAyB,uBAAA,WAAAA,wBAAAzB,GAAA;MACA;IAAA,CACA;IAEA,gBACA0B,iBAAA,WAAAA,kBAAA1B,GAAA;MACA;IAAA;EAEA;AACA", "ignoreList": []}]}