{"remainingRequest": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\src\\views\\dataReport\\answer\\answerShow.vue?vue&type=template&id=8df067d6&scoped=true", "dependencies": [{"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\src\\views\\dataReport\\answer\\answerShow.vue", "mtime": 1755499162045}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}