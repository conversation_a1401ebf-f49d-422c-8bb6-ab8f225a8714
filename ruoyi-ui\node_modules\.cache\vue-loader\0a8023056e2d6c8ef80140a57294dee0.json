{"remainingRequest": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\src\\views\\leave\\material\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\src\\views\\leave\\material\\index.vue", "mtime": 1755499098414}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgeyBsaXN0TWF0ZXJpYWwsIGdldE1hdGVyaWFsLCBkZWxNYXRlcmlhbCwgYWRkTWF0ZXJpYWwsIHVwZGF0ZU1hdGVyaWFsLCBleHBvcnRNYXRlcmlhbCB9IGZyb20gIkAvYXBpL2xlYXZlL21hdGVyaWFsIjsNCmltcG9ydCB7IGpzIH0gZnJvbSAianMtYmVhdXRpZnkiOw0KDQpleHBvcnQgZGVmYXVsdCB7DQogIG5hbWU6ICJNYXRlcmlhbCIsDQogIGNvbXBvbmVudHM6IHsNCiAgfSwNCiAgZGF0YSgpIHsNCiAgICByZXR1cm4gew0KICAgICAgLy8g6YGu572p5bGCDQogICAgICBsb2FkaW5nOiB0cnVlLA0KICAgICAgLy8g6YCJ5Lit5pWw57uEDQogICAgICBpZHM6IFtdLA0KICAgICAgLy8g6Z2e5Y2V5Liq56aB55SoDQogICAgICBzaW5nbGU6IHRydWUsDQogICAgICAvLyDpnZ7lpJrkuKrnpoHnlKgNCiAgICAgIG11bHRpcGxlOiB0cnVlLA0KICAgICAgLy8g5pi+56S65pCc57Si5p2h5Lu2DQogICAgICBzaG93U2VhcmNoOiB0cnVlLA0KICAgICAgLy8g5oC75p2h5pWwDQogICAgICB0b3RhbDogMCwNCiAgICAgIC8vIOWHuumXqOivgeeJqei1hOihqOagvOaVsOaNrg0KICAgICAgbWF0ZXJpYWxMaXN0OiBbXSwNCiAgICAgIC8vIOW8ueWHuuWxguagh+mimA0KICAgICAgdGl0bGU6ICIiLA0KICAgICAgLy8g5piv5ZCm5pi+56S65by55Ye65bGCDQogICAgICBvcGVuOiBmYWxzZSwNCiAgICAgIC8vIG9wZW5EZXRhaWw6IGZhbHNlLA0KICAgICAgLy8g5p+l6K+i5Y+C5pWwDQogICAgICBxdWVyeVBhcmFtczogew0KICAgICAgICBwYWdlTnVtOiAxLA0KICAgICAgICBwYWdlU2l6ZTogMTAsDQogICAgICAgIG1hdGVyaWFsTmFtZTogbnVsbCwNCiAgICAgICAgbWF0ZXJpYWxTcGVjOiBudWxsLA0KICAgICAgICBtZWFzdXJlVW5pdDogbnVsbCwNCiAgICAgICAgbWVhc3VyZUZsYWc6IG51bGwsDQogICAgICAgIG1hdGVyaWFsVHlwZTogbnVsbCwNCiAgICAgICAgcXVlcnlXb3JkOiBudWxsLA0KICAgICAgICBjcmVhdGVCeTogbnVsbCwNCiAgICAgICAgY3JlYXRlVGltZTogbnVsbCwNCiAgICAgICAgdXBkYXRlQnk6IG51bGwsDQogICAgICAgIHVwZGF0ZVRpbWU6IG51bGwsDQogICAgICAgIHJlbWFyazogbnVsbA0KICAgICAgfSwNCiAgICAgIC8vIOihqOWNleWPguaVsA0KICAgICAgZm9ybToge30sDQogICAgICAvLyDooajljZXmoKHpqowNCiAgICAgIHJ1bGVzOiB7DQogICAgICB9DQogICAgfTsNCiAgfSwNCiAgY3JlYXRlZCgpIHsNCiAgICB0aGlzLmdldExpc3QoKTsNCiAgfSwNCiAgbWV0aG9kczogew0KICAgIC8qKiDmn6Xor6Llh7rpl6jor4HnianotYTliJfooaggKi8NCiAgICBnZXRMaXN0KCkgew0KICAgICAgdGhpcy5sb2FkaW5nID0gdHJ1ZTsNCiAgICAgIGxpc3RNYXRlcmlhbCh0aGlzLnF1ZXJ5UGFyYW1zKS50aGVuKHJlc3BvbnNlID0+IHsNCiAgICAgICAgdGhpcy5tYXRlcmlhbExpc3QgPSByZXNwb25zZS5yb3dzOw0KICAgICAgICB0aGlzLnRvdGFsID0gcmVzcG9uc2UudG90YWw7DQogICAgICAgIHRoaXMubG9hZGluZyA9IGZhbHNlOw0KICAgICAgfSk7DQogICAgfSwNCiAgICAvLyDlj5bmtojmjInpkq4NCiAgICBjYW5jZWwoKSB7DQogICAgICB0aGlzLm9wZW4gPSBmYWxzZTsNCiAgICAgIHRoaXMucmVzZXQoKTsNCiAgICB9LA0KICAgIC8vIGRldGFpbENhbmNlbCgpIHsNCiAgICAvLyAgIHRoaXMub3BlbkRldGFpbCA9IGZhbHNlOw0KICAgIC8vIH0sDQogICAgLy8g6KGo5Y2V6YeN572uDQogICAgcmVzZXQoKSB7DQogICAgICB0aGlzLmZvcm0gPSB7DQogICAgICAgIGlkOiBudWxsLA0KICAgICAgICBtYXRlcmlhbE5hbWU6IG51bGwsDQogICAgICAgIG1hdGVyaWFsU3BlYzogbnVsbCwNCiAgICAgICAgbWVhc3VyZVVuaXQ6IG51bGwsDQogICAgICAgIG1lYXN1cmVGbGFnOiBudWxsLA0KICAgICAgICByZW1hcms6IG51bGwsDQogICAgICAgIGNyZWF0ZVRpbWU6IG51bGwsDQogICAgICAgIGNyZWF0ZUJ5OiBudWxsLA0KICAgICAgICB1cGRhdGVUaW1lOiBudWxsLA0KICAgICAgICB1cGRhdGVCeTogbnVsbCwNCiAgICAgICAgbWF0ZXJpYWxUeXBlOiBudWxsLA0KICAgICAgICBxdWVyeVdvcmQ6IG51bGwNCiAgICAgIH07DQogICAgICB0aGlzLnJlc2V0Rm9ybSgiZm9ybSIpOw0KICAgIH0sDQogICAgLyoqIOaQnOe0ouaMiemSruaTjeS9nCAqLw0KICAgIGhhbmRsZVF1ZXJ5KCkgew0KICAgICAgdGhpcy5xdWVyeVBhcmFtcy5wYWdlTnVtID0gMTsNCiAgICAgIHRoaXMuZ2V0TGlzdCgpOw0KICAgIH0sDQogICAgLyoqIOmHjee9ruaMiemSruaTjeS9nCAqLw0KICAgIHJlc2V0UXVlcnkoKSB7DQogICAgICB0aGlzLnJlc2V0Rm9ybSgicXVlcnlGb3JtIik7DQogICAgICB0aGlzLmhhbmRsZVF1ZXJ5KCk7DQogICAgfSwNCiAgICAvLyDlpJrpgInmoYbpgInkuK3mlbDmja4NCiAgICBoYW5kbGVTZWxlY3Rpb25DaGFuZ2Uoc2VsZWN0aW9uKSB7DQogICAgICB0aGlzLmlkcyA9IHNlbGVjdGlvbi5tYXAoaXRlbSA9PiBpdGVtLmlkKQ0KICAgICAgdGhpcy5zaW5nbGUgPSBzZWxlY3Rpb24ubGVuZ3RoICE9PSAxDQogICAgICB0aGlzLm11bHRpcGxlID0gIXNlbGVjdGlvbi5sZW5ndGgNCiAgICB9LA0KICAgIC8qKiDmlrDlop7mjInpkq7mk43kvZwgKi8NCiAgICBoYW5kbGVBZGQoKSB7DQogICAgICB0aGlzLnJlc2V0KCk7DQogICAgICB0aGlzLm9wZW4gPSB0cnVlOw0KICAgICAgdGhpcy50aXRsZSA9ICLmt7vliqDlh7rpl6jor4HnianotYQiOw0KICAgIH0sDQogICAgLyoqIOS/ruaUueaMiemSruaTjeS9nCAqLw0KICAgIGhhbmRsZVVwZGF0ZShyb3cpIHsNCiAgICAgIHRoaXMucmVzZXQoKTsNCiAgICAgIGNvbnN0IGlkID0gcm93LmlkIHx8IHRoaXMuaWRzDQogICAgICBnZXRNYXRlcmlhbChpZCkudGhlbihyZXNwb25zZSA9PiB7DQogICAgICAgIHRoaXMuZm9ybSA9IHJlc3BvbnNlLmRhdGE7DQogICAgICAgIHRoaXMub3BlbiA9IHRydWU7DQogICAgICAgIHRoaXMudGl0bGUgPSAi5L+u5pS55Ye66Zeo6K+B54mp6LWEIjsNCiAgICAgIH0pOw0KICAgIH0sDQoNCiAgICAvLyAvKiog6K+m5oOF5oyJ6ZKu5pON5L2cICovDQogICAgLy8gaGFuZGxlRGV0YWlsKHJvdykgew0KICAgIC8vICAgdGhpcy5mb3JtID0gSlNPTi5wYXJzZShKU09OLnN0cmluZ2lmeShyb3cpKTsNCiAgICAvLyAgIHRoaXMub3BlbkRldGFpbCA9IHRydWU7DQogICAgLy8gICB0aGlzLnRpdGxlID0gIueJqeaWmeinhOagvOeuoeeQhiI7DQogICAgLy8gfSwNCiAgICAvKiog5o+Q5Lqk5oyJ6ZKuICovDQogICAgc3VibWl0Rm9ybSgpIHsNCiAgICAgIHRoaXMuJHJlZnNbImZvcm0iXS52YWxpZGF0ZSh2YWxpZCA9PiB7DQogICAgICAgIGlmICh2YWxpZCkgew0KICAgICAgICAgIGlmICh0aGlzLmZvcm0uaWQgIT0gbnVsbCkgew0KICAgICAgICAgICAgdXBkYXRlTWF0ZXJpYWwodGhpcy5mb3JtKS50aGVuKHJlc3BvbnNlID0+IHsNCiAgICAgICAgICAgICAgdGhpcy5tc2dTdWNjZXNzKCLkv67mlLnmiJDlip8iKTsNCiAgICAgICAgICAgICAgdGhpcy5vcGVuID0gZmFsc2U7DQogICAgICAgICAgICAgIHRoaXMuZ2V0TGlzdCgpOw0KICAgICAgICAgICAgfSk7DQogICAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICAgIGFkZE1hdGVyaWFsKHRoaXMuZm9ybSkudGhlbihyZXNwb25zZSA9PiB7DQogICAgICAgICAgICAgIHRoaXMubXNnU3VjY2Vzcygi5paw5aKe5oiQ5YqfIik7DQogICAgICAgICAgICAgIHRoaXMub3BlbiA9IGZhbHNlOw0KICAgICAgICAgICAgICB0aGlzLmdldExpc3QoKTsNCiAgICAgICAgICAgIH0pOw0KICAgICAgICAgIH0NCiAgICAgICAgfQ0KICAgICAgfSk7DQogICAgfSwNCiAgICAvKiog5Yig6Zmk5oyJ6ZKu5pON5L2cICovDQogICAgaGFuZGxlRGVsZXRlKHJvdykgew0KICAgICAgY29uc3QgaWRzID0gcm93Lm1hdGVyaWFsTmFtZSB8fCB0aGlzLmlkczsNCiAgICAgIHRoaXMuJGNvbmZpcm0oJ+aYr+WQpuehruiupOW6n+W8g+eJqei1hOWQjeensOS4uiInICsgaWRzICsgJyLnmoTmlbDmja7pobk/JywgIuitpuWRiiIsIHsNCiAgICAgICAgY29uZmlybUJ1dHRvblRleHQ6ICLnoa7lrpoiLA0KICAgICAgICBjYW5jZWxCdXR0b25UZXh0OiAi5Y+W5raIIiwNCiAgICAgICAgdHlwZTogIndhcm5pbmciDQogICAgICB9KS50aGVuKGZ1bmN0aW9uICgpIHsNCiAgICAgICAgcmV0dXJuIGRlbE1hdGVyaWFsKGlkcyk7DQogICAgICB9KS50aGVuKCgpID0+IHsNCiAgICAgICAgdGhpcy5nZXRMaXN0KCk7DQogICAgICAgIHRoaXMubXNnU3VjY2Vzcygi5Yig6Zmk5oiQ5YqfIik7DQogICAgICB9KQ0KICAgIH0sDQogICAgLyoqIOWvvOWHuuaMiemSruaTjeS9nCAqLw0KICAgIGhhbmRsZUV4cG9ydCgpIHsNCiAgICAgIGNvbnN0IHF1ZXJ5UGFyYW1zID0gdGhpcy5xdWVyeVBhcmFtczsNCiAgICAgIHRoaXMuJGNvbmZpcm0oJ+aYr+WQpuehruiupOWvvOWHuuaJgOacieWHuumXqOivgeeJqei1hOaVsOaNrumhuT8nLCAi6K2m5ZGKIiwgew0KICAgICAgICBjb25maXJtQnV0dG9uVGV4dDogIuehruWumiIsDQogICAgICAgIGNhbmNlbEJ1dHRvblRleHQ6ICLlj5bmtogiLA0KICAgICAgICB0eXBlOiAid2FybmluZyINCiAgICAgIH0pLnRoZW4oZnVuY3Rpb24gKCkgew0KICAgICAgICByZXR1cm4gZXhwb3J0TWF0ZXJpYWwocXVlcnlQYXJhbXMpOw0KICAgICAgfSkudGhlbihyZXNwb25zZSA9PiB7DQogICAgICAgIHRoaXMuZG93bmxvYWQocmVzcG9uc2UubXNnKTsNCiAgICAgIH0pDQogICAgfQ0KICB9DQp9Ow0K"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA8HA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/leave/material", "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <el-form :model=\"queryParams\" ref=\"queryForm\" :inline=\"true\" v-show=\"showSearch\" label-width=\"110px\">\r\n      <el-form-item label=\"物料名称\" prop=\"materialName\">\r\n        <el-input v-model=\"queryParams.materialName\" placeholder=\"请输入物料名称\" clearable size=\"small\"\r\n          @keyup.enter.native=\"handleQuery\" />\r\n      </el-form-item>\r\n      <el-form-item label=\"是否计质量标记\" prop=\"materialType\">\r\n        <el-select v-model=\"queryParams.materialType\" placeholder=\"请选择是否计质量标记\">\r\n          <el-option label=\"计量\" value=\"1\" />\r\n          <el-option label=\"不计量\" value=\"0\" />\r\n        </el-select>\r\n      </el-form-item>\r\n      <!-- <el-form-item label=\"计量单位\" prop=\"measureUnit\">\r\n        <el-input v-model=\"queryParams.measureUnit\" placeholder=\"请输入计量单位\" clearable size=\"small\"\r\n          @keyup.enter.native=\"handleQuery\" />\r\n      </el-form-item> -->\r\n      <el-form-item label=\"是否贵重金属\" prop=\"materialType\">\r\n        <el-select v-model=\"queryParams.materialType\" placeholder=\"请选择是否贵重金属\">\r\n          <el-option label=\"是\" value=\"1\" />\r\n          <el-option label=\"否\" value=\"0\" />\r\n        </el-select>\r\n      </el-form-item>\r\n      <el-form-item>\r\n        <el-button type=\"cyan\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleQuery\">搜索</el-button>\r\n        <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\r\n      </el-form-item>\r\n    </el-form>\r\n\r\n    <el-row :gutter=\"10\" class=\"mb8\">\r\n      <el-col :span=\"1.5\">\r\n        <el-button type=\"primary\" icon=\"el-icon-plus\" size=\"mini\" @click=\"handleAdd\"\r\n          >新增</el-button>\r\n      </el-col>\r\n      <el-col :span=\"1.5\">\r\n        <el-button type=\"warning\" icon=\"el-icon-download\" size=\"mini\" @click=\"handleExport\"\r\n          >导出</el-button>\r\n      </el-col>\r\n      <right-toolbar :showSearch.sync=\"showSearch\" @queryTable=\"getList\"></right-toolbar>\r\n    </el-row>\r\n\r\n    <el-table v-loading=\"loading\" :data=\"materialList\" @selection-change=\"handleSelectionChange\">\r\n      <el-table-column type=\"selection\" width=\"55\" align=\"center\" />\r\n      <el-table-column label=\"物资名称\" align=\"center\" prop=\"materialName\" />\r\n      <!-- <el-table-column label=\"计量单位\" align=\"center\" prop=\"measureUnit\" /> -->\r\n      <el-table-column label=\"计质量标记\" align=\"center\" prop=\"measureFlag\">\r\n        <template slot-scope=\"scope\">\r\n          <el-tag v-if=\"scope.row.measureFlag == '1'\" type=\"success\">只质量</el-tag>\r\n          <el-tag v-if=\"scope.row.measureFlag == '0'\" type=\"warning\">只质检</el-tag>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"贵重金属\" align=\"center\" prop=\"materialType\">\r\n        <template slot-scope=\"scope\">\r\n          <el-tag v-if=\"scope.row.materialType == '1'\" type=\"success\">是</el-tag>\r\n          <el-tag v-if=\"scope.row.materialType == '0'\" type=\"warning\">否</el-tag>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"拼音头\" align=\"center\" prop=\"queryWord\" />\r\n      <el-table-column label=\"备注\" align=\"center\" prop=\"remark\" />\r\n      <el-table-column label=\"创建人\" align=\"center\" prop=\"createBy\" width=\"180\"/>\r\n      <el-table-column label=\"创建时间\" align=\"center\" prop=\"createTime\"/>\r\n      </el-table-column>\r\n      <el-table-column label=\"操作\" align=\"center\" class-name=\"small-padding fixed-width\">\r\n        <template slot-scope=\"scope\">\r\n          <!-- <el-button size=\"mini\" type=\"text\" icon=\"el-icon-edit\" @click=\"handleDetail(scope.row)\">详情</el-button> -->\r\n          <el-button type=\"text\" icon=\"el-icon-edit\" size=\"mini\" @click=\"handleUpdate(scope.row)\">修改</el-button>\r\n          <el-button size=\"text\" type=\"text\" icon=\"el-icon-delete\" @click=\"handleDelete(scope.row)\">废弃</el-button>\r\n        </template>\r\n      </el-table-column>\r\n    </el-table>\r\n\r\n    <pagination v-show=\"total > 0\" :total=\"total\" :page.sync=\"queryParams.pageNum\" :limit.sync=\"queryParams.pageSize\"\r\n      @pagination=\"getList\" />\r\n\r\n    <!-- 添加或修改出门证物资对话框 -->\r\n    <el-dialog :title=\"title\" :visible.sync=\"open\" width=\"500px\" append-to-body>\r\n      <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" label-width=\"90px\">\r\n        <el-form-item label=\"物资名称\" prop=\"materialName\">\r\n          <el-input v-model=\"form.materialName\" placeholder=\"请输入物资名称\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"计质量标记\" prop=\"drawingType\">\r\n          <el-select v-model=\"form.measureFlag\" placeholder=\"请选择计质量标记\">\r\n            <el-option label=\"只计量\" :value=\"1\" />\r\n            <el-option label=\"只质检\" :value=\"0\" />\r\n          </el-select>\r\n        </el-form-item>\r\n        <el-form-item label=\"拼音头\" prop=\"queryWord\">\r\n          <el-input v-model=\"form.queryWord\" placeholder=\"请输入拼音头\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"贵重金属\" prop=\"materialType\">\r\n          <el-select v-model=\"form.materialType\" placeholder=\"请选择是否贵重金属\">\r\n            <el-option label=\"是\" :value=\"1\" />\r\n            <el-option label=\"否\" :value=\"0\" />\r\n          </el-select>\r\n        </el-form-item>\r\n        <el-form-item label=\"备注\" prop=\"remark\">\r\n          <el-input v-model=\"form.remark\" placeholder=\"请输入备注\" />\r\n        </el-form-item>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button type=\"primary\" @click=\"submitForm\">确 定</el-button>\r\n        <el-button @click=\"cancel\">取 消</el-button>\r\n      </div>\r\n    </el-dialog>\r\n    <!-- \r\n    <el-dialog title=\"物料规格管理\" :visible.sync=\"openDetail\" width=\"600px\" append-to-body>\r\n      <el-form :model=\"form\" label-width=\"120px\">\r\n        <el-form-item label=\"物料名称：\" prop=\"materialName\">\r\n          {{ form.materialName }}\r\n        </el-form-item>\r\n        <el-form-item label=\"计质量标记：\" prop=\"drawingType\">\r\n          {{ form.drawingType == 0 ? '只质检' : '只质量' }}\r\n        </el-form-item>\r\n        <el-form-item label=\"拼音头：\" prop=\"queryWord\">\r\n          {{ form.queryWord }}\r\n        </el-form-item>\r\n        <el-form-item label=\"贵重金属：\" prop=\"materialType\">\r\n          {{ form.materialType == 0 ? '否' : '是' }}\r\n        </el-form-item>\r\n      </el-form>\r\n    </el-dialog> -->\r\n\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { listMaterial, getMaterial, delMaterial, addMaterial, updateMaterial, exportMaterial } from \"@/api/leave/material\";\r\nimport { js } from \"js-beautify\";\r\n\r\nexport default {\r\n  name: \"Material\",\r\n  components: {\r\n  },\r\n  data() {\r\n    return {\r\n      // 遮罩层\r\n      loading: true,\r\n      // 选中数组\r\n      ids: [],\r\n      // 非单个禁用\r\n      single: true,\r\n      // 非多个禁用\r\n      multiple: true,\r\n      // 显示搜索条件\r\n      showSearch: true,\r\n      // 总条数\r\n      total: 0,\r\n      // 出门证物资表格数据\r\n      materialList: [],\r\n      // 弹出层标题\r\n      title: \"\",\r\n      // 是否显示弹出层\r\n      open: false,\r\n      // openDetail: false,\r\n      // 查询参数\r\n      queryParams: {\r\n        pageNum: 1,\r\n        pageSize: 10,\r\n        materialName: null,\r\n        materialSpec: null,\r\n        measureUnit: null,\r\n        measureFlag: null,\r\n        materialType: null,\r\n        queryWord: null,\r\n        createBy: null,\r\n        createTime: null,\r\n        updateBy: null,\r\n        updateTime: null,\r\n        remark: null\r\n      },\r\n      // 表单参数\r\n      form: {},\r\n      // 表单校验\r\n      rules: {\r\n      }\r\n    };\r\n  },\r\n  created() {\r\n    this.getList();\r\n  },\r\n  methods: {\r\n    /** 查询出门证物资列表 */\r\n    getList() {\r\n      this.loading = true;\r\n      listMaterial(this.queryParams).then(response => {\r\n        this.materialList = response.rows;\r\n        this.total = response.total;\r\n        this.loading = false;\r\n      });\r\n    },\r\n    // 取消按钮\r\n    cancel() {\r\n      this.open = false;\r\n      this.reset();\r\n    },\r\n    // detailCancel() {\r\n    //   this.openDetail = false;\r\n    // },\r\n    // 表单重置\r\n    reset() {\r\n      this.form = {\r\n        id: null,\r\n        materialName: null,\r\n        materialSpec: null,\r\n        measureUnit: null,\r\n        measureFlag: null,\r\n        remark: null,\r\n        createTime: null,\r\n        createBy: null,\r\n        updateTime: null,\r\n        updateBy: null,\r\n        materialType: null,\r\n        queryWord: null\r\n      };\r\n      this.resetForm(\"form\");\r\n    },\r\n    /** 搜索按钮操作 */\r\n    handleQuery() {\r\n      this.queryParams.pageNum = 1;\r\n      this.getList();\r\n    },\r\n    /** 重置按钮操作 */\r\n    resetQuery() {\r\n      this.resetForm(\"queryForm\");\r\n      this.handleQuery();\r\n    },\r\n    // 多选框选中数据\r\n    handleSelectionChange(selection) {\r\n      this.ids = selection.map(item => item.id)\r\n      this.single = selection.length !== 1\r\n      this.multiple = !selection.length\r\n    },\r\n    /** 新增按钮操作 */\r\n    handleAdd() {\r\n      this.reset();\r\n      this.open = true;\r\n      this.title = \"添加出门证物资\";\r\n    },\r\n    /** 修改按钮操作 */\r\n    handleUpdate(row) {\r\n      this.reset();\r\n      const id = row.id || this.ids\r\n      getMaterial(id).then(response => {\r\n        this.form = response.data;\r\n        this.open = true;\r\n        this.title = \"修改出门证物资\";\r\n      });\r\n    },\r\n\r\n    // /** 详情按钮操作 */\r\n    // handleDetail(row) {\r\n    //   this.form = JSON.parse(JSON.stringify(row));\r\n    //   this.openDetail = true;\r\n    //   this.title = \"物料规格管理\";\r\n    // },\r\n    /** 提交按钮 */\r\n    submitForm() {\r\n      this.$refs[\"form\"].validate(valid => {\r\n        if (valid) {\r\n          if (this.form.id != null) {\r\n            updateMaterial(this.form).then(response => {\r\n              this.msgSuccess(\"修改成功\");\r\n              this.open = false;\r\n              this.getList();\r\n            });\r\n          } else {\r\n            addMaterial(this.form).then(response => {\r\n              this.msgSuccess(\"新增成功\");\r\n              this.open = false;\r\n              this.getList();\r\n            });\r\n          }\r\n        }\r\n      });\r\n    },\r\n    /** 删除按钮操作 */\r\n    handleDelete(row) {\r\n      const ids = row.materialName || this.ids;\r\n      this.$confirm('是否确认废弃物资名称为\"' + ids + '\"的数据项?', \"警告\", {\r\n        confirmButtonText: \"确定\",\r\n        cancelButtonText: \"取消\",\r\n        type: \"warning\"\r\n      }).then(function () {\r\n        return delMaterial(ids);\r\n      }).then(() => {\r\n        this.getList();\r\n        this.msgSuccess(\"删除成功\");\r\n      })\r\n    },\r\n    /** 导出按钮操作 */\r\n    handleExport() {\r\n      const queryParams = this.queryParams;\r\n      this.$confirm('是否确认导出所有出门证物资数据项?', \"警告\", {\r\n        confirmButtonText: \"确定\",\r\n        cancelButtonText: \"取消\",\r\n        type: \"warning\"\r\n      }).then(function () {\r\n        return exportMaterial(queryParams);\r\n      }).then(response => {\r\n        this.download(response.msg);\r\n      })\r\n    }\r\n  }\r\n};\r\n</script>\r\n"]}]}