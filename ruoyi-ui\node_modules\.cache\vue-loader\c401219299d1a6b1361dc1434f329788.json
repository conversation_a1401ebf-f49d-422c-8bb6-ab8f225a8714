{"remainingRequest": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\src\\views\\leave\\plan\\index.vue?vue&type=style&index=0&id=315d6a44&lang=css", "dependencies": [{"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\src\\views\\leave\\plan\\index.vue", "mtime": 1755499098437}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\@vue\\cli-service\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKDQoubWI4IHsNCiAgbWFyZ2luLWJvdHRvbTogOHB4Ow0KfQ0KDQoucGxhbi10YWJsZSB7DQogIHdpZHRoOiAxMDAlOw0KfQ0KDQoucGxhbi10YWJsZSB0aCB7DQogIGJhY2tncm91bmQtY29sb3I6ICNmNWY3ZmE7DQogIGNvbG9yOiAjNjA2MjY2Ow0KICBmb250LXdlaWdodDogNjAwOw0KICB0ZXh0LWFsaWduOiBjZW50ZXIgIWltcG9ydGFudDsNCn0NCg0KLnBsYW4tdGFibGUgdGQgew0KICB0ZXh0LWFsaWduOiBjZW50ZXIgIWltcG9ydGFudDsNCn0NCg0KLyog6KGo5Y2V5qC35byP6LCD5pW0ICovDQouZWwtZm9ybS1pdGVtIHsNCiAgbWFyZ2luLWJvdHRvbTogMTVweDsNCiAgd2lkdGg6IDEwMCU7DQp9DQoNCi5lbC1mb3JtLWl0ZW1fX2NvbnRlbnQgew0KICB3aWR0aDogY2FsYygxMDAlIC0gODBweCk7DQp9DQoNCi5lbC1kYXRlLWVkaXRvci5lbC1pbnB1dCB7DQogIHdpZHRoOiAxMDAlOw0KfQ0KDQouZWwtZGF0ZS1lZGl0b3ItLWRhdGVyYW5nZS5lbC1pbnB1dF9faW5uZXIgew0KICB3aWR0aDogMTAwJSAhaW1wb3J0YW50Ow0KfQ0K"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAukBA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/leave/plan", "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <el-form :model=\"queryParams\" ref=\"queryForm\" :inline=\"true\" v-show=\"showSearch\" label-width=\"80px\">\r\n      <el-row :gutter=\"30\">\r\n        <el-col :span=\"6\">\r\n          <el-form-item label=\"计划号\" prop=\"planNo\">\r\n            <el-input\r\n              v-model=\"queryParams.planNo\"\r\n              placeholder=\"请输入计划号\"\r\n              clearable\r\n              size=\"small\"\r\n              @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n        </el-col>\r\n        <el-col :span=\"6\">\r\n          <el-form-item label=\"计划类型\" prop=\"planType\">\r\n            <el-select v-model=\"queryParams.planType\" placeholder=\"请选择计划类型\" clearable size=\"small\" style=\"width: 100%\">\r\n              <el-option label=\"出厂不返回\" value=\"1\" />\r\n              <el-option label=\"出厂返回\" value=\"2\" />\r\n              <el-option label=\"跨区调拨\" value=\"3\" />\r\n              <el-option label=\"退货申请\" value=\"4\" />\r\n            </el-select>\r\n          </el-form-item>\r\n        </el-col>\r\n        \r\n        <el-col :span=\"6\">\r\n          <el-form-item label=\"是否计量\" prop=\"measureFlag\">\r\n            <el-select v-model=\"queryParams.measureFlag\" placeholder=\"请选择是否计量\" clearable size=\"small\" style=\"width: 100%\">\r\n              <el-option label=\"计量\" value=\"1\" />\r\n              <el-option label=\"不计量\" value=\"0\" />\r\n            </el-select>\r\n          </el-form-item>\r\n        </el-col>\r\n\r\n        <el-col :span=\"6\">\r\n          <el-form-item label=\"申请单位\" prop=\"sourceCompany\">\r\n            <el-input\r\n              v-model=\"queryParams.sourceCompany\"\r\n              placeholder=\"请输入申请单位\"\r\n              clearable\r\n              size=\"small\"\r\n              @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n        </el-col>\r\n\r\n        <el-col :span=\"6\">\r\n          <el-form-item label=\"收货单位\" prop=\"receiveCompany\">\r\n            <el-input\r\n              v-model=\"queryParams.receiveCompany\"\r\n              placeholder=\"请输入收货单位\"\r\n              clearable\r\n              size=\"small\"\r\n              @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n        </el-col>\r\n\r\n        <el-col :span=\"6\">\r\n          <el-form-item label=\"返回单位\" prop=\"targetCompany\">\r\n            <el-input\r\n              v-model=\"queryParams.targetCompany\"\r\n              placeholder=\"请输入返回单位\"\r\n              clearable\r\n              size=\"small\"\r\n              @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n        </el-col>\r\n\r\n        <!-- <el-col :span=\"6\">\r\n          <el-form-item label=\"物资专管员\" prop=\"specialManager\">\r\n            <el-input\r\n              v-model=\"queryParams.specialManager\"\r\n              placeholder=\"请输入物资专管员\"\r\n              clearable\r\n              size=\"small\"\r\n              @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n        </el-col> -->\r\n\r\n        <el-col :span=\"6\">\r\n          <el-form-item label=\"计划状态\" prop=\"planStatus\">\r\n            <el-select v-model=\"queryParams.planStatus\" placeholder=\"请选择计划状态\" clearable size=\"small\" style=\"width: 100%\">\r\n              <el-option label=\"待分厂审批\" value=\"1\" />\r\n              <el-option label=\"待分厂复审\" value=\"2\" />\r\n              <el-option label=\"待生产指挥中心审批\" value=\"3\" />\r\n              <el-option label=\"审批完成\" value=\"4\" />\r\n              <el-option label=\"已出厂\" value=\"5\" />\r\n              <el-option label=\"部分收货\" value=\"6\" />\r\n              <el-option label=\"已完成\" value=\"7\" />\r\n              <el-option label=\"驳回\" value=\"11\" />\r\n              <el-option label=\"废弃\" value=\"12\" />\r\n              <el-option label=\"过期\" value=\"13\" />\r\n            </el-select>\r\n          </el-form-item>\r\n        </el-col>\r\n\r\n        <el-col :span=\"12\">\r\n          <el-form-item label=\"返回时间\" prop=\"planReturnTime\">\r\n            <el-date-picker \r\n              clearable \r\n              size=\"small\" \r\n              style=\"width: 100%\"\r\n              v-model=\"daterangePlanReturn\"\r\n              type=\"daterange\"\r\n              value-format=\"yyyy-MM-dd\"\r\n              range-separator=\"至\"\r\n              start-placeholder=\"开始日期\"\r\n              end-placeholder=\"结束日期\"\r\n              @change=\"handlePlanReturnTimeChange\">\r\n            </el-date-picker>\r\n          </el-form-item>\r\n        </el-col>\r\n\r\n        <el-col :span=\"12\">\r\n          <el-form-item label=\"有效期\" prop=\"expireTime\">\r\n            <el-date-picker \r\n              clearable \r\n              size=\"small\" \r\n              style=\"width: 100%\"\r\n              v-model=\"daterangeExpire\"\r\n              type=\"daterange\"\r\n              value-format=\"yyyy-MM-dd\"\r\n              range-separator=\"至\"\r\n              start-placeholder=\"开始日期\"\r\n              end-placeholder=\"结束日期\"\r\n              @change=\"handleExpireTimeChange\">\r\n            </el-date-picker>\r\n          </el-form-item>\r\n        </el-col>\r\n        \r\n        <el-col :span=\"12\">\r\n          <el-form-item label=\"申请时间\" prop=\"applyTime\">\r\n            <el-date-picker \r\n              clearable \r\n              size=\"small\" \r\n              style=\"width: 100%\"\r\n              v-model=\"daterangeApply\"\r\n              type=\"daterange\"\r\n              value-format=\"yyyy-MM-dd\"\r\n              range-separator=\"至\"\r\n              start-placeholder=\"开始日期\"\r\n              end-placeholder=\"结束日期\"\r\n              @change=\"handleApplyTimeChange\">\r\n            </el-date-picker>\r\n          </el-form-item>\r\n        </el-col>\r\n        \r\n        <el-col :span=\"24\" style=\"text-align: center; margin-top: 10px\">\r\n          <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleQuery\">搜索</el-button>\r\n          <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\r\n        </el-col>\r\n      </el-row>\r\n    </el-form>\r\n\r\n    <el-row :gutter=\"10\" class=\"mb8\">\r\n      <el-col :span=\"1.5\">\r\n        <el-button v-if=\"this.showAddBtn\"\r\n          type=\"primary\"\r\n          icon=\"el-icon-plus\"\r\n          size=\"mini\"\r\n          @click=\"handleAdd\"\r\n        >新增</el-button>\r\n      </el-col>\r\n      <!-- <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"success\"\r\n          icon=\"el-icon-edit\"\r\n          size=\"mini\"\r\n          :disabled=\"single\"\r\n          @click=\"handleUpdate\"\r\n        >修改</el-button>\r\n      </el-col> -->\r\n      <el-col :span=\"1.5\">\r\n        <el-button v-if=\"this.showCancelBtn\"\r\n          type=\"danger\"\r\n          icon=\"el-icon-close\"\r\n          size=\"mini\"\r\n          :disabled=\"single\"\r\n          @click=\"handleInvalidate\"\r\n        >作废</el-button>\r\n      </el-col>\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"warning\"\r\n          icon=\"el-icon-printer\"\r\n          size=\"mini\"\r\n          :disabled=\"single\"\r\n          @click=\"handlePrintApplication\"\r\n        >打印申请单</el-button>\r\n      </el-col>\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"info\"\r\n          icon=\"el-icon-printer\"\r\n          size=\"mini\"\r\n          :disabled=\"single\"\r\n          @click=\"handlePrintMaterialList\"\r\n        >打印物料清单</el-button>\r\n      </el-col>\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"primary\"\r\n          plain\r\n          icon=\"el-icon-printer\"\r\n          size=\"mini\"\r\n          :disabled=\"single\"\r\n          @click=\"handlePrintPermit\"\r\n        >打印出门证</el-button>\r\n      </el-col>\r\n      <!-- <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"danger\"\r\n          icon=\"el-icon-delete\"\r\n          size=\"mini\"\r\n          :disabled=\"multiple\"\r\n          @click=\"handleDelete\"\r\n        >删除</el-button>\r\n      </el-col>\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"warning\"\r\n          icon=\"el-icon-download\"\r\n          size=\"mini\"\r\n          @click=\"handleExport\"\r\n        >导出</el-button>\r\n      </el-col> -->\r\n\t  <right-toolbar :showSearch.sync=\"showSearch\" @queryTable=\"getList\"></right-toolbar>\r\n    </el-row>\r\n\r\n    <el-table v-loading=\"loading\" :data=\"planList\" @selection-change=\"handleSelectionChange\" class=\"plan-table\">\r\n      <el-table-column type=\"selection\" width=\"55\" align=\"center\" />\r\n      <el-table-column label=\"申请编号\" align=\"center\" prop=\"applyNo\" width=\"180\" />\r\n      <el-table-column label=\"计划号\" align=\"center\" prop=\"planNo\" />\r\n      <el-table-column label=\"计划类型\" align=\"center\" prop=\"planType\" width=\"120\" >\r\n      <!-- tag标签 -->\r\n      <template slot-scope=\"scope\">\r\n        <el-tag v-if=\"scope.row.planType == '1'\" type=\"success\">出厂不返回</el-tag>\r\n        <el-tag v-if=\"scope.row.planType == '2'\" type=\"warning\">出厂返回</el-tag>\r\n        <el-tag v-if=\"scope.row.planType == '3'\" type=\"info\">跨区调拨</el-tag>\r\n        <el-tag v-if=\"scope.row.planType == '4'\" type=\"danger\">退货申请</el-tag>\r\n      </template>\r\n\r\n      </el-table-column>\r\n      <el-table-column label=\"申请人\" align=\"center\" prop=\"applyUserName\" />\r\n      <el-table-column label=\"申请单位\" align=\"center\" prop=\"sourceCompany\" width=\"200\" />\r\n      <el-table-column label=\"是否计量\" align=\"center\" prop=\"measureFlag\" >\r\n        <!-- tag标签 -->\r\n        <template slot-scope=\"scope\">\r\n          <el-tag v-if=\"scope.row.measureFlag == '1'\" type=\"success\">计量</el-tag>\r\n          <el-tag v-if=\"scope.row.measureFlag == '0'\" type=\"danger\">不计量</el-tag>\r\n        </template>\r\n      </el-table-column>\r\n      <!-- <el-table-column label=\"计划量\" align=\"center\" prop=\"plannedAmount\" /> -->\r\n      <el-table-column label=\"收货单位\" align=\"center\" prop=\"receiveCompany\" width=\"200\" />\r\n      <el-table-column label=\"返回单位\" align=\"center\" prop=\"targetCompany\" />\r\n      \r\n      <el-table-column label=\"申请有效期\" align=\"center\" prop=\"expireTime\" width=\"180\">\r\n        <template slot-scope=\"scope\">\r\n          <span>{{ parseTime(scope.row.expireTime, '{y}-{m}-{d}') }}</span>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"出厂原因\" align=\"center\" prop=\"reason\" />\r\n      <el-table-column label=\"计划状态\" align=\"center\" prop=\"planStatus\" >\r\n        <!-- tag标签 -->\r\n        <template slot-scope=\"scope\">\r\n          <el-tag v-if=\"scope.row.planStatus == '1'\" type=\"warning\">待分厂审批</el-tag>\r\n          <el-tag v-if=\"scope.row.planStatus == '2'\" type=\"warning\">待分厂复审</el-tag>\r\n          <el-tag v-if=\"scope.row.planStatus == '3'\" type=\"warning\">待生产指挥中心审批</el-tag>\r\n          <el-tag v-if=\"scope.row.planStatus == '4'\" type=\"success\">审批完成</el-tag>\r\n          <el-tag v-if=\"scope.row.planStatus == '5'\" type=\"success\">已出厂</el-tag>\r\n          <el-tag v-if=\"scope.row.planStatus == '6'\" type=\"info\">部分收货</el-tag>\r\n          <el-tag v-if=\"scope.row.planStatus == '7'\" type=\"success\">已完成</el-tag>\r\n          <el-tag v-if=\"scope.row.planStatus == '11'\" type=\"danger\">驳回</el-tag>\r\n          <el-tag v-if=\"scope.row.planStatus == '12'\" type=\"danger\">废弃</el-tag>\r\n          <el-tag v-if=\"scope.row.planStatus == '13'\" type=\"danger\">过期</el-tag>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"申请时间\" align=\"center\" prop=\"applyTime\" width=\"180\">\r\n        <template slot-scope=\"scope\">\r\n          <span>{{ parseTime(scope.row.applyTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>\r\n        </template>\r\n      </el-table-column>\r\n      \r\n      <el-table-column label=\"分厂审核\" align=\"center\" prop=\"factoryApproveFlag\" >\r\n        <template slot-scope=\"scope\">\r\n          <el-tag v-if=\"scope.row.factoryApproveFlag === '1'\" type=\"success\">同意</el-tag>\r\n          <el-tag v-else-if=\"scope.row.factoryApproveFlag === '0'\" type=\"danger\">拒绝</el-tag>\r\n          <span v-else>-</span>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"分厂复审\" align=\"center\" prop=\"factorySecApproveFlag\" >\r\n        <template slot-scope=\"scope\">\r\n          <el-tag v-if=\"scope.row.factorySecApproveFlag === '1'\" type=\"success\">同意</el-tag>\r\n          <el-tag v-else-if=\"scope.row.factorySecApproveFlag === '0'\" type=\"danger\">拒绝</el-tag>\r\n          <span v-else>-</span>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"生产指挥中心审核\" align=\"center\" prop=\"centerApproveFlag\" >\r\n        <template slot-scope=\"scope\">\r\n          <el-tag v-if=\"scope.row.centerApproveFlag === '1'\" type=\"success\">同意</el-tag>\r\n          <el-tag v-else-if=\"scope.row.centerApproveFlag === '0'\" type=\"danger\">拒绝</el-tag>\r\n          <span v-else>-</span>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"操作\" align=\"center\" class-name=\"small-padding fixed-width\" fixed=\"right\">\r\n        <template slot-scope=\"scope\">\r\n          <el-button\r\n            v-if=\"scope.row.showModifyBtn\"\r\n            size=\"mini\"\r\n            type=\"text\"\r\n            icon=\"el-icon-edit\"\r\n            @click=\"handleUpdate(scope.row)\"\r\n          >修改</el-button>\r\n          <!-- <el-button\r\n            size=\"mini\"\r\n            type=\"text\"\r\n            icon=\"el-icon-delete\"\r\n            @click=\"handleDelete(scope.row)\"\r\n          >删除</el-button> -->\r\n          <el-button\r\n            size=\"mini\"\r\n            type=\"text\"\r\n            icon=\"el-icon-view\"\r\n            @click=\"handleDetail(scope.row)\"\r\n          >详情</el-button>\r\n        </template>\r\n      </el-table-column>\r\n    </el-table>\r\n\r\n    <pagination\r\n      v-show=\"total>0\"\r\n      :total=\"total\"\r\n      :page.sync=\"queryParams.pageNum\"\r\n      :limit.sync=\"queryParams.pageSize\"\r\n      @pagination=\"getList\"\r\n    />\r\n\r\n    \r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { listPlan, getPlan, delPlan, addPlan, updatePlan, exportPlan } from \"@/api/leave/plan\";\r\nimport Editor from '@/components/Editor';\r\n\r\nexport default {\r\n  name: \"Plan\",\r\n  components: {\r\n    Editor,\r\n  },\r\n  data() {\r\n    return {\r\n      // 遮罩层\r\n      loading: true,\r\n      // 选中数组\r\n      applyNos: [],\r\n      // 非单个禁用\r\n      single: true,\r\n      // 非多个禁用\r\n      multiple: true,\r\n      // 显示搜索条件\r\n      showSearch: true,\r\n      // 总条数\r\n      total: 0,\r\n      // 出门证计划申请表格数据\r\n      planList: [],\r\n      // 日期范围\r\n      daterangePlanReturn: [],\r\n      daterangeExpire: [],\r\n      daterangeApply: [],\r\n      // 弹出层标题\r\n      title: \"\",\r\n      // 是否显示弹出层\r\n      open: false,\r\n      //是否显示新增按钮\r\n      showAddBtn:false,\r\n      //是否显示废弃按钮\r\n      showCancelBtn:false,\r\n      // 查询参数\r\n      queryParams: {\r\n        pageNum: 1,\r\n        pageSize: 10,\r\n        planNo: null,\r\n        planType: null,\r\n        businessCategory: null,\r\n        measureFlag: null,\r\n        plannedAmount: null,\r\n        receiveCompany: null,\r\n        receiveCompanyCode: null,\r\n        targetCompany: null,\r\n        targetCompanyCode: null,\r\n        sourceCompany: null,\r\n        sourceCompanyCode: null,\r\n        planReturnStartTime: null,\r\n        planReturnEndTime: null,\r\n        realReturnTime: null,\r\n        monitor: null,\r\n        specialManager: null,\r\n        expireStartTime: null,\r\n        expireEndTime: null,\r\n        reason: null,\r\n        itemType: null,\r\n        planStatus: null,\r\n        applyStartTime: null,\r\n        applyEndTime: null,\r\n        applyWorkNo: null,\r\n        factoryApproveTime: null,\r\n        factoryApproveWorkNo: null,\r\n        factoryApproveFlag: null,\r\n        factoryApproveContent: null,\r\n        factorySecApproveFlag: null,\r\n        factorySecApproveTime: null,\r\n        factorySecApproveWorkNo: null,\r\n        factorySecApproveContent: null,\r\n        centerApproveTime: null,\r\n        centerApproveWorkNo: null,\r\n        centerApproveFlag: null,\r\n        centerApproveContent: null,\r\n        applyFileUrl: null,\r\n      },\r\n      // 表单参数\r\n      form: {},\r\n      // 表单校验\r\n      rules: {\r\n      }\r\n    };\r\n  },\r\n  created() {\r\n    this.getList();\r\n  },\r\n  watch: {\r\n  '$route.query.refresh': {\r\n    immediate: true,\r\n    handler(newVal) {\r\n      if (newVal) {\r\n        this.getList(); // 刷新列表数据的方法\r\n          }\r\n        }\r\n      }\r\n    },\r\n  methods: {\r\n    /** 查询出门证计划申请列表 */\r\n    getList() {\r\n      this.loading = true;\r\n      listPlan(this.queryParams).then(response => {\r\n        this.planList = response.rows;\r\n        console.log(this.planList);\r\n        this.showAddBtn =this.planList[0].showAddBtn;\r\n        this.showCancelBtn =this.planList[0].showCancelBtn;\r\n        this.total = response.total;\r\n        this.loading = false;\r\n      });\r\n    },\r\n    // 取消按钮\r\n    cancel() {\r\n      this.open = false;\r\n      this.reset();\r\n    },\r\n    \r\n    /** 搜索按钮操作 */\r\n    handleQuery() {\r\n      this.queryParams.pageNum = 1;\r\n      this.getList();\r\n    },\r\n    /** 重置按钮操作 */\r\n    resetQuery() {\r\n      this.resetForm(\"queryForm\");\r\n      this.handleQuery();\r\n    },\r\n    // 多选框选中数据\r\n    handleSelectionChange(selection) {\r\n      this.applyNos = selection.map(item => item.applyNo)\r\n      this.single = selection.length!==1\r\n      this.multiple = !selection.length\r\n    },\r\n    /** 新增按钮操作 */\r\n    handleAdd() {\r\n      this.$router.push(\"/leave/plan/edit\");\r\n    },\r\n    \r\n    /** 修改按钮操作 */\r\n    handleUpdate(row) {\r\n      if (this.applyNos.length > 0) {\r\n        this.$router.push(`/leave/plan/edit/${this.applyNos[0]}`);\r\n      } else {\r\n        this.$router.push(`/leave/plan/edit/${row.applyNo}`);\r\n      }\r\n    },\r\n    \r\n    /** 删除按钮操作 */\r\n    handleDelete(row) {\r\n      const ids = row.id || this.ids;\r\n      this.$confirm('是否确认删除出门证计划申请编号为\"' + ids + '\"的数据项?', \"警告\", {\r\n          confirmButtonText: \"确定\",\r\n          cancelButtonText: \"取消\",\r\n          type: \"warning\"\r\n        }).then(function() {\r\n          return delPlan(ids);\r\n        }).then(() => {\r\n          this.getList();\r\n          this.msgSuccess(\"删除成功\");\r\n        })\r\n    },\r\n    /** 导出按钮操作 */\r\n    handleExport() {\r\n      const queryParams = this.queryParams;\r\n      this.$confirm('是否确认导出所有出门证计划申请数据项?', \"警告\", {\r\n          confirmButtonText: \"确定\",\r\n          cancelButtonText: \"取消\",\r\n          type: \"warning\"\r\n        }).then(function() {\r\n          return exportPlan(queryParams);\r\n        }).then(response => {\r\n          this.download(response.msg);\r\n        })\r\n    },\r\n    handleDetail(row) {\r\n      // 跳转到详情页，并传递ID参数\r\n      this.$router.push({\r\n        path: `/leave/plan/detail/${row.applyNo}`\r\n      });\r\n    },\r\n    // 计划返回时间范围发生变化\r\n    handlePlanReturnTimeChange(val) {\r\n      if (val) {\r\n        this.queryParams.planReturnStartTime = val[0];\r\n        this.queryParams.planReturnEndTime = val[1];\r\n      } else {\r\n        this.queryParams.planReturnStartTime = null;\r\n        this.queryParams.planReturnEndTime = null;\r\n      }\r\n    },\r\n    \r\n    // 申请有效期范围发生变化\r\n    handleExpireTimeChange(val) {\r\n      if (val) {\r\n        this.queryParams.expireStartTime = val[0];\r\n        this.queryParams.expireEndTime = val[1];\r\n      } else {\r\n        this.queryParams.expireStartTime = null;\r\n        this.queryParams.expireEndTime = null;\r\n      }\r\n    },\r\n    \r\n    // 申请时间范围发生变化\r\n    handleApplyTimeChange(val) {\r\n      if (val) {\r\n        this.queryParams.applyStartTime = val[0];\r\n        this.queryParams.applyEndTime = val[1];\r\n      } else {\r\n        this.queryParams.applyStartTime = null;\r\n        this.queryParams.applyEndTime = null;\r\n      }\r\n    },\r\n    \r\n    /** 作废按钮操作 */\r\n    handleInvalidate(row) {\r\n      // TODO: 实现作废功能\r\n    },\r\n    \r\n    /** 打印申请单按钮操作 */\r\n    handlePrintApplication(row) {\r\n      // TODO: 实现打印申请单功能\r\n    },\r\n    \r\n    /** 打印物料清单按钮操作 */\r\n    handlePrintMaterialList(row) {\r\n      // TODO: 实现打印物料清单功能\r\n    },\r\n    \r\n    /** 打印出门证按钮操作 */\r\n    handlePrintPermit(row) {\r\n      // TODO: 实现打印出门证功能\r\n    },\r\n  }\r\n};\r\n</script>\r\n\r\n<style>\r\n.mb8 {\r\n  margin-bottom: 8px;\r\n}\r\n\r\n.plan-table {\r\n  width: 100%;\r\n}\r\n\r\n.plan-table th {\r\n  background-color: #f5f7fa;\r\n  color: #606266;\r\n  font-weight: 600;\r\n  text-align: center !important;\r\n}\r\n\r\n.plan-table td {\r\n  text-align: center !important;\r\n}\r\n\r\n/* 表单样式调整 */\r\n.el-form-item {\r\n  margin-bottom: 15px;\r\n  width: 100%;\r\n}\r\n\r\n.el-form-item__content {\r\n  width: calc(100% - 80px);\r\n}\r\n\r\n.el-date-editor.el-input {\r\n  width: 100%;\r\n}\r\n\r\n.el-date-editor--daterange.el-input__inner {\r\n  width: 100% !important;\r\n}\r\n</style>\r\n"]}]}