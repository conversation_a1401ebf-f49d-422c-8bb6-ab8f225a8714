{"remainingRequest": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??vue-loader-options!E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\src\\views\\qualityCost\\companySummary1\\index.vue?vue&type=template&id=646471de&scoped=true", "dependencies": [{"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\src\\views\\qualityCost\\companySummary1\\index.vue", "mtime": 1755499162051}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}]}