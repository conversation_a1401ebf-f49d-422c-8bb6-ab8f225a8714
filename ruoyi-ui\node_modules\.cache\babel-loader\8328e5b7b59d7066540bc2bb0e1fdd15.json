{"remainingRequest": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\src\\views\\truck\\alloy\\list.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\src\\views\\truck\\alloy\\list.vue", "mtime": 1755499162068}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\babel.config.js", "mtime": 1688548084091}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_reservation", "require", "name", "data", "loading", "showSearch", "total", "orderList", "queryParams", "pageNum", "pageSize", "reservationNo", "supplierSalesName", "applyCompanyName", "alloyType", "alloyLabel", "carNo", "<PERSON><PERSON><PERSON>", "enterDoor", "status", "statusOptions", "value", "label", "editStatusDialogVisible", "editStatusForm", "created", "getList", "methods", "_this", "listAlloyOrder", "then", "response", "rows", "handleQuery", "reset<PERSON><PERSON>y", "$refs", "queryForm", "resetFields", "handleDetail", "row", "$router", "push", "concat", "handleExport", "_this2", "$confirm", "confirmButtonText", "cancelButtonText", "type", "exportAlloyOrder", "download", "msg", "openEditStatusDialog", "submitEditStatus", "_this3", "updateAlloyOrderStatus", "code", "$message", "success", "error", "parseTime", "time", "pattern", "date", "Date", "isNaN", "getTime", "y", "getFullYear", "m", "getMonth", "toString", "padStart", "d", "getDate", "h", "getHours", "i", "getMinutes", "s", "getSeconds", "getBusinessDept", "val", "getEntranceGate", "getElectrodeDesc", "getStatusLabel", "item", "find", "getStatusTagType", "statusTypeMap"], "sources": ["src/views/truck/alloy/list.vue"], "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <el-form :model=\"queryParams\" ref=\"queryForm\" :inline=\"true\" v-show=\"showSearch\" label-width=\"auto\">\r\n      <el-form-item label=\"预约编号\" prop=\"reservationNo\">\r\n        <el-input v-model=\"queryParams.reservationNo\" placeholder=\"请输入预约编号\" clearable size=\"small\"\r\n          @keyup.enter.native=\"handleQuery\" />\r\n      </el-form-item>\r\n      <el-form-item label=\"业务部门\" prop=\"approvalDept\">\r\n        <el-select v-model=\"queryParams.approvalDept\" placeholder=\"请选择业务部门\" clearable size=\"small\">\r\n          <el-option label=\"采购中心\" :value=\"1\" />\r\n          <el-option label=\"商务部\" :value=\"2\" />\r\n        </el-select>\r\n      </el-form-item>\r\n      <el-form-item label=\"供方业务员姓名\" prop=\"supplierSalesName\">\r\n        <el-input v-model=\"queryParams.supplierSalesName\" placeholder=\"请输入供方业务员姓名\" clearable size=\"small\"\r\n          @keyup.enter.native=\"handleQuery\" />\r\n      </el-form-item>\r\n      <el-form-item label=\"申请单位名称\" prop=\"applyCompanyName\">\r\n        <el-input v-model=\"queryParams.applyCompanyName\" placeholder=\"请输入申请单位名称\" clearable size=\"small\"\r\n          @keyup.enter.native=\"handleQuery\" />\r\n      </el-form-item>\r\n      <el-form-item label=\"合金类型\" prop=\"alloyType\">\r\n        <el-input v-model=\"queryParams.alloyType\" placeholder=\"请输入合金类型\" clearable size=\"small\"\r\n          @keyup.enter.native=\"handleQuery\" />\r\n      </el-form-item>\r\n      <el-form-item label=\"合金\" prop=\"alloyLabel\">\r\n        <el-input v-model=\"queryParams.alloyLabel\" placeholder=\"请输入合金名称\" clearable size=\"small\"\r\n          @keyup.enter.native=\"handleQuery\" />\r\n      </el-form-item>\r\n      <el-form-item label=\"电极规格\" prop=\"electrodeType\">\r\n        <el-select v-model=\"queryParams.electrodeType\" placeholder=\"电极规格\" clearable size=\"small\">\r\n          <el-option label=\"400\" :value=\"1\" />\r\n          <el-option label=\"450\" :value=\"2\" />\r\n          <el-option label=\"700\" :value=\"3\" />\r\n        </el-select>\r\n      </el-form-item>\r\n      <el-form-item label=\"车牌号\" prop=\"carNo\">\r\n        <el-input v-model=\"queryParams.carNo\" placeholder=\"请输入车牌号\" clearable size=\"small\"\r\n          @keyup.enter.native=\"handleQuery\" />\r\n      </el-form-item>\r\n      <el-form-item label=\"司机名\" prop=\"driverName\">\r\n        <el-input v-model=\"queryParams.driverName\" placeholder=\"请输入司机名\" clearable size=\"small\"\r\n          @keyup.enter.native=\"handleQuery\" />\r\n      </el-form-item>\r\n      <el-form-item label=\"进厂大门\" prop=\"enterDoor\">\r\n        <el-select v-model=\"queryParams.enterDoor\" placeholder=\"请选择进厂大门\" clearable size=\"small\">\r\n          <el-option label=\"安全村\" :value=\"1\" />\r\n          <el-option label=\"三号门\" :value=\"2\" />\r\n        </el-select>\r\n      </el-form-item>\r\n      <el-form-item label=\"状态\" prop=\"status\">\r\n        <el-select v-model=\"queryParams.status\" placeholder=\"请选择状态\" clearable size=\"small\">\r\n          <el-option v-for=\"item in statusOptions\" :key=\"item.value\" :label=\"item.label\" :value=\"item.value\" />\r\n        </el-select>\r\n      </el-form-item>\r\n      <el-form-item>\r\n        <el-button type=\"cyan\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleQuery\">搜索</el-button>\r\n        <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\r\n      </el-form-item>\r\n    </el-form>\r\n\r\n    <el-row :gutter=\"10\" class=\"mb8\">\r\n      <el-col :span=\"1.5\">\r\n        <el-button type=\"warning\" icon=\"el-icon-download\" size=\"mini\" @click=\"handleExport\">导出</el-button>\r\n      </el-col>\r\n      <right-toolbar :showSearch.sync=\"showSearch\" @queryTable=\"getList\"></right-toolbar>\r\n    </el-row>\r\n\r\n    <el-table v-loading=\"loading\" :data=\"orderList\">\r\n      <el-table-column label=\"预约编号\" align=\"center\" prop=\"reservationNo\" width=\"150\" />\r\n      <el-table-column label=\"业务部门\" align=\"center\">\r\n        <template slot-scope=\"scope\">\r\n          <span>{{ getBusinessDept(scope.row.approvalDept) }}</span>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"申请单位\" align=\"center\" prop=\"applyCompanyName\" width=\"180\" />\r\n      <el-table-column label=\"合金类型\" align=\"center\" prop=\"alloyType\" />\r\n      <el-table-column label=\"合金\" align=\"center\" prop=\"alloyLabel\" />\r\n      <el-table-column label=\"合金吨数\" align=\"center\" prop=\"estimatedWeight\" />\r\n      <el-table-column label=\"电极规格\" align=\"center\" prop=\"electrodeType\" width=\"90\">\r\n        <template slot-scope=\"scope\">\r\n          <span>{{ getElectrodeDesc(scope.row.electrodeType) }}</span>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"预计送货日期\" align=\"center\" prop=\"expectedDeliveryTime\" width=\"120\">\r\n        <template slot-scope=\"scope\">\r\n          <span>{{ parseTime(scope.row.expectedDeliveryTime, '{y}-{m}-{d}') }}</span>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"车牌\" align=\"center\" prop=\"carNo\" width=\"90\" />\r\n      <el-table-column label=\"有效开始时间\" align=\"center\" prop=\"effectiveStartTime\" width=\"180\">\r\n        <template slot-scope=\"scope\">\r\n          <span>{{ parseTime(scope.row.effectiveStartTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"有效结束时间\" align=\"center\" prop=\"effectiveEndTime\" width=\"180\">\r\n        <template slot-scope=\"scope\">\r\n          <span>{{ parseTime(scope.row.effectiveEndTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"进厂大门\" align=\"center\" prop=\"enterDoor\">\r\n        <template slot-scope=\"scope\">\r\n          <span>{{ getEntranceGate(scope.row.enterDoor) }}</span>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"司机\" align=\"center\" prop=\"driverName\" />\r\n      <el-table-column label=\"状态\" align=\"center\" prop=\"status\">\r\n        <template slot-scope=\"scope\">\r\n          <el-tag :type=\"getStatusTagType(scope.row.status)\" size=\"small\">\r\n            {{ getStatusLabel(scope.row.status) }}\r\n          </el-tag>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"供方业务员\" align=\"center\" prop=\"supplierSalesName\" width=\"100\" />\r\n      <el-table-column label=\"创建时间\" align=\"center\" prop=\"createTime\" width=\"180\">\r\n        <template slot-scope=\"scope\">\r\n          <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"操作\" align=\"center\" class-name=\"small-padding fixed-width\" fixed=\"right\">\r\n        <template slot-scope=\"scope\">\r\n          <el-button size=\"mini\" type=\"text\" icon=\"el-icon-view\" @click=\"handleDetail(scope.row)\">详情</el-button>\r\n          <el-button size=\"mini\" type=\"text\" icon=\"el-icon-edit\"\r\n            @click=\"openEditStatusDialog(scope.row)\">修改状态</el-button>\r\n        </template>\r\n      </el-table-column>\r\n    </el-table>\r\n\r\n    <pagination v-show=\"total > 0\" :total=\"total\" :page.sync=\"queryParams.pageNum\" :limit.sync=\"queryParams.pageSize\"\r\n      @pagination=\"getList\" />\r\n\r\n    <el-dialog :title=\"'修改预约单状态'\" :visible.sync=\"editStatusDialogVisible\" width=\"400px\">\r\n      <el-form :model=\"editStatusForm\" label-width=\"80px\">\r\n        <el-form-item label=\"状态\">\r\n          <el-select v-model=\"editStatusForm.status\" placeholder=\"请选择状态\">\r\n            <el-option v-for=\"item in statusOptions\" :key=\"item.value\" :label=\"item.label\" :value=\"item.value\" />\r\n          </el-select>\r\n        </el-form-item>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"editStatusDialogVisible = false\">取消</el-button>\r\n        <el-button type=\"primary\" @click=\"submitEditStatus\">确定</el-button>\r\n      </div>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { listAlloyOrder, exportAlloyOrder, updateAlloyOrderStatus } from '@/api/truck/alloy/reservation';\r\n\r\nexport default {\r\n  name: 'AlloyOrderList',\r\n  data() {\r\n    return {\r\n      loading: true,\r\n      showSearch: true,\r\n      total: 0,\r\n      orderList: [],\r\n      queryParams: {\r\n        pageNum: 1,\r\n        pageSize: 10,\r\n        reservationNo: null,\r\n        supplierSalesName: null,\r\n        applyCompanyName: null,\r\n        alloyType: null,\r\n        alloyLabel: null,\r\n        carNo: null,\r\n        driverName: null,\r\n        enterDoor: null,\r\n        status: null\r\n      },\r\n      statusOptions: [\r\n        { value: '1', label: '待审核' },\r\n        { value: '2', label: '待签到' },\r\n        { value: '3', label: '物管分配' },\r\n        { value: '4', label: '待入厂' },\r\n        { value: '5', label: '已入厂' },\r\n        { value: '6', label: '物管确认' },\r\n        { value: '7', label: '已出厂' },\r\n        { value: '21', label: '驳回' },\r\n        { value: '22', label: '待取消' },\r\n        { value: '23', label: '已取消' }\r\n      ],\r\n      editStatusDialogVisible: false,\r\n      editStatusForm: {\r\n        reservationNo: '',\r\n        status: ''\r\n      }\r\n    };\r\n  },\r\n  created() {\r\n    this.getList();\r\n  },\r\n  methods: {\r\n    getList() {\r\n      this.loading = true;\r\n      listAlloyOrder(this.queryParams).then(response => {\r\n        this.orderList = response.rows || [];\r\n        this.total = response.total || 0;\r\n        this.loading = false;\r\n      });\r\n    },\r\n    handleQuery() {\r\n      this.queryParams.pageNum = 1;\r\n      this.getList();\r\n    },\r\n    resetQuery() {\r\n      this.$refs.queryForm.resetFields();\r\n      this.handleQuery();\r\n    },\r\n    handleDetail(row) {\r\n      this.$router.push(`/truck/alloy/detail/${row.reservationNo}`);\r\n    },\r\n    handleExport() {\r\n      const queryParams = this.queryParams;\r\n      this.$confirm('是否确认导出所有合金车辆预约数据项?', \"警告\", {\r\n        confirmButtonText: \"确定\",\r\n        cancelButtonText: \"取消\",\r\n        type: \"warning\"\r\n      }).then(() => {\r\n        return exportAlloyOrder(queryParams);\r\n      }).then(response => {\r\n        this.download(response.msg);\r\n      });\r\n    },\r\n    openEditStatusDialog(row) {\r\n      this.editStatusForm.reservationNo = row.reservationNo;\r\n      this.editStatusForm.status = row.status;\r\n      this.editStatusDialogVisible = true;\r\n    },\r\n    submitEditStatus() {\r\n      updateAlloyOrderStatus(this.editStatusForm).then(response => {\r\n        if (response.code === 200) {\r\n          this.$message.success('状态修改成功');\r\n          this.editStatusDialogVisible = false;\r\n          this.getList();\r\n        } else {\r\n          this.$message.error(response.msg || '状态修改失败');\r\n        }\r\n      });\r\n    },\r\n    parseTime(time, pattern) {\r\n      if (!time) return '';\r\n      const date = new Date(time);\r\n      if (isNaN(date.getTime())) return time;\r\n\r\n      const y = date.getFullYear();\r\n      const m = (date.getMonth() + 1).toString().padStart(2, '0');\r\n      const d = date.getDate().toString().padStart(2, '0');\r\n      const h = date.getHours().toString().padStart(2, '0');\r\n      const i = date.getMinutes().toString().padStart(2, '0');\r\n      const s = date.getSeconds().toString().padStart(2, '0');\r\n\r\n      if (pattern === '{y}-{m}-{d}') {\r\n        return `${y}-${m}-${d}`;\r\n      } else if (pattern === '{y}-{m}-{d} {h}:{i}') {\r\n        return `${y}-${m}-${d} ${h}:${i}`;\r\n      } else if (pattern === '{y}-{m}-{d} {h}:{i}:{s}') {\r\n        return `${y}-${m}-${d} ${h}:${i}:${s}`;\r\n      }\r\n      return time;\r\n    },\r\n    getBusinessDept(val) {\r\n      if (val == '1') return '采购中心';\r\n      if (val == '2') return '商务部';\r\n      return '';\r\n    },\r\n    getEntranceGate(val) {\r\n      if (val == '1') return '安全村';\r\n      if (val == '2') return '三号门';\r\n      return '';\r\n    },\r\n    getElectrodeDesc(val) {\r\n      if (val == '1') return '400';\r\n      if (val == '2') return '450';\r\n      if (val == '3') return '700';\r\n      return '';\r\n    },\r\n    getStatusLabel(val) {\r\n      const item = this.statusOptions.find(i => i.value == val);\r\n      return item ? item.label : '未知';\r\n    },\r\n    getStatusTagType(val) {\r\n      const statusTypeMap = {\r\n        '1': 'warning',    // 待审核\r\n        '2': 'info',       // 待签到\r\n        '3': 'primary',    // 物管分配\r\n        '4': 'warning',    // 待入厂\r\n        '5': 'success',    // 已入厂\r\n        '6': 'primary',    // 物管确认\r\n        '7': 'success',    // 已出厂\r\n        '21': 'danger',    // 驳回\r\n        '22': 'warning',   // 待取消\r\n        '23': 'info'       // 已取消\r\n      };\r\n      return statusTypeMap[val] || 'info';\r\n    }\r\n  }\r\n};\r\n</script>\r\n"], "mappings": ";;;;;;;;;;;;;AAoJA,IAAAA,YAAA,GAAAC,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCAEA;EACAC,IAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,OAAA;MACAC,UAAA;MACAC,KAAA;MACAC,SAAA;MACAC,WAAA;QACAC,OAAA;QACAC,QAAA;QACAC,aAAA;QACAC,iBAAA;QACAC,gBAAA;QACAC,SAAA;QACAC,UAAA;QACAC,KAAA;QACAC,UAAA;QACAC,SAAA;QACAC,MAAA;MACA;MACAC,aAAA,GACA;QAAAC,KAAA;QAAAC,KAAA;MAAA,GACA;QAAAD,KAAA;QAAAC,KAAA;MAAA,GACA;QAAAD,KAAA;QAAAC,KAAA;MAAA,GACA;QAAAD,KAAA;QAAAC,KAAA;MAAA,GACA;QAAAD,KAAA;QAAAC,KAAA;MAAA,GACA;QAAAD,KAAA;QAAAC,KAAA;MAAA,GACA;QAAAD,KAAA;QAAAC,KAAA;MAAA,GACA;QAAAD,KAAA;QAAAC,KAAA;MAAA,GACA;QAAAD,KAAA;QAAAC,KAAA;MAAA,GACA;QAAAD,KAAA;QAAAC,KAAA;MAAA,EACA;MACAC,uBAAA;MACAC,cAAA;QACAb,aAAA;QACAQ,MAAA;MACA;IACA;EACA;EACAM,OAAA,WAAAA,QAAA;IACA,KAAAC,OAAA;EACA;EACAC,OAAA;IACAD,OAAA,WAAAA,QAAA;MAAA,IAAAE,KAAA;MACA,KAAAxB,OAAA;MACA,IAAAyB,2BAAA,OAAArB,WAAA,EAAAsB,IAAA,WAAAC,QAAA;QACAH,KAAA,CAAArB,SAAA,GAAAwB,QAAA,CAAAC,IAAA;QACAJ,KAAA,CAAAtB,KAAA,GAAAyB,QAAA,CAAAzB,KAAA;QACAsB,KAAA,CAAAxB,OAAA;MACA;IACA;IACA6B,WAAA,WAAAA,YAAA;MACA,KAAAzB,WAAA,CAAAC,OAAA;MACA,KAAAiB,OAAA;IACA;IACAQ,UAAA,WAAAA,WAAA;MACA,KAAAC,KAAA,CAAAC,SAAA,CAAAC,WAAA;MACA,KAAAJ,WAAA;IACA;IACAK,YAAA,WAAAA,aAAAC,GAAA;MACA,KAAAC,OAAA,CAAAC,IAAA,wBAAAC,MAAA,CAAAH,GAAA,CAAA5B,aAAA;IACA;IACAgC,YAAA,WAAAA,aAAA;MAAA,IAAAC,MAAA;MACA,IAAApC,WAAA,QAAAA,WAAA;MACA,KAAAqC,QAAA;QACAC,iBAAA;QACAC,gBAAA;QACAC,IAAA;MACA,GAAAlB,IAAA;QACA,WAAAmB,6BAAA,EAAAzC,WAAA;MACA,GAAAsB,IAAA,WAAAC,QAAA;QACAa,MAAA,CAAAM,QAAA,CAAAnB,QAAA,CAAAoB,GAAA;MACA;IACA;IACAC,oBAAA,WAAAA,qBAAAb,GAAA;MACA,KAAAf,cAAA,CAAAb,aAAA,GAAA4B,GAAA,CAAA5B,aAAA;MACA,KAAAa,cAAA,CAAAL,MAAA,GAAAoB,GAAA,CAAApB,MAAA;MACA,KAAAI,uBAAA;IACA;IACA8B,gBAAA,WAAAA,iBAAA;MAAA,IAAAC,MAAA;MACA,IAAAC,mCAAA,OAAA/B,cAAA,EAAAM,IAAA,WAAAC,QAAA;QACA,IAAAA,QAAA,CAAAyB,IAAA;UACAF,MAAA,CAAAG,QAAA,CAAAC,OAAA;UACAJ,MAAA,CAAA/B,uBAAA;UACA+B,MAAA,CAAA5B,OAAA;QACA;UACA4B,MAAA,CAAAG,QAAA,CAAAE,KAAA,CAAA5B,QAAA,CAAAoB,GAAA;QACA;MACA;IACA;IACAS,SAAA,WAAAA,UAAAC,IAAA,EAAAC,OAAA;MACA,KAAAD,IAAA;MACA,IAAAE,IAAA,OAAAC,IAAA,CAAAH,IAAA;MACA,IAAAI,KAAA,CAAAF,IAAA,CAAAG,OAAA,YAAAL,IAAA;MAEA,IAAAM,CAAA,GAAAJ,IAAA,CAAAK,WAAA;MACA,IAAAC,CAAA,IAAAN,IAAA,CAAAO,QAAA,QAAAC,QAAA,GAAAC,QAAA;MACA,IAAAC,CAAA,GAAAV,IAAA,CAAAW,OAAA,GAAAH,QAAA,GAAAC,QAAA;MACA,IAAAG,CAAA,GAAAZ,IAAA,CAAAa,QAAA,GAAAL,QAAA,GAAAC,QAAA;MACA,IAAAK,CAAA,GAAAd,IAAA,CAAAe,UAAA,GAAAP,QAAA,GAAAC,QAAA;MACA,IAAAO,CAAA,GAAAhB,IAAA,CAAAiB,UAAA,GAAAT,QAAA,GAAAC,QAAA;MAEA,IAAAV,OAAA;QACA,UAAApB,MAAA,CAAAyB,CAAA,OAAAzB,MAAA,CAAA2B,CAAA,OAAA3B,MAAA,CAAA+B,CAAA;MACA,WAAAX,OAAA;QACA,UAAApB,MAAA,CAAAyB,CAAA,OAAAzB,MAAA,CAAA2B,CAAA,OAAA3B,MAAA,CAAA+B,CAAA,OAAA/B,MAAA,CAAAiC,CAAA,OAAAjC,MAAA,CAAAmC,CAAA;MACA,WAAAf,OAAA;QACA,UAAApB,MAAA,CAAAyB,CAAA,OAAAzB,MAAA,CAAA2B,CAAA,OAAA3B,MAAA,CAAA+B,CAAA,OAAA/B,MAAA,CAAAiC,CAAA,OAAAjC,MAAA,CAAAmC,CAAA,OAAAnC,MAAA,CAAAqC,CAAA;MACA;MACA,OAAAlB,IAAA;IACA;IACAoB,eAAA,WAAAA,gBAAAC,GAAA;MACA,IAAAA,GAAA;MACA,IAAAA,GAAA;MACA;IACA;IACAC,eAAA,WAAAA,gBAAAD,GAAA;MACA,IAAAA,GAAA;MACA,IAAAA,GAAA;MACA;IACA;IACAE,gBAAA,WAAAA,iBAAAF,GAAA;MACA,IAAAA,GAAA;MACA,IAAAA,GAAA;MACA,IAAAA,GAAA;MACA;IACA;IACAG,cAAA,WAAAA,eAAAH,GAAA;MACA,IAAAI,IAAA,QAAAlE,aAAA,CAAAmE,IAAA,WAAAV,CAAA;QAAA,OAAAA,CAAA,CAAAxD,KAAA,IAAA6D,GAAA;MAAA;MACA,OAAAI,IAAA,GAAAA,IAAA,CAAAhE,KAAA;IACA;IACAkE,gBAAA,WAAAA,iBAAAN,GAAA;MACA,IAAAO,aAAA;QACA;QAAA;QACA;QAAA;QACA;QAAA;QACA;QAAA;QACA;QAAA;QACA;QAAA;QACA;QAAA;QACA;QAAA;QACA;QAAA;QACA;MACA;MACA,OAAAA,aAAA,CAAAP,GAAA;IACA;EACA;AACA", "ignoreList": []}]}