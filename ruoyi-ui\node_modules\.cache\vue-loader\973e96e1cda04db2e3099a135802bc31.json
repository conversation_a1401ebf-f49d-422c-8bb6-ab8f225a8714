{"remainingRequest": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\src\\views\\suppPunishment\\project-module.vue?vue&type=style&index=0&id=9c3979e0&scoped=true&lang=css", "dependencies": [{"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\src\\views\\suppPunishment\\project-module.vue", "mtime": 1755499162060}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\@vue\\cli-service\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCg0KLmRpYWxvZy1mb290ZXIgew0KICB0ZXh0LWFsaWduOiBjZW50ZXI7DQp9DQoNCjo6di1kZWVwIC5lbC10YWJsZSB0Ym9keSB0cjpob3ZlciA+IHRkIHsNCiAgYmFja2dyb3VuZC1jb2xvcjogI2Y1ZjdmYSAhaW1wb3J0YW50Ow0KfQ0KDQo6OnYtZGVlcCAuZWwtdGFibGUgdGJvZHkgdHIuY3VycmVudC1yb3cgPiB0ZCB7DQogIGJhY2tncm91bmQtY29sb3I6ICNlY2Y1ZmYgIWltcG9ydGFudDsNCn0NCg=="}, {"version": 3, "sources": ["project-module.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAoKA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA", "file": "project-module.vue", "sourceRoot": "src/views/suppPunishment", "sourcesContent": ["<template>\r\n  <el-dialog\r\n    title=\"项目查询\"\r\n    :visible.sync=\"visible\"\r\n    width=\"900px\"\r\n    append-to-body\r\n    @close=\"handleClose\"\r\n  >\r\n    <!-- 查询条件 -->\r\n    <el-form :model=\"queryParams\" ref=\"queryForm\" :inline=\"true\" label-width=\"100px\">\r\n      <el-form-item label=\"项目编号\" prop=\"projectNo\">\r\n        <el-input\r\n          v-model=\"queryParams.projectNo\"\r\n          placeholder=\"请输入项目编号\"\r\n          clearable\r\n          size=\"small\"\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"项目名称\" prop=\"projectName\">\r\n        <el-input\r\n          v-model=\"queryParams.projectName\"\r\n          placeholder=\"请输入项目名称\"\r\n          clearable\r\n          size=\"small\"\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item >\r\n        <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleQuery\">搜索</el-button>\r\n        <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\r\n      </el-form-item>\r\n    </el-form>\r\n\r\n    <!-- 项目列表 -->\r\n    <el-table \r\n      v-loading=\"loading\" \r\n      :data=\"projectList\" \r\n      @row-click=\"handleRowClick\"\r\n      @row-dblclick=\"handleRowDoubleClick\"\r\n      highlight-current-row\r\n      style=\"cursor: pointer;\"\r\n    >\r\n      <el-table-column label=\"项目编号\" align=\"center\" prop=\"projectNo\" width=\"150\" />\r\n      <el-table-column label=\"项目名称\" align=\"center\" prop=\"projectName\" />\r\n      <el-table-column label=\"操作\" align=\"center\" width=\"100\">\r\n        <template slot-scope=\"scope\">\r\n          <el-button\r\n            size=\"mini\"\r\n            type=\"text\"\r\n            @click=\"handleSelect(scope.row)\"\r\n          >选择</el-button>\r\n        </template>\r\n      </el-table-column>\r\n    </el-table>\r\n\r\n    <!-- 分页 -->\r\n    <pagination\r\n      v-show=\"total>0\"\r\n      :total=\"total\"\r\n      :page.sync=\"queryParams.pageNum\"\r\n      :limit.sync=\"queryParams.pageSize\"\r\n      @pagination=\"getList\"\r\n    />\r\n\r\n    <!-- 底部按钮 -->\r\n    <div slot=\"footer\" class=\"dialog-footer\">\r\n      <el-button @click=\"handleClose\">取 消</el-button>\r\n    </div>\r\n  </el-dialog>\r\n</template>\r\n\r\n<script>\r\nimport { listProject } from \"@/api/suppPunishment/project\";\r\n\r\nexport default {\r\n  name: \"ProjectDialog\",\r\n  data() {\r\n    return {\r\n      // 遮罩层\r\n      loading: true,\r\n      // 弹窗显示状态\r\n      visible: false,\r\n      // 总条数\r\n      total: 0,\r\n      // 项目列表\r\n      projectList: [],\r\n      // 当前选中行\r\n      currentRow: null,\r\n      // 查询参数\r\n      queryParams: {\r\n        pageNum: 1,\r\n        pageSize: 10,\r\n        projectNo: null,\r\n        projectName: null\r\n      }\r\n    };\r\n  },\r\n  methods: {\r\n    /** 显示弹窗 */\r\n    show() {\r\n      this.visible = true;\r\n      this.resetQuery();\r\n      this.getList();\r\n    },\r\n    /** 关闭弹窗 */\r\n    handleClose() {\r\n      this.visible = false;\r\n      this.reset();\r\n    },\r\n    /** 重置数据 */\r\n    reset() {\r\n      this.projectList = [];\r\n      this.currentRow = null;\r\n      this.total = 0;\r\n      this.loading = false;\r\n    },\r\n    /** 查询项目列表 */\r\n    getList() {\r\n      this.loading = true;\r\n      listProject(this.queryParams).then(response => {\r\n        this.projectList = response.rows;\r\n        this.total = response.total;\r\n        this.loading = false;\r\n      }).catch(() => {\r\n        this.loading = false;\r\n      });\r\n    },\r\n    /** 搜索按钮操作 */\r\n    handleQuery() {\r\n      this.queryParams.pageNum = 1;\r\n      this.getList();\r\n    },\r\n    /** 重置按钮操作 */\r\n    resetQuery() {\r\n      this.queryParams = {\r\n        pageNum: 1,\r\n        pageSize: 10,\r\n        projectNo: null,\r\n        projectName: null\r\n      };\r\n      this.handleQuery();\r\n    },\r\n    /** 行点击事件 */\r\n    handleRowClick(row) {\r\n      this.currentRow = row;\r\n    },\r\n    /** 行双击事件 */\r\n    handleRowDoubleClick(row) {\r\n      this.handleSelect(row);\r\n    },\r\n    /** 选择项目 */\r\n    handleSelect(row) {\r\n      this.$emit('select', {\r\n        projectNo: row.projectNo,\r\n        projectName: row.projectName\r\n      });\r\n      this.handleClose();\r\n    }\r\n  }\r\n};\r\n</script>\r\n\r\n<style scoped>\r\n.dialog-footer {\r\n  text-align: center;\r\n}\r\n\r\n::v-deep .el-table tbody tr:hover > td {\r\n  background-color: #f5f7fa !important;\r\n}\r\n\r\n::v-deep .el-table tbody tr.current-row > td {\r\n  background-color: #ecf5ff !important;\r\n}\r\n</style>\r\n\r\n"]}]}