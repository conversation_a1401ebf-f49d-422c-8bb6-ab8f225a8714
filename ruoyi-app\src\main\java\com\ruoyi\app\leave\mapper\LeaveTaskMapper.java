package com.ruoyi.app.leave.mapper;

import java.util.List;

import com.ruoyi.app.leave.domain.DProcessTMeasure;
import com.ruoyi.app.leave.domain.LeaveTask;
import com.ruoyi.common.annotation.DataSource;
import com.ruoyi.common.enums.DataSourceType;

/**
 * 出门证任务Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-03-13
 */
public interface LeaveTaskMapper 
{
    /**
     * 根据计划编号查询任务列表
     * 
     * @param planNo 计划编号
     * @return 任务列表
     */
    public List<LeaveTask> selectByPlanNo(String planNo);

    /**
     * 查询出门证任务
     * 
     * @param id 出门证任务主键
     * @return 出门证任务
     */
    public LeaveTask selectLeaveTaskById(Long id);

    /**
     * 查询出门证任务列表
     * 
     * @param leaveTask 出门证任务
     * @return 出门证任务集合
     */
    public List<LeaveTask> selectLeaveTaskList(LeaveTask leaveTask);

    /**
     * 新增出门证任务
     * 
     * @param leaveTask 出门证任务
     * @return 结果
     */
    public int insertLeaveTask(LeaveTask leaveTask);

    /**
     * 修改出门证任务
     * 
     * @param leaveTask 出门证任务
     * @return 结果
     */
    public int updateLeaveTask(LeaveTask leaveTask);

    /**
     * 删除出门证任务
     * 
     * @param id 出门证任务主键
     * @return 结果
     */
    public int deleteLeaveTaskById(Long id);

    /**
     * 批量删除出门证任务
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteLeaveTaskByIds(Long[] ids);

    List<LeaveTask> isAllowDispatch(LeaveTask leaveTask);

    @DataSource(DataSourceType.MEASURE)
    List<DProcessTMeasure> selectProcessTypeListByValidFlag(DProcessTMeasure d_process_tMeasure);
}
