{"remainingRequest": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\src\\views\\dataReport\\answer\\answerShow.vue?vue&type=style&index=0&id=8df067d6&scoped=true&lang=scss", "dependencies": [{"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\src\\views\\dataReport\\answer\\answerShow.vue", "mtime": 1755499162045}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\@vue\\cli-service\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKDQovKiDorr7nva7mu5rliqjmnaHnmoTmoLflvI8gKi8NCjo6LXdlYmtpdC1zY3JvbGxiYXIgew0KICB3aWR0aDogMTBweDsNCiAgLyog56uW5ZCR5rua5Yqo5p2h5a695bqmICovDQogIGhlaWdodDogMTVweDsNCiAgLyog5qiq5ZCR5rua5Yqo5p2h5a695bqmICovDQogIGJhY2tncm91bmQtY29sb3I6ICNmZmZmZmY7DQp9DQoNCi8qIOa7muWKqOanvSAqLw0KOjotd2Via2l0LXNjcm9sbGJhci10cmFjayB7DQogIC8qIOWFtuWunuebtOaOpeWcqCAgOjotd2Via2l0LXNjcm9sbGJhciDkuK3orr7nva7kuZ/og73ovr7liLDlkIzmoLfnmoTop4bop4nmlYjmnpwqLw0KICAvKiAtd2Via2l0LWJveC1zaGFkb3c6IGluc2V0IDAgMCA2cHggcmdiYSgxNzcsIDIyMywgMTE3LCAwLjcpOyAqLw0KICBiYWNrZ3JvdW5kLWNvbG9yOiAjZTRlNGU0Ow0KICBib3JkZXItcmFkaXVzOiAxMHB4Ow0KfQ0KDQovKiDmu5rliqjmnaHmu5HlnZcgKi8NCjo6LXdlYmtpdC1zY3JvbGxiYXItdGh1bWIgew0KICBib3JkZXItcmFkaXVzOiA1cHg7DQogIC13ZWJraXQtYm94LXNoYWRvdzogaW5zZXQgMCAwIDZweCByZ2JhKDE1OCwgMTU2LCAxNTYsIDAuNjE2KTsNCn0NCg0KOjotd2Via2l0LXNjcm9sbGJhci10aHVtYjpob3ZlciB7DQogIGJhY2tncm91bmQ6IHJnYmEoMTM5LCAxMzgsIDEzOCwgMC42MTYpOw0KICAtd2Via2l0LWJveC1zaGFkb3c6IHVuc2V0Ow0KfQ0KDQo6Oi13ZWJraXQtc2Nyb2xsYmFyLXRodW1iOndpbmRvdy1pbmFjdGl2ZSB7DQogIC8qIOWuueWZqOS4jeiiq+a/gOa0u+aXtueahOagt+W8jyAqLw0KICBiYWNrZ3JvdW5kOiAjYmRiZGJkNjY7DQp9DQoNCjo6LXdlYmtpdC1zY3JvbGxiYXItY29ybmVyIHsNCiAgLyog5Lik5Liq5rua5Yqo5p2h5Lqk5rGH5aSE6L656KeS55qE5qC35byPICovDQogIGJhY2tncm91bmQtY29sb3I6ICNjYWNhY2E2NjsNCn0NCg0KLnN0aWNreSB7DQogIHBhZGRpbmc6IDIwcHg7DQogIHBvc2l0aW9uOiAtd2Via2l0LXN0aWNreTsNCiAgcG9zaXRpb246IHN0aWNreTsNCiAgdG9wOiAwOyAvKiDnspjmgKflrprkvY3nmoTotbflp4vkvY3nva4gKi8NCiAgei1pbmRleDogMTAwOyAvKiDnoa7kv53mjInpkq7lnKjljaHniYfkuYvkuIogKi8NCn0NCi5lbC10YWJzLS1jYXJkIHsNCiAgaGVpZ2h0OiBjYWxjKDEwMHZoIC0gMTEwcHgpOw0KfQ0KLmVsLXRhYi1wYW5lIHsNCiAgaGVpZ2h0OiBjYWxjKDEwMHZoIC0gMTEwcHgpOw0KICBvdmVyZmxvdy15OiBhdXRvOw0KfQ0K"}, {"version": 3, "sources": ["answerShow.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAmmCA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "answerShow.vue", "sourceRoot": "src/views/dataReport/answer", "sourcesContent": ["<template>\r\n  <div>\r\n    <el-form\r\n      :model=\"queryParams\"\r\n      ref=\"queryForm\"\r\n      :inline=\"true\"\r\n      v-show=\"showSearch\"\r\n    >\r\n      <el-row :gutter=\"20\" style=\"margin: 20px\">\r\n        <el-form-item label=\"报表名称\" prop=\"dimensionalityId\">\r\n          <el-select\r\n            v-model=\"queryParams.dimensionalityId\"\r\n            placeholder=\"请选择\"\r\n            filterable\r\n            @change=\"handleQuery\"\r\n          >\r\n            <el-option\r\n              v-for=\"item in rootList\"\r\n              :key=\"item.value\"\r\n              :label=\"item.label\"\r\n              :value=\"item.value\"\r\n            >\r\n            </el-option>\r\n          </el-select>\r\n        </el-form-item>\r\n        <el-form-item label=\"填报时间\" v-if=\"count == '1'\"  >\r\n          <el-date-picker\r\n            v-model=\"queryParams.fcDate\"\r\n            value-format=\"yyyy-MM-dd\"\r\n            type=\"date\"\r\n            :default-value=\"new Date()\"\r\n            :picker-options=\"pickerOptions\"\r\n            placeholder=\"选择时间\"\r\n            @change=\"handleDateChange\"\r\n          >\r\n          </el-date-picker>\r\n        </el-form-item>\r\n\r\n        <el-form-item label=\"填报时间\" v-if=\"count == '0'\">\r\n          <el-date-picker\r\n            v-model=\"queryParams.fcDate\"\r\n            value-format=\"yyyy-MM-01\"\r\n            type=\"month\"\r\n            :default-value=\"new Date()\"\r\n            :picker-options=\"pickerOptions\"\r\n            placeholder=\"选择时间\"\r\n            @change=\"handleDateChange\"\r\n          >\r\n          </el-date-picker>\r\n        </el-form-item>\r\n        <el-form-item label=\"填报问题\" prop=\"formQuestion\">\r\n          <el-input\r\n            v-model=\"queryParams.formQuestion\"\r\n            placeholder=\"请输入填报问题\"\r\n            clearable\r\n            size=\"small\"\r\n            @keyup.enter.native=\"handleQuery\"\r\n          />\r\n        </el-form-item>\r\n\r\n        <el-form-item>\r\n          <el-button\r\n            type=\"cyan\"\r\n            icon=\"el-icon-search\"\r\n            size=\"mini\"\r\n            @click=\"handleQuery\"\r\n            >搜索</el-button\r\n          >\r\n          <el-form-item>\r\n\r\n\r\n\r\n          </el-form-item>\r\n          <!-- <el-button\r\n            type=\"warning\"\r\n            icon=\"el-icon-download\"\r\n            size=\"mini\"\r\n            @click=\"importOpen = true\"\r\n            >数据导入导出</el-button\r\n          > -->\r\n        </el-form-item>\r\n      </el-row>\r\n      <el-row :gutter=\"10\" class=\"mb8\">\r\n      <el-button v-if=\"aloneList(dimensionalityName)  || containsSubstring('安全责任工资',dimensionalityName) \"\r\n            type=\"danger\"\r\n            icon=\"el-icon-download\"\r\n            size=\"mini\"\r\n            @click=\"SpecialImportOpen = true\"\r\n            >单周期报表导出</el-button\r\n          >\r\n\r\n          <el-button v-if=\"aloneList(dimensionalityName) || containsSubstring('安全责任工资',dimensionalityName) \"\r\n            type=\"warning\"\r\n            icon=\"el-icon-search\"\r\n            size=\"mini\"\r\n            @click=\"handlePreview\"\r\n            >数据预览</el-button\r\n          >\r\n          <el-button v-if=\"queryParams.dimensionalityId == 456 \"\r\n            type=\"danger\"\r\n            icon=\"el-icon-download\"\r\n            size=\"mini\"\r\n            @click=\"connectOpen = true\"\r\n            >格式化报表导出</el-button\r\n          >\r\n          <el-button v-if=\"queryParams.dimensionalityId == 456 \"\r\n            type=\"warning\"\r\n            icon=\"el-icon-search\"\r\n            size=\"mini\"\r\n            @click=\"preViewOpen = true\"\r\n            >格式化数据预览</el-button\r\n          >\r\n          <!-- <el-button \r\n            type=\"danger\"\r\n            icon=\"el-icon-download\"\r\n            size=\"mini\"\r\n            @click=\"SpecialImportOpen = true\"\r\n            >报表导出测试</el-button\r\n          > -->\r\n      </el-row>\r\n      <el-row :gutter=\"10\" class=\"mb8\">\r\n        <!-- <el-col :span=\"1.5\">管理部门：{{deptName}}\r\n        </el-col> -->\r\n        <right-toolbar\r\n          :showSearch.sync=\"showSearch\"\r\n          @queryTable=\"getList\"\r\n        ></right-toolbar>\r\n      </el-row>\r\n    </el-form>\r\n\r\n    <vxe-form\r\n      ref=\"formRef\"\r\n      :data=\"formData\"\r\n      @submit=\"handleSubmit\"\r\n      border\r\n      title-background\r\n      vertical-align=\"center\"\r\n      title-width=\"300\"\r\n      title-bold\r\n    >\r\n      <vxe-form-group\r\n        v-for=\"(group, index) in answerList\"\r\n        :key=\"index\"\r\n        span=\"24\"\r\n        :title=\"group.title\"\r\n        title-bold\r\n        vertical\r\n      >\r\n        <vxe-form-item\r\n          v-for=\"(question, qIndex) in group.list\"\r\n          :key=\"qIndex\"\r\n          :title=\"question.formQuestion\"\r\n          :field=\"answerList[index].list[qIndex].formValue\"\r\n          :span=\"question.formType == '3' ? 24 : 12\"\r\n          :item-render=\"{}\"\r\n        >\r\n          <template #default=\"params\">\r\n            <vxe-tag\r\n              v-if=\"question.status == '0'\"\r\n              status=\"primary\"\r\n              content=\"主要颜色\"\r\n              >待审核</vxe-tag\r\n            >\r\n            <vxe-tag\r\n              v-if=\"question.status == '1'\"\r\n              status=\"warning\"\r\n              content=\"信息颜色\"\r\n              >审核中</vxe-tag\r\n            >\r\n            <vxe-tag\r\n              v-if=\"question.status == '2'\"\r\n              status=\"success\"\r\n              content=\"信息颜色\"\r\n              >审核完成</vxe-tag\r\n            >\r\n            <vxe-tag\r\n              v-if=\"question.status == '3'\"\r\n              status=\"danger\"\r\n              content=\"警告颜色\"\r\n              >驳回理由: {{ question.assessment }}</vxe-tag\r\n            >\r\n\r\n            <vxe-input\r\n              v-if=\"question.formType == '0'\"\r\n              v-model=\"answerList[index].list[qIndex].formValue\"\r\n              @change=\"changeEvent(params)\"\r\n              type=\"integer\"\r\n            ></vxe-input>\r\n            <vxe-input\r\n              v-if=\"question.formType == '1'\"\r\n              v-model=\"answerList[index].list[qIndex].formValue\"\r\n              @change=\"changeEvent(params)\"\r\n              type=\"'float'\"\r\n            ></vxe-input>\r\n            <vxe-input\r\n              v-if=\"question.formType == '2'\"\r\n              v-model=\"answerList[index].list[qIndex].formValue\"\r\n              @change=\"changeEvent(params)\"\r\n              type=\"text\"\r\n            ></vxe-input>\r\n            <vxe-textarea\r\n              v-if=\"question.formType == '3'\"\r\n              v-model=\"answerList[index].list[qIndex].formValue\"\r\n              :placeholder=\"question.formQuestion\"\r\n            ></vxe-textarea>\r\n            <vxe-text v-if=\"question.formNote != null\" status=\"warning\"\r\n              >问题指标:{{ question.formNote }}<br\r\n            /></vxe-text>\r\n            <vxe-text v-if=\"question.formNote1 != null\" status=\"warning\"\r\n              >问题备注:{{ question.formNote1 }}<br\r\n            /></vxe-text>\r\n            <vxe-text v-if=\"question.maximum != null\" status=\"primary\"\r\n              >最大值:{{ question.maximum }}<br\r\n            /></vxe-text>\r\n            <vxe-text v-if=\"question.minimum != null\" status=\"primary\"\r\n              >最小值:{{ question.minimum }}<br\r\n            /></vxe-text>\r\n            <vxe-text v-if=\"question.formUnit != null\" status=\"primary\"\r\n              >问题单位:{{ question.formUnit }}<br\r\n            /></vxe-text>\r\n            <vxe-tag\r\n              v-if=\"\r\n                question.formValue != null &&\r\n                question.formValue != '' &&\r\n                (question.formType == '0' || question.formType == '1') &&\r\n                ((question.minimum != null &&\r\n                  question.formValue < question.minimum) ||\r\n                  (question.maximum != null &&\r\n                    question.formValue > question.maximum))\r\n              \"\r\n              status=\"danger\"\r\n              content=\"警告颜色\"\r\n              >输入值超出预计范围，请输入原因和改进措施</vxe-tag\r\n            >\r\n            <vxe-textarea\r\n              v-if=\"\r\n                question.formValue != null &&\r\n                question.formValue != '' &&\r\n                (question.formType == '0' || question.formType == '1') &&\r\n                ((question.minimum != null &&\r\n                  question.formValue < question.minimum) ||\r\n                  (question.maximum != null &&\r\n                    question.formValue > question.maximum))\r\n              \"\r\n              v-model=\"answerList[index].list[qIndex].reason\"\r\n              @change=\"changeEvent(params)\"\r\n              placeholder=\"请填写原因\"\r\n            ></vxe-textarea>\r\n            <vxe-textarea\r\n              v-if=\"\r\n                question.formValue != null &&\r\n                question.formValue != '' &&\r\n                (question.formType == '0' || question.formType == '1') &&\r\n                ((question.minimum != null &&\r\n                  question.formValue < question.minimum) ||\r\n                  (question.maximum != null &&\r\n                    question.formValue > question.maximum))\r\n              \"\r\n              v-model=\"answerList[index].list[qIndex].measure\"\r\n              @change=\"changeEvent(params)\"\r\n              placeholder=\"请输入改进措施\"\r\n            ></vxe-textarea>\r\n          </template>\r\n        </vxe-form-item>\r\n      </vxe-form-group>\r\n\r\n      <!-- <vxe-form-item align=\"center\" span=\"24\" :item-render=\"{}\">\r\n        <template #default>\r\n          <vxe-button\r\n            type=\"submit\"\r\n            status=\"primary\"\r\n            content=\"提交\"\r\n          ></vxe-button>\r\n        </template>\r\n      </vxe-form-item> -->\r\n    </vxe-form>\r\n\r\n    <el-dialog\r\n      title=\"选择导出范围\"\r\n      :visible.sync=\"importOpen\"\r\n      width=\"400px\"\r\n      append-to-body\r\n      destroy-on-close\r\n    >\r\n      <span>数据日期范围：</span>\r\n      <el-date-picker\r\n        v-model=\"dateValue\"\r\n        type=\"daterange\"\r\n        range-separator=\"至\"\r\n        start-placeholder=\"开始日期\"\r\n        end-placeholder=\"结束日期\"\r\n        value-format=\"yyyy-MM-dd\"\r\n        @change=\"onDateChange\"\r\n      >\r\n      </el-date-picker>\r\n      <el-row :gutter=\"10\" style=\"margin-top: 10px;\">\r\n       \r\n        <el-col :span=\"1.5\">\r\n          <el-button\r\n            size=\"small\"\r\n            type=\"info\"\r\n            plain\r\n            icon=\"el-icon-link\"\r\n            @click=\"downloadTemplate\"\r\n            >数据下载</el-button\r\n          >\r\n        </el-col>\r\n         <!-- <el-col :span=\"1.5\" >\r\n          <el-upload\r\n            accept=\".xlsx, .xls\"\r\n            :headers=\"upload.headers\"\r\n            :disabled=\"upload.isUploading\"\r\n            :action=\"upload.url\"\r\n            :show-file-list=\"false\"\r\n            :multiple=\"false\"\r\n            :on-progress=\"handleFileUploadProgress\"\r\n            :on-success=\"handleFileSuccess\"\r\n          >\r\n            <el-button size=\"small\" type=\"warning\" plain icon=\"el-icon-download\"\r\n              >表格导入</el-button\r\n            >\r\n          </el-upload>\r\n        </el-col> -->\r\n      </el-row>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"importOpen = false\">取 消</el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <el-dialog\r\n      title=\"单周期报表导出\"\r\n      :visible.sync=\"SpecialImportOpen\"\r\n      width=\"400px\"\r\n      append-to-body\r\n      destroy-on-close\r\n    >\r\n      <span>选择导出时间：</span>\r\n      <el-date-picker\r\n            v-model=\"specialFcDate\"\r\n            value-format=\"yyyy-MM-dd\"\r\n            type=\"date\"\r\n            :default-value=\"new Date()\"\r\n            placeholder=\"选择时间\"\r\n          >\r\n      </el-date-picker>\r\n      <el-row :gutter=\"10\" style=\"margin-top: 10px;\">\r\n       \r\n        <el-col :span=\"1.5\">\r\n          <el-button\r\n            size=\"small\"\r\n            type=\"info\"\r\n            plain\r\n            icon=\"el-icon-link\"\r\n            @click=\"downloadTemplateSpecial\"\r\n            >数据下载</el-button\r\n          >\r\n        </el-col>\r\n         <!-- <el-col :span=\"1.5\" >\r\n          <el-upload\r\n            accept=\".xlsx, .xls\"\r\n            :headers=\"uploadSpecial.headers\"\r\n            :disabled=\"uploadSpecial.isUploading\"\r\n            :action=\"uploadSpecial.url\"\r\n            :show-file-list=\"false\"\r\n            :multiple=\"false\"\r\n            :on-progress=\"handleFileUploadProgress\"\r\n            :on-success=\"handleFileSuccess\"\r\n          >\r\n            <el-button size=\"small\" type=\"warning\" plain icon=\"el-icon-download\"\r\n              >表格导入</el-button\r\n            >\r\n          </el-upload>\r\n        </el-col> -->\r\n      </el-row>\r\n      <!-- <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"SpecialImportOpen = false\">取 消</el-button>\r\n      </div> -->\r\n    </el-dialog>\r\n\r\n    <el-dialog\r\n      title=\"选择导出范围\"\r\n      :visible.sync=\"connectOpen\"\r\n      width=\"400px\"\r\n      append-to-body\r\n      destroy-on-close\r\n    >\r\n      <span>数据日期范围：</span>\r\n      <el-date-picker\r\n        v-model=\"dateValue\"\r\n        type=\"daterange\"\r\n        range-separator=\"至\"\r\n        start-placeholder=\"开始日期\"\r\n        end-placeholder=\"结束日期\"\r\n        value-format=\"yyyy-MM-dd\"\r\n        @change=\"onDateChange\"\r\n      >\r\n      </el-date-picker>\r\n      <el-row :gutter=\"10\" style=\"margin-top: 10px;\">\r\n       \r\n        <el-col :span=\"1.5\">\r\n          <el-button\r\n            size=\"small\"\r\n            type=\"info\"\r\n            plain\r\n            icon=\"el-icon-link\"\r\n            @click=\"downloadConnectTemplate\"\r\n            >数据下载</el-button\r\n          >\r\n        </el-col>\r\n      </el-row>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"importOpen = false\">取 消</el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <el-dialog\r\n      title=\"选择预览时间范围\"\r\n      :visible.sync=\"preViewOpen\"\r\n      width=\"400px\"\r\n      append-to-body\r\n      destroy-on-close\r\n    >\r\n      <span>数据日期范围：</span>\r\n      <el-date-picker\r\n        v-model=\"dateValue\"\r\n        type=\"daterange\"\r\n        range-separator=\"至\"\r\n        start-placeholder=\"开始日期\"\r\n        end-placeholder=\"结束日期\"\r\n        value-format=\"yyyy-MM-dd\"\r\n        @change=\"onDateChange\"\r\n      >\r\n      </el-date-picker>\r\n      <el-row :gutter=\"10\" style=\"margin-top: 10px;\">\r\n       \r\n        <el-col :span=\"1.5\">\r\n          <el-button\r\n            size=\"small\"\r\n            type=\"info\"\r\n            plain\r\n            icon=\"el-icon-link\"\r\n            @click=\"handlePreview1\"\r\n            >数据预览</el-button\r\n          >\r\n        </el-col>\r\n      </el-row>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"importOpen = false\">取 消</el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <el-dialog  title=\"数据预览\"  :visible.sync=\"searchopen\" width=\"1800px\" >\r\n        <div class=\"test\">\r\n          <vue-office-excel\r\n              :src=\"customBlobContent\"\r\n              style=\"height: 100vh;\"\r\n          />\r\n        </div>\r\n    </el-dialog>\r\n\r\n  </div>\r\n</template>\r\n  \r\n  <script>\r\nimport { answerstatuslistAdmin ,formFrequency} from \"@/api/tYjy/form\";\r\nimport answerInput from \"./input\";\r\nimport { newAdd, addAlone } from \"@/api/tYjy/answer\";\r\nimport { getAllRootListForAnswer,getAllRootList } from \"@/api/tYjy/dimensionality\";\r\nimport { getToken } from \"@/utils/auth\";\r\nimport axios from \"axios\";\r\nimport * as xlsx from 'xlsx';\r\nexport default {\r\n  name: \"Answer\",\r\n  components: { answerInput },\r\n  data() {\r\n    return {\r\n      pickerOptions: {\r\n        disabledDate(time) {\r\n          return time.getTime() > Date.now();\r\n        },\r\n      },\r\n      frequencyOptions: [],\r\n\r\n      queryParams: {\r\n        formQuestion: undefined,\r\n        fcDate: undefined,\r\n        dimensionalityId: undefined,\r\n        formQuestion: undefined,\r\n      },\r\n\r\n      formType: null,\r\n      dimensionalityNames: null,\r\n      dimensionalityName: null,\r\n      specialFcDate:null,\r\n      drawerShow: false,\r\n      stickyTop: 0,\r\n      loading: false,\r\n      showSearch: true,\r\n      answerList: [],\r\n      formData: {},\r\n      row: {},\r\n      rootList: [],\r\n      userInfo: {},\r\n      datesave:{},\r\n      pathsave:{},\r\n      deptName:null,\r\n      dateValue: null,\r\n      queryImport: {\r\n        startDate: null,\r\n        endDate: null,\r\n        rootId: null,\r\n      },\r\n      count:1,\r\n      importOpen:false,\r\n      SpecialImportOpen:false,\r\n      connectOpen:false,\r\n      preViewOpen:false,\r\n      // 导入参数\r\n      upload: {\r\n        // 是否禁用上传\r\n        isUploading: false,\r\n        // 设置上传的请求头部\r\n        headers: { Authorization: \"Bearer \" + getToken() },\r\n        // 上传的地址\r\n        url:\r\n          process.env.VUE_APP_BASE_API +\r\n          \"/web/TYjy/answer/importData\",\r\n\r\n      },\r\n\r\n      uploadSpecial: {\r\n        // 是否禁用上传\r\n        isUploading: false,\r\n        // 设置上传的请求头部\r\n        headers: { Authorization: \"Bearer \" + getToken() },\r\n        // 上传的地址\r\n        url:\r\n          process.env.VUE_APP_BASE_API +\r\n          \"/web/TYjy/answer/importDataSpecial\",\r\n\r\n      },\r\n      excelHtml:\"\",\r\n      searchopen:false,\r\n      excelData: [], // 存储 Excel 数据\r\n      exceltitle: [],\r\n      customBlobContent:\"\"\r\n    };\r\n  },\r\n  mounted() {\r\n    this.userInfo = JSON.parse(JSON.stringify(this.$store.state.user));\r\n  },\r\n\r\n  created() {\r\n    const dimensionalityId = this.$route.query && this.$route.query.dimensionalityId;\r\n    const fcDate = this.$route.query && this.$route.query.fcDate;\r\n\r\n    this.dimensionalityName=this.$route.query && this.$route.query.dimensionalityName;\r\n    this.queryParams.dimensionalityId=dimensionalityId\r\n    this.queryParams.fcDate=fcDate\r\n    this.initData();\r\n\r\n\r\n    // if(this.$route.query)\r\n    // {\r\n    //   const dimensionalityId = this.$route.query && this.$route.query.dimensionalityId;\r\n    //   // const dimensionalityName = this.$route.query && this.$route.query.dimensionalityName;\r\n    //   const fcDate = this.$route.query && this.$route.query.fcDate;\r\n    //   this.queryParams.dimensionalityId=dimensionalityId\r\n    //   // this.queryParams.dimensionalityName=dimensionalityName\r\n    //   this.queryParams.fcDate=fcDate\r\n    //   this.initData1();\r\n    // }\r\n    // else\r\n    // {\r\n    //   this.initData();\r\n    // }\r\n  },\r\n  methods: {\r\n    onDateChange() {\r\n      console.log(this.dateValue);\r\n      if (this.dateValue != null && this.dateValue != \"\") {\r\n        this.queryImport.startDate = this.dateValue[0];\r\n        this.queryImport.endDate = this.dateValue[1];\r\n      } else {\r\n        this.queryImport.startDate = \"\";\r\n        this.queryImport.endDate = \"\";\r\n      }\r\n    },\r\n    clickNode($event, node) {\r\n      $event.target.parentElement.parentElement.firstElementChild.click();\r\n    },\r\n    changeEvent(params) {\r\n      const $form = this.$refs.formRef;\r\n      if ($form) {\r\n        $form.updateStatus(params);\r\n      }\r\n    },\r\n    disabledDate(time) {\r\n      return time.getTime() < Date.now() - 8.64e7; // 8.64e7 毫秒数代表一天\r\n    },\r\n    inputChange(val, row) {\r\n      row.formValue = val;\r\n    },\r\n    handleScroll() {\r\n      this.isSticky = window.scrollY >= this.stickyTop;\r\n    },\r\n    initData() {\r\n      // getAllRootListForAnswer().then((res) => {\r\n      //   this.rootList = res.data;\r\n      //   console.log(\"angry\",this.queryParams)\r\n        // if(this.queryParams.dimensionalityId==null)\r\n        // {\r\n        //   // this.queryParams.dimensionalityId = this.rootList[0].value;\r\n        //   // this.deptName= this.rootList[0].deptName;\r\n        //   // this.deptCode= this.rootList[0].deptCode;\r\n        // }\r\n        // else\r\n        // {\r\n        //   // this.queryParams.dimensionalityId = this.queryParams.dimensionalityId;\r\n        //   for(let i=0;i<this.rootList.length;i++)\r\n        //   {\r\n        //     if(this.queryParams.dimensionalityId == this.rootList[i].value)\r\n        //     {\r\n        //       this.queryParams.dimensionalityId = this.rootList[i].value;\r\n        //       this.deptName= this.rootList[i].deptName;\r\n        //       this.deptCode= this.rootList[i].deptCode;\r\n        //     }\r\n        //   }\r\n        // }\r\n      //   this.getList();\r\n      // });\r\n      getAllRootList().then((res) => {\r\n        this.rootList = res.data;\r\n        if(this.queryParams.dimensionalityId==null)\r\n        {\r\n          // this.queryParams.dimensionalityId = this.rootList[0].value;\r\n          // this.deptName= this.rootList[0].deptName;\r\n          // this.deptCode= this.rootList[0].deptCode;\r\n        }\r\n        else\r\n        {\r\n          // this.queryParams.dimensionalityId = this.queryParams.dimensionalityId;\r\n          for(let i=0;i<this.rootList.length;i++)\r\n          {\r\n            if(this.queryParams.dimensionalityId == this.rootList[i].value)\r\n            {\r\n              this.queryParams.dimensionalityId = this.rootList[i].value;\r\n              this.deptName= this.rootList[i].deptName;\r\n              this.deptCode= this.rootList[i].deptCode;\r\n            }\r\n          }\r\n        }\r\n        this.getList();\r\n      });\r\n    },\r\n    initData1() {\r\n      getAllRootListForAnswer().then((res) => {\r\n        this.rootList = res.data;\r\n        for(let i=0;i<this.rootList.length;i++)\r\n        {\r\n          if(this.queryParams.dimensionalityId == this.rootList[i].value)\r\n          {\r\n            this.deptName= this.rootList[0].deptName;\r\n          }\r\n        }\r\n        this.getList();\r\n      });\r\n    },\r\n\r\n    /** 查询TYjyAnswer列表 */\r\n    getList() {\r\n      formFrequency({dimensionalityId: this.queryParams.dimensionalityId}).then((res) => {\r\n           if(this.count!=res.data)\r\n           {\r\n            this.queryParams.fcDate=undefined\r\n           }\r\n           this.count=res.data\r\n      });\r\n      this.answerList = [];\r\n      answerstatuslistAdmin({\r\n        fcDate: this.queryParams.fcDate,\r\n        dimensionalityId: this.queryParams.dimensionalityId,\r\n        formQuestion: this.queryParams.formQuestion,\r\n      }).then((res) => {\r\n        let answerList = [];\r\n        let list = res.data;\r\n        for(let i=0;i<list.length;i++)\r\n        {\r\n            this.datesave[list[i].formId]=list[i].formValue\r\n            this.pathsave[list[i].dimensionalityName]=list[i].dimensionalityPath\r\n        }\r\n        // 使用 map 提取 dimensionalityName 属性到一个数组\r\n        let dimensionalityPaths = []\r\n        let Pathset=[]\r\n        list.forEach((x) => \r\n          {\r\n            if(!Pathset.includes(x.dimensionalityPath))\r\n            {\r\n              Pathset.push(x.dimensionalityPath)\r\n              dimensionalityPaths.push({ originalName: x.dimensionalityName, sortKey: x.dimensionalityPath})\r\n            }   \r\n          }\r\n        );\r\n        dimensionalityPaths.forEach((title) => {\r\n          let group = {\r\n            title: \"\",\r\n            list: [],\r\n          };\r\n          group.title = title.originalName;\r\n          group.list = list.filter((item) => item.dimensionalityPath === title.sortKey);\r\n          // 假设你有一个数组来存储所有的组\r\n          answerList.push(group); // 将生成的组添加到groups数组中\r\n        });\r\n        this.answerList = answerList;\r\n        console.log(\"angry\",this.answerList);\r\n\r\n\r\n\r\n        // // 使用 map 提取 dimensionalityName 属性到一个数组\r\n        // let dimensionalityNames = list.map((x) => x.dimensionalityName);\r\n\r\n        // // 提取 / 后的前三位字符，并与原字符串配对\r\n        // dimensionalityNames = dimensionalityNames.map((name) => {\r\n        //   // let key = name.includes(\"/\") ? name.split(\"/\")[1].slice(0, 3) : \"\";\r\n        //   let key = this.pathsave[name];\r\n        //   return { originalName: name, sortKey: key };\r\n        // });\r\n\r\n        // // 按照提取出的前三字符排序\r\n        // dimensionalityNames.sort((a, b) => a.sortKey.localeCompare(b.sortKey));\r\n        // // console.log(\"test0\",dimensionalityNames)\r\n        // // 如果需要，可以提取排序后的原始名字\r\n        // dimensionalityNames = dimensionalityNames.map(\r\n        //   (item) => item.originalName\r\n        // );\r\n\r\n        // // 使用 Set 去重\r\n        // let uniqueDimensionalityNames = [...new Set(dimensionalityNames)];\r\n\r\n        // uniqueDimensionalityNames.forEach((title) => {\r\n        //   let group = {\r\n        //     title: \"\",\r\n        //     list: [],\r\n        //   };\r\n        //   group.title = title;\r\n        //   group.list = list.filter((item) => item.dimensionalityName === title);\r\n        //   // 假设你有一个数组来存储所有的组\r\n        //   answerList.push(group); // 将生成的组添加到groups数组中\r\n        // });\r\n        // this.answerList = answerList;\r\n        // console.log(\"angry\",this.answerList);\r\n        this.$forceUpdate();\r\n      });\r\n    },\r\n    handleQuery() {\r\n      for(let i=0;i<this.rootList.length;i++)\r\n      {\r\n        if(this.queryParams.dimensionalityId == this.rootList[i].value)\r\n        {\r\n          this.deptName= this.rootList[0].deptName;\r\n          this.deptCode= this.rootList[i].deptCode;\r\n          this.dimensionalityName=this.rootList[i].label\r\n        }\r\n      }\r\n      this.getList();\r\n    },\r\n    handleDateChange() {\r\n      this.getList();\r\n    },\r\n    resetQuery() {\r\n      this.queryParams.fcDate = undefined;\r\n      this.queryParams.formQuestion = undefined;\r\n      this.queryParams.dimensionalityId = undefined;\r\n      this.getList();\r\n    },\r\n\r\n    handlePreview() {\r\n      let queryImport={}\r\n      queryImport.rootId = this.queryParams.dimensionalityId\r\n      queryImport.fcDate = this.queryParams.fcDate\r\n      queryImport.type=\"1\"\r\n      if(this.dimensionalityName=='研究院目标指标一览')\r\n      {\r\n          this.downloadXlsx(\r\n          \"/web/TYjy/answer/exportWithTemplate\",\r\n          {\r\n            ...queryImport,\r\n          },\r\n          this.dimensionalityName+\"(\" +this.specialFcDate+\r\n            \")\" +\r\n            `数据.xlsx`\r\n        ).then((blob) => {\r\n          let reader = new FileReader();\r\n          reader.readAsArrayBuffer(blob);\r\n          \r\n          reader.onload = (evt) => {\r\n            this.customBlobContent=reader.result;\r\n            let ints = new Uint8Array(evt.target.result); //要使用读取的内容，所以将读取内容转化成Uint8Array\r\n            ints = ints.slice(0, blob.size);\r\n            let workBook = xlsx.read(ints, { type: \"array\" });\r\n            let sheetNames = workBook.SheetNames;\r\n            let sheetName = sheetNames[0];\r\n            let workSheet = workBook.Sheets[sheetName];\r\n            //获取Excle内容，并将空内容用空值保存\r\n            let excelTable = xlsx.utils.sheet_to_json(workSheet);\r\n            // 获取Excel头部\r\n            let tableThead = Array.from(Object.keys(excelTable[0])).map(\r\n              (item) => {\r\n                return item\r\n              }\r\n            );\r\n            this.excelData = excelTable;\r\n            this.exceltitle=tableThead\r\n            this.excelHtml= excelTable\r\n            this.searchopen = true;\r\n          }\r\n        });\r\n      }\r\n      else\r\n      {\r\n        this.downloadXlsx(\r\n        \"/web/TYjy/answer/exportTemplateSpecial\",\r\n        {\r\n          ...queryImport,\r\n        },\r\n        this.dimensionalityName+\"(\" +this.specialFcDate+\r\n          \")\" +\r\n          `数据.xlsx`\r\n      ).then((blob) => {\r\n        let reader = new FileReader();\r\n        reader.readAsArrayBuffer(blob);\r\n\r\n        reader.onload = (evt) => {\r\n          this.customBlobContent=reader.result;\r\n          let ints = new Uint8Array(evt.target.result); //要使用读取的内容，所以将读取内容转化成Uint8Array\r\n          ints = ints.slice(0, blob.size);\r\n          let workBook = xlsx.read(ints, { type: \"array\" });\r\n          let sheetNames = workBook.SheetNames;\r\n          let sheetName = sheetNames[0];\r\n          let workSheet = workBook.Sheets[sheetName];\r\n          //获取Excle内容，并将空内容用空值保存\r\n          let excelTable = xlsx.utils.sheet_to_json(workSheet);\r\n          // 获取Excel头部\r\n          let tableThead = Array.from(Object.keys(excelTable[0])).map(\r\n            (item) => {\r\n              return item\r\n            }\r\n          );\r\n          this.excelData = excelTable;\r\n          this.exceltitle=tableThead\r\n          this.excelHtml= excelTable\r\n          this.searchopen = true;\r\n        }\r\n      });\r\n      }\r\n    },\r\n\r\n    handlePreview1() {\r\n      // let queryImport={}\r\n      // queryImport.rootId = this.queryParams.dimensionalityId\r\n      // queryImport.fcDate = this.queryParams.fcDate\r\n      // queryImport.type=\"1\"\r\n        if (\r\n        this.queryImport.startDate == null ||\r\n        this.queryImport.startDate == \"\"||\r\n        this.queryImport.endDate == null||\r\n        this.queryImport.endDate == \"\"\r\n      ) {\r\n        this.$notify.error({\r\n          title: \"错误\",\r\n          message: \"导出前请先输入开始结束时间\",\r\n        });\r\n        return;\r\n      }\r\n      this.queryImport.rootId = this.queryParams.dimensionalityId\r\n      this.queryImport.type=\"1\"\r\n      this.downloadXlsx(\r\n        \"/web/TYjy/answer/exportTemplateNomral\",\r\n        {\r\n          ...this.queryImport,\r\n        },\r\n        this.dimensionalityName+\"(\" +this.specialFcDate+\r\n          \")\" +\r\n          `数据.xlsx`\r\n      ).then((blob) => {\r\n        let reader = new FileReader();\r\n        reader.readAsArrayBuffer(blob);\r\n        \r\n        reader.onload = (evt) => {\r\n          this.customBlobContent=reader.result;\r\n          let ints = new Uint8Array(evt.target.result); //要使用读取的内容，所以将读取内容转化成Uint8Array\r\n          ints = ints.slice(0, blob.size);\r\n          let workBook = xlsx.read(ints, { type: \"array\" });\r\n          let sheetNames = workBook.SheetNames;\r\n          let sheetName = sheetNames[0];\r\n          let workSheet = workBook.Sheets[sheetName];\r\n          //获取Excle内容，并将空内容用空值保存\r\n          let excelTable = xlsx.utils.sheet_to_json(workSheet);\r\n          // 获取Excel头部\r\n          let tableThead = Array.from(Object.keys(excelTable[0])).map(\r\n            (item) => {\r\n              return item\r\n            }\r\n          );\r\n          this.excelData = excelTable;\r\n          this.exceltitle=tableThead\r\n          this.excelHtml= excelTable\r\n          this.searchopen = true;\r\n        }\r\n      });\r\n    },\r\n\r\n    handleUpload({ file }) {\r\n      const formData = new FormData();\r\n      formData.append(\"file\", file);\r\n      return axios\r\n        .post(\r\n          process.env.VUE_APP_BASE_API + \"/common/uploadMinioDataReport\",\r\n          formData\r\n        )\r\n        .then((res) => {\r\n          return {\r\n            ...res.data,\r\n          };\r\n        });\r\n    },\r\n    /** 提交按钮 */\r\n    handleSubmit() {\r\n      // 首先对 answerList 进行处理：合并、过滤和转换\r\n      let processedLists = this.answerList\r\n        .reduce((acc, current) => {\r\n          return acc.concat(current.list);\r\n        }, [])\r\n        .filter((x) => {\r\n          // 过滤条件\r\n          return (\r\n            x.formValue != null &&\r\n            x.formValue != \"\" &&\r\n            ((![\"0\", \"1\"].includes(x.status))&&\r\n            (\r\n              ([\"2\", \"3\"].includes(x.status) && this.datesave[x.formId]!=x.formValue))\r\n              ||([\"4\"].includes(x.status))\r\n            )\r\n          );\r\n        });\r\n\r\n      // 对符合条件的元素进行 formValue 的转换\r\n      processedLists.forEach((x) => {\r\n        if ([\"0\", \"1\"].includes(x.formType)) {\r\n          x.formValue = parseFloat(x.formValue);\r\n        }\r\n        x.fcDate = this.queryParams.fcDate;\r\n      });\r\n\r\n      // 最后进行深拷贝\r\n      let allLists = JSON.parse(JSON.stringify(processedLists));\r\n\r\n      console.log(\"allLists:\", allLists);\r\n      newAdd(allLists).then((res) => {\r\n        this.getList();\r\n        this.msgSuccess(\"保存成功\");\r\n      });\r\n    },\r\n    \r\n    handleFileUploadProgress() {\r\n      this.upload.isUploading = true;\r\n    },\r\n    handleFileSuccess(response) {\r\n      console.log(response)\r\n      if (response.code == 200) {\r\n        this.$modal.msgSuccess(\"上传成功\");\r\n        this.getList();\r\n        this.importOpen = false;\r\n        this.SpecialImportOpen = false;\r\n      }\r\n      else {\r\n        this.$modal.msgError(\"上传失败\")\r\n      }\r\n      this.upload.isUploading = false;\r\n    },\r\n    // 模板下载\r\n    downloadTemplate(){\r\n    \r\n      if (\r\n        this.queryImport.startDate == null ||\r\n        this.queryImport.startDate == \"\"||\r\n        this.queryImport.endDate == null||\r\n        this.queryImport.endDate == \"\"\r\n      ) {\r\n        this.$notify.error({\r\n          title: \"错误\",\r\n          message: \"导出前请先输入开始结束时间\",\r\n        });\r\n        return;\r\n      }\r\n      this.queryImport.rootId = this.queryParams.dimensionalityId\r\n      this.downloadFile(\r\n        \"/web/TYjy/answer/exportTemplate\",\r\n        {\r\n          ...this.queryImport,\r\n        },\r\n        \"(\" +\r\n          this.queryImport.startDate +\r\n          \"-\" +\r\n          this.queryImport.endDate +\r\n          \")\" +\r\n          `数据.xlsx`\r\n      );\r\n    \r\n    },\r\n    downloadTemplateSpecial(){\r\n      if (this.specialFcDate == null ) {\r\n        this.specialFcDate= this.queryParams.fcDate\r\n      }\r\n\r\n      // if (this.specialFcDate == null ) {\r\n      //   this.$notify.error({\r\n      //     title: \"错误\",\r\n      //     message: \"未选择时间\",\r\n      //   });\r\n      //   return;\r\n      // }\r\n      if(this.dimensionalityName=='研究院目标指标一览')\r\n      {\r\n        let queryImport={}\r\n        queryImport.rootId = this.queryParams.dimensionalityId\r\n        queryImport.fcDate = this.specialFcDate\r\n        queryImport.type=\"1\"\r\n        this.downloadFile(\r\n          \"/web/TYjy/answer/exportWithTemplate\",\r\n          {\r\n            ...queryImport,\r\n          },\r\n          this.dimensionalityName+\"(\" +this.specialFcDate+\r\n            \")\" +\r\n            `数据.xlsx`\r\n        );\r\n      }\r\n      else\r\n      {\r\n        let queryImport={}\r\n        queryImport.rootId = this.queryParams.dimensionalityId\r\n        queryImport.fcDate = this.specialFcDate\r\n        queryImport.type=\"1\"\r\n        this.downloadFile(\r\n          \"/web/TYjy/answer/exportTemplateSpecial\",\r\n          {\r\n            ...queryImport,\r\n          },\r\n          this.dimensionalityName+\"(\" +this.specialFcDate+\r\n            \")\" +\r\n            `数据.xlsx`\r\n        );\r\n      }\r\n\r\n    },\r\n        // 模板下载\r\n    downloadConnectTemplate(){\r\n    if (\r\n      this.queryImport.startDate == null ||\r\n      this.queryImport.startDate == \"\"||\r\n      this.queryImport.endDate == null||\r\n      this.queryImport.endDate == \"\"\r\n    ) {\r\n      this.$notify.error({\r\n        title: \"错误\",\r\n        message: \"导出前请先输入开始结束时间\",\r\n      });\r\n      return;\r\n    }\r\n    this.queryImport.rootId = this.queryParams.dimensionalityId\r\n    this.queryImport.type=\"1\"\r\n    this.downloadFile(\r\n      \"/web/TYjy/answer/exportTemplateNomral\",\r\n      {\r\n        ...this.queryImport,\r\n      },\r\n      \"(\" +\r\n        this.queryImport.startDate +\r\n        \"-\" +\r\n        this.queryImport.endDate +\r\n        \")\" +\r\n        `数据.xlsx`\r\n    );\r\n   },\r\n   containsSubstring(substring, string) {\r\n      return string.includes(substring);\r\n   },\r\n   aloneList(string) {\r\n      if(string== '气体结算月报')\r\n      {\r\n        return true;\r\n      }\r\n      if(string== '高炉、转炉煤气月报表')\r\n      {\r\n        return true;\r\n      }\r\n      if(string== '天然气消耗月报表')\r\n      {\r\n        return true;\r\n      }\r\n      if(string== '蒸汽消耗月报表')\r\n      {\r\n        return true;\r\n      }\r\n      if(string== '电量月报表')\r\n      {\r\n        return true;\r\n      }\r\n      if(string== '特板事业部2025年经济责任制奖罚汇总')\r\n      {\r\n        return true;\r\n      }\r\n      if(string== '研究院目标指标一览')\r\n      {\r\n        return true;\r\n      }\r\n      return false;\r\n    },\r\n  },\r\n};\r\n</script>\r\n<style scoped lang=\"scss\">\r\n/* 设置滚动条的样式 */\r\n::-webkit-scrollbar {\r\n  width: 10px;\r\n  /* 竖向滚动条宽度 */\r\n  height: 15px;\r\n  /* 横向滚动条宽度 */\r\n  background-color: #ffffff;\r\n}\r\n\r\n/* 滚动槽 */\r\n::-webkit-scrollbar-track {\r\n  /* 其实直接在  ::-webkit-scrollbar 中设置也能达到同样的视觉效果*/\r\n  /* -webkit-box-shadow: inset 0 0 6px rgba(177, 223, 117, 0.7); */\r\n  background-color: #e4e4e4;\r\n  border-radius: 10px;\r\n}\r\n\r\n/* 滚动条滑块 */\r\n::-webkit-scrollbar-thumb {\r\n  border-radius: 5px;\r\n  -webkit-box-shadow: inset 0 0 6px rgba(158, 156, 156, 0.616);\r\n}\r\n\r\n::-webkit-scrollbar-thumb:hover {\r\n  background: rgba(139, 138, 138, 0.616);\r\n  -webkit-box-shadow: unset;\r\n}\r\n\r\n::-webkit-scrollbar-thumb:window-inactive {\r\n  /* 容器不被激活时的样式 */\r\n  background: #bdbdbd66;\r\n}\r\n\r\n::-webkit-scrollbar-corner {\r\n  /* 两个滚动条交汇处边角的样式 */\r\n  background-color: #cacaca66;\r\n}\r\n\r\n.sticky {\r\n  padding: 20px;\r\n  position: -webkit-sticky;\r\n  position: sticky;\r\n  top: 0; /* 粘性定位的起始位置 */\r\n  z-index: 100; /* 确保按钮在卡片之上 */\r\n}\r\n.el-tabs--card {\r\n  height: calc(100vh - 110px);\r\n}\r\n.el-tab-pane {\r\n  height: calc(100vh - 110px);\r\n  overflow-y: auto;\r\n}\r\n</style>\r\n  "]}]}