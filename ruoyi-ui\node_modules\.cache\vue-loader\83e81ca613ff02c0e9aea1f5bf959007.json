{"remainingRequest": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\src\\views\\suppPunishment\\suppInfo-module.vue?vue&type=style&index=0&id=22b2412e&scoped=true&lang=css", "dependencies": [{"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\src\\views\\suppPunishment\\suppInfo-module.vue", "mtime": 1755499162063}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\@vue\\cli-service\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoNCi8qIOihqOagvOihjOaCrOWBnOaViOaenCAqLw0KOjp2LWRlZXAgLmVsLXRhYmxlIHRib2R5IHRyOmhvdmVyIHsNCiAgYmFja2dyb3VuZC1jb2xvcjogI2Y1ZjdmYTsNCn0NCg0KLyog5p+l6K+i6KGo5Y2V5qC35byPIC0g6L6T5YWl5qGG5bem5a+56b2Q77yM5oyJ6ZKu5Y+z5a+56b2QICovDQouZWwtZm9ybS0taW5saW5lIHsNCiAgZGlzcGxheTogZmxleDsNCiAganVzdGlmeS1jb250ZW50OiBzcGFjZS1iZXR3ZWVuOw0KICBhbGlnbi1pdGVtczogY2VudGVyOw0KICBtYXJnaW4tYm90dG9tOiAyMHB4Ow0KfQ0KDQovKiDovpPlhaXmoYbljLrln5/lt6blr7npvZAgKi8NCi5lbC1mb3JtLS1pbmxpbmUgLmVsLWZvcm0taXRlbTpub3QoOmxhc3QtY2hpbGQpIHsNCiAgbWFyZ2luLXJpZ2h0OiAxNXB4Ow0KICBtYXJnaW4tYm90dG9tOiAwOw0KfQ0KDQovKiDmjInpkq7ljLrln5/lj7Plr7npvZAgKi8NCi5lbC1mb3JtLS1pbmxpbmUgLmVsLWZvcm0taXRlbTpsYXN0LWNoaWxkIHsNCiAgbWFyZ2luLWxlZnQ6IGF1dG87DQogIG1hcmdpbi1yaWdodDogMDsNCiAgbWFyZ2luLWJvdHRvbTogMDsNCn0NCg0KLyog57uf5LiA6L6T5YWl5qGG5a695bqmICovDQouZWwtZm9ybS0taW5saW5lIC5lbC1mb3JtLWl0ZW0gLmVsLWlucHV0IHsNCiAgd2lkdGg6IDIwMHB4Ow0KfQ0KDQovKiDlvLnnqpfmoIfpopjlsYXkuK0gKi8NCjo6di1kZWVwIC5lbC1kaWFsb2dfX2hlYWRlciB7DQogIHRleHQtYWxpZ246IGNlbnRlcjsNCn0NCg0KOjp2LWRlZXAgLmVsLWRpYWxvZ19fdGl0bGUgew0KICB0ZXh0LWFsaWduOiBjZW50ZXI7DQogIHdpZHRoOiAxMDAlOw0KICBkaXNwbGF5OiBibG9jazsNCn0NCg=="}, {"version": 3, "sources": ["suppInfo-module.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA8KA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA", "file": "suppInfo-module.vue", "sourceRoot": "src/views/suppPunishment", "sourcesContent": ["<template>\r\n  <el-dialog\r\n    title=\"供应商信息查询\"\r\n    :visible.sync=\"visible\"\r\n    width=\"900px\"\r\n    append-to-body\r\n    @close=\"handleClose\"\r\n  >\r\n    <!-- 查询条件 -->\r\n    <el-form :model=\"queryParams\" ref=\"queryForm\" :inline=\"true\" label-width=\"100px\">\r\n      <el-form-item label=\"供应商代码\" prop=\"suppId\">\r\n        <el-input\r\n          v-model=\"queryParams.suppId\"\r\n          placeholder=\"请输入供应商代码\"\r\n          clearable\r\n          size=\"small\"\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"供应商名称\" prop=\"suppName\">\r\n        <el-input\r\n          v-model=\"queryParams.suppName\"\r\n          placeholder=\"请输入供应商名称\"\r\n          clearable\r\n          size=\"small\"\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item >\r\n        <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleQuery\">搜索</el-button>\r\n        <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\r\n      </el-form-item>\r\n    </el-form>\r\n\r\n    <!-- 供应商列表 -->\r\n    <el-table \r\n      v-loading=\"loading\" \r\n      :data=\"suppList\" \r\n      @row-click=\"handleRowClick\"\r\n      @row-dblclick=\"handleRowDoubleClick\"\r\n      highlight-current-row\r\n      style=\"cursor: pointer;\"\r\n    >\r\n      <el-table-column label=\"供应商代码\" align=\"center\" prop=\"suppId\" width=\"120\" />\r\n      <el-table-column label=\"供应商名称\" align=\"center\" prop=\"suppName\" />\r\n      <el-table-column label=\"供应商中文简称\" align=\"center\" prop=\"suppShortName\" />\r\n      <el-table-column label=\"供应商地址\" align=\"center\" prop=\"suppAddress\" />\r\n      <el-table-column label=\"操作\" align=\"center\" width=\"100\">\r\n        <template slot-scope=\"scope\">\r\n          <el-button\r\n            size=\"mini\"\r\n            type=\"text\"\r\n            icon=\"el-icon-check\"\r\n            @click=\"handleSelect(scope.row)\"\r\n          >选择</el-button>\r\n        </template>\r\n      </el-table-column>\r\n    </el-table>\r\n\r\n    <!-- 分页 -->\r\n    <pagination\r\n      v-show=\"total > 0\"\r\n      :total=\"total\"\r\n      :page.sync=\"queryParams.pageNum\"\r\n      :limit.sync=\"queryParams.pageSize\"\r\n      @pagination=\"getList\"\r\n    />\r\n\r\n    <!-- 底部按钮 -->\r\n    <div slot=\"footer\" class=\"dialog-footer\">\r\n      <el-button @click=\"handleClose\">取 消</el-button>\r\n    </div>\r\n  </el-dialog>\r\n</template>\r\n\r\n<script>\r\nimport { listSuppInfo } from \"@/api/suppPunishment/suppInfo\";\r\n\r\nexport default {\r\n  name: \"SuppInfoDialog\",\r\n  data() {\r\n    return {\r\n      // 是否显示弹出层\r\n      visible: false,\r\n      // 遮罩层\r\n      loading: true,\r\n      // 总条数\r\n      total: 0,\r\n      // 供应商信息表格数据\r\n      suppList: [],\r\n      // 查询参数\r\n      queryParams: {\r\n        pageNum: 1,\r\n        pageSize: 10,\r\n        suppId: null,\r\n        suppName: null\r\n      }\r\n    };\r\n  },\r\n  methods: {\r\n    /** 显示弹窗 */\r\n    show() {\r\n      this.visible = true;\r\n      this.resetQuery();\r\n      this.getList();\r\n    },\r\n    \r\n    /** 隐藏弹窗 */\r\n    hide() {\r\n      this.visible = false;\r\n    },\r\n    \r\n    /** 关闭弹窗 */\r\n    handleClose() {\r\n      this.visible = false;\r\n      this.reset();\r\n    },\r\n    \r\n    /** 重置数据 */\r\n    reset() {\r\n      this.suppList = [];\r\n      this.total = 0;\r\n      this.queryParams = {\r\n        pageNum: 1,\r\n        pageSize: 10,\r\n        suppId: null,\r\n        suppName: null\r\n      };\r\n    },\r\n    \r\n    /** 查询供应商信息列表 */\r\n    getList() {\r\n      this.loading = true;\r\n      listSuppInfo(this.queryParams).then(response => {\r\n        this.suppList = response.rows;\r\n        this.total = response.total;\r\n        this.loading = false;\r\n      }).catch(() => {\r\n        this.loading = false;\r\n      });\r\n    },\r\n    \r\n    /** 搜索按钮操作 */\r\n    handleQuery() {\r\n      this.queryParams.pageNum = 1;\r\n      this.getList();\r\n    },\r\n    \r\n    /** 重置按钮操作 */\r\n    resetQuery() {\r\n      this.resetForm(\"queryForm\");\r\n      this.handleQuery();\r\n    },\r\n    \r\n    /** 行点击事件 */\r\n    handleRowClick(row) {\r\n      // 可以在这里添加行选中效果\r\n    },\r\n    \r\n    /** 行双击事件 */\r\n    handleRowDoubleClick(row) {\r\n      this.handleSelect(row);\r\n    },\r\n    \r\n    /** 选择供应商 */\r\n    handleSelect(row) {\r\n      this.$emit('select', row);\r\n      this.handleClose();\r\n    }\r\n  }\r\n};\r\n</script>\r\n\r\n<style scoped>\r\n/* 表格行悬停效果 */\r\n::v-deep .el-table tbody tr:hover {\r\n  background-color: #f5f7fa;\r\n}\r\n\r\n/* 查询表单样式 - 输入框左对齐，按钮右对齐 */\r\n.el-form--inline {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 20px;\r\n}\r\n\r\n/* 输入框区域左对齐 */\r\n.el-form--inline .el-form-item:not(:last-child) {\r\n  margin-right: 15px;\r\n  margin-bottom: 0;\r\n}\r\n\r\n/* 按钮区域右对齐 */\r\n.el-form--inline .el-form-item:last-child {\r\n  margin-left: auto;\r\n  margin-right: 0;\r\n  margin-bottom: 0;\r\n}\r\n\r\n/* 统一输入框宽度 */\r\n.el-form--inline .el-form-item .el-input {\r\n  width: 200px;\r\n}\r\n\r\n/* 弹窗标题居中 */\r\n::v-deep .el-dialog__header {\r\n  text-align: center;\r\n}\r\n\r\n::v-deep .el-dialog__title {\r\n  text-align: center;\r\n  width: 100%;\r\n  display: block;\r\n}\r\n</style>\r\n"]}]}