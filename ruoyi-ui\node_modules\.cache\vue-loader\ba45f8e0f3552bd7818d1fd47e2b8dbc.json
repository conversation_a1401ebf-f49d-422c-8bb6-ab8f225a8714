{"remainingRequest": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\src\\views\\dgcb\\supplier\\addSupplyInfo\\index.vue?vue&type=style&index=0&id=70aba9be&scoped=true&lang=css", "dependencies": [{"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\src\\views\\dgcb\\supplier\\addSupplyInfo\\index.vue", "mtime": 1755499098391}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\@vue\\cli-service\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCg0KLm15Ym94LWNhcmQgew0KICBib3JkZXItcmFkaXVzOiA0cHg7DQogIGJvcmRlcjogMXB4IHNvbGlkICNlYmVlZjU7DQogIGJhY2tncm91bmQtY29sb3I6ICNmZmY7DQogIG92ZXJmbG93OiBoaWRkZW47DQogIGNvbG9yOiAjMzAzMTMzOw0KICBib3gtc2hhZG93OiAwIDJweCAxMnB4IDAgcmdiYSgwLCAwLCAwLCAuMSk7DQogIG1hcmdpbi1ib3R0b206IDEwcHgNCn0NCg0KLm15Ym94LWhlYWRlciB7DQogIHBhZGRpbmc6IDE4cHggMjBweDsNCiAgYm94LXNpemluZzogYm9yZGVyLWJveDsNCn0NCg0KLm15Ym94LWJvZHkgew0KICBib3JkZXItdG9wOiAxcHggc29saWQgI2ViZWVmNTsNCiAgLyogcGFkZGluZzogMjBweDsgKi8NCn0NCg0KLmVsLWRpYWxvZ19fYm9keSB7DQogIHBhZGRpbmc6IDEwcHggMzBweDsNCn0NCg0KLmNvbnRyYWN0Q2FyZCB7DQogIG1hcmdpbi1ib3R0b206IDEwcHgNCn0NCg0KLmRpYWxvZy1ib3ggew0KICBvdmVyZmxvdzogYXV0bzsNCn0NCg0KLmRpYWxvZy1ib3g6Oi13ZWJraXQtc2Nyb2xsYmFyIHsNCiAgZGlzcGxheTogbm9uZTsNCn0NCg0KLmRpYWxvZy1mb290ZXIgew0KICB0ZXh0LWFsaWduOiBjZW50ZXINCn0NCg0KLnRyYW5zaXRpb24tYm94IHsNCiAgZ3JpZC1hdXRvLWNvbHVtbnM6IDEwcHg7DQogIHdpZHRoOiBhdXRvOw0KICBoZWlnaHQ6IGF1dG87DQogIGJvcmRlci1yYWRpdXM6IDRweDsNCiAgYmFja2dyb3VuZC1jb2xvcjogI0NDQ0NDQzsNCiAgdGV4dC1hbGlnbjogY2VudGVyOw0KICBjb2xvcjogI0RDMTQzNzsNCiAgcGFkZGluZzogMjBweCAyMHB4Ow0KICAtd2Via2l0LWJveC1zaXppbmc6IGJvcmRlci1ib3g7DQogIGJveC1zaXppbmc6IGJvcmRlci1ib3g7DQogIG1hcmdpbi1yaWdodDogMjBweDsNCiAgdGV4dC1hbGlnbjogbGVmdDsNCiAgbWFyZ2luLWJvdHRvbTogMjBweDsNCn0NCg0KLmltYWdlLWdyaWQgew0KICBkaXNwbGF5OiBncmlkOw0KICBncmlkLXRlbXBsYXRlLWNvbHVtbnM6IHJlcGVhdChhdXRvLWZpbGwsIG1pbm1heCgxNTBweCwgMWZyKSk7DQogIGdhcDogMTBweDsNCn0NCg0KLmN1c3RvbS1mb3JtLWxhYmVsPj4+LmVsLWZvcm0taXRlbV9fbGFiZWwgew0KICBjb2xvcjogIzAwNTJjYzsNCn0NCg0KDQouaGlnaGxpZ2h0IHsNCiAgY29sb3I6IHJlZDsNCiAgZm9udC13ZWlnaHQ6IGJvbGQ7DQp9DQoNCi5kaWFsb2ctYm9keSBwIHsNCiAgbWFyZ2luLWJvdHRvbTogMTBweDsNCn0NCg0KLmRpYWxvZy1ib2R5IG9sIHsNCiAgbWFyZ2luLWJvdHRvbTogMTBweDsNCn0NCg0KLmRpYWxvZy1ib2R5IG9sIGxpIHsNCiAgbWFyZ2luLWJvdHRvbTogNXB4Ow0KfQ0K"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAyXA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;;AAGA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/dgcb/supplier/addSupplyInfo", "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <el-alert title=\"提示：若无法找到适合的物资编码，请联系业务部门人员进行物资新增，并绑定合同！\" type=\"success\" effect=\"dark\"></el-alert>\r\n    <br>\r\n    <el-alert title=\"提示：供货清单仅允许选择“X”结尾代表新件的物资编码，若需配送返修件，请前往返修单页面申请\" type=\"success\" effect=\"dark\">\r\n    </el-alert>\r\n    <br>\r\n    <!-- <div class=\"transition-box\">\r\n\r\n        <div>\r\n            <strong>提示：若无法找到适合的物资编码，请联系业务部门人员进行物资新增，并绑定合同！<br>\r\n                提示：供货清单仅允许选择“X”结尾代表新件的物资编码，若需配送返修件，请前往返修单页面申请<br>\r\n                提示：尊敬的供应商们，根据管理需要，特此通知：自3月11日0点至3月17日24点期间，请注意对计量类物资不进行过磅操作。请将送货单交至物管处后前往分厂卸货。从3月18日0点开始，将恢复原有送货流程。请您通知相关司机人员遵守上述安排。感谢您的配合与理解！\r\n            </strong>\r\n        </div>\r\n\r\n    </div> -->\r\n\r\n    <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" label-width=\"120px\">\r\n      <div style=\"width:100%\">\r\n        <!-- <el-form-item label=\"运输车牌号：\" prop=\"carNum\">\r\n            <el-input style=\"width:220px\" v-model=\"form.carNum\" clearable></el-input>\r\n        </el-form-item> -->\r\n\r\n        <el-form-item label=\"货车司机\" prop=\"driverId\" :rules=\"[{ required: true, message: '司机信息不能为空' }]\">\r\n          <el-select style=\"width:300px\" v-model=\"form.driverId\" filterable :filter-method=\"filterDriverData\"\r\n            placeholder=\"请选择（如果显示不出请在输入框搜索）\" @change=\"handleDriverChange\">\r\n            <el-option v-for=\"item in filteredDriverOptions.slice(0, 50)\" :key=\"item.id\" :label=\"item.driverInfo\"\r\n              :value=\"item.id\">\r\n            </el-option>\r\n          </el-select>\r\n\r\n          <el-button style=\"margin-left: 10px; font-size: 14px; padding: 5px 10px;\" type=\"primary\"\r\n            @click=\"openNewDriverWindow\">前往新增或修改司机信息\r\n          </el-button>\r\n        </el-form-item>\r\n        <el-form-item class=\"custom-form-label\" label=\"提醒：\">\r\n          <span style=\"color:#0052cc;\">应海关高认要求，入厂货车必须提供货车司机具体信息</span>\r\n        </el-form-item>\r\n\r\n\r\n        <el-form-item label=\"司机名称\" prop=\"name\" v-if=\"form.name != null\">\r\n          <el-input v-model=\"form.name\" placeholder=\"请输入司机名称\" disabled style=\"width:300px\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"手机号\" prop=\"phone\" v-if=\"form.phone != null\">\r\n          <el-input v-model=\"form.phone\" placeholder=\"请输入手机号\" disabled style=\"width:300px\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"身份证号\" prop=\"idCard\" v-if=\"form.idCard != null\">\r\n          <el-input v-model=\"form.idCard\" placeholder=\"请输入身份证号\" disabled style=\"width:300px\" />\r\n        </el-form-item>\r\n\r\n        <el-form-item label=\"人脸照片\" prop=\"faceImg\" v-if=\"form.photo != null && form.photo != ''\">\r\n          <div class=\"image-grid\">\r\n            <!-- <el-image v-for=\"(image, index) in form.faceImgList\" :key=\"index\" :src=\"image\" fit=\"cover\"\r\n                      lazy></el-image> -->\r\n            <el-image style=\"width: 200px; height: 200px\" :src=\"form.photo\" fit=\"cover\"></el-image>\r\n          </div>\r\n        </el-form-item>\r\n\r\n        <el-form-item label=\"驾驶证照片\" prop=\"driverLicenseImgs\" v-if=\"form.driverLicenseImgs != null && form.driverLicenseImgs != ''\">\r\n          <div class=\"image-grid\">\r\n            <!-- <el-image v-for=\"(image, index) in form.drivingLicenseImgList\" :key=\"index\" :src=\"image\" fit=\"cover\"\r\n              lazy></el-image> -->\r\n            <el-image style=\"width: 200px; height: 200px\" :src=\"form.driverLicenseImgs\" fit=\"cover\"></el-image>\r\n          </div>\r\n        </el-form-item>\r\n\r\n        <el-form-item label=\"行驶证照片\" prop=\"vehicleLicenseImgs\" v-if=\"form.vehicleLicenseImgs != null && form.vehicleLicenseImgs != ''\">\r\n          <div class=\"image-grid\">\r\n            <!-- <el-image v-for=\"(image, index) in form.driverLicenseImgList\" :key=\"index\" :src=\"image\" fit=\"cover\"\r\n              lazy></el-image> -->\r\n            <el-image style=\"width: 200px; height: 200px\" :src=\"form.vehicleLicenseImgs\" fit=\"cover\"></el-image>\r\n          </div>\r\n        </el-form-item>\r\n\r\n        <el-form-item label=\"货车\" prop=\"carId\" :rules=\"[{ required: true, message: '货车信息不能为空' }]\">\r\n          <el-select style=\"width:300px\" v-model=\"form.carId\" filterable :filter-method=\"filterCarData\"\r\n            placeholder=\"请选择（如果显示不出请在输入框搜索）\" @change=\"handleCarChange\">\r\n            <el-option v-for=\"item in filteredCarOptions.slice(0, 50)\" :key=\"item.id\" :label=\"item.carNumber\"\r\n              :value=\"item.id\">\r\n            </el-option>\r\n          </el-select>\r\n\r\n          <el-button style=\"margin-left: 10px; font-size: 14px; padding: 5px 10px;\" type=\"primary\"\r\n            @click=\"openNewCarWindow\">前往新增或修改货车信息\r\n          </el-button>\r\n        </el-form-item>\r\n\r\n        <el-form-item label=\"车牌号\" prop=\"carNum\" v-if=\"form.carNumber != null\">\r\n          <el-input v-model=\"form.carNumber\" placeholder=\"请输入车牌号\" disabled style=\"width:300px\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"车辆排放标准\" prop=\"vehicleEmissionStandards\" v-if=\"form.vehicleEmissionStandards != null\">\r\n                   <el-select v-model=\"form.vehicleEmissionStandards\" placeholder=\"请选择车辆排放标准\" disabled style=\"width:300px\">\r\n            <el-option v-for=\"dict in vehicleEmissionStandardsOptions\" :key=\"dict.dictValue\" :label=\"dict.dictLabel\"\r\n              :value=\"dict.dictValue\"></el-option>\r\n          </el-select>\r\n        </el-form-item>\r\n\r\n\r\n        <el-form-item label=\"供货时间：\" prop=\"supplyTime\">\r\n          <el-date-picker value-format=\"yyyy-MM-dd\" v-model=\"form.supplyTime\" type=\"date\" placeholder=\"选择日期\"\r\n            style=\"width:300px\">\r\n          </el-date-picker>\r\n        </el-form-item>\r\n\r\n        <el-form-item class=\"custom-form-label\" label=\"提醒：\">\r\n          <span style=\"color:#0052cc;\">供货时间开始三天内，货车准许入厂，请认真填写供货时间</span>\r\n        </el-form-item>\r\n\r\n        <el-form-item label=\"是否计量：\" prop=\"measureFlag\">\r\n          <el-radio-group @input=\"measureChange\" v-model=\"form.measureFlag\">\r\n            <el-radio :label=\"1\">是</el-radio>\r\n            <el-radio :label=\"0\">否</el-radio>\r\n          </el-radio-group>\r\n        </el-form-item>\r\n        <el-form-item label=\"物资信息：\">\r\n          <el-row :gutter=\"10\" class=\"mb8\">\r\n            <el-col :span=\"1.5\">\r\n              <el-button type=\"primary\" icon=\"el-icon-plus\" size=\"mini\" @click=\"handleAddContract\">选择物资\r\n              </el-button>\r\n            </el-col>\r\n          </el-row>\r\n          <el-card class=\"box-card contractCard\" v-for=\"(contract, index) in contractList\" :key=\"index\">\r\n            <div slot=\"header\" class=\"clearfix\">\r\n              <span>合同编号：{{ contract.contractNo }}</span>\r\n            </div>\r\n            <!-- <div v-for=\"item in contract.itemList\" :key=\"item.itemNo\" class=\"text item\">\r\n                    <el-row>\r\n                        <el-col>\r\n                            <div style=\"padding: 6px 0;\">\r\n                                {{ `${item.itemName}（编号：${item.itemNo}，规格：${item.itemSpec})` }}\r\n                                <el-button icon=\"el-icon-delete\" circle type=\"danger\" size=\"mini\" @click=\"handleItemDel(contract,item)\"></el-button>\r\n                            </div>\r\n                        </el-col>\r\n                        <el-col>\r\n                            <el-form ref=\"itemform\" :model=\"item\">\r\n                                <el-form-item v-if=\"!form.measureFlag\" :rules=\"[{ required: true, message: '数量不能为空'}]\"\r\n                                label=\"数量：\" label-width=\"100px\" prop=\"amount\">\r\n                                    <el-input v-model.number=\"item.amount\" style=\"width:220px\"\r\n                    type=\"number\" clearable></el-input> {{ item.measureUnit }}\r\n                                </el-form-item>\r\n                                <el-form-item v-if=\"form.measureFlag\" :rules=\"[{ required: true, message: '数量不能为空'}]\"\r\n                                label=\"数量：\" label-width=\"100px\" prop=\"amount\">\r\n                                    <el-input v-model.number=\"item.amount\" style=\"width:220px\"\r\n                    type=\"number\" clearable></el-input> 件\r\n                                </el-form-item>\r\n                                <el-form-item v-if=\"form.measureFlag\" style=\"margin-top: 10px;\" :rules=\"[{ required: true, message: '数量不能为空'}]\"\r\n                                label=\"净重：\" label-width=\"100px\" prop=\"amount\">\r\n                                    <el-input v-model.number=\"item.supplyWeight\" style=\"width:220px\"\r\n                    type=\"number\" clearable></el-input> {{ item.measureUnit }}\r\n                                </el-form-item>\r\n                            </el-form>\r\n                        </el-col>\r\n                    </el-row>\r\n                </div> -->\r\n            <el-table :data=\"contract.itemList\" :header-cell-style=\"itemHeaderStyle\" style=\"width: 100%\">\r\n              <el-table-column prop=\"itemNo\" label=\"物资编码\" min-width=\"150\">\r\n              </el-table-column>\r\n              <el-table-column prop=\"itemName\" label=\"物资名称\" min-width=\"200\">\r\n              </el-table-column>\r\n              <el-table-column label=\"规格\" min-width=\"150\">\r\n                <template slot-scope=\"scopes\">\r\n                  {{\r\n                    scopes.row.itemSpec == null ?\r\n                      `无(分厂:${scopes.row.factoryDesc},产线:${scopes.row.productionLineDesc})` :\r\n                      `${scopes.row.itemSpec}(分厂:${scopes.row.factoryDesc}，产线:${scopes.row.productionLineDesc})`\r\n                  }}\r\n                </template>\r\n              </el-table-column>\r\n              <!-- 非计量 -->\r\n              <el-table-column v-if=\"!form.measureFlag\" label=\"数量\" min-width=\"150\">\r\n\r\n                <template slot-scope=\"scope\">\r\n                  <div style=\"display: flex;\">\r\n                    <el-form ref=\"itemform\" :model=\"scope.row\" inline-message>\r\n                      <el-form-item :rules=\"[{ required: true, message: '数量不能为空' }]\" prop=\"amount\">\r\n                        <el-input v-model.number=\"scope.row.amount\" placeholder=\"请输入数量\" type=\"number\" />\r\n                      </el-form-item>\r\n                    </el-form>\r\n                    <span style=\"line-height: 36px;padding-left: 5px;\">{{\r\n                      scope.row.measureUnit\r\n                    }}</span>\r\n                  </div>\r\n                </template>\r\n              </el-table-column>\r\n              <!-- 计量 -->\r\n              <el-table-column v-if=\"form.measureFlag\" label=\"数量\" min-width=\"100\">\r\n\r\n                <template slot-scope=\"scope\">\r\n                  <div style=\"display: flex;\">\r\n                    <el-form ref=\"itemform\" :model=\"scope.row\" inline-message>\r\n                      <el-form-item :rules=\"[{ required: true, message: '数量不能为空' }]\" prop=\"amount\">\r\n                        <el-input v-model.number=\"scope.row.amount\" placeholder=\"请输入数量\" type=\"number\" />\r\n                      </el-form-item>\r\n                    </el-form>\r\n                    <span style=\"line-height: 36px;padding-left: 5px;\">件</span>\r\n                  </div>\r\n                </template>\r\n              </el-table-column>\r\n              <el-table-column v-if=\"form.measureFlag\" label=\"净重\" min-width=\"100\">\r\n\r\n                <template slot-scope=\"scope\">\r\n                  <div style=\"display: flex;\">\r\n                    <el-form ref=\"itemform\" :model=\"scope.row\" inline-message>\r\n                      <el-form-item :rules=\"[{ required: true, message: '净重不能为空' }]\" prop=\"supplyWeight\">\r\n                        <el-input v-model.number=\"scope.row.supplyWeight\" placeholder=\"请输入重量\" type=\"number\" />\r\n                      </el-form-item>\r\n                    </el-form>\r\n                    <span style=\"line-height: 36px;padding-left: 5px;\">{{\r\n                      scope.row.measureUnit\r\n                    }}</span>\r\n                  </div>\r\n                </template>\r\n              </el-table-column>\r\n              <el-table-column label=\"操作\" width=\"100\">\r\n\r\n                <template slot-scope=\"scope\">\r\n                  <el-button type=\"text\" size=\"small\" @click=\"handleItemDel(contract, scope.row.itemNo)\">删除\r\n                  </el-button>\r\n                </template>\r\n              </el-table-column>\r\n            </el-table>\r\n          </el-card>\r\n        </el-form-item>\r\n        <!-- <el-button v-if=\"contractList.length > 0\" type=\"success\" icon=\"el-icon-check\" size=\"mini\" @click=\"submitSupply\">提交清单</el-button> -->\r\n      </div>\r\n    </el-form>\r\n\r\n    <!-- 添加或修改对话框 -->\r\n    <el-dialog :title=\"title\" :visible.sync=\"open\" append-to-body fullscreen>\r\n      <div>\r\n        <el-form :model=\"contractSearchParam\" ref=\"contractSearchForm\" :inline=\"true\" label-width=\"68px\">\r\n          <el-form-item label=\"合同编号\" prop=\"contractNo\">\r\n            <el-input v-model=\"contractSearchParam.contractNo\" placeholder=\"请输入合同编号\" clearable size=\"small\"\r\n              @keyup.enter.native=\"handleContractSearch\" />\r\n          </el-form-item>\r\n          <el-form-item>\r\n            <el-button type=\"cyan\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleContractSearch\">搜索\r\n            </el-button>\r\n            <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetContractSearch\">重置</el-button>\r\n          </el-form-item>\r\n        </el-form>\r\n      </div>\r\n      <div style=\"height:634px\" class=\"dialog-box\">\r\n        <el-empty v-if=\"showContractData.length == 0\" description=\"无数据\"></el-empty>\r\n        <div v-else class=\"mybox-card\" v-for=\"(contract, index) in showContractData\" :key=\"index\">\r\n          <div class=\"mybox-header\" @click=\"handleItemOpenClose(contract)\">\r\n            <div class=\"clearfix\">\r\n              <span style=\"font-size:16px;margin-right: 20px;\">合同名称：{{ contract.contractName }}（编号：{{\r\n                contract.contractNo\r\n              }}）</span>\r\n              <!-- <el-tag v-if=\"contract.status == 1\" type=\"success\">可用</el-tag>\r\n              <el-tag v-else type=\"danger\">禁用</el-tag> -->\r\n              <i v-if=\"contract.isOpen\" style=\"float: right; padding: 3px 0\" class=\"el-icon-arrow-up\"></i>\r\n              <i v-else class=\"el-icon-arrow-down\" style=\"float: right; padding: 3px 0\"></i>\r\n              <!-- <el-button  v-if=\"contract.isOpen\" style=\"float: right; padding: 3px 0\" type=\"text\"  @click=\"closeItemSelection(contract)\">收起</el-button>\r\n              <el-button v-else style=\"float: right; padding: 3px 0\" type=\"text\" @click=\"openItemSelection(contract)\">展开</el-button> -->\r\n            </div>\r\n          </div>\r\n          <div class=\"mybox-body\" v-if=\"contract.isOpen\">\r\n            <el-form :model=\"contract.itemSearchParam\" :ref=\"itemRef(contract)\" :inline=\"true\" label-width=\"68px\">\r\n              <el-form-item label=\"物资编号\" prop=\"itemNo\">\r\n                <el-input v-model=\"contract.itemSearchParam.itemNo\" placeholder=\"请输入物资编号\" clearable size=\"small\" />\r\n              </el-form-item>\r\n              <el-form-item label=\"物资名称\" prop=\"itemName\">\r\n                <el-input v-model=\"contract.itemSearchParam.itemName\" placeholder=\"请输入物资名称\" clearable size=\"small\" />\r\n              </el-form-item>\r\n              <el-form-item>\r\n                <el-button type=\"cyan\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleItemSearch(contract)\">搜索\r\n                </el-button>\r\n                <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetItemSearch(contract)\">重置\r\n                </el-button>\r\n              </el-form-item>\r\n            </el-form>\r\n            <el-table :ref=\"contract.contractNo\" :data=\"contract.showTableList\" :header-cell-style=\"itemHeaderStyle\"\r\n              style=\"width: 100%\" :row-key=\"getRowKeys\" @selection-change=\"handleSelectionChange($event, contract)\">\r\n              <el-table-column type=\"selection\" width=\"55\" :selectable=\"itemIsSelectable\" :reserve-selection=\"true\">\r\n              </el-table-column>\r\n              <el-table-column prop=\"itemNo\" label=\"物资编码\" min-width=\"150\">\r\n              </el-table-column>\r\n              <el-table-column prop=\"itemName\" label=\"物资名称\" min-width=\"200\">\r\n              </el-table-column>\r\n              <el-table-column label=\"规格\" min-width=\"150\">\r\n\r\n                <template slot-scope=\"scopes\">\r\n                  {{\r\n                    scopes.row.itemSpec == null ?\r\n                      `无(分厂:${scopes.row.factoryDesc},产线:${scopes.row.productionLineDesc})` :\r\n                      `${scopes.row.itemSpec}(分厂:${scopes.row.factoryDesc},产线:${scopes.row.productionLineDesc})`\r\n                  }}\r\n                </template>\r\n              </el-table-column>\r\n              <!-- <el-table-column\r\n              label=\"状态\">\r\n              <template slot-scope=\"props\">\r\n                  <el-tag v-if=\"props.row.status == 1\" type=\"success\">可用</el-tag>\r\n                  <el-tag v-else type=\"danger\">禁用</el-tag>\r\n              </template>\r\n          </el-table-column> -->\r\n              <!-- 非计量 -->\r\n              <el-table-column v-if=\"!form.measureFlag\" label=\"数量\" min-width=\"150\">\r\n\r\n                <template slot-scope=\"scope\">\r\n                  <div style=\"display: flex;\">\r\n                    <el-input v-model=\"scope.row.amount\" placeholder=\"请输入数量\" type=\"number\" />\r\n                    <span style=\"line-height: 36px;padding-left: 5px;\">{{\r\n                      scope.row.measureUnit\r\n                    }}</span>\r\n                  </div>\r\n                </template>\r\n              </el-table-column>\r\n              <!-- 计量 -->\r\n              <el-table-column v-if=\"form.measureFlag\" label=\"数量\" min-width=\"100\">\r\n\r\n                <template slot-scope=\"scope\">\r\n                  <div style=\"display: flex;\">\r\n                    <el-input v-model=\"scope.row.amount\" placeholder=\"请输入数量\" type=\"number\" />\r\n                    <span style=\"line-height: 36px;padding-left: 5px;\">件</span>\r\n                  </div>\r\n                </template>\r\n              </el-table-column>\r\n              <el-table-column v-if=\"form.measureFlag\" label=\"净重\" min-width=\"100\">\r\n\r\n                <template slot-scope=\"scope\">\r\n                  <div style=\"display: flex;\">\r\n                    <el-input v-model=\"scope.row.supplyWeight\" placeholder=\"请输入重量\" type=\"number\" />\r\n                    <span style=\"line-height: 36px;padding-left: 5px;\">{{\r\n                      scope.row.measureUnit\r\n                    }}</span>\r\n                  </div>\r\n                </template>\r\n              </el-table-column>\r\n            </el-table>\r\n\r\n            <el-pagination background small @current-change=\"handleCurrentChange($event, contract, index)\"\r\n              :current-page.sync=\"contract.currentPage\" :page-size=\"contract.pageSize\" layout=\"total,prev, pager, next\"\r\n              :total=\"contract.total\">\r\n            </el-pagination>\r\n            <!-- <el-checkbox-group v-model=\"contract.checkboxGroup\" size=\"small\"> -->\r\n            <!-- <el-checkbox v-for=\"item in contract.itemList\" :key=\"item.itemNo\" :label=\"item.itemName\" @change=\"checkChange($event,item)\" border></el-checkbox> -->\r\n            <!-- </el-checkbox-group> -->\r\n          </div>\r\n        </div>\r\n      </div>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button type=\"primary\" @click=\"submitContract\">确 定</el-button>\r\n        <el-button @click=\"cancel\">取 消</el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <el-dialog title=\"用户协议\" :visible.sync=\"showDialog\" width=\"60%\" :close-on-click-modal=\"false\"\r\n      :close-on-press-escape=\"false\" center :modal=\"false\">\r\n      <div class=\"dialog-body\">\r\n        <p>尊敬的合作伙伴：</p>\r\n        <p>您好！衷心感谢您一直以来对我司的支持，为了强化生产现场管理、保障我司的信息安全，特将有关规定告知如下：</p>\r\n        <ol>\r\n          <li>1、请您在进入我司区域后，包括办公区域、生产区域、研发区域、厂区道路等，<span class=\"highlight\">未经允许，不随意拍照或录像。</span></li>\r\n          <li>2、请您妥善保管工作照或录像，<span class=\"highlight\">不将其随意转发</span>任何无关人员，<span\r\n              class=\"highlight\">不擅自剪辑、传播、上传、发布任何平台。</span>\r\n          </li>\r\n          <li>3、请您在我司许可的指定工作区域内活动，<span class=\"highlight\">不随意走动，全力保护我司的各类信息安全，遵守我司的各项管理规定。</span></li>\r\n          <li>4、请对您公司<span class=\"highlight\">所有派出</span>进入我司区域工作的<span class=\"highlight\">人员进行宣贯，知晓并遵守</span>以上三项规定。</li>\r\n        </ol>\r\n      </div>\r\n      <span slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button :disabled=\"!isAgreeEnabled\" @click=\"onAgreeClick\">本人已阅知，并承诺遵守{{ countDown ? '(' + countDown + ')' :\r\n          ''\r\n        }}</el-button>\r\n      </span>\r\n    </el-dialog>\r\n  </div>\r\n\r\n\r\n\r\n</template>\r\n\r\n<style scoped>\r\n.mybox-card {\r\n  border-radius: 4px;\r\n  border: 1px solid #ebeef5;\r\n  background-color: #fff;\r\n  overflow: hidden;\r\n  color: #303133;\r\n  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, .1);\r\n  margin-bottom: 10px\r\n}\r\n\r\n.mybox-header {\r\n  padding: 18px 20px;\r\n  box-sizing: border-box;\r\n}\r\n\r\n.mybox-body {\r\n  border-top: 1px solid #ebeef5;\r\n  /* padding: 20px; */\r\n}\r\n\r\n.el-dialog__body {\r\n  padding: 10px 30px;\r\n}\r\n\r\n.contractCard {\r\n  margin-bottom: 10px\r\n}\r\n\r\n.dialog-box {\r\n  overflow: auto;\r\n}\r\n\r\n.dialog-box::-webkit-scrollbar {\r\n  display: none;\r\n}\r\n\r\n.dialog-footer {\r\n  text-align: center\r\n}\r\n\r\n.transition-box {\r\n  grid-auto-columns: 10px;\r\n  width: auto;\r\n  height: auto;\r\n  border-radius: 4px;\r\n  background-color: #CCCCCC;\r\n  text-align: center;\r\n  color: #DC1437;\r\n  padding: 20px 20px;\r\n  -webkit-box-sizing: border-box;\r\n  box-sizing: border-box;\r\n  margin-right: 20px;\r\n  text-align: left;\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.image-grid {\r\n  display: grid;\r\n  grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));\r\n  gap: 10px;\r\n}\r\n\r\n.custom-form-label>>>.el-form-item__label {\r\n  color: #0052cc;\r\n}\r\n\r\n\r\n.highlight {\r\n  color: red;\r\n  font-weight: bold;\r\n}\r\n\r\n.dialog-body p {\r\n  margin-bottom: 10px;\r\n}\r\n\r\n.dialog-body ol {\r\n  margin-bottom: 10px;\r\n}\r\n\r\n.dialog-body ol li {\r\n  margin-bottom: 5px;\r\n}\r\n</style>\r\n\r\n<script>\r\nimport { listContract, addBatch } from \"@/api/dgcb/supplier/supply\";\r\nimport { listAllDriver, getXctgDriverUserList, getXctgDriverCarList } from \"@/api/dgcb/driver/driver\";\r\nimport { isNotify, addNotify } from \"@/api/truck/notify/notify\";\r\nimport UploadImage from '@/components/MoreUploadImage';//引用组件\r\n\r\nexport default {\r\n  components: {\r\n    UploadImage,\r\n  },\r\n  name: \"addSupplyInfo\",\r\n  props: {\r\n    submitCancel: {\r\n      type: Function,\r\n      default: null\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      searchDriverQuery: '',\r\n      filteredDriverOptions: [],\r\n\r\n      searchCarQuery: '',\r\n      filteredCarOptions: [],\r\n\r\n      itemHeaderStyle: {\r\n        \"background-color\": \"#fff\"\r\n      },\r\n      driverList: [],\r\n      carList: [],\r\n      // 物资信息隐藏序列\r\n      openIndex: [],\r\n      // 遮罩层\r\n      loading: true,\r\n      // 弹出层标题\r\n      title: \"\",\r\n      // 是否显示弹出层\r\n      open: false,\r\n      // 合同待确认选择项\r\n      contractSelection: [],\r\n      // 已选择合同列表\r\n      contractList: [],\r\n      // 物资多选信息\r\n      selectData: {},\r\n      // 显示物资列表\r\n      // showTableList:[],\r\n      // 源合同数据\r\n      contractData: [],\r\n      // 过滤数据\r\n      showContractData: [],\r\n      // 删除数据\r\n      delItemData: [],\r\n      form: {\r\n        carNum: null,\r\n        supplyTime: null,\r\n        measureFlag: 0,\r\n        status: 1,\r\n        tdgcb05List: []\r\n      },\r\n      // 合同搜索条件\r\n      contractSearchParam: {\r\n        contractNo: \"\"\r\n      },\r\n      // 表单校验\r\n      rules: {\r\n        /**\r\n         carNum: [\r\n         {\r\n         required: true,\r\n         message: \"车牌号不能为空\",\r\n         trigger: \"blur\"\r\n         },\r\n         {\r\n         pattern: /^(([京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼使领][A-Z](([0-9]{5}[DF])|([DF]([A-HJ-NP-Z0-9])[0-9]{4})))|([京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼使领][A-Z][A-HJ-NP-Z0-9]{4}[A-HJ-NP-Z0-9挂学警港澳使领]))$/,\r\n         message: \"车牌号格式不正确\"\r\n         }\r\n         ],*/\r\n        supplyTime: [\r\n          {\r\n            required: true,\r\n            message: \"供货时间不能为空\",\r\n            trigger: \"blur\"\r\n          }\r\n        ],\r\n      },\r\n      // 测试合同数据\r\n      responseData: [{\r\n        contractNo: '1',\r\n        contractName: '合同名称1',\r\n        validTime: '2016-05-02',\r\n        tdgcb03List: []\r\n      }],\r\n      showDialog: false,\r\n      isAgreeEnabled: false,\r\n      countDown: 5,\r\n      timer: null,\r\n    }\r\n  },\r\n\r\n  computed: {\r\n    // 默认显示前50条，若有搜索，则显示搜索后的数据\r\n    displayDriverListOptions() {\r\n      return this.searchDriverQuery ? this.filteredDriverOptions : this.driverList.slice(0, 50);\r\n    },\r\n    displayCarListOptions() {\r\n      return this.searchCarQuery ? this.filteredCarOptions : this.carList.slice(0, 50);\r\n    }\r\n  },\r\n\r\n  created() {\r\n    console.log('showDialog:', this.showDialog);\r\n    this.getList();\r\n    this.getDriverList();\r\n    this.getCarList();\r\n\r\n    let param = {}\r\n    param.businessType = 1;\r\n    isNotify(param).then(response => {\r\n      if (response.data) { // 假设接口返回的数据为 true 或 false\r\n        this.showDialog = true;\r\n      } else {\r\n        this.showDialog = false;\r\n      }\r\n    }).catch(error => {\r\n      console.error('Failed to call isNotify:', error);\r\n      this.showDialog = false; // 如果接口调用失败，不显示弹框\r\n    });\r\n\r\n\r\n    this.timer = setInterval(() => {\r\n      if (this.countDown > 0) {\r\n        this.countDown--;\r\n      } else {\r\n        this.isAgreeEnabled = true;\r\n        clearInterval(this.timer);\r\n      }\r\n    }, 1000);\r\n  },\r\n  beforeDestroy() {\r\n    clearInterval(this.timer);\r\n  },\r\n  methods: {\r\n    // 搜索过滤逻辑\r\n    filterDriverData(query) {\r\n      this.searchDriverQuery = query;\r\n\r\n      if (this.searchDriverQuery) {\r\n\r\n        this.filteredDriverOptions = this.driverList.filter(item =>\r\n          item.driverInfo.includes(query)\r\n        );\r\n      } else {\r\n\r\n        this.filteredDriverOptions = this.driverList.slice(0, 50);\r\n      }\r\n    },\r\n\r\n    // 搜索过滤逻辑\r\n    filterCarData(query) {\r\n      this.searchCarQuery = query;\r\n\r\n      if (this.searchCarQuery) {\r\n\r\n        this.filteredCarOptions = this.carList.filter(item =>\r\n          item.carNumber.includes(query)\r\n        );\r\n      } else {\r\n\r\n        this.filteredCarOptions = this.carList.slice(0, 50);\r\n      }\r\n    },\r\n\r\n    onAgreeClick() {\r\n      if (this.isAgreeEnabled) {\r\n        this.showDialog = false;\r\n        // 在这里可以添加用户同意后的逻辑\r\n        let param = {};\r\n        param.businessType = 1;\r\n        addNotify(param).then(response => {\r\n          // 处理 addNotify 接口成功的逻辑\r\n          console.log('addNotify success:', response);\r\n          // 可以在这里添加其他逻辑，比如提示用户操作成功\r\n        }).catch(error => {\r\n          // 处理 addNotify 接口失败的逻辑\r\n          console.error('addNotify failed:', error);\r\n          // 可以在这里添加其他逻辑，比如提示用户操作失败\r\n        });\r\n      }\r\n    },\r\n    openNewDriverWindow() {\r\n      const newWindowUrl = 'https://ydxt.citicsteel.com:8099/truckManage/xctgDriverUser'; // 替换为实际要跳转的页面 URL\r\n      window.open(newWindowUrl, '_blank'); // 打开新窗口并跳转至指定 URL\r\n    },\r\n    openNewCarWindow() {\r\n      const newWindowUrl = 'https://ydxt.citicsteel.com:8099/truckManage/xctgDriverCar'; // 替换为实际要跳转的页面 URL\r\n      window.open(newWindowUrl, '_blank'); // 打开新窗口并跳转至指定 URL\r\n    },\r\n    /** 查询批次列表 */\r\n    getList() {\r\n      this.loading = true;\r\n      let param = \"measureFlagTemp=\" + this.form.measureFlag;\r\n      listContract(param).then(response => {\r\n        let responseData = response.data;\r\n        this.handleResponseData(responseData);\r\n      });\r\n      // this.handleResponseData(this.responseData);\r\n      this.loading = false;\r\n    },\r\n    /** 查询司机信息列表 */\r\n    getDriverList() {\r\n      this.loading = true;\r\n      // listAllDriver().then(response => {\r\n      //   this.driverList = response.data;\r\n      //   this.loading = false;\r\n      // });\r\n      getXctgDriverUserList().then(response => {\r\n        this.driverList = response.data;\r\n        this.filteredDriverOptions = this.driverList,\r\n          this.loading = false;\r\n      });\r\n    },\r\n\r\n    /** 查询司机信息列表 */\r\n    getCarList() {\r\n      console.log(\"success\");\r\n      this.loading = true;\r\n      // listAllDriver().then(response => {\r\n      //   this.driverList = response.data;\r\n      //   this.loading = false;\r\n      // });\r\n      getXctgDriverCarList().then(response => {\r\n        this.carList = response.data;\r\n        this.filteredCarOptions = this.carList;\r\n        this.loading = false;\r\n      });\r\n    },\r\n\r\n\r\n\r\n    handleDriverChange() {\r\n      //通过driverId获取司机信息\r\n      if (this.form.driverId != null) {\r\n        this.driverList.forEach(item => {\r\n          if (item.id == this.form.driverId) {\r\n            this.form.name = item.name;\r\n            this.form.idCard = item.idCard;\r\n            this.form.company = item.company;\r\n            this.form.phone = item.phone;\r\n            this.form.photo = item.photo;\r\n            this.form.faceImgList = item.faceImgList;\r\n            this.form.driverLicenseImgs = item.driverLicenseImgs;\r\n            this.form.vehicleLicenseImgs = item.vehicleLicenseImgs;\r\n\r\n          }\r\n        });\r\n      }\r\n    },\r\n\r\n    handleCarChange() {\r\n      console.log(\"success\");\r\n      //通过driverId获取司机信息\r\n      if (this.form.carId != null) {\r\n        this.carList.forEach(item => {\r\n          if (item.id == this.form.carId) {\r\n            this.form.carNumber = item.carNumber;\r\n            if (item.vehicleEmissionStandards == 1) {\r\n              this.form.vehicleEmissionStandards = \"国五\";\r\n            } else if (item.vehicleEmissionStandards == 2) {\r\n              this.form.vehicleEmissionStandards = \"国六\";\r\n            } else if (item.vehicleEmissionStandards == 3) {\r\n              this.form.vehicleEmissionStandards = \"新能源\";\r\n            } else {\r\n              this.form.vehicleEmissionStandards = \"\";\r\n            }\r\n            // this.form.vehicleEmissionStandards = item.vehicleEmissionStandards;\r\n\r\n          }\r\n        });\r\n      }\r\n    },\r\n\r\n    //性别转换\r\n    sexFormat(sex) {\r\n      if (sex == 0) {\r\n        return \"未知\";\r\n      } else if (sex == 1) {\r\n        return \"男\";\r\n      } else if (sex == 2) {\r\n        return \"女\";\r\n      } else {\r\n        return \"\";\r\n      }\r\n\r\n    },\r\n\r\n    //车辆排放标准转换\r\n    vehicleEmissionStandardsFormat(vehicleEmissionStandards) {\r\n      if (vehicleEmissionStandards == 1) {\r\n        return \"国五\";\r\n      } else if (vehicleEmissionStandards == 2) {\r\n        return \"国六\";\r\n      } else if (vehicleEmissionStandards == 3) {\r\n        return \"新能源\";\r\n      } else {\r\n        return \"\";\r\n      }\r\n    },\r\n\r\n    // 处理请求数据\r\n    handleResponseData(response) {\r\n      this.contractData = [];\r\n      this.showContractData = [];\r\n      response.forEach(data => {\r\n        let contractInfo = this.contractObj();\r\n        contractInfo.contractNo = data.contractNo;\r\n        contractInfo.contractName = data.contractName;\r\n        contractInfo.validTime = data.validTime;\r\n        contractInfo.currentPage = 1;\r\n        contractInfo.pageSize = 10;\r\n        contractInfo.total = data.tdgcb03List.length;\r\n        contractInfo.isOpen = false; // 合同默认收起\r\n        contractInfo.itemSearchParam = { itemNo: \"\", itemName: \"\" };\r\n        if (data.tdgcb03List != null) {\r\n          data.tdgcb03List.forEach(item => {\r\n            let itemInfo = this.itemObj();\r\n            itemInfo.contractNo = data.contractNo;\r\n            itemInfo.contractName = data.contractName;\r\n            itemInfo.itemNo = item.itemNo;\r\n            itemInfo.itemName = item.tdgcb01.itemName;\r\n            itemInfo.itemSpec = item.tdgcb01.itemSpec;\r\n            itemInfo.measureUnit = item.tdgcb01.measureUnit;\r\n            itemInfo.factoryDesc = item.tdgcb01.factoryDesc;\r\n            itemInfo.productionLineDesc = item.tdgcb01.productionLineDesc;\r\n            itemInfo.status = item.status;\r\n            contractInfo.itemList.push(itemInfo);\r\n          })\r\n          contractInfo.searchItemList = contractInfo.itemList.concat();\r\n          contractInfo.showTableList = contractInfo.searchItemList.slice(0, contractInfo.pageSize)\r\n        }\r\n        this.contractData.push(contractInfo);\r\n      })\r\n      this.showContractData = this.contractData.concat();\r\n    },\r\n    // 同步选择信息数据\r\n    handleDifferentData() {\r\n      this.contractData.forEach(contract => {\r\n        this.contractList.forEach(c => {\r\n          console.log()\r\n          if (c.contractNo == contract.contractNo) {\r\n            contract.itemList.forEach(item => {\r\n              c.itemList.forEach(i => {\r\n                if (i.itemNo == item.itemNo) {\r\n                  console.log(\"befor\", item)\r\n                  item.amount = i.amount;\r\n                  item.supplyWeight = i.supplyWeight;\r\n                  console.log(\"after\", item)\r\n                }\r\n              })\r\n            })\r\n          }\r\n        })\r\n      })\r\n      this.itemDataChangeFlag = 0;\r\n    },\r\n\r\n    contractObj() {\r\n      return {\r\n        status: 1,\r\n        contractNo: null,\r\n        contractName: null,\r\n        validTime: null,\r\n        itemList: []\r\n      }\r\n    },\r\n\r\n    itemObj() {\r\n      return {\r\n        contractNo: null,  // 所属合同编号\r\n        contractName: null,   // 所属合同名\r\n        itemNo: null,   // 物资名称\r\n        itemName: null,  // 物资名称\r\n        amount: null,  // 数量\r\n        itemSpec: null,  // 物资规格\r\n        measureFlag: null, // 是否计量\r\n        measureUnit: null,  // 计量单位\r\n        supplyWeight: null //供货重量\r\n      }\r\n    },\r\n\r\n    itemRef(contract) {\r\n      return contract.contractNo + \"itemRef\"\r\n    },\r\n\r\n    /** 新增按钮操作 */\r\n    handleAddContract() {\r\n      // this.reset();\r\n      // this.contractSelection = JSON.parse(JSON.stringify(this.contractList));\r\n      // if(this.contractList.length > 0) this.contractSelection = this.contractList.concat();\r\n      if (this.contractList.length > 0) this.handleDifferentData();\r\n      // 处理已选\r\n      this.showContractData.forEach(c => {\r\n        if (c.isOpen) {\r\n          this.toggleSelection(c);\r\n        }\r\n      })\r\n      this.open = true;\r\n      this.title = \"选择物资\";\r\n    },\r\n\r\n    // 计量改变\r\n    measureChange(e) {\r\n      this.form.measureFlag = e;\r\n      let that = this;\r\n      if (this.contractList.length > 0) {\r\n        // 已选择物资\r\n        this.$confirm('改变是否计量将清除当前已选物资，是否继续\"?', \"警告\", {\r\n          confirmButtonText: \"确定\",\r\n          cancelButtonText: \"取消\",\r\n          type: \"warning\"\r\n        }).then(function () {\r\n          // 确定\r\n          console.log(\"确定\")\r\n          that.contractList = [];\r\n          that.selectData = {};\r\n          that.contractSelection = [];\r\n          that.getList();\r\n        }).catch(action => {\r\n          // 取消\r\n          if (action == \"cancel\") {\r\n            console.log(action);\r\n            if (this.form.measureFlag == 0) {\r\n              this.form.measureFlag = 1;\r\n            } else {\r\n              this.form.measureFlag = 0;\r\n            }\r\n          }\r\n        })\r\n      } else {\r\n        this.getList();\r\n      }\r\n    },\r\n\r\n    // 取消按钮\r\n    cancel() {\r\n      this.open = false;\r\n      // this.reset();\r\n    },\r\n\r\n    // 选择合同确定\r\n    submitContract() {\r\n      // console.log(this.contractSelection);\r\n      this.contractList = JSON.parse(JSON.stringify(this.contractSelection));\r\n      // this.contractList = this.contractSelection.concat();\r\n      // console.log(this.contractList);\r\n      this.handleEmptyContract();\r\n      this.open = false;\r\n    },\r\n    // 展开合同\r\n    openItemSelection(contract) {\r\n      contract.isOpen = true;\r\n      this.toggleSelection(contract);\r\n    },\r\n    // 收起合同\r\n    closeItemSelection(contract) {\r\n      contract.isOpen = false;\r\n    },\r\n    // 处理合同展开收起\r\n    handleItemOpenClose(contract) {\r\n      if (!contract.status) return;\r\n      if (contract.isOpen) {\r\n        this.closeItemSelection(contract);\r\n      } else {\r\n        this.openItemSelection(contract);\r\n      }\r\n    },\r\n\r\n    // 判断物资是否可选\r\n    itemIsSelectable(row, index) {\r\n      if (row.status) {\r\n        console.log(\"是\");\r\n        return true;\r\n      } else {\r\n        return false;\r\n      }\r\n    },\r\n\r\n    // 提交清单\r\n    submitSupply() {\r\n      if (this.contractList.length == 0) {\r\n        this.msgError(\"请添加物资\");\r\n        return;\r\n      }\r\n      this.$refs[\"form\"].validate(valid => {\r\n        if (valid) {\r\n          if (this.handleSubmitData()) {\r\n            // console.log(this.form);\r\n            if (this.form.measureFlag == 1) this.form.status = 11;\r\n            addBatch(this.form).then(response => {\r\n              // console.log(response);\r\n              if (response.code == 200) {\r\n                this.msgSuccess(\"添加成功\");\r\n                this.submitCancel();\r\n              }\r\n            })\r\n          }\r\n          ;\r\n        }\r\n      })\r\n    },\r\n\r\n    // 处理提交数据\r\n    handleSubmitData() {\r\n      let paramList = [];\r\n      let v = true;\r\n      this.$refs[\"itemform\"].forEach(f => {\r\n        f.validate(valid => {\r\n          if (!valid) {\r\n            v = false\r\n          }\r\n          ; //数量验证不通过\r\n        })\r\n      })\r\n      if (v) {\r\n        this.contractList.forEach((contract, index) => {\r\n          contract.itemList.forEach(item => {\r\n            paramList.push(item);\r\n          })\r\n        })\r\n      }\r\n      this.form.tdgcb05List = paramList;\r\n      return v;\r\n    },\r\n\r\n    // 物资选择改变事件\r\n    handleSelectionChange(e, data) {\r\n      let no = data.contractNo;\r\n      if (!this.selectData[`${no}`]) {\r\n        this.$set(this.selectData, `${no}`, []);\r\n      }\r\n      this.selectData[`${no}`] = e;\r\n      let flag = false; // 表示合同还未添加\r\n      this.contractSelection.forEach(contract => {\r\n        if (contract.contractNo == data.contractNo) {\r\n          flag = true;\r\n          // 已有合同则添加物资信息\r\n          // contract.itemList = this.selectData[`${no}`];\r\n          contract.itemList = e;\r\n        }\r\n      })\r\n      if (!flag) {\r\n        // 合同未添加则新增合同\r\n        let contractInfo = this.contractObj();\r\n        contractInfo.contractNo = data.contractNo;\r\n        contractInfo.contractName = data.contractName;\r\n        contractInfo.validTime = data.validTime;\r\n        contractInfo.measureFlag = data.measureFlag;\r\n        // contractInfo.itemList = this.selectData[`${no}`];\r\n        contractInfo.itemList = e;\r\n        this.contractSelection.push(contractInfo);\r\n      }\r\n    },\r\n\r\n    // 当前页选择\r\n    toggleSelection(contract) {\r\n      const no = contract.contractNo;\r\n      const rows = this.selectData[`${no}`];\r\n      console.log(rows)\r\n      this.$nextTick(() => {\r\n        if (rows) {\r\n          rows.forEach(row => {\r\n            if (this.delItemData.includes(row)) {\r\n              this.$refs[`${no}`][0].toggleRowSelection(row, false);\r\n              row.amount = null;\r\n              row.supplyWeight = null;\r\n              this.delItemData.splice(this.delItemData.indexOf(row), 1);\r\n            } else {\r\n              this.$refs[`${no}`][0].toggleRowSelection(row, true);\r\n            }\r\n          });\r\n        } else {\r\n          this.$refs[`${no}`][0].clearSelection();\r\n        }\r\n      })\r\n      console.log(\"del\", this.delItemData)\r\n    },\r\n\r\n    // 物资列表当前页改变\r\n    handleCurrentChange(currentPage, contract) {\r\n      contract.showTableList = contract.searchItemList.slice(contract.pageSize * (currentPage - 1), contract.pageSize * (currentPage - 1) + contract.pageSize);\r\n    },\r\n    // 合同搜索\r\n    handleContractSearch() {\r\n      let newlist = []\r\n      if (this.contractSearchParam.contractNo == \"\") {\r\n        this.showContractData = this.contractData.concat();\r\n      } else {\r\n        this.contractData.forEach(contract => {\r\n          if (contract.contractNo.includes(this.contractSearchParam.contractNo)) {\r\n            newlist.push(contract);\r\n          }\r\n        })\r\n        this.showContractData = newlist;\r\n      }\r\n      // 处理已选\r\n      this.showContractData.forEach(c => {\r\n        if (c.isOpen) {\r\n          this.toggleSelection(c);\r\n        }\r\n      })\r\n    },\r\n    /** 重置按钮操作 */\r\n    resetContractSearch() {\r\n      this.resetForm(\"contractSearchForm\");\r\n      this.handleContractSearch();\r\n    },\r\n\r\n    //物资搜索\r\n    handleItemSearch(contract) {\r\n      const itemSearchParam = contract.itemSearchParam;\r\n      let newItemList = [];\r\n      if (itemSearchParam.itemName == \"\" && itemSearchParam.itemNo == \"\") {\r\n        newItemList = contract.itemList;\r\n      } else {\r\n        contract.itemList.forEach(item => {\r\n          if (item.itemName.includes(itemSearchParam.itemName) && item.itemNo.includes(itemSearchParam.itemNo)) {\r\n            newItemList.push(item);\r\n          }\r\n        })\r\n      }\r\n      contract.searchItemList = newItemList;\r\n      contract.total = newItemList.length;\r\n      contract.currentPage = 1;\r\n      contract.showTableList = contract.searchItemList.slice(0, contract.pageSize);\r\n      // this.toggleSelection(contract);\r\n    },\r\n    // 物资重置\r\n    resetItemSearch(contract) {\r\n      const itemRef = this.itemRef(contract);\r\n      this.$refs[`${itemRef}`][0].resetFields();\r\n      this.handleItemSearch(contract);\r\n    },\r\n\r\n    //  获取行键\r\n    getRowKeys(row) {\r\n      return row.itemNo;\r\n    },\r\n\r\n    // 物资删除点击事件\r\n    handleItemDel(contract, itemNo) {\r\n      // 物资列表中删除\r\n      this.contractList.forEach(c => {\r\n        if (c.contractNo == contract.contractNo) {\r\n          c.itemList.forEach((i, index) => {\r\n            if (i.itemNo == itemNo) {\r\n              c.itemList.splice(index, 1);\r\n            }\r\n          })\r\n        }\r\n      })\r\n      // 已选项中删除\r\n      Object.keys(this.selectData).forEach(no => {\r\n        if (no == contract.contractNo) {\r\n          this.selectData[`${no}`].forEach(i => {\r\n            if (i.itemNo == itemNo) {\r\n              this.delItemData.push(i);\r\n            }\r\n          })\r\n        }\r\n      })\r\n      this.handleEmptyContract();\r\n    },\r\n    // 处理空合同\r\n    handleEmptyContract() {\r\n      this.contractList = this.contractList.filter(c => c.itemList.length != 0)\r\n    }\r\n  },\r\n}\r\n</script>\r\n"]}]}