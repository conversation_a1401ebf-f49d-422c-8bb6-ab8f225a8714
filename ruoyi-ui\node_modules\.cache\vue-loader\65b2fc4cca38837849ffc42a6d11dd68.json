{"remainingRequest": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\src\\views\\dataReport\\answer\\answerForm.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\src\\views\\dataReport\\answer\\answerForm.vue", "mtime": 1755499162044}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgeyBhbnN3ZXJMaXN0UGx1c0FsbCxmb3JtRnJlcXVlbmN5IH0gZnJvbSAiQC9hcGkvdFlqeS9mb3JtIjsNCmltcG9ydCBhbnN3ZXJJbnB1dCBmcm9tICIuL2lucHV0IjsNCmltcG9ydCB7IG5ld0FkZCwgYWRkQWxvbmUgfSBmcm9tICJAL2FwaS90WWp5L2Fuc3dlciI7DQppbXBvcnQgeyBnZXRBbGxSb290TGlzdEZvckFuc3dlciB9IGZyb20gIkAvYXBpL3RZankvZGltZW5zaW9uYWxpdHkiOw0KaW1wb3J0IHsgZ2V0VG9rZW4gfSBmcm9tICJAL3V0aWxzL2F1dGgiOw0KaW1wb3J0IGF4aW9zIGZyb20gImF4aW9zIjsNCmltcG9ydCAqIGFzIHhsc3ggZnJvbSAneGxzeCc7DQoNCi8v5byV5YWl55u45YWz5qC35byPDQoNCmV4cG9ydCBkZWZhdWx0IHsNCiAgbmFtZTogIkFuc3dlciIsDQogIGNvbXBvbmVudHM6IHsgYW5zd2VySW5wdXQgfSwNCiAgZGF0YSgpIHsNCiAgICByZXR1cm4gew0KICAgICAgcGlja2VyT3B0aW9uczogew0KICAgICAgICBkaXNhYmxlZERhdGUodGltZSkgew0KICAgICAgICAgIHJldHVybiB0aW1lLmdldFRpbWUoKSA+IERhdGUubm93KCk7DQogICAgICAgIH0sDQogICAgICB9LA0KICAgICAgZnJlcXVlbmN5T3B0aW9uczogW10sDQoNCiAgICAgIHF1ZXJ5UGFyYW1zOiB7DQogICAgICAgIGZvcm1RdWVzdGlvbjogdW5kZWZpbmVkLA0KICAgICAgICBmY0RhdGU6IHVuZGVmaW5lZCwNCiAgICAgICAgZGltZW5zaW9uYWxpdHlJZDogdW5kZWZpbmVkLA0KICAgICAgICBmb3JtUXVlc3Rpb246IHVuZGVmaW5lZCwNCiAgICAgIH0sDQoNCiAgICAgIGZvcm1UeXBlOiBudWxsLA0KICAgICAgZGltZW5zaW9uYWxpdHlOYW1lczogbnVsbCwNCiAgICAgIGRyYXdlclNob3c6IGZhbHNlLA0KICAgICAgc3RpY2t5VG9wOiAwLA0KICAgICAgbG9hZGluZzogZmFsc2UsDQogICAgICBzaG93U2VhcmNoOiB0cnVlLA0KICAgICAgYW5zd2VyTGlzdDogW10sDQogICAgICBmb3JtRGF0YToge30sDQogICAgICByb3c6IHt9LA0KICAgICAgcm9vdExpc3Q6IFtdLA0KICAgICAgdXNlckluZm86IHt9LA0KICAgICAgZGF0ZXNhdmU6e30sDQogICAgICBwYXRoc2F2ZTp7fSwNCiAgICAgIGNvdW50OjEsDQogICAgICBkZXB0TmFtZTpudWxsLA0KICAgICAgZGltZW5zaW9uYWxpdHlOYW1lOm51bGwsDQogICAgICBkYXRlVmFsdWU6IG51bGwsDQogICAgICBzcGVjaWFsRmNEYXRlOm51bGwsDQogICAgICBxdWVyeUltcG9ydDogew0KICAgICAgICBzdGFydERhdGU6IG51bGwsDQogICAgICAgIGVuZERhdGU6IG51bGwsDQogICAgICAgIHJvb3RJZDogbnVsbCwNCiAgICAgIH0sDQogICAgICBpbXBvcnRPcGVuOmZhbHNlLA0KICAgICAgU3BlY2lhbEltcG9ydE9wZW46ZmFsc2UsDQogICAgICAvLyDlr7zlhaXlj4LmlbANCiAgICAgIHVwbG9hZDogew0KICAgICAgICAvLyDmmK/lkKbnpoHnlKjkuIrkvKANCiAgICAgICAgaXNVcGxvYWRpbmc6IGZhbHNlLA0KICAgICAgICAvLyDorr7nva7kuIrkvKDnmoTor7fmsYLlpLTpg6gNCiAgICAgICAgaGVhZGVyczogeyBBdXRob3JpemF0aW9uOiAiQmVhcmVyICIgKyBnZXRUb2tlbigpIH0sDQogICAgICAgIC8vIOS4iuS8oOeahOWcsOWdgA0KICAgICAgICB1cmw6DQogICAgICAgICAgcHJvY2Vzcy5lbnYuVlVFX0FQUF9CQVNFX0FQSSArDQogICAgICAgICAgIi93ZWIvVFlqeS9hbnN3ZXIvaW1wb3J0RGF0YSIsDQogICAgICB9LA0KDQoNCiAgICAgIHVwbG9hZFNwZWNpYWw6IHsNCiAgICAgICAgLy8g5piv5ZCm56aB55So5LiK5LygDQogICAgICAgIGlzVXBsb2FkaW5nOiBmYWxzZSwNCiAgICAgICAgLy8g6K6+572u5LiK5Lyg55qE6K+35rGC5aS06YOoDQogICAgICAgIGhlYWRlcnM6IHsgQXV0aG9yaXphdGlvbjogIkJlYXJlciAiICsgZ2V0VG9rZW4oKSB9LA0KICAgICAgICAvLyDkuIrkvKDnmoTlnLDlnYANCiAgICAgICAgdXJsOg0KICAgICAgICAgIHByb2Nlc3MuZW52LlZVRV9BUFBfQkFTRV9BUEkgKw0KICAgICAgICAgICIvd2ViL1RZankvYW5zd2VyL2ltcG9ydERhdGFTcGVjaWFsIiwNCg0KICAgICAgfSwNCiAgICAgIGV4Y2VsSHRtbDoiIiwNCiAgICAgIHNlYXJjaG9wZW46ZmFsc2UsDQogICAgICBleGNlbERhdGE6IFtdLCAvLyDlrZjlgqggRXhjZWwg5pWw5o2uDQogICAgICBleGNlbHRpdGxlOiBbXSwNCiAgICAgIGN1c3RvbUJsb2JDb250ZW50OiIiDQogICAgfTsNCiAgfSwNCiAgbW91bnRlZCgpIHsNCiAgICB0aGlzLnVzZXJJbmZvID0gSlNPTi5wYXJzZShKU09OLnN0cmluZ2lmeSh0aGlzLiRzdG9yZS5zdGF0ZS51c2VyKSk7DQogIH0sDQoNCiAgY3JlYXRlZCgpIHsNCiAgICBjb25zdCBkaW1lbnNpb25hbGl0eUlkID0gdGhpcy4kcm91dGUucXVlcnkgJiYgdGhpcy4kcm91dGUucXVlcnkuZGltZW5zaW9uYWxpdHlJZDsNCiAgICBjb25zdCBmY0RhdGUgPSB0aGlzLiRyb3V0ZS5xdWVyeSAmJiB0aGlzLiRyb3V0ZS5xdWVyeS5mY0RhdGU7DQogICAgdGhpcy5xdWVyeVBhcmFtcy5kaW1lbnNpb25hbGl0eUlkPWRpbWVuc2lvbmFsaXR5SWQNCiAgICB0aGlzLnF1ZXJ5UGFyYW1zLmZjRGF0ZT1mY0RhdGUNCiAgICB0aGlzLmluaXREYXRhKCk7DQoNCg0KICAgIC8vIGlmKHRoaXMuJHJvdXRlLnF1ZXJ5KQ0KICAgIC8vIHsNCiAgICAvLyAgIGNvbnN0IGRpbWVuc2lvbmFsaXR5SWQgPSB0aGlzLiRyb3V0ZS5xdWVyeSAmJiB0aGlzLiRyb3V0ZS5xdWVyeS5kaW1lbnNpb25hbGl0eUlkOw0KICAgIC8vICAgLy8gY29uc3QgZGltZW5zaW9uYWxpdHlOYW1lID0gdGhpcy4kcm91dGUucXVlcnkgJiYgdGhpcy4kcm91dGUucXVlcnkuZGltZW5zaW9uYWxpdHlOYW1lOw0KICAgIC8vICAgY29uc3QgZmNEYXRlID0gdGhpcy4kcm91dGUucXVlcnkgJiYgdGhpcy4kcm91dGUucXVlcnkuZmNEYXRlOw0KICAgIC8vICAgdGhpcy5xdWVyeVBhcmFtcy5kaW1lbnNpb25hbGl0eUlkPWRpbWVuc2lvbmFsaXR5SWQNCiAgICAvLyAgIC8vIHRoaXMucXVlcnlQYXJhbXMuZGltZW5zaW9uYWxpdHlOYW1lPWRpbWVuc2lvbmFsaXR5TmFtZQ0KICAgIC8vICAgdGhpcy5xdWVyeVBhcmFtcy5mY0RhdGU9ZmNEYXRlDQogICAgLy8gICB0aGlzLmluaXREYXRhMSgpOw0KICAgIC8vIH0NCiAgICAvLyBlbHNlDQogICAgLy8gew0KICAgIC8vICAgdGhpcy5pbml0RGF0YSgpOw0KICAgIC8vIH0NCiAgfSwNCiAgbWV0aG9kczogew0KICAgIG9uRGF0ZUNoYW5nZSgpIHsNCiAgICAgIGNvbnNvbGUubG9nKHRoaXMuZGF0ZVZhbHVlKTsNCiAgICAgIGlmICh0aGlzLmRhdGVWYWx1ZSAhPSBudWxsICYmIHRoaXMuZGF0ZVZhbHVlICE9ICIiKSB7DQogICAgICAgIHRoaXMucXVlcnlJbXBvcnQuc3RhcnREYXRlID0gdGhpcy5kYXRlVmFsdWVbMF07DQogICAgICAgIHRoaXMucXVlcnlJbXBvcnQuZW5kRGF0ZSA9IHRoaXMuZGF0ZVZhbHVlWzFdOw0KICAgICAgfSBlbHNlIHsNCiAgICAgICAgdGhpcy5xdWVyeUltcG9ydC5zdGFydERhdGUgPSAiIjsNCiAgICAgICAgdGhpcy5xdWVyeUltcG9ydC5lbmREYXRlID0gIiI7DQogICAgICB9DQogICAgfSwNCiAgICBjbGlja05vZGUoJGV2ZW50LCBub2RlKSB7DQogICAgICAkZXZlbnQudGFyZ2V0LnBhcmVudEVsZW1lbnQucGFyZW50RWxlbWVudC5maXJzdEVsZW1lbnRDaGlsZC5jbGljaygpOw0KICAgIH0sDQogICAgY2hhbmdlRXZlbnQocGFyYW1zKSB7DQogICAgICBjb25zdCAkZm9ybSA9IHRoaXMuJHJlZnMuZm9ybVJlZjsNCiAgICAgIGlmICgkZm9ybSkgew0KICAgICAgICAkZm9ybS51cGRhdGVTdGF0dXMocGFyYW1zKTsNCiAgICAgIH0NCiAgICB9LA0KICAgIGRpc2FibGVkRGF0ZSh0aW1lKSB7DQogICAgICByZXR1cm4gdGltZS5nZXRUaW1lKCkgPCBEYXRlLm5vdygpIC0gOC42NGU3OyAvLyA4LjY0ZTcg5q+r56eS5pWw5Luj6KGo5LiA5aSpDQogICAgfSwNCiAgICBpbnB1dENoYW5nZSh2YWwsIHJvdykgew0KICAgICAgcm93LmZvcm1WYWx1ZSA9IHZhbDsNCiAgICB9LA0KICAgIGhhbmRsZVNjcm9sbCgpIHsNCiAgICAgIHRoaXMuaXNTdGlja3kgPSB3aW5kb3cuc2Nyb2xsWSA+PSB0aGlzLnN0aWNreVRvcDsNCiAgICB9LA0KICAgIGluaXREYXRhKCkgew0KICAgICAgZ2V0QWxsUm9vdExpc3RGb3JBbnN3ZXIoKS50aGVuKChyZXMpID0+IHsNCiAgICAgICAgdGhpcy5yb290TGlzdCA9IHJlcy5kYXRhOw0KICAgICAgICBpZih0aGlzLnF1ZXJ5UGFyYW1zLmRpbWVuc2lvbmFsaXR5SWQ9PW51bGwpDQogICAgICAgIHsNCiAgICAgICAgICB0aGlzLnF1ZXJ5UGFyYW1zLmRpbWVuc2lvbmFsaXR5SWQgPSB0aGlzLnJvb3RMaXN0WzBdLnZhbHVlOw0KICAgICAgICAgIHRoaXMuZGVwdE5hbWU9IHRoaXMucm9vdExpc3RbMF0uZGVwdE5hbWU7DQogICAgICAgICAgdGhpcy5kZXB0Q29kZT0gdGhpcy5yb290TGlzdFswXS5kZXB0Q29kZTsNCiAgICAgICAgICB0aGlzLmRpbWVuc2lvbmFsaXR5TmFtZT10aGlzLnJvb3RMaXN0WzBdLmxhYmVsDQogICAgICAgIH0NCiAgICAgICAgZWxzZQ0KICAgICAgICB7DQogICAgICAgICAgLy8gdGhpcy5xdWVyeVBhcmFtcy5kaW1lbnNpb25hbGl0eUlkID0gdGhpcy5xdWVyeVBhcmFtcy5kaW1lbnNpb25hbGl0eUlkOw0KICAgICAgICAgIGZvcihsZXQgaT0wO2k8dGhpcy5yb290TGlzdC5sZW5ndGg7aSsrKQ0KICAgICAgICAgIHsNCiAgICAgICAgICAgIGlmKHRoaXMucXVlcnlQYXJhbXMuZGltZW5zaW9uYWxpdHlJZCA9PSB0aGlzLnJvb3RMaXN0W2ldLnZhbHVlKQ0KICAgICAgICAgICAgew0KICAgICAgICAgICAgICB0aGlzLnF1ZXJ5UGFyYW1zLmRpbWVuc2lvbmFsaXR5SWQgPSB0aGlzLnJvb3RMaXN0W2ldLnZhbHVlOw0KICAgICAgICAgICAgICB0aGlzLmRlcHROYW1lPSB0aGlzLnJvb3RMaXN0W2ldLmRlcHROYW1lOw0KICAgICAgICAgICAgICB0aGlzLmRlcHRDb2RlPSB0aGlzLnJvb3RMaXN0W2ldLmRlcHRDb2RlOw0KICAgICAgICAgICAgICB0aGlzLmRpbWVuc2lvbmFsaXR5TmFtZT10aGlzLnJvb3RMaXN0W2ldLmxhYmVsDQogICAgICAgICAgICB9DQogICAgICAgICAgfQ0KICAgICAgICB9DQogICAgICAgIHRoaXMuZ2V0TGlzdCgpOw0KICAgICAgfSk7DQogICAgfSwNCiAgICBpbml0RGF0YTEoKSB7DQogICAgICBnZXRBbGxSb290TGlzdEZvckFuc3dlcigpLnRoZW4oKHJlcykgPT4gew0KICAgICAgICB0aGlzLnJvb3RMaXN0ID0gcmVzLmRhdGE7DQogICAgICAgIGZvcihsZXQgaT0wO2k8dGhpcy5yb290TGlzdC5sZW5ndGg7aSsrKQ0KICAgICAgICB7DQogICAgICAgICAgaWYodGhpcy5xdWVyeVBhcmFtcy5kaW1lbnNpb25hbGl0eUlkID09IHRoaXMucm9vdExpc3RbaV0udmFsdWUpDQogICAgICAgICAgew0KICAgICAgICAgICAgdGhpcy5kZXB0TmFtZT0gdGhpcy5yb290TGlzdFswXS5kZXB0TmFtZTsNCiAgICAgICAgICB9DQogICAgICAgIH0NCiAgICAgICAgdGhpcy5nZXRMaXN0KCk7DQogICAgICB9KTsNCiAgICB9LA0KICAgIGNvbnRhaW5zU3Vic3RyaW5nKHN1YnN0cmluZywgc3RyaW5nKSB7DQogICAgICByZXR1cm4gc3RyaW5nLmluY2x1ZGVzKHN1YnN0cmluZyk7DQogICAgfSwNCiAgICBhbG9uZUxpc3Qoc3RyaW5nKSB7DQogICAgICBpZihzdHJpbmc9PSAn5rCU5L2T57uT566X5pyI5oqlJykNCiAgICAgIHsNCiAgICAgICAgcmV0dXJuIHRydWU7DQogICAgICB9DQogICAgICBpZihzdHJpbmc9PSAn6auY54KJ44CB6L2s54KJ54Wk5rCU5pyI5oql6KGoJykNCiAgICAgIHsNCiAgICAgICAgcmV0dXJuIHRydWU7DQogICAgICB9DQogICAgICBpZihzdHJpbmc9PSAn5aSp54S25rCU5raI6ICX5pyI5oql6KGoJykNCiAgICAgIHsNCiAgICAgICAgcmV0dXJuIHRydWU7DQogICAgICB9DQogICAgICBpZihzdHJpbmc9PSAn6JK45rG95raI6ICX5pyI5oql6KGoJykNCiAgICAgIHsNCiAgICAgICAgcmV0dXJuIHRydWU7DQogICAgICB9DQogICAgICBpZihzdHJpbmc9PSAn55S16YeP5pyI5oql6KGoJykNCiAgICAgIHsNCiAgICAgICAgcmV0dXJuIHRydWU7DQogICAgICB9DQogICAgICBpZihzdHJpbmc9PSAn54m55p2/5LqL5Lia6YOoMjAyNeW5tOe7j+a1jui0o+S7u+WItuWllue9muaxh+aAuycpDQogICAgICB7DQogICAgICAgIHJldHVybiB0cnVlOw0KICAgICAgfQ0KICAgICAgLy8gaWYoc3RyaW5nPT0gJ+eglOeptumZouebruagh+aMh+agh+S4gOiniCcpDQogICAgICAvLyB7DQogICAgICAvLyAgIHJldHVybiB0cnVlOw0KICAgICAgLy8gfQ0KICAgICAgcmV0dXJuIGZhbHNlOw0KICAgIH0sDQoNCiAgICAvKiog5p+l6K+iVFlqeUFuc3dlcuWIl+ihqCAqLw0KICAgIGdldExpc3QoKSB7DQogICAgICBmb3JtRnJlcXVlbmN5KHtkaW1lbnNpb25hbGl0eUlkOiB0aGlzLnF1ZXJ5UGFyYW1zLmRpbWVuc2lvbmFsaXR5SWR9KS50aGVuKChyZXMpID0+IHsNCiAgICAgICAgICAgaWYodGhpcy5jb3VudCE9cmVzLmRhdGEpDQogICAgICAgICAgIHsNCiAgICAgICAgICAgIHRoaXMucXVlcnlQYXJhbXMuZmNEYXRlPXVuZGVmaW5lZA0KICAgICAgICAgICB9DQogICAgICAgICAgIHRoaXMuY291bnQ9cmVzLmRhdGENCiAgICAgIH0pOw0KDQogICAgICB0aGlzLmFuc3dlckxpc3QgPSBbXTsNCiAgICAgIGFuc3dlckxpc3RQbHVzQWxsKHsNCiAgICAgICAgZmNEYXRlOiB0aGlzLnF1ZXJ5UGFyYW1zLmZjRGF0ZSwNCiAgICAgICAgZGltZW5zaW9uYWxpdHlJZDogdGhpcy5xdWVyeVBhcmFtcy5kaW1lbnNpb25hbGl0eUlkLA0KICAgICAgICBmb3JtUXVlc3Rpb246IHRoaXMucXVlcnlQYXJhbXMuZm9ybVF1ZXN0aW9uLA0KICAgICAgfSkudGhlbigocmVzKSA9PiB7DQogICAgICAgIGxldCBhbnN3ZXJMaXN0ID0gW107DQogICAgICAgIGxldCBsaXN0ID0gcmVzLmRhdGE7DQogICAgICAgIGZvcihsZXQgaT0wO2k8dGhpcy5yb290TGlzdC5sZW5ndGg7aSsrKQ0KICAgICAgICB7DQogICAgICAgICAgaWYodGhpcy5xdWVyeVBhcmFtcy5kaW1lbnNpb25hbGl0eUlkID09IHRoaXMucm9vdExpc3RbaV0udmFsdWUpDQogICAgICAgICAgew0KICAgICAgICAgICAgdGhpcy5xdWVyeVBhcmFtcy5kaW1lbnNpb25hbGl0eUlkID0gdGhpcy5yb290TGlzdFtpXS52YWx1ZTsNCiAgICAgICAgICAgIHRoaXMuZGVwdE5hbWU9IHRoaXMucm9vdExpc3RbaV0uZGVwdE5hbWU7DQogICAgICAgICAgICB0aGlzLmRlcHRDb2RlPSB0aGlzLnJvb3RMaXN0W2ldLmRlcHRDb2RlOw0KICAgICAgICAgICAgdGhpcy5kaW1lbnNpb25hbGl0eU5hbWU9dGhpcy5yb290TGlzdFtpXS5sYWJlbA0KICAgICAgICAgIH0NCiAgICAgICAgfQ0KICAgICAgICBpZih0aGlzLmNvbnRhaW5zU3Vic3RyaW5nKCflronlhajotKPku7vlt6XotYTogIPmoLjooagnLHRoaXMuZGltZW5zaW9uYWxpdHlOYW1lKSkNCiAgICAgICAgew0KICAgICAgICAgIGNvbnNvbGUubG9nKCJ0ZXN0MSIpDQogICAgICAgICAgbGV0IG51bT0wDQogICAgICAgICAgZm9yKGxldCBpPTA7aTxsaXN0Lmxlbmd0aDtpKyspDQogICAgICAgICAgew0KICAgICAgICAgICAgaWYobGlzdFtpXS5mb3JtUXVlc3Rpb24hPSfoh6ror4TmgLvliIYnICYmIGxpc3RbaV0uZm9ybVF1ZXN0aW9uIT0n5Y6C6ZW/6K+E5YiGJykNCiAgICAgICAgICAgIHsNCiAgICAgICAgICAgICAgbnVtPTE7DQogICAgICAgICAgICAgIGNvbnNvbGUubG9nKCJ0ZXN0MiIsbGlzdFtpXS5mb3JtUXVlc3Rpb24pDQogICAgICAgICAgICAgIGJyZWFrOw0KICAgICAgICAgICAgfQ0KICAgICAgICAgIH0NCiAgICAgICAgICBpZihudW09PTApDQogICAgICAgICAgew0KICAgICAgICAgICAgZm9yKGxldCBpPTA7aTxsaXN0Lmxlbmd0aDtpKyspDQogICAgICAgICAgICB7DQogICAgICAgICAgICAgIGxpc3RbaV0uZGltZW5zaW9uYWxpdHlOYW1lPWxpc3RbaV0uZGltZW5zaW9uYWxpdHlOYW1lLnJlcGxhY2UoJy/kuIPjgIHogIPmoLjor4TliIYnLCAnJykNCiAgICAgICAgICAgIH0NCiAgICAgICAgICAgIGNvbnNvbGUubG9nKCJ0ZXN0MyIsbGlzdCkNCiAgICAgICAgICB9DQogICAgICAgIH0NCiAgICAgICAgDQogICAgICAgIGZvcihsZXQgaT0wO2k8bGlzdC5sZW5ndGg7aSsrKQ0KICAgICAgICB7DQogICAgICAgICAgICB0aGlzLmRhdGVzYXZlW2xpc3RbaV0uZm9ybUlkXT1saXN0W2ldLmZvcm1WYWx1ZQ0KICAgICAgICAgICAgdGhpcy5wYXRoc2F2ZVtsaXN0W2ldLmRpbWVuc2lvbmFsaXR5TmFtZV09bGlzdFtpXS5kaW1lbnNpb25hbGl0eVBhdGgNCiAgICAgICAgfQ0KICAgICAgICAvLyDkvb/nlKggbWFwIOaPkOWPliBkaW1lbnNpb25hbGl0eU5hbWUg5bGe5oCn5Yiw5LiA5Liq5pWw57uEDQogICAgICAgIGxldCBkaW1lbnNpb25hbGl0eU5hbWVzID0gbGlzdC5tYXAoKHgpID0+IHguZGltZW5zaW9uYWxpdHlOYW1lKTsNCg0KICAgICAgICAvLyDmj5Dlj5YgLyDlkI7nmoTliY3kuInkvY3lrZfnrKbvvIzlubbkuI7ljp/lrZfnrKbkuLLphY3lr7kNCiAgICAgICAgZGltZW5zaW9uYWxpdHlOYW1lcyA9IGRpbWVuc2lvbmFsaXR5TmFtZXMubWFwKChuYW1lKSA9PiB7DQogICAgICAgICAgLy8gbGV0IGtleSA9IG5hbWUuaW5jbHVkZXMoIi8iKSA/IG5hbWUuc3BsaXQoIi8iKVsxXS5zbGljZSgwLCAzKSA6ICIiOw0KICAgICAgICAgIGxldCBrZXkgPSB0aGlzLnBhdGhzYXZlW25hbWVdOw0KICAgICAgICAgIHJldHVybiB7IG9yaWdpbmFsTmFtZTogbmFtZSwgc29ydEtleToga2V5IH07DQogICAgICAgIH0pOw0KDQogICAgICAgIC8vIOaMieeFp+aPkOWPluWHuueahOWJjeS4ieWtl+espuaOkuW6jw0KICAgICAgICBkaW1lbnNpb25hbGl0eU5hbWVzLnNvcnQoKGEsIGIpID0+IGEuc29ydEtleS5sb2NhbGVDb21wYXJlKGIuc29ydEtleSkpOw0KICAgICAgICAvLyBjb25zb2xlLmxvZygidGVzdDAiLGRpbWVuc2lvbmFsaXR5TmFtZXMpDQogICAgICAgIC8vIOWmguaenOmcgOimge+8jOWPr+S7peaPkOWPluaOkuW6j+WQjueahOWOn+Wni+WQjeWtlw0KICAgICAgICBkaW1lbnNpb25hbGl0eU5hbWVzID0gZGltZW5zaW9uYWxpdHlOYW1lcy5tYXAoDQogICAgICAgICAgKGl0ZW0pID0+IGl0ZW0ub3JpZ2luYWxOYW1lDQogICAgICAgICk7DQoNCiAgICAgICAgLy8g5L2/55SoIFNldCDljrvph40NCiAgICAgICAgbGV0IHVuaXF1ZURpbWVuc2lvbmFsaXR5TmFtZXMgPSBbLi4ubmV3IFNldChkaW1lbnNpb25hbGl0eU5hbWVzKV07DQoNCiAgICAgICAgdW5pcXVlRGltZW5zaW9uYWxpdHlOYW1lcy5mb3JFYWNoKCh0aXRsZSkgPT4gew0KICAgICAgICAgIGxldCBncm91cCA9IHsNCiAgICAgICAgICAgIHRpdGxlOiAiIiwNCiAgICAgICAgICAgIGxpc3Q6IFtdLA0KICAgICAgICAgIH07DQogICAgICAgICAgZ3JvdXAudGl0bGUgPSB0aXRsZTsNCiAgICAgICAgICBncm91cC5saXN0ID0gbGlzdC5maWx0ZXIoKGl0ZW0pID0+IGl0ZW0uZGltZW5zaW9uYWxpdHlOYW1lID09PSB0aXRsZSk7DQogICAgICAgICAgLy8g5YGH6K6+5L2g5pyJ5LiA5Liq5pWw57uE5p2l5a2Y5YKo5omA5pyJ55qE57uEDQogICAgICAgICAgYW5zd2VyTGlzdC5wdXNoKGdyb3VwKTsgLy8g5bCG55Sf5oiQ55qE57uE5re75Yqg5YiwZ3JvdXBz5pWw57uE5LitDQogICAgICAgIH0pOw0KICAgICAgICB0aGlzLmFuc3dlckxpc3QgPSBhbnN3ZXJMaXN0Ow0KICAgICAgICAvLyBjb25zb2xlLmxvZygidGVzdDExMSIsYW5zd2VyTGlzdCkNCiAgICAgICAgdGhpcy4kZm9yY2VVcGRhdGUoKTsNCiAgICAgIH0pOw0KICAgIH0sDQogICAgaGFuZGxlUXVlcnkoKSB7DQogICAgICBmb3IobGV0IGk9MDtpPHRoaXMucm9vdExpc3QubGVuZ3RoO2krKykNCiAgICAgIHsNCiAgICAgICAgaWYodGhpcy5xdWVyeVBhcmFtcy5kaW1lbnNpb25hbGl0eUlkID09IHRoaXMucm9vdExpc3RbaV0udmFsdWUpDQogICAgICAgIHsNCiAgICAgICAgICB0aGlzLmRlcHROYW1lPSB0aGlzLnJvb3RMaXN0W2ldLmRlcHROYW1lOw0KICAgICAgICAgIHRoaXMuZGVwdENvZGU9IHRoaXMucm9vdExpc3RbaV0uZGVwdENvZGU7DQogICAgICAgICAgdGhpcy5kaW1lbnNpb25hbGl0eU5hbWU9dGhpcy5yb290TGlzdFtpXS5sYWJlbA0KICAgICAgICB9DQogICAgICB9DQogICAgICB0aGlzLmdldExpc3QoKTsNCiAgICB9LA0KICAgIGhhbmRsZVByZXZpZXcoKSB7DQogICAgICBsZXQgcXVlcnlJbXBvcnQ9e30NCiAgICAgIHF1ZXJ5SW1wb3J0LnJvb3RJZCA9IHRoaXMucXVlcnlQYXJhbXMuZGltZW5zaW9uYWxpdHlJZA0KICAgICAgcXVlcnlJbXBvcnQuZmNEYXRlID0gdGhpcy5xdWVyeVBhcmFtcy5mY0RhdGUNCiAgICAgIHF1ZXJ5SW1wb3J0LnR5cGU9IjAiDQogICAgICBpZih0aGlzLmRpbWVuc2lvbmFsaXR5TmFtZT09J+eglOeptumZouebruagh+aMh+agh+S4gOiniCcpDQogICAgICB7DQogICAgICAgIHRoaXMuZG93bmxvYWRYbHN4KA0KICAgICAgICAiL3dlYi9UWWp5L2Fuc3dlci9leHBvcnRXaXRoVGVtcGxhdGUiLA0KICAgICAgICB7DQogICAgICAgICAgLi4ucXVlcnlJbXBvcnQsDQogICAgICAgIH0sDQogICAgICAgIHRoaXMuZGltZW5zaW9uYWxpdHlOYW1lKyIoIiArdGhpcy5zcGVjaWFsRmNEYXRlKw0KICAgICAgICAgICIpIiArDQogICAgICAgICAgYOaVsOaNri54bHN4YA0KICAgICAgKS50aGVuKChibG9iKSA9PiB7DQogICAgICAgIGxldCByZWFkZXIgPSBuZXcgRmlsZVJlYWRlcigpOw0KICAgICAgICByZWFkZXIucmVhZEFzQXJyYXlCdWZmZXIoYmxvYik7DQogICAgICAgIHJlYWRlci5vbmxvYWQgPSAoZXZ0KSA9PiB7DQogICAgICAgICAgdGhpcy5jdXN0b21CbG9iQ29udGVudD1yZWFkZXIucmVzdWx0Ow0KICAgICAgICAgIGxldCBpbnRzID0gbmV3IFVpbnQ4QXJyYXkoZXZ0LnRhcmdldC5yZXN1bHQpOyAvL+imgeS9v+eUqOivu+WPlueahOWGheWuue+8jOaJgOS7peWwhuivu+WPluWGheWuuei9rOWMluaIkFVpbnQ4QXJyYXkNCiAgICAgICAgICBpbnRzID0gaW50cy5zbGljZSgwLCBibG9iLnNpemUpOw0KICAgICAgICAgIGxldCB3b3JrQm9vayA9IHhsc3gucmVhZChpbnRzLCB7IHR5cGU6ICJhcnJheSIgfSk7DQogICAgICAgICAgbGV0IHNoZWV0TmFtZXMgPSB3b3JrQm9vay5TaGVldE5hbWVzOw0KICAgICAgICAgIGxldCBzaGVldE5hbWUgPSBzaGVldE5hbWVzWzBdOw0KICAgICAgICAgIGxldCB3b3JrU2hlZXQgPSB3b3JrQm9vay5TaGVldHNbc2hlZXROYW1lXTsNCiAgICAgICAgICAvL+iOt+WPlkV4Y2xl5YaF5a6577yM5bm25bCG56m65YaF5a6555So56m65YC85L+d5a2YDQogICAgICAgICAgbGV0IGV4Y2VsVGFibGUgPSB4bHN4LnV0aWxzLnNoZWV0X3RvX2pzb24od29ya1NoZWV0KTsNCiAgICAgICAgICAvLyDojrflj5ZFeGNlbOWktOmDqA0KICAgICAgICAgIGxldCB0YWJsZVRoZWFkID0gQXJyYXkuZnJvbShPYmplY3Qua2V5cyhleGNlbFRhYmxlWzBdKSkubWFwKA0KICAgICAgICAgICAgKGl0ZW0pID0+IHsNCiAgICAgICAgICAgICAgcmV0dXJuIGl0ZW0NCiAgICAgICAgICAgIH0NCiAgICAgICAgICApOw0KICAgICAgICAgIHRoaXMuZXhjZWxEYXRhID0gZXhjZWxUYWJsZTsNCiAgICAgICAgICB0aGlzLmV4Y2VsdGl0bGU9dGFibGVUaGVhZA0KICAgICAgICAgIHRoaXMuZXhjZWxIdG1sPSBleGNlbFRhYmxlDQogICAgICAgICAgdGhpcy5zZWFyY2hvcGVuID0gdHJ1ZTsNCiAgICAgICAgfQ0KICAgICAgfSk7DQogICAgICB9DQogICAgICBlbHNlDQogICAgICB7DQogICAgICAgIHRoaXMuZG93bmxvYWRYbHN4KA0KICAgICAgICAiL3dlYi9UWWp5L2Fuc3dlci9leHBvcnRUZW1wbGF0ZVNwZWNpYWwiLA0KICAgICAgICB7DQogICAgICAgICAgLi4ucXVlcnlJbXBvcnQsDQogICAgICAgIH0sDQogICAgICAgIHRoaXMuZGltZW5zaW9uYWxpdHlOYW1lKyIoIiArdGhpcy5zcGVjaWFsRmNEYXRlKw0KICAgICAgICAgICIpIiArDQogICAgICAgICAgYOaVsOaNri54bHN4YA0KICAgICAgKS50aGVuKChibG9iKSA9PiB7DQogICAgICAgIGxldCByZWFkZXIgPSBuZXcgRmlsZVJlYWRlcigpOw0KICAgICAgICByZWFkZXIucmVhZEFzQXJyYXlCdWZmZXIoYmxvYik7DQogICAgICAgIHJlYWRlci5vbmxvYWQgPSAoZXZ0KSA9PiB7DQogICAgICAgICAgdGhpcy5jdXN0b21CbG9iQ29udGVudD1yZWFkZXIucmVzdWx0Ow0KICAgICAgICAgIGxldCBpbnRzID0gbmV3IFVpbnQ4QXJyYXkoZXZ0LnRhcmdldC5yZXN1bHQpOyAvL+imgeS9v+eUqOivu+WPlueahOWGheWuue+8jOaJgOS7peWwhuivu+WPluWGheWuuei9rOWMluaIkFVpbnQ4QXJyYXkNCiAgICAgICAgICBpbnRzID0gaW50cy5zbGljZSgwLCBibG9iLnNpemUpOw0KICAgICAgICAgIGxldCB3b3JrQm9vayA9IHhsc3gucmVhZChpbnRzLCB7IHR5cGU6ICJhcnJheSIgfSk7DQogICAgICAgICAgbGV0IHNoZWV0TmFtZXMgPSB3b3JrQm9vay5TaGVldE5hbWVzOw0KICAgICAgICAgIGxldCBzaGVldE5hbWUgPSBzaGVldE5hbWVzWzBdOw0KICAgICAgICAgIGxldCB3b3JrU2hlZXQgPSB3b3JrQm9vay5TaGVldHNbc2hlZXROYW1lXTsNCiAgICAgICAgICAvL+iOt+WPlkV4Y2xl5YaF5a6577yM5bm25bCG56m65YaF5a6555So56m65YC85L+d5a2YDQogICAgICAgICAgbGV0IGV4Y2VsVGFibGUgPSB4bHN4LnV0aWxzLnNoZWV0X3RvX2pzb24od29ya1NoZWV0KTsNCiAgICAgICAgICAvLyDojrflj5ZFeGNlbOWktOmDqA0KICAgICAgICAgIGxldCB0YWJsZVRoZWFkID0gQXJyYXkuZnJvbShPYmplY3Qua2V5cyhleGNlbFRhYmxlWzBdKSkubWFwKA0KICAgICAgICAgICAgKGl0ZW0pID0+IHsNCiAgICAgICAgICAgICAgcmV0dXJuIGl0ZW0NCiAgICAgICAgICAgIH0NCiAgICAgICAgICApOw0KICAgICAgICAgIHRoaXMuZXhjZWxEYXRhID0gZXhjZWxUYWJsZTsNCiAgICAgICAgICB0aGlzLmV4Y2VsdGl0bGU9dGFibGVUaGVhZA0KICAgICAgICAgIHRoaXMuZXhjZWxIdG1sPSBleGNlbFRhYmxlDQogICAgICAgICAgdGhpcy5zZWFyY2hvcGVuID0gdHJ1ZTsNCiAgICAgICAgfQ0KICAgICAgfSk7DQogICAgICB9DQogICAgfSwNCiAgICANCiAgICBoYW5kbGVEYXRlQ2hhbmdlKCkgew0KICAgICAgdGhpcy5nZXRMaXN0KCk7DQogICAgfSwNCiAgICByZXNldFF1ZXJ5KCkgew0KICAgICAgdGhpcy5xdWVyeVBhcmFtcy5mY0RhdGUgPSB1bmRlZmluZWQ7DQogICAgICB0aGlzLnF1ZXJ5UGFyYW1zLmZvcm1RdWVzdGlvbiA9IHVuZGVmaW5lZDsNCiAgICAgIHRoaXMucXVlcnlQYXJhbXMuZGltZW5zaW9uYWxpdHlJZCA9IHVuZGVmaW5lZDsNCiAgICAgIHRoaXMuZ2V0TGlzdCgpOw0KICAgIH0sDQogICAgaGFuZGxlVXBsb2FkKHsgZmlsZSB9KSB7DQogICAgICBjb25zdCBmb3JtRGF0YSA9IG5ldyBGb3JtRGF0YSgpOw0KICAgICAgZm9ybURhdGEuYXBwZW5kKCJmaWxlIiwgZmlsZSk7DQogICAgICByZXR1cm4gYXhpb3MNCiAgICAgICAgLnBvc3QoDQogICAgICAgICAgcHJvY2Vzcy5lbnYuVlVFX0FQUF9CQVNFX0FQSSArICIvY29tbW9uL3VwbG9hZE1pbmlvRGF0YVJlcG9ydCIsDQogICAgICAgICAgZm9ybURhdGENCiAgICAgICAgKQ0KICAgICAgICAudGhlbigocmVzKSA9PiB7DQogICAgICAgICAgcmV0dXJuIHsNCiAgICAgICAgICAgIC4uLnJlcy5kYXRhLA0KICAgICAgICAgIH07DQogICAgICAgIH0pOw0KICAgIH0sDQogICAgLyoqIOaPkOS6pOaMiemSriAqLw0KICAgIGhhbmRsZVN1Ym1pdCgpIHsNCiAgICAgIGNvbnNvbGUubG9nKHRoaXMuYW5zd2VyTGlzdCk7DQogICAgICBjb25zb2xlLmxvZygidGVzdDEiLHRoaXMuZGF0ZXNhdmUpOw0KICAgICAgLy8g6aaW5YWI5a+5IGFuc3dlckxpc3Qg6L+b6KGM5aSE55CG77ya5ZCI5bm244CB6L+H5ruk5ZKM6L2s5o2iDQogICAgICBsZXQgcHJvY2Vzc2VkTGlzdHMgPSB0aGlzLmFuc3dlckxpc3QNCiAgICAgICAgLnJlZHVjZSgoYWNjLCBjdXJyZW50KSA9PiB7DQogICAgICAgICAgcmV0dXJuIGFjYy5jb25jYXQoY3VycmVudC5saXN0KTsNCiAgICAgICAgfSwgW10pDQogICAgICAgIC5maWx0ZXIoKHgpID0+IHsNCiAgICAgICAgICAvLyDov4fmu6TmnaHku7YNCiAgICAgICAgICBjb25zb2xlLmxvZygidGVzdDEiLHguc3RhdHVzKTsNCiAgICAgICAgICBjb25zb2xlLmxvZyh0aGlzLmRhdGVzYXZlW3guZm9ybUlkXSk7DQogICAgICAgICAgY29uc29sZS5sb2coeC5mb3JtVmFsdWUpOw0KICAgICAgICAgIHJldHVybiAoDQogICAgICAgICAgICB4LmZvcm1WYWx1ZSAhPSBudWxsICYmDQogICAgICAgICAgICB4LmZvcm1WYWx1ZSAhPSAiIiAmJg0KICAgICAgICAgICAgKCghWyIxIl0uaW5jbHVkZXMoeC5zdGF0dXMpKSYmDQogICAgICAgICAgICAoDQogICAgICAgICAgICAgIChbIjAiLCIyIiwiMyJdLmluY2x1ZGVzKHguc3RhdHVzKSAmJiB0aGlzLmRhdGVzYXZlW3guZm9ybUlkXSE9eC5mb3JtVmFsdWUpKQ0KICAgICAgICAgICAgICB8fChbIjQiXS5pbmNsdWRlcyh4LnN0YXR1cykpDQogICAgICAgICAgICApDQogICAgICAgICAgKTsNCiAgICAgICAgfSk7DQoNCiAgICAgIC8vIOWvueespuWQiOadoeS7tueahOWFg+e0oOi/m+ihjCBmb3JtVmFsdWUg55qE6L2s5o2iDQogICAgICBwcm9jZXNzZWRMaXN0cy5mb3JFYWNoKCh4KSA9PiB7DQogICAgICAgIGlmIChbIjAiLCAiMSJdLmluY2x1ZGVzKHguZm9ybVR5cGUpKSB7DQogICAgICAgICAgeC5mb3JtVmFsdWUgPSBwYXJzZUZsb2F0KHguZm9ybVZhbHVlKTsNCiAgICAgICAgfQ0KICAgICAgICB4LmZjRGF0ZSA9IHRoaXMucXVlcnlQYXJhbXMuZmNEYXRlOw0KICAgICAgfSk7DQoNCiAgICAgIC8vIOacgOWQjui/m+ihjOa3seaLt+i0nQ0KICAgICAgbGV0IGFsbExpc3RzID0gSlNPTi5wYXJzZShKU09OLnN0cmluZ2lmeShwcm9jZXNzZWRMaXN0cykpOw0KDQogICAgICAvLyBjb25zb2xlLmxvZygiYWxsTGlzdHM6IiwgYWxsTGlzdHMpOw0KICAgICAgY29uc3Qgbm93ID0gbmV3IERhdGUoKTsNCiAgICAgIA0KICAgICAgbGV0IGRhdGVzdHI9Iuivt+ehruWumuaYr+WQpuimgeaPkOS6pOaVsOaNriINCiAgICAgIGlmKHRoaXMucXVlcnlQYXJhbXMuZmNEYXRlPT1udWxsKQ0KICAgICAgew0KICAgICAgICAvLyDojrflj5blubTmnIjml6UNCiAgICAgICAgY29uc3QgeWVhciA9IG5vdy5nZXRGdWxsWWVhcigpOw0KICAgICAgICBjb25zdCBtb250aCA9IFN0cmluZyhub3cuZ2V0TW9udGgoKSArIDEpLnBhZFN0YXJ0KDIsICcwJyk7IC8vIOaciOS7veS7jjDlvIDlp4vvvIzpnIDopoErMQ0KICAgICAgICBjb25zdCBkYXkgPSBTdHJpbmcobm93LmdldERhdGUoKSkucGFkU3RhcnQoMiwgJzAnKTsNCg0KICAgICAgICAvLyDmoLzlvI/ljJbkuLogeXl5eeW5tE1N5pyIZGTml6UNCiAgICAgICAgY29uc3QgZm9ybWF0MSA9IGAke3llYXJ95bm0JHttb250aH3mnIgke2RheX3ml6VgOw0KDQogICAgICAgIC8vIOagvOW8j+WMluS4uiB5eXl55bm0TU3mnIgNCiAgICAgICAgY29uc3QgZm9ybWF0MiA9IGAke3llYXJ95bm0JHttb250aH3mnIhgOw0KICAgICAgICANCiAgICAgICAgaWYodGhpcy5jb3VudD09JzEnKQ0KICAgICAgICB7DQogICAgICAgICAgZGF0ZXN0cj0n5oKo5pyq6YCJ5oup5pe26Ze0LOivt+ehruWumuaYr+WQpuimgeaPkOS6pCcrZm9ybWF0MSsn55qE5pWw5o2uPycNCiAgICAgICAgfQ0KICAgICAgICBlbHNlDQogICAgICAgIHsNCiAgICAgICAgICBkYXRlc3RyPSfmgqjmnKrpgInmi6nml7bpl7Qs6K+356Gu5a6a5piv5ZCm6KaB5o+Q5LqkJytmb3JtYXQyKyfnmoTmlbDmja4/Jw0KICAgICAgICB9DQogICAgICB9DQogICAgICB0aGlzLiRjb25maXJtKA0KICAgICAgICBkYXRlc3RyLA0KICAgICAgICB7DQogICAgICAgICAgY29uZmlybUJ1dHRvblRleHQ6ICLnoa7lrpoiLA0KICAgICAgICAgIGNhbmNlbEJ1dHRvblRleHQ6ICLlj5bmtogiLA0KICAgICAgICAgIHR5cGU6ICJ3YXJuaW5nIiwNCg0KICAgICAgICB9DQogICAgICApLnRoZW4oKCk9PnsNCiAgICAgICAgbmV3QWRkKGFsbExpc3RzKS50aGVuKChyZXMpID0+IHsNCiAgICAgICAgdGhpcy5nZXRMaXN0KCk7DQogICAgICAgIHRoaXMubXNnU3VjY2Vzcygi5L+d5a2Y5oiQ5YqfIik7DQogICAgICB9KTsNCiAgICAgIH0pLmNhdGNoKCgpPT57fSk7DQoNCiAgICAgIC8vIG5ld0FkZChhbGxMaXN0cykudGhlbigocmVzKSA9PiB7DQogICAgICAvLyAgIHRoaXMuZ2V0TGlzdCgpOw0KICAgICAgLy8gICB0aGlzLm1zZ1N1Y2Nlc3MoIuS/neWtmOaIkOWKnyIpOw0KICAgICAgLy8gfSk7DQogICAgfSwNCiAgICANCiAgICBoYW5kbGVGaWxlVXBsb2FkUHJvZ3Jlc3MoKSB7DQogICAgICB0aGlzLnVwbG9hZC5pc1VwbG9hZGluZyA9IHRydWU7DQogICAgfSwNCiAgICBoYW5kbGVGaWxlU3VjY2VzcyhyZXNwb25zZSkgew0KICAgICAgY29uc29sZS5sb2cocmVzcG9uc2UpDQogICAgICBpZiAocmVzcG9uc2UuY29kZSA9PSAyMDApIHsNCiAgICAgICAgdGhpcy4kbW9kYWwubXNnU3VjY2Vzcygi5LiK5Lyg5oiQ5YqfIik7DQogICAgICAgIHRoaXMuZ2V0TGlzdCgpOw0KICAgICAgICB0aGlzLmltcG9ydE9wZW4gPSBmYWxzZTsNCiAgICAgICAgdGhpcy5TcGVjaWFsSW1wb3J0T3BlbiA9IGZhbHNlOw0KICAgICAgfQ0KICAgICAgZWxzZSB7DQogICAgICAgIHRoaXMuJG1vZGFsLm1zZ0Vycm9yKCLkuIrkvKDlpLHotKUiKQ0KICAgICAgfQ0KICAgICAgdGhpcy51cGxvYWQuaXNVcGxvYWRpbmcgPSBmYWxzZTsNCiAgICB9LA0KICAgIC8vIOaooeadv+S4i+i9vQ0KICAgIGRvd25sb2FkVGVtcGxhdGUoKXsNCiAgICANCiAgICAgIGlmICgNCiAgICAgICAgdGhpcy5xdWVyeUltcG9ydC5zdGFydERhdGUgPT0gbnVsbCB8fA0KICAgICAgICB0aGlzLnF1ZXJ5SW1wb3J0LnN0YXJ0RGF0ZSA9PSAiInx8DQogICAgICAgIHRoaXMucXVlcnlJbXBvcnQuZW5kRGF0ZSA9PSBudWxsfHwNCiAgICAgICAgdGhpcy5xdWVyeUltcG9ydC5lbmREYXRlID09ICIiDQogICAgICApIHsNCiAgICAgICAgdGhpcy4kbm90aWZ5LmVycm9yKHsNCiAgICAgICAgICB0aXRsZTogIumUmeivryIsDQogICAgICAgICAgbWVzc2FnZTogIuWvvOWHuuWJjeivt+WFiOi+k+WFpeW8gOWni+e7k+adn+aXtumXtCIsDQogICAgICAgIH0pOw0KICAgICAgICByZXR1cm47DQogICAgICB9DQogICAgICB0aGlzLnF1ZXJ5SW1wb3J0LnJvb3RJZCA9IHRoaXMucXVlcnlQYXJhbXMuZGltZW5zaW9uYWxpdHlJZA0KICAgICAgdGhpcy5kb3dubG9hZEZpbGUoDQogICAgICAgICIvd2ViL1RZankvYW5zd2VyL2V4cG9ydFRlbXBsYXRlIiwNCiAgICAgICAgew0KICAgICAgICAgIC4uLnRoaXMucXVlcnlJbXBvcnQsDQogICAgICAgIH0sDQogICAgICAgICIoIiArDQogICAgICAgICAgdGhpcy5xdWVyeUltcG9ydC5zdGFydERhdGUgKw0KICAgICAgICAgICItIiArDQogICAgICAgICAgdGhpcy5xdWVyeUltcG9ydC5lbmREYXRlICsNCiAgICAgICAgICAiKSIgKw0KICAgICAgICAgIGDmlbDmja4ueGxzeGANCiAgICAgICk7DQogICAgDQogICAgfSwNCg0KICAgICAgICAvLyDmqKHmnb/kuIvovb0NCiAgICBkb3dubG9hZFRlbXBsYXRlU3BlY2lhbCgpew0KICAgICAgaWYgKHRoaXMuc3BlY2lhbEZjRGF0ZSA9PSBudWxsICkgew0KICAgICAgICB0aGlzLnNwZWNpYWxGY0RhdGU9IHRoaXMucXVlcnlQYXJhbXMuZmNEYXRlDQogICAgICB9DQoNCiAgICAgIC8vIGlmICgNCiAgICAgIC8vICAgdGhpcy5zcGVjaWFsRmNEYXRlID09IG51bGwgDQogICAgICAvLyApIHsNCiAgICAgIC8vICAgdGhpcy4kbm90aWZ5LmVycm9yKHsNCiAgICAgIC8vICAgICB0aXRsZTogIumUmeivryIsDQogICAgICAvLyAgICAgbWVzc2FnZTogIuWvvOWHuuWJjeivt+WFiOi+k+WFpeW8gOWni+e7k+adn+aXtumXtCIsDQogICAgICAvLyAgIH0pOw0KICAgICAgLy8gICByZXR1cm47DQogICAgICAvLyB9DQogICAgICBsZXQgcXVlcnlJbXBvcnQ9e30NCiAgICAgIHF1ZXJ5SW1wb3J0LnJvb3RJZCA9IHRoaXMucXVlcnlQYXJhbXMuZGltZW5zaW9uYWxpdHlJZA0KICAgICAgcXVlcnlJbXBvcnQuZmNEYXRlID0gdGhpcy5zcGVjaWFsRmNEYXRlDQogICAgICBxdWVyeUltcG9ydC50eXBlPSIwIg0KICAgICAgdGhpcy5kb3dubG9hZEZpbGUoDQogICAgICAgICIvd2ViL1RZankvYW5zd2VyL2V4cG9ydFRlbXBsYXRlU3BlY2lhbCIsDQogICAgICAgIHsNCiAgICAgICAgICAuLi5xdWVyeUltcG9ydCwNCiAgICAgICAgfSwNCiAgICAgICAgdGhpcy5kaW1lbnNpb25hbGl0eU5hbWUrIigiICt0aGlzLnNwZWNpYWxGY0RhdGUrDQogICAgICAgICAgIikiICsNCiAgICAgICAgICBg5pWw5o2uLnhsc3hgDQogICAgICApOw0KICANCiAgICB9LA0KDQogIH0sDQp9Ow0K"}, {"version": 3, "sources": ["answerForm.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAw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file": "answerForm.vue", "sourceRoot": "src/views/dataReport/answer", "sourcesContent": ["<template>\r\n  <div>\r\n    <el-form\r\n      :model=\"queryParams\"\r\n      ref=\"queryForm\"\r\n      :inline=\"true\"\r\n      v-show=\"showSearch\"\r\n    >\r\n      <el-row :gutter=\"20\" style=\"margin: 20px\">\r\n        <el-form-item label=\"报表名称\" prop=\"dimensionalityId\">\r\n          <el-select\r\n            v-model=\"queryParams.dimensionalityId\"\r\n            placeholder=\"请选择\"\r\n            filterable\r\n            @change=\"handleQuery\"\r\n          >\r\n            <el-option\r\n              v-for=\"item in rootList\"\r\n              :key=\"item.value\"\r\n              :label=\"item.label\"\r\n              :value=\"item.value\"\r\n            >\r\n            </el-option>\r\n          </el-select>\r\n        </el-form-item>\r\n        <el-form-item label=\"填报时间\" v-if=\"count == '1'\"  >\r\n          <el-date-picker\r\n            v-model=\"queryParams.fcDate\"\r\n            value-format=\"yyyy-MM-dd\"\r\n            type=\"date\"\r\n            :default-value=\"new Date()\"\r\n            :picker-options=\"pickerOptions\"\r\n            placeholder=\"选择时间\"\r\n            @change=\"handleDateChange\"\r\n          >\r\n          </el-date-picker>\r\n        </el-form-item>\r\n\r\n        <el-form-item label=\"填报时间\" v-if=\"count == '0'\">\r\n          <el-date-picker\r\n            v-model=\"queryParams.fcDate\"\r\n            value-format=\"yyyy-MM-01\"\r\n            type=\"month\"\r\n            :default-value=\"new Date()\"\r\n            :picker-options=\"pickerOptions\"\r\n            placeholder=\"选择时间\"\r\n            @change=\"handleDateChange\"\r\n          >\r\n          </el-date-picker>\r\n        </el-form-item>\r\n\r\n\r\n        <el-form-item label=\"填报问题\" prop=\"formQuestion\">\r\n          <el-input\r\n            v-model=\"queryParams.formQuestion\"\r\n            placeholder=\"请输入填报问题\"\r\n            clearable\r\n            size=\"small\"\r\n            @keyup.enter.native=\"handleQuery\"\r\n          />\r\n        </el-form-item>\r\n\r\n        <el-form-item>\r\n          <el-button\r\n            type=\"cyan\"\r\n            icon=\"el-icon-search\"\r\n            size=\"mini\"\r\n            @click=\"handleQuery\"\r\n            >搜索</el-button\r\n          >\r\n        </el-form-item>\r\n        </el-row>\r\n        <el-row :gutter=\"10\" class=\"mb8\">\r\n        <el-form-item>\r\n          <el-button  v-if=\" !containsSubstring('安全责任工资考核表',dimensionalityName) \"\r\n            type=\"warning\"\r\n            icon=\"el-icon-download\"\r\n            size=\"mini\"\r\n            @click=\"importOpen = true\"\r\n            >数据导入导出</el-button\r\n          >\r\n          \r\n          <el-button v-if=\" aloneList(dimensionalityName) || containsSubstring('安全责任工资考核表',dimensionalityName)\"\r\n            type=\"danger\"\r\n            icon=\"el-icon-download\"\r\n            size=\"mini\"\r\n            @click=\"SpecialImportOpen = true\"\r\n            >单周期报表导入导出</el-button>\r\n\r\n          <el-button v-if=\"aloneList(dimensionalityName)  || containsSubstring('安全责任工资考核表',dimensionalityName) \"\r\n            type=\"warning\"\r\n            icon=\"el-icon-search\"\r\n            size=\"mini\"\r\n            @click=\"handlePreview\"\r\n            >数据预览</el-button\r\n          >\r\n        </el-form-item>\r\n      </el-row>\r\n      <el-row :gutter=\"10\" class=\"mb8\">\r\n        <el-col :span=\"1.5\">报表名称：{{dimensionalityName}}</el-col>\r\n        \r\n        <right-toolbar\r\n          :showSearch.sync=\"showSearch\"\r\n          @queryTable=\"getList\"\r\n        ></right-toolbar>\r\n      </el-row>\r\n      <el-row :gutter=\"10\" class=\"mb8\">\r\n        <el-col :span=\"1.5\">管理部门：{{deptName}}</el-col>\r\n        <right-toolbar\r\n          :showSearch.sync=\"showSearch\"\r\n          @queryTable=\"getList\"\r\n        ></right-toolbar>\r\n      </el-row>\r\n    </el-form>\r\n    <vxe-form\r\n      ref=\"formRef\"\r\n      :data=\"formData\"\r\n      @submit=\"handleSubmit\"\r\n      border\r\n      title-background\r\n      vertical-align=\"center\"\r\n      title-width=\"300\"\r\n      title-bold\r\n    >\r\n      <vxe-form-group\r\n        v-for=\"(group, index) in answerList\"\r\n        :key=\"index\"\r\n        span=\"24\"\r\n        :title=\"group.title\"\r\n        title-bold\r\n        vertical\r\n      >\r\n        <vxe-form-item\r\n          v-for=\"(question, qIndex) in group.list\"\r\n          :key=\"qIndex\"\r\n          :title=\"question.formQuestion\"\r\n          :field=\"answerList[index].list[qIndex].formValue\"\r\n          :span=\"question.formType == '3' ? 24 : 12\"\r\n          :item-render=\"{}\"\r\n        >\r\n          <template #default=\"params\">\r\n            <vxe-tag\r\n              v-if=\"question.status == '0'\"\r\n              status=\"primary\"\r\n              content=\"主要颜色\"\r\n              >待审核 （审核人姓名：{{question.checkWorkNo}}  审核人工号：{{question.checkUserName}}）</vxe-tag\r\n            >\r\n            <vxe-tag\r\n              v-if=\"question.status == '1'\"\r\n              status=\"warning\"\r\n              content=\"信息颜色\"\r\n              >审核中</vxe-tag\r\n            >\r\n            <vxe-tag\r\n              v-if=\"question.status == '2'\"\r\n              status=\"success\"\r\n              content=\"信息颜色\"\r\n              >审核完成</vxe-tag\r\n            >\r\n            <vxe-tag\r\n              v-if=\"question.status == '3'\"\r\n              status=\"danger\"\r\n              content=\"警告颜色\"\r\n              >驳回理由: {{ question.assessment }}</vxe-tag\r\n            >\r\n\r\n            <vxe-input\r\n              v-if=\"question.formType == '0'\"\r\n              v-model=\"answerList[index].list[qIndex].formValue\"\r\n              @change=\"changeEvent(params)\"\r\n              type=\"integer\"\r\n            ></vxe-input>\r\n            <vxe-input\r\n              v-if=\"question.formType == '1'\"\r\n              v-model=\"answerList[index].list[qIndex].formValue\"\r\n              @change=\"changeEvent(params)\"\r\n              type=\"'float'\"\r\n            ></vxe-input>\r\n            <vxe-input\r\n              v-if=\"question.formType == '2'\"\r\n              v-model=\"answerList[index].list[qIndex].formValue\"\r\n              @change=\"changeEvent(params)\"\r\n              type=\"text\"\r\n            ></vxe-input>\r\n            <vxe-textarea\r\n              v-if=\"question.formType == '3'\"\r\n              v-model=\"answerList[index].list[qIndex].formValue\"\r\n              :placeholder=\"question.formQuestion\"\r\n            ></vxe-textarea>\r\n            <vxe-text v-if=\"question.formNote != null\" status=\"warning\"\r\n              >指标:{{ question.formNote }}<br\r\n            /></vxe-text>\r\n            <vxe-text v-if=\"question.formNote1 != null\" status=\"warning\"\r\n              >备注:{{ question.formNote1 }}<br\r\n            /></vxe-text>\r\n            <vxe-text v-if=\"question.maximum != null\" status=\"primary\"\r\n              >最大值:{{ question.maximum }}<br\r\n            /></vxe-text>\r\n            <vxe-text v-if=\"question.minimum != null\" status=\"primary\"\r\n              >最小值:{{ question.minimum }}<br\r\n            /></vxe-text>\r\n            <vxe-text v-if=\"question.formUnit != null\" status=\"primary\"\r\n              >单位:{{ question.formUnit }}<br\r\n            /></vxe-text>\r\n            <vxe-tag\r\n              v-if=\"\r\n                question.formValue != null &&\r\n                question.formValue != '' &&\r\n                (question.formType == '0' || question.formType == '1') &&\r\n                ((question.minimum != null &&\r\n                  question.formValue < question.minimum) ||\r\n                  (question.maximum != null &&\r\n                    question.formValue > question.maximum))\r\n              \"\r\n              status=\"danger\"\r\n              content=\"警告颜色\"\r\n              >输入值超出预计范围，请输入原因和改进措施</vxe-tag\r\n            >\r\n            <vxe-textarea\r\n              v-if=\"\r\n                question.formValue != null &&\r\n                question.formValue != '' &&\r\n                (question.formType == '0' || question.formType == '1') &&\r\n                ((question.minimum != null &&\r\n                  question.formValue < question.minimum) ||\r\n                  (question.maximum != null &&\r\n                    question.formValue > question.maximum))\r\n              \"\r\n              v-model=\"answerList[index].list[qIndex].reason\"\r\n              @change=\"changeEvent(params)\"\r\n              placeholder=\"请填写原因\"\r\n            ></vxe-textarea>\r\n            <vxe-textarea\r\n              v-if=\"\r\n                question.formValue != null &&\r\n                question.formValue != '' &&\r\n                (question.formType == '0' || question.formType == '1') &&\r\n                ((question.minimum != null &&\r\n                  question.formValue < question.minimum) ||\r\n                  (question.maximum != null &&\r\n                    question.formValue > question.maximum))\r\n              \"\r\n              v-model=\"answerList[index].list[qIndex].measure\"\r\n              @change=\"changeEvent(params)\"\r\n              placeholder=\"请输入改进措施\"\r\n            ></vxe-textarea>\r\n          </template>\r\n        </vxe-form-item>\r\n      </vxe-form-group>\r\n\r\n      <vxe-form-item align=\"center\" span=\"24\" :item-render=\"{}\">\r\n        <template #default>\r\n          <vxe-button\r\n            type=\"submit\"\r\n            status=\"primary\"\r\n            content=\"提交\"\r\n          ></vxe-button>\r\n        </template>\r\n      </vxe-form-item>\r\n    </vxe-form>\r\n\r\n    <el-dialog\r\n      title=\"选择导出范围\"\r\n      :visible.sync=\"importOpen\"\r\n      width=\"400px\"\r\n      append-to-body\r\n      destroy-on-close\r\n    >\r\n      <span>数据日期范围：</span>\r\n      <el-date-picker\r\n        v-model=\"dateValue\"\r\n        type=\"daterange\"\r\n        range-separator=\"至\"\r\n        start-placeholder=\"开始日期\"\r\n        end-placeholder=\"结束日期\"\r\n        value-format=\"yyyy-MM-dd\"\r\n        @change=\"onDateChange\"\r\n      >\r\n      </el-date-picker>\r\n      <el-row :gutter=\"10\" style=\"margin-top: 10px;\">\r\n       \r\n        <el-col :span=\"1.5\">\r\n          <el-button\r\n            size=\"small\"\r\n            type=\"info\"\r\n            plain\r\n            icon=\"el-icon-link\"\r\n            @click=\"downloadTemplate\"\r\n            >数据下载</el-button\r\n          >\r\n        </el-col>\r\n         <el-col :span=\"1.5\" >\r\n          <el-upload\r\n            accept=\".xlsx, .xls\"\r\n            :headers=\"upload.headers\"\r\n            :disabled=\"upload.isUploading\"\r\n            :action=\"upload.url\"\r\n            :show-file-list=\"false\"\r\n            :multiple=\"false\"\r\n            :on-progress=\"handleFileUploadProgress\"\r\n            :on-success=\"handleFileSuccess\"\r\n          >\r\n            <el-button size=\"small\" type=\"warning\" plain icon=\"el-icon-download\"\r\n              >表格导入</el-button\r\n            >\r\n          </el-upload>\r\n        </el-col>\r\n      </el-row>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"importOpen = false\">取 消</el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n\r\n\r\n    <el-dialog\r\n      title=\"单位时间报表导出\"\r\n      :visible.sync=\"SpecialImportOpen\"\r\n      width=\"400px\"\r\n      append-to-body\r\n      destroy-on-close\r\n    >\r\n      <span>选择导出时间：</span>\r\n      <el-date-picker\r\n            v-model=\"specialFcDate\"\r\n            value-format=\"yyyy-MM-dd\"\r\n            type=\"date\"\r\n            :default-value=\"new Date()\"\r\n            placeholder=\"选择时间\"\r\n          >\r\n      </el-date-picker>\r\n      <el-row :gutter=\"10\" style=\"margin-top: 10px;\">\r\n       \r\n        <el-col :span=\"1.5\">\r\n          <el-button\r\n            size=\"small\"\r\n            type=\"info\"\r\n            plain\r\n            icon=\"el-icon-link\"\r\n            @click=\"downloadTemplateSpecial\"\r\n            >数据下载</el-button\r\n          >\r\n        </el-col>\r\n         <el-col :span=\"1.5\" >\r\n          <el-upload\r\n            accept=\".xlsx, .xls\"\r\n            :headers=\"uploadSpecial.headers\"\r\n            :disabled=\"uploadSpecial.isUploading\"\r\n            :action=\"uploadSpecial.url\"\r\n            :show-file-list=\"false\"\r\n            :multiple=\"false\"\r\n            :on-progress=\"handleFileUploadProgress\"\r\n            :on-success=\"handleFileSuccess\"\r\n          >\r\n            <el-button size=\"small\" type=\"warning\" plain icon=\"el-icon-download\"\r\n              >表格导入</el-button\r\n            >\r\n          </el-upload>\r\n        </el-col>\r\n      </el-row>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"SpecialImportOpen = false\">取 消</el-button>\r\n      </div>\r\n    </el-dialog>\r\n    <el-dialog  title=\"文件预览\"  :visible.sync=\"searchopen\" width=\"1800px\" >\r\n        <div class=\"test\">\r\n          <vue-office-excel\r\n              :src=\"customBlobContent\"\r\n              style=\"height: 100vh;\"\r\n          />\r\n        </div>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n  \r\n  <script>\r\nimport { answerListPlusAll,formFrequency } from \"@/api/tYjy/form\";\r\nimport answerInput from \"./input\";\r\nimport { newAdd, addAlone } from \"@/api/tYjy/answer\";\r\nimport { getAllRootListForAnswer } from \"@/api/tYjy/dimensionality\";\r\nimport { getToken } from \"@/utils/auth\";\r\nimport axios from \"axios\";\r\nimport * as xlsx from 'xlsx';\r\n\r\n//引入相关样式\r\n\r\nexport default {\r\n  name: \"Answer\",\r\n  components: { answerInput },\r\n  data() {\r\n    return {\r\n      pickerOptions: {\r\n        disabledDate(time) {\r\n          return time.getTime() > Date.now();\r\n        },\r\n      },\r\n      frequencyOptions: [],\r\n\r\n      queryParams: {\r\n        formQuestion: undefined,\r\n        fcDate: undefined,\r\n        dimensionalityId: undefined,\r\n        formQuestion: undefined,\r\n      },\r\n\r\n      formType: null,\r\n      dimensionalityNames: null,\r\n      drawerShow: false,\r\n      stickyTop: 0,\r\n      loading: false,\r\n      showSearch: true,\r\n      answerList: [],\r\n      formData: {},\r\n      row: {},\r\n      rootList: [],\r\n      userInfo: {},\r\n      datesave:{},\r\n      pathsave:{},\r\n      count:1,\r\n      deptName:null,\r\n      dimensionalityName:null,\r\n      dateValue: null,\r\n      specialFcDate:null,\r\n      queryImport: {\r\n        startDate: null,\r\n        endDate: null,\r\n        rootId: null,\r\n      },\r\n      importOpen:false,\r\n      SpecialImportOpen:false,\r\n      // 导入参数\r\n      upload: {\r\n        // 是否禁用上传\r\n        isUploading: false,\r\n        // 设置上传的请求头部\r\n        headers: { Authorization: \"Bearer \" + getToken() },\r\n        // 上传的地址\r\n        url:\r\n          process.env.VUE_APP_BASE_API +\r\n          \"/web/TYjy/answer/importData\",\r\n      },\r\n\r\n\r\n      uploadSpecial: {\r\n        // 是否禁用上传\r\n        isUploading: false,\r\n        // 设置上传的请求头部\r\n        headers: { Authorization: \"Bearer \" + getToken() },\r\n        // 上传的地址\r\n        url:\r\n          process.env.VUE_APP_BASE_API +\r\n          \"/web/TYjy/answer/importDataSpecial\",\r\n\r\n      },\r\n      excelHtml:\"\",\r\n      searchopen:false,\r\n      excelData: [], // 存储 Excel 数据\r\n      exceltitle: [],\r\n      customBlobContent:\"\"\r\n    };\r\n  },\r\n  mounted() {\r\n    this.userInfo = JSON.parse(JSON.stringify(this.$store.state.user));\r\n  },\r\n\r\n  created() {\r\n    const dimensionalityId = this.$route.query && this.$route.query.dimensionalityId;\r\n    const fcDate = this.$route.query && this.$route.query.fcDate;\r\n    this.queryParams.dimensionalityId=dimensionalityId\r\n    this.queryParams.fcDate=fcDate\r\n    this.initData();\r\n\r\n\r\n    // if(this.$route.query)\r\n    // {\r\n    //   const dimensionalityId = this.$route.query && this.$route.query.dimensionalityId;\r\n    //   // const dimensionalityName = this.$route.query && this.$route.query.dimensionalityName;\r\n    //   const fcDate = this.$route.query && this.$route.query.fcDate;\r\n    //   this.queryParams.dimensionalityId=dimensionalityId\r\n    //   // this.queryParams.dimensionalityName=dimensionalityName\r\n    //   this.queryParams.fcDate=fcDate\r\n    //   this.initData1();\r\n    // }\r\n    // else\r\n    // {\r\n    //   this.initData();\r\n    // }\r\n  },\r\n  methods: {\r\n    onDateChange() {\r\n      console.log(this.dateValue);\r\n      if (this.dateValue != null && this.dateValue != \"\") {\r\n        this.queryImport.startDate = this.dateValue[0];\r\n        this.queryImport.endDate = this.dateValue[1];\r\n      } else {\r\n        this.queryImport.startDate = \"\";\r\n        this.queryImport.endDate = \"\";\r\n      }\r\n    },\r\n    clickNode($event, node) {\r\n      $event.target.parentElement.parentElement.firstElementChild.click();\r\n    },\r\n    changeEvent(params) {\r\n      const $form = this.$refs.formRef;\r\n      if ($form) {\r\n        $form.updateStatus(params);\r\n      }\r\n    },\r\n    disabledDate(time) {\r\n      return time.getTime() < Date.now() - 8.64e7; // 8.64e7 毫秒数代表一天\r\n    },\r\n    inputChange(val, row) {\r\n      row.formValue = val;\r\n    },\r\n    handleScroll() {\r\n      this.isSticky = window.scrollY >= this.stickyTop;\r\n    },\r\n    initData() {\r\n      getAllRootListForAnswer().then((res) => {\r\n        this.rootList = res.data;\r\n        if(this.queryParams.dimensionalityId==null)\r\n        {\r\n          this.queryParams.dimensionalityId = this.rootList[0].value;\r\n          this.deptName= this.rootList[0].deptName;\r\n          this.deptCode= this.rootList[0].deptCode;\r\n          this.dimensionalityName=this.rootList[0].label\r\n        }\r\n        else\r\n        {\r\n          // this.queryParams.dimensionalityId = this.queryParams.dimensionalityId;\r\n          for(let i=0;i<this.rootList.length;i++)\r\n          {\r\n            if(this.queryParams.dimensionalityId == this.rootList[i].value)\r\n            {\r\n              this.queryParams.dimensionalityId = this.rootList[i].value;\r\n              this.deptName= this.rootList[i].deptName;\r\n              this.deptCode= this.rootList[i].deptCode;\r\n              this.dimensionalityName=this.rootList[i].label\r\n            }\r\n          }\r\n        }\r\n        this.getList();\r\n      });\r\n    },\r\n    initData1() {\r\n      getAllRootListForAnswer().then((res) => {\r\n        this.rootList = res.data;\r\n        for(let i=0;i<this.rootList.length;i++)\r\n        {\r\n          if(this.queryParams.dimensionalityId == this.rootList[i].value)\r\n          {\r\n            this.deptName= this.rootList[0].deptName;\r\n          }\r\n        }\r\n        this.getList();\r\n      });\r\n    },\r\n    containsSubstring(substring, string) {\r\n      return string.includes(substring);\r\n    },\r\n    aloneList(string) {\r\n      if(string== '气体结算月报')\r\n      {\r\n        return true;\r\n      }\r\n      if(string== '高炉、转炉煤气月报表')\r\n      {\r\n        return true;\r\n      }\r\n      if(string== '天然气消耗月报表')\r\n      {\r\n        return true;\r\n      }\r\n      if(string== '蒸汽消耗月报表')\r\n      {\r\n        return true;\r\n      }\r\n      if(string== '电量月报表')\r\n      {\r\n        return true;\r\n      }\r\n      if(string== '特板事业部2025年经济责任制奖罚汇总')\r\n      {\r\n        return true;\r\n      }\r\n      // if(string== '研究院目标指标一览')\r\n      // {\r\n      //   return true;\r\n      // }\r\n      return false;\r\n    },\r\n\r\n    /** 查询TYjyAnswer列表 */\r\n    getList() {\r\n      formFrequency({dimensionalityId: this.queryParams.dimensionalityId}).then((res) => {\r\n           if(this.count!=res.data)\r\n           {\r\n            this.queryParams.fcDate=undefined\r\n           }\r\n           this.count=res.data\r\n      });\r\n\r\n      this.answerList = [];\r\n      answerListPlusAll({\r\n        fcDate: this.queryParams.fcDate,\r\n        dimensionalityId: this.queryParams.dimensionalityId,\r\n        formQuestion: this.queryParams.formQuestion,\r\n      }).then((res) => {\r\n        let answerList = [];\r\n        let list = res.data;\r\n        for(let i=0;i<this.rootList.length;i++)\r\n        {\r\n          if(this.queryParams.dimensionalityId == this.rootList[i].value)\r\n          {\r\n            this.queryParams.dimensionalityId = this.rootList[i].value;\r\n            this.deptName= this.rootList[i].deptName;\r\n            this.deptCode= this.rootList[i].deptCode;\r\n            this.dimensionalityName=this.rootList[i].label\r\n          }\r\n        }\r\n        if(this.containsSubstring('安全责任工资考核表',this.dimensionalityName))\r\n        {\r\n          console.log(\"test1\")\r\n          let num=0\r\n          for(let i=0;i<list.length;i++)\r\n          {\r\n            if(list[i].formQuestion!='自评总分' && list[i].formQuestion!='厂长评分')\r\n            {\r\n              num=1;\r\n              console.log(\"test2\",list[i].formQuestion)\r\n              break;\r\n            }\r\n          }\r\n          if(num==0)\r\n          {\r\n            for(let i=0;i<list.length;i++)\r\n            {\r\n              list[i].dimensionalityName=list[i].dimensionalityName.replace('/七、考核评分', '')\r\n            }\r\n            console.log(\"test3\",list)\r\n          }\r\n        }\r\n        \r\n        for(let i=0;i<list.length;i++)\r\n        {\r\n            this.datesave[list[i].formId]=list[i].formValue\r\n            this.pathsave[list[i].dimensionalityName]=list[i].dimensionalityPath\r\n        }\r\n        // 使用 map 提取 dimensionalityName 属性到一个数组\r\n        let dimensionalityNames = list.map((x) => x.dimensionalityName);\r\n\r\n        // 提取 / 后的前三位字符，并与原字符串配对\r\n        dimensionalityNames = dimensionalityNames.map((name) => {\r\n          // let key = name.includes(\"/\") ? name.split(\"/\")[1].slice(0, 3) : \"\";\r\n          let key = this.pathsave[name];\r\n          return { originalName: name, sortKey: key };\r\n        });\r\n\r\n        // 按照提取出的前三字符排序\r\n        dimensionalityNames.sort((a, b) => a.sortKey.localeCompare(b.sortKey));\r\n        // console.log(\"test0\",dimensionalityNames)\r\n        // 如果需要，可以提取排序后的原始名字\r\n        dimensionalityNames = dimensionalityNames.map(\r\n          (item) => item.originalName\r\n        );\r\n\r\n        // 使用 Set 去重\r\n        let uniqueDimensionalityNames = [...new Set(dimensionalityNames)];\r\n\r\n        uniqueDimensionalityNames.forEach((title) => {\r\n          let group = {\r\n            title: \"\",\r\n            list: [],\r\n          };\r\n          group.title = title;\r\n          group.list = list.filter((item) => item.dimensionalityName === title);\r\n          // 假设你有一个数组来存储所有的组\r\n          answerList.push(group); // 将生成的组添加到groups数组中\r\n        });\r\n        this.answerList = answerList;\r\n        // console.log(\"test111\",answerList)\r\n        this.$forceUpdate();\r\n      });\r\n    },\r\n    handleQuery() {\r\n      for(let i=0;i<this.rootList.length;i++)\r\n      {\r\n        if(this.queryParams.dimensionalityId == this.rootList[i].value)\r\n        {\r\n          this.deptName= this.rootList[i].deptName;\r\n          this.deptCode= this.rootList[i].deptCode;\r\n          this.dimensionalityName=this.rootList[i].label\r\n        }\r\n      }\r\n      this.getList();\r\n    },\r\n    handlePreview() {\r\n      let queryImport={}\r\n      queryImport.rootId = this.queryParams.dimensionalityId\r\n      queryImport.fcDate = this.queryParams.fcDate\r\n      queryImport.type=\"0\"\r\n      if(this.dimensionalityName=='研究院目标指标一览')\r\n      {\r\n        this.downloadXlsx(\r\n        \"/web/TYjy/answer/exportWithTemplate\",\r\n        {\r\n          ...queryImport,\r\n        },\r\n        this.dimensionalityName+\"(\" +this.specialFcDate+\r\n          \")\" +\r\n          `数据.xlsx`\r\n      ).then((blob) => {\r\n        let reader = new FileReader();\r\n        reader.readAsArrayBuffer(blob);\r\n        reader.onload = (evt) => {\r\n          this.customBlobContent=reader.result;\r\n          let ints = new Uint8Array(evt.target.result); //要使用读取的内容，所以将读取内容转化成Uint8Array\r\n          ints = ints.slice(0, blob.size);\r\n          let workBook = xlsx.read(ints, { type: \"array\" });\r\n          let sheetNames = workBook.SheetNames;\r\n          let sheetName = sheetNames[0];\r\n          let workSheet = workBook.Sheets[sheetName];\r\n          //获取Excle内容，并将空内容用空值保存\r\n          let excelTable = xlsx.utils.sheet_to_json(workSheet);\r\n          // 获取Excel头部\r\n          let tableThead = Array.from(Object.keys(excelTable[0])).map(\r\n            (item) => {\r\n              return item\r\n            }\r\n          );\r\n          this.excelData = excelTable;\r\n          this.exceltitle=tableThead\r\n          this.excelHtml= excelTable\r\n          this.searchopen = true;\r\n        }\r\n      });\r\n      }\r\n      else\r\n      {\r\n        this.downloadXlsx(\r\n        \"/web/TYjy/answer/exportTemplateSpecial\",\r\n        {\r\n          ...queryImport,\r\n        },\r\n        this.dimensionalityName+\"(\" +this.specialFcDate+\r\n          \")\" +\r\n          `数据.xlsx`\r\n      ).then((blob) => {\r\n        let reader = new FileReader();\r\n        reader.readAsArrayBuffer(blob);\r\n        reader.onload = (evt) => {\r\n          this.customBlobContent=reader.result;\r\n          let ints = new Uint8Array(evt.target.result); //要使用读取的内容，所以将读取内容转化成Uint8Array\r\n          ints = ints.slice(0, blob.size);\r\n          let workBook = xlsx.read(ints, { type: \"array\" });\r\n          let sheetNames = workBook.SheetNames;\r\n          let sheetName = sheetNames[0];\r\n          let workSheet = workBook.Sheets[sheetName];\r\n          //获取Excle内容，并将空内容用空值保存\r\n          let excelTable = xlsx.utils.sheet_to_json(workSheet);\r\n          // 获取Excel头部\r\n          let tableThead = Array.from(Object.keys(excelTable[0])).map(\r\n            (item) => {\r\n              return item\r\n            }\r\n          );\r\n          this.excelData = excelTable;\r\n          this.exceltitle=tableThead\r\n          this.excelHtml= excelTable\r\n          this.searchopen = true;\r\n        }\r\n      });\r\n      }\r\n    },\r\n    \r\n    handleDateChange() {\r\n      this.getList();\r\n    },\r\n    resetQuery() {\r\n      this.queryParams.fcDate = undefined;\r\n      this.queryParams.formQuestion = undefined;\r\n      this.queryParams.dimensionalityId = undefined;\r\n      this.getList();\r\n    },\r\n    handleUpload({ file }) {\r\n      const formData = new FormData();\r\n      formData.append(\"file\", file);\r\n      return axios\r\n        .post(\r\n          process.env.VUE_APP_BASE_API + \"/common/uploadMinioDataReport\",\r\n          formData\r\n        )\r\n        .then((res) => {\r\n          return {\r\n            ...res.data,\r\n          };\r\n        });\r\n    },\r\n    /** 提交按钮 */\r\n    handleSubmit() {\r\n      console.log(this.answerList);\r\n      console.log(\"test1\",this.datesave);\r\n      // 首先对 answerList 进行处理：合并、过滤和转换\r\n      let processedLists = this.answerList\r\n        .reduce((acc, current) => {\r\n          return acc.concat(current.list);\r\n        }, [])\r\n        .filter((x) => {\r\n          // 过滤条件\r\n          console.log(\"test1\",x.status);\r\n          console.log(this.datesave[x.formId]);\r\n          console.log(x.formValue);\r\n          return (\r\n            x.formValue != null &&\r\n            x.formValue != \"\" &&\r\n            ((![\"1\"].includes(x.status))&&\r\n            (\r\n              ([\"0\",\"2\",\"3\"].includes(x.status) && this.datesave[x.formId]!=x.formValue))\r\n              ||([\"4\"].includes(x.status))\r\n            )\r\n          );\r\n        });\r\n\r\n      // 对符合条件的元素进行 formValue 的转换\r\n      processedLists.forEach((x) => {\r\n        if ([\"0\", \"1\"].includes(x.formType)) {\r\n          x.formValue = parseFloat(x.formValue);\r\n        }\r\n        x.fcDate = this.queryParams.fcDate;\r\n      });\r\n\r\n      // 最后进行深拷贝\r\n      let allLists = JSON.parse(JSON.stringify(processedLists));\r\n\r\n      // console.log(\"allLists:\", allLists);\r\n      const now = new Date();\r\n      \r\n      let datestr=\"请确定是否要提交数据\"\r\n      if(this.queryParams.fcDate==null)\r\n      {\r\n        // 获取年月日\r\n        const year = now.getFullYear();\r\n        const month = String(now.getMonth() + 1).padStart(2, '0'); // 月份从0开始，需要+1\r\n        const day = String(now.getDate()).padStart(2, '0');\r\n\r\n        // 格式化为 yyyy年MM月dd日\r\n        const format1 = `${year}年${month}月${day}日`;\r\n\r\n        // 格式化为 yyyy年MM月\r\n        const format2 = `${year}年${month}月`;\r\n        \r\n        if(this.count=='1')\r\n        {\r\n          datestr='您未选择时间,请确定是否要提交'+format1+'的数据?'\r\n        }\r\n        else\r\n        {\r\n          datestr='您未选择时间,请确定是否要提交'+format2+'的数据?'\r\n        }\r\n      }\r\n      this.$confirm(\r\n        datestr,\r\n        {\r\n          confirmButtonText: \"确定\",\r\n          cancelButtonText: \"取消\",\r\n          type: \"warning\",\r\n\r\n        }\r\n      ).then(()=>{\r\n        newAdd(allLists).then((res) => {\r\n        this.getList();\r\n        this.msgSuccess(\"保存成功\");\r\n      });\r\n      }).catch(()=>{});\r\n\r\n      // newAdd(allLists).then((res) => {\r\n      //   this.getList();\r\n      //   this.msgSuccess(\"保存成功\");\r\n      // });\r\n    },\r\n    \r\n    handleFileUploadProgress() {\r\n      this.upload.isUploading = true;\r\n    },\r\n    handleFileSuccess(response) {\r\n      console.log(response)\r\n      if (response.code == 200) {\r\n        this.$modal.msgSuccess(\"上传成功\");\r\n        this.getList();\r\n        this.importOpen = false;\r\n        this.SpecialImportOpen = false;\r\n      }\r\n      else {\r\n        this.$modal.msgError(\"上传失败\")\r\n      }\r\n      this.upload.isUploading = false;\r\n    },\r\n    // 模板下载\r\n    downloadTemplate(){\r\n    \r\n      if (\r\n        this.queryImport.startDate == null ||\r\n        this.queryImport.startDate == \"\"||\r\n        this.queryImport.endDate == null||\r\n        this.queryImport.endDate == \"\"\r\n      ) {\r\n        this.$notify.error({\r\n          title: \"错误\",\r\n          message: \"导出前请先输入开始结束时间\",\r\n        });\r\n        return;\r\n      }\r\n      this.queryImport.rootId = this.queryParams.dimensionalityId\r\n      this.downloadFile(\r\n        \"/web/TYjy/answer/exportTemplate\",\r\n        {\r\n          ...this.queryImport,\r\n        },\r\n        \"(\" +\r\n          this.queryImport.startDate +\r\n          \"-\" +\r\n          this.queryImport.endDate +\r\n          \")\" +\r\n          `数据.xlsx`\r\n      );\r\n    \r\n    },\r\n\r\n        // 模板下载\r\n    downloadTemplateSpecial(){\r\n      if (this.specialFcDate == null ) {\r\n        this.specialFcDate= this.queryParams.fcDate\r\n      }\r\n\r\n      // if (\r\n      //   this.specialFcDate == null \r\n      // ) {\r\n      //   this.$notify.error({\r\n      //     title: \"错误\",\r\n      //     message: \"导出前请先输入开始结束时间\",\r\n      //   });\r\n      //   return;\r\n      // }\r\n      let queryImport={}\r\n      queryImport.rootId = this.queryParams.dimensionalityId\r\n      queryImport.fcDate = this.specialFcDate\r\n      queryImport.type=\"0\"\r\n      this.downloadFile(\r\n        \"/web/TYjy/answer/exportTemplateSpecial\",\r\n        {\r\n          ...queryImport,\r\n        },\r\n        this.dimensionalityName+\"(\" +this.specialFcDate+\r\n          \")\" +\r\n          `数据.xlsx`\r\n      );\r\n  \r\n    },\r\n\r\n  },\r\n};\r\n</script>\r\n<style scoped lang=\"scss\">\r\n\r\n.test{\r\n  height: 100vh;\r\n}\r\n\r\n.excel-preview {\r\n  margin-top: 20px;\r\n  overflow: auto;\r\n  max-height: 500px;\r\n  border: 1px solid #ddd;\r\n  padding: 10px;\r\n}\r\n\r\n/* 设置滚动条的样式 */\r\n::-webkit-scrollbar {\r\n  width: 10px;\r\n  /* 竖向滚动条宽度 */\r\n  height: 15px;\r\n  /* 横向滚动条宽度 */\r\n  background-color: #ffffff;\r\n}\r\n\r\n/* 滚动槽 */\r\n::-webkit-scrollbar-track {\r\n  /* 其实直接在  ::-webkit-scrollbar 中设置也能达到同样的视觉效果*/\r\n  /* -webkit-box-shadow: inset 0 0 6px rgba(177, 223, 117, 0.7); */\r\n  background-color: #e4e4e4;\r\n  border-radius: 10px;\r\n}\r\n\r\n/* 滚动条滑块 */\r\n::-webkit-scrollbar-thumb {\r\n  border-radius: 5px;\r\n  -webkit-box-shadow: inset 0 0 6px rgba(158, 156, 156, 0.616);\r\n}\r\n\r\n::-webkit-scrollbar-thumb:hover {\r\n  background: rgba(139, 138, 138, 0.616);\r\n  -webkit-box-shadow: unset;\r\n}\r\n\r\n::-webkit-scrollbar-thumb:window-inactive {\r\n  /* 容器不被激活时的样式 */\r\n  background: #bdbdbd66;\r\n}\r\n\r\n::-webkit-scrollbar-corner {\r\n  /* 两个滚动条交汇处边角的样式 */\r\n  background-color: #cacaca66;\r\n}\r\n\r\n.sticky {\r\n  padding: 20px;\r\n  position: -webkit-sticky;\r\n  position: sticky;\r\n  top: 0; /* 粘性定位的起始位置 */\r\n  z-index: 100; /* 确保按钮在卡片之上 */\r\n}\r\n.el-tabs--card {\r\n  height: calc(100vh - 110px);\r\n}\r\n.el-tab-pane {\r\n  height: calc(100vh - 110px);\r\n  overflow-y: auto;\r\n}\r\n</style>\r\n  "]}]}