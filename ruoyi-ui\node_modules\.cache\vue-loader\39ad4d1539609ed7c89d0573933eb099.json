{"remainingRequest": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\src\\views\\drawing\\technical\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\src\\views\\drawing\\technical\\index.vue", "mtime": 1755499162048}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgeyBsaXN0RHJhd2luZywgZ2V0RHJhd2luZywgZGVsRHJhd2luZywgYWRkRHJhd2luZyxhZGRUZWNobmljYWxBZ3JlZW1lbnQsIHVwZGF0ZURyYXdpbmcsIGV4cG9ydERyYXdpbmcsIHNlbmRBcHByb3ZlIH0gZnJvbSAiQC9hcGkvZHJhd2luZy9kcmF3aW5nIjsNCmltcG9ydCB7IGxpc3RNYXRlcmlhbEluZm8gfSBmcm9tICJAL2FwaS9tYXRlcmlhbEluZm8vbWF0ZXJpYWxJbmZvIjsNCmltcG9ydCB7IGdldExpc3RCeVJvbGVLZXkgfSBmcm9tICdAL2FwaS9zeXN0ZW0vcm9sZSc7DQoNCg0KZXhwb3J0IGRlZmF1bHQgew0KICBuYW1lOiAiVGVjaG5pY2FsIiwNCiAgZGF0YSgpIHsNCiAgICByZXR1cm4gew0KICAgICAgLy8g6YGu572p5bGCDQogICAgICBsb2FkaW5nOiBmYWxzZSwNCiAgICAgIGlkczogW10sDQogICAgICAvLyDmmL7npLrmkJzntKLmnaHku7YNCiAgICAgIHNob3dTZWFyY2g6IHRydWUsDQogICAgICAvLyDmgLvmnaHmlbANCiAgICAgIHRvdGFsOiAwLA0KICAgICAgdXNlclRvdGFsOiAwLA0KICAgICAgdG90YWxUYmw6IDAsDQogICAgICAvLyDlm77nurjlupPooajmoLzmlbDmja4NCiAgICAgIGRyYXdpbmdMaXN0OiBbXSwNCiAgICAgIC8vIOeJqeaWmee8lueggeWIl+ihqOaVsOaNrg0KICAgICAgdGJsTGlzdDogW10sDQogICAgICB1c2VyTGlzdDogW10sDQogICAgICAvLyDmjqfliLblhajpgInlpI3pgInmoYbnmoTnirbmgIENCiAgICAgIHNlbGVjdEFsbDogZmFsc2UsDQogICAgICAvLyDnlKjkuo7kv53lrZjpgInkuK3nmoTooYzmlbDmja4NCiAgICAgIHNlbGVjdGVkUm93OiBudWxsLA0KICAgICAgLy8g5L+d5a2Y6YCJ5Lit55qE54mp5paZ57yW56CB5pWw5o2uDQogICAgICBzZWxlY3RlZE1hdGVyaWFsOiBudWxsLA0KICAgICAgc2VsZWN0ZWRVc2VySWQ6IG51bGwsDQogICAgICBzZWxlY3RlZFVzZXI6IG51bGwsDQogICAgICAvLyDlvLnlh7rlsYLmoIfpopgNCiAgICAgIHRpdGxlOiAiIiwNCiAgICAgIC8vIOaYr+WQpuaYvuekuuW8ueWHuuWxgg0KICAgICAgb3BlbjogZmFsc2UsDQogICAgICAvL+mZhOS7tuWIl+ihqA0KICAgICAgZmlsZUxpc3Q6IFtdLA0KICAgICAgLy/mioDmnK/ljY/orq7liJfooagNCiAgICAgIHRlY2hGaWxlTGlzdDpbXSwNCiAgICAgIC8v6Lef6Liq5piv5ZCm6YCJ5oup5LqG4oCc5YaF6YOo5Zu+57q44oCdDQogICAgICBpc0ludGVybmFsRHJhd2luZzogZmFsc2UsDQogICAgICBpc0V4dGVybmFsRHJhd2luZzogZmFsc2UsDQogICAgICBpc0V4dGVybmFsUmV2aWV3ZXJWaXNpYmxlOiBmYWxzZSwNCiAgICAgIGlzVGJsTW9kYWxWaXNpYmxlOiBmYWxzZSwNCiAgICAgIGlzVGJsQ2xvc2U6IGZhbHNlLA0KICAgICAgc2hvd0RpYWxvZzogZmFsc2UsDQogICAgICB0YmxUaXRsZTogJ+mAieaLqeeJqeaWmee8lueggScsDQogICAgICB1cGxvYWQ6IHsNCiAgICAgICAgLy/orr7nva7kuIrkvKDnmoTor7fmsYLlpLTpg6gNCiAgICAgICAgaGVhZGVyczoge30sDQogICAgICAgIC8vIOS4iuS8oOeahOWcsOWdgA0KICAgICAgICB1cmw6IHByb2Nlc3MuZW52LlZVRV9BUFBfQkFTRV9BUEkgKyAiL2FwcC9jb21tb24vdXBsb2FkRGVjcnlwdFBkZk1pbmlvIiwNCiAgICAgICAgaXNVcGxvYWRpbmc6IGZhbHNlLA0KICAgICAgfSwNCiAgICAgIC8vIOafpeivouWPguaVsA0KICAgICAgcXVlcnlQYXJhbXM6IHsNCiAgICAgICAgcGFnZU51bTogMSwNCiAgICAgICAgcGFnZVNpemU6IDEwLA0KICAgICAgICBzdWl0RXF1aXBtZW50OiBudWxsLCBkcmF3aW5nTm86ICcnLCBtYXRlcmlhbHNDb2RlOiAnJywgZHJhd2luZ1R5cGU6IG51bGwsIGRyYXdpbmdTdGF0dXM6IG51bGwsIGRyYXdpbmdSZXNvdXJjZTogbnVsbCwgc3BlY2lNb2RlbDogbnVsbCwgcmV2aWV3ZXI6IG51bGwsIGRlc2lnbmVySW5uZXI6IG51bGwsIHJldmlld2VyT3V0ZXI6IG51bGwsIGRyYXdpbmdDb21wYW55T3V0ZXI6IG51bGwsIHRlbmFudElkOiBudWxsLCBhZGRGYWN0b3J5OiBudWxsLCByZW1hcmtzOiBudWxsLCBhdHRhY2htZW50VXVpZDogbnVsbCwgaW5zdGFuY2VJZDogbnVsbCwgaXNOb3RpY2VDeDogbnVsbCwgb2JqZWN0VmVyc2lvbk51bWJlcjogbnVsbCwgZmxvd1N0YXR1czogbnVsbCwgY3JlYXRlVXNlck5vOiBudWxsLCBjcmVhdGVEYXRlVGltZTogbnVsbCwgdXBkYXRlVXNlck5vOiBudWxsLCB1cGRhdGVEYXRlVGltZTogbnVsbCwgY29tcGFueUlkOiBudWxsLCBhdHRhY2htZW50OiBudWxsLCBmaWxlVXJsOiBudWxsDQogICAgICB9LA0KICAgICAgdGJsUXVlcnlQYXJhbXM6IHsNCiAgICAgICAgcGFnZU51bTogMSwNCiAgICAgICAgcGFnZVNpemU6IDEwLA0KICAgICAgICBtYXRlcmlhbHNDb2RlOiAnJywgY3JlYXRlRGF0ZVRpbWU6IG51bGwsIG1hdGVyaWFsc05hbWU6IG51bGwsIHNwZWNpTW9kZWw6IG51bGwNCiAgICAgIH0sDQogICAgICAvL+afpeivouWuoeaguOS6uuWPguaVsA0KICAgICAgYXBwcm92ZXJQYXJhbXM6ew0KICAgICAgICBwYWdlTnVtOiAxLA0KICAgICAgICBwYWdlU2l6ZTogMTAsDQogICAgICAgIHVzZXJOYW1lOiAnJywNCiAgICAgICAgbmlja05hbWU6IHVuZGVmaW5lZCwNCiAgICAgICAgcm9sZUtleTogdW5kZWZpbmVkDQogICAgICB9LA0KICAgICAgLy8g6KGo5Y2V5Y+C5pWwDQogICAgICBmb3JtOiB7DQogICAgICAgIGRyYXdpbmdSZXNvdXJjZTogJycsIC8vIOm7mOiupOaDheWGteS4i+ayoeaciemAieaLqeS7u+S9leWbvue6uOadpea6kA0KICAgICAgICBkZXNpZ25lcklubmVyOiAnJywNCiAgICAgICAgcmV2aWV3ZXJPdXRlcjogJycsDQogICAgICAgIGRyYXdpbmdDb21wYW55T3V0ZXI6ICcnLA0KICAgICAgICByZXZpZXdlck91dGVyOiAnJywNCiAgICAgICAgc3BlY2lNb2RlbFJlYWRvbmx5OiBmYWxzZSwNCiAgICAgICAgbWF0ZXJpYWxzQ29kZTogJycsDQogICAgICAgIHNwZWNpTW9kZWw6ICcnLA0KICAgICAgICBkcmF3aW5nVHlwZTogJycsDQogICAgICAgIGZpbGVVcmw6ICcnLA0KICAgICAgfSwNCiAgICAgIC8vIOihqOWNleagoemqjA0KICAgICAgcnVsZXM6IHsNCiAgICAgICAgZHJhd2luZ05vOiBbeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogJ+ivt+i+k+WFpeWbvue6uOWPtycsIHRyaWdnZXI6ICdibHVyJyB9XSwNCiAgICAgICAgbWF0ZXJpYWxzQ29kZTogW3sgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICfor7fovpPlhaXnianmlpnnvJbnoIEnLCB0cmlnZ2VyOiAnYmx1cicgfV0sDQogICAgICAgIGRyYXdpbmdUeXBlOiBbeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogJ+ivt+mAieaLqeWbvue6uOexu+WeiycsIHRyaWdnZXI6ICdjaGFuZ2UnIH1dLA0KICAgICAgICBkcmF3aW5nUmVzb3VyY2U6IFt7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAn6K+36YCJ5oup5Zu+57q45p2l5rqQJywgdHJpZ2dlcjogJ2NoYW5nZScgfV0NCiAgICAgIH0sDQogICAgfTsNCiAgfSwNCiAgY3JlYXRlZCgpIHsNCiAgICB0aGlzLmdldExpc3QoKTsNCiAgICB0aGlzLmdldFVzZXJMaXN0KCk7DQogIH0sDQogIG1ldGhvZHM6IHsNCg0KICAgIC8v5pe26Ze05qC85byP5YyWDQogICAgZm9ybWF0VGltZShpc29UaW1lU3RyaW5nKSB7DQogICAgICBpZiAoIWlzb1RpbWVTdHJpbmcpIHJldHVybiAnJzsNCg0KICAgICAgLy8g6Kej5p6QIElTTyA4NjAxIOaXtumXtOWtl+espuS4sg0KICAgICAgY29uc3QgZGF0ZSA9IG5ldyBEYXRlKGlzb1RpbWVTdHJpbmcpOw0KDQogICAgICAvLyDmj5Dlj5blubTmnIjml6Xml7bliIbnp5INCiAgICAgIGNvbnN0IHllYXIgPSBkYXRlLmdldEZ1bGxZZWFyKCk7DQogICAgICBjb25zdCBtb250aCA9IFN0cmluZyhkYXRlLmdldE1vbnRoKCkgKyAxKS5wYWRTdGFydCgyLCAnMCcpOyAvLyDmnIjku73mmK/ku44w5byA5aeL55qE77yM5omA5Lul5YqgMQ0KICAgICAgY29uc3QgZGF5ID0gU3RyaW5nKGRhdGUuZ2V0RGF0ZSgpKS5wYWRTdGFydCgyLCAnMCcpOw0KICAgICAgY29uc3QgaG91cnMgPSBTdHJpbmcoZGF0ZS5nZXRIb3VycygpKS5wYWRTdGFydCgyLCAnMCcpOw0KICAgICAgY29uc3QgbWludXRlcyA9IFN0cmluZyhkYXRlLmdldE1pbnV0ZXMoKSkucGFkU3RhcnQoMiwgJzAnKTsNCiAgICAgIGNvbnN0IHNlY29uZHMgPSBTdHJpbmcoZGF0ZS5nZXRTZWNvbmRzKCkpLnBhZFN0YXJ0KDIsICcwJyk7DQoNCiAgICAgIC8vIOi/lOWbnuagvOW8j+WMlueahOWtl+espuS4sg0KICAgICAgcmV0dXJuIGAke3llYXJ9LSR7bW9udGh9LSR7ZGF5fSAke2hvdXJzfToke21pbnV0ZXN9OiR7c2Vjb25kc31gOw0KDQogICAgfSwNCiAgICAgLy8g5a2X56ym5Liy5pe26Ze05qC85byP5YyWDQogICAgIGZvcm1hdFN0cmluZ1RpbWUodGltZSkgew0KICAgICAgaWYgKHR5cGVvZiB0aW1lID09PSAic3RyaW5nIikgew0KICAgICAgICByZXR1cm4gKA0KICAgICAgICAgIHRpbWUuc3Vic3RyaW5nKDAsIDQpICsNCiAgICAgICAgICAiLSIgKw0KICAgICAgICAgIHRpbWUuc3Vic3RyaW5nKDQsIDYpICsNCiAgICAgICAgICAiLSIgKw0KICAgICAgICAgIHRpbWUuc3Vic3RyaW5nKDYsIDgpICsNCiAgICAgICAgICAiICIgKw0KICAgICAgICAgIHRpbWUuc3Vic3RyaW5nKDgsIDEwKSArDQogICAgICAgICAgIjoiICsNCiAgICAgICAgICB0aW1lLnN1YnN0cmluZygxMCwgMTIpICsNCiAgICAgICAgICAiOiIgKw0KICAgICAgICAgIHRpbWUuc3Vic3RyaW5nKDEyLCAxNCkNCiAgICAgICAgKTsNCiAgICAgIH0gZWxzZSB7DQogICAgICAgIHJldHVybiB0aW1lOw0KICAgICAgfQ0KICAgIH0sDQogICAgdGFibGVSb3dDbGFzc05hbWUoeyByb3csIHJvd0luZGV4IH0pIHsNCiAgICAgIGlmIChyb3cuc3RhdHVzID09ICcxJykgew0KICAgICAgICByZXR1cm4gJ3dhcm5pbmctcm93Jw0KICAgICAgfQ0KICAgICAgcmV0dXJuICcnDQogICAgfSwNCg0KICAgIC8qKiDmn6Xor6Llm77nurjlupPliJfooaggKi8NCiAgICBnZXRMaXN0KCkgew0KICAgICAgdGhpcy5sb2FkaW5nID0gdHJ1ZTsNCiAgICAgIGxpc3REcmF3aW5nKHRoaXMucXVlcnlQYXJhbXMpLnRoZW4ocmVzcG9uc2UgPT4gew0KICAgICAgICB0aGlzLmRyYXdpbmdMaXN0ID0gcmVzcG9uc2Uucm93czsNCiAgICAgICAgdGhpcy50b3RhbCA9IHJlc3BvbnNlLnRvdGFsOw0KICAgICAgICB0aGlzLmxvYWRpbmcgPSBmYWxzZTsNCiAgICAgIH0pOw0KICAgIH0sDQogICAgLyoq5p+l6K+i54mp5paZ57yW56CB5YiX6KGoICovDQogICAgZ2V0TWF0ZXJpYWxMaXN0KCkgew0KICAgICAgdGhpcy5sb2FkaW5nID0gdHJ1ZTsNCiAgICAgIGxpc3RNYXRlcmlhbEluZm8odGhpcy50YmxRdWVyeVBhcmFtcykudGhlbihyZXNwb25zZSA9PiB7DQogICAgICAgIHRoaXMudGJsTGlzdCA9IHJlc3BvbnNlLnJvd3M7DQogICAgICAgIHRoaXMudG90YWxUYmwgPSByZXNwb25zZS50b3RhbDsNCiAgICAgICAgdGhpcy5sb2FkaW5nID0gZmFsc2U7DQogICAgICB9KTsNCiAgICB9LA0KICAgIC8qKuafpeivoueUqOaIt+WIl+ihqCAqLw0KICAgIGdldFVzZXJMaXN0KCkgew0KICAgICAgdGhpcy5sb2FkaW5nID0gdHJ1ZQ0KICAgICAgdGhpcy5hcHByb3ZlclBhcmFtcy5yb2xlS2V5ID0gJ2RyYXdpbmcuYXBwcm92ZXIuZmFjdG9yeSc7DQogICAgICBnZXRMaXN0QnlSb2xlS2V5KHRoaXMuYXBwcm92ZXJQYXJhbXMpLnRoZW4oKHJlc3BvbnNlKSA9PiB7DQogICAgICAgICAgdGhpcy51c2VyTGlzdCA9IHJlc3BvbnNlLnJvd3MNCiAgICAgICAgICB0aGlzLnVzZXJUb3RhbCA9IHJlc3BvbnNlLnRvdGFsDQogICAgICAgICAgdGhpcy5sb2FkaW5nID0gZmFsc2UNCiAgICAgICAgfQ0KICAgICAgKQ0KICAgIH0sDQogICAgLy8g5Y+W5raI5oyJ6ZKuDQogICAgY2FuY2VsKCkgew0KICAgICAgdGhpcy5vcGVuID0gZmFsc2U7DQogICAgICB0aGlzLnJlc2V0KCk7DQogICAgfSwNCiAgICBjbGVhck1hdGVyaWFsc0NvZGUoKSB7DQogICAgICB0aGlzLmZvcm0ubWF0ZXJpYWxzQ29kZSA9ICcnOyAvLyDmuIXnqbrnianmlpnnvJbnoIENCiAgICAgIHRoaXMuZm9ybS5zcGVjaU1vZGVsID0gJyc7DQogICAgfSwNCiAgICBjbGVhclNlYXJjaE1hdGVyaWFsc0NvZGUoKSB7DQogICAgICB0aGlzLnF1ZXJ5UGFyYW1zLm1hdGVyaWFsc0NvZGUgPSAnJzsNCiAgICB9LA0KICAgIC8vIOihqOWNlemHjee9rg0KICAgIHJlc2V0KCkgew0KICAgICAgdGhpcy5mb3JtID0gew0KICAgICAgICBpZDogbnVsbCwgb3JpZ2luYWxJZDogbnVsbCwgc3VpdEVxdWlwbWVudDogbnVsbCwgZHJhd2luZ05vOiBudWxsLCBtYXRlcmlhbHNDb2RlOiBudWxsLCBkcmF3aW5nVHlwZTogbnVsbCwgZHJhd2luZ1N0YXR1czogbnVsbCwgZHJhd2luZ1Jlc291cmNlOiBudWxsLCBzcGVjaU1vZGVsOiBudWxsLCByZXZpZXdlcjogbnVsbCwNCiAgICAgICAgZGVzaWduZXJJbm5lcjogbnVsbCwgcmV2aWV3ZXJPdXRlcjogbnVsbCwgZHJhd2luZ0NvbXBhbnlPdXRlcjogbnVsbCwgdGVuYW50SWQ6IG51bGwsIGFkZEZhY3Rvcnk6IG51bGwsIHJlbWFya3M6IG51bGwsDQogICAgICAgIGF0dGFjaG1lbnRVdWlkOiBudWxsLCBpbnN0YW5jZUlkOiBudWxsLCBpc05vdGljZUN4OiBudWxsLCBvYmplY3RWZXJzaW9uTnVtYmVyOiBudWxsLCBmbG93U3RhdHVzOiBudWxsLCBjcmVhdGVVc2VyTm86IG51bGwsIGNyZWF0ZURhdGVUaW1lOiBudWxsLA0KICAgICAgICB1cGRhdGVVc2VyTm86IG51bGwsIHVwZGF0ZURhdGVUaW1lOiBudWxsLCBjb21wYW55SWQ6IG51bGwsIGF0dGFjaG1lbnQ6IG51bGwsIGZpbGVVcmw6IG51bGwNCiAgICAgIH07DQogICAgICB0aGlzLnJlc2V0Rm9ybSgiZm9ybSIpOw0KICAgICAgdGhpcy5maWxlTGlzdCA9IFtdOw0KICAgICAgdGhpcy50ZWNoRmlsZUxpc3QgPSBbXTsNCiAgICB9LA0KICAgIG9uRHJhd2luZ1Jlc291cmNlQ2hhbmdlKHZhbHVlKSB7DQogICAgICBpZiAodmFsdWUgPT09ICcyJykgeyAvLyDlhoXpg6jlm77nurgNCiAgICAgICAgdGhpcy5pc0ludGVybmFsRHJhd2luZyA9IHRydWU7DQogICAgICAgIHRoaXMuaXNFeHRlcm5hbERyYXdpbmcgPSBmYWxzZTsNCiAgICAgICAgdGhpcy5pc0V4dGVybmFsUmV2aWV3ZXJWaXNpYmxlID0gZmFsc2U7DQogICAgICB9IGVsc2UgaWYgKHZhbHVlID09PSAnMScpIHsgLy8g5aSW5p2l5Zu+57q4DQogICAgICAgIHRoaXMuaXNJbnRlcm5hbERyYXdpbmcgPSBmYWxzZTsNCiAgICAgICAgdGhpcy5pc0V4dGVybmFsRHJhd2luZyA9IHRydWU7DQogICAgICAgIHRoaXMuaXNFeHRlcm5hbFJldmlld2VyVmlzaWJsZSA9IHRydWU7DQogICAgICB9IGVsc2UgeyAvLyDlpoLmnpzmsqHmnInpgInmi6nku7vkvZXlgLwNCiAgICAgICAgdGhpcy5pc0ludGVybmFsRHJhd2luZyA9IGZhbHNlOw0KICAgICAgICB0aGlzLmlzRXh0ZXJuYWxEcmF3aW5nID0gZmFsc2U7DQogICAgICAgIHRoaXMuaXNFeHRlcm5hbFJldmlld2VyVmlzaWJsZSA9IGZhbHNlOw0KICAgICAgfQ0KICAgIH0sDQogICAgLy8g54mp5paZ57yW56CB5YiX6KGo55qE6YCJ5oup5Y+Y5YyWDQogICAgaGFuZGxlVGJsU2VsZWN0aW9uQ2hhbmdlKHJvd3MpIHsNCiAgICAgIHRoaXMuc2VsZWN0ZWRNYXRlcmlhbCA9IHJvd3NbMF07DQogICAgfSwNCiAgICAvL+eUqOS6juafpeivouWbvue6uOWuoeaJueWNlQ0KICAgIHF1ZXJ5TWF0ZXJpYWxDb2RlKCkgew0KICAgICAgY29uc29sZS5sb2codGhpcy5zZWxlY3RlZE1hdGVyaWFsKQ0KICAgICAgaWYgKHRoaXMuc2VsZWN0ZWRNYXRlcmlhbCkgew0KICAgICAgICBpZiAodGhpcy5vcGVuKSB7DQogICAgICAgICAgLy/mlrDlop7miJbkv67mlLnml7YNCiAgICAgICAgICAvL+WIpOaWreaYr+WQpuWPr+eUqA0KICAgICAgICAgIGlmKHRoaXMuc2VsZWN0ZWRNYXRlcmlhbC5zdGF0ZUlkICE9PSAnQScpew0KICAgICAgICAgICAgdGhpcy4kbWVzc2FnZS53YXJuaW5nKCfor6XnianmlpnnvJbnoIHlt7LlgZznlKjvvIzor7fph43mlrDpgInmi6nvvIEnKTsNCiAgICAgICAgICAgIHJldHVybjsNCiAgICAgICAgICB9DQogICAgICAgICAgdGhpcy5mb3JtLm1hdGVyaWFsc0NvZGUgPSB0aGlzLnNlbGVjdGVkTWF0ZXJpYWwuaXRlbUlkOw0KICAgICAgICAgIHRoaXMuZm9ybS5zcGVjaU1vZGVsID0gdGhpcy5zZWxlY3RlZE1hdGVyaWFsLml0ZW1Nb2RlbDsNCiAgICAgICAgICB0aGlzLmZvcm0uc3BlY2lNb2RlbFJlYWRvbmx5ID0gdHJ1ZTsgLy8g6K6+572u6KeE5qC85Z6L5Y+36L6T5YWl5qGG5Li65Y+q6K+7DQogICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgLy/mkJzntKLml7YNCiAgICAgICAgICB0aGlzLnF1ZXJ5UGFyYW1zLm1hdGVyaWFsc0NvZGUgPSB0aGlzLnNlbGVjdGVkTWF0ZXJpYWwuaXRlbUlkOw0KICAgICAgICB9DQoNCg0KICAgICAgICB0aGlzLnJlc2V0VGJsUXVlcnkoKTsgLy8g6YeN572u54mp5paZ57yW56CB5YiX6KGo5pCc57Si5p2h5Lu2DQogICAgICAgIHRoaXMuc2VsZWN0ZWRNYXRlcmlhbCA9IG51bGw7IC8vIOa4heepuuW3sumAieaLqeeahOeJqeaWmQ0KICAgICAgICB0aGlzLmlzVGJsTW9kYWxWaXNpYmxlID0gZmFsc2U7IC8vIOWFs+mXreWvueivneahhg0KICAgICAgfSBlbHNlIHsNCiAgICAgICAgLy8g5aaC5p6c5rKh5pyJ6YCJ5oup5Lu75L2V54mp5paZ77yM5YiZ5o+Q56S655So5oi3DQogICAgICAgIHRoaXMuJG1lc3NhZ2Uud2FybmluZygn6K+36YCJ5oup5LiA5p2h54mp5paZ57yW56CB6K6w5b2V77yBJyk7DQogICAgICB9DQogICAgfSwNCiAgICAvLw0KDQoNCiAgICAvLyDlpITnkIbmkJzntKLpg6jliIbnianmlpnnvJbnoIHliJfooajnmoTnoa7lrprmjInpkq4NCiAgICBUYmxTZWxlY3RGb3JtKCkgew0KICAgICAgaWYgKHRoaXMuc2VsZWN0ZWRNYXRlcmlhbCkgew0KICAgICAgICAvLyDlpoLmnpzpgInmi6nkuobnianmlpnvvIzliJnmm7TmlrDooajljZXkuK3nmoTnianmlpnnvJbnoIHlkozop4TmoLzlnovlj7cNCiAgICAgICAgdGhpcy5xdWVyeVBhcmFtcy5tYXRlcmlhbHNDb2RlID0gdGhpcy5zZWxlY3RlZE1hdGVyaWFsLm1hdGVyaWFsc0NvZGU7DQogICAgICAgIHRoaXMucmVzZXRUYmxRdWVyeSgpOyAvLyDph43nva7nianmlpnnvJbnoIHliJfooajmkJzntKLmnaHku7YNCiAgICAgICAgdGhpcy5zZWxlY3RlZE1hdGVyaWFsID0gbnVsbDsgLy8g5riF56m65bey6YCJ5oup55qE54mp5paZDQogICAgICAgIHRoaXMuaXNUYmxDbG9zZSA9IGZhbHNlOyAvLyDlhbPpl63lr7nor53moYYNCiAgICAgICAgdGhpcy5pc1RibE1vZGFsVmlzaWJsZSA9IGZhbHNlOyAvLyDlhbPpl63lj6bkuIDkuKrlr7nor53moYYNCiAgICAgIH0gZWxzZSB7DQogICAgICAgIC8vIOWmguaenOayoeaciemAieaLqeS7u+S9leeJqeaWme+8jOWImeaPkOekuueUqOaItw0KICAgICAgICB0aGlzLiRtZXNzYWdlLndhcm5pbmcoJ+ivt+mAieaLqeS4gOadoeeJqeaWmee8lueggeiusOW9le+8gScpOw0KICAgICAgfQ0KICAgIH0sDQoNCiAgICB0b2dnbGVTZWxlY3Rpb24oKSB7DQogICAgICB0aGlzLiRyZWZzLm11bHRpcGxlVGFibGUudG9nZ2xlQWxsU2VsZWN0aW9uKCk7DQogICAgfSwNCg0KICAgIC8v5Zu+57q45Y2V6YCJDQogICAgaGFuZGxlU2VsZWN0aW9uQ2hhbmdlKHJvdykgew0KICAgICAgaWYgKHRoaXMuc2VsZWN0ZWRSb3cgPT09IHJvdykgew0KICAgICAgICB0aGlzLnNlbGVjdGVkUm93ID0gbnVsbDsgLy8g5aaC5p6c5b2T5YmN6YCJ5Lit55qE6KGM5YaN5qyh6KKr54K55Ye777yM5YiZ5Y+W5raI6YCJ5LitDQogICAgICB9IGVsc2Ugew0KICAgICAgICB0aGlzLnNlbGVjdGVkUm93ID0gcm93OyAvLyDlkKbliJnvvIzpgInkuK3or6XooYwNCiAgICAgIH0NCiAgICB9LA0KDQogICAgLy/nianmlpnljZXpgIkNCiAgICBoYW5kbGVNYXRlcmlhbHNSb3dDbGljayhyb3cpIHsNCiAgICAgICAgdGhpcy5zZWxlY3RlZE1hdGVyaWFsID0gcm93Ow0KICAgIH0sDQoNCiAgICBoYW5kbGVBcHByb3ZlclJvd0NsaWNrKHJvdykgew0KICAgICAgLy8g5Y2V5Ye76KGM5pe26Kem5Y+RDQogICAgICAgIHRoaXMuc2VsZWN0ZWRVc2VySWQgPSByb3cudXNlck5hbWU7DQogICAgfSwNCg0KDQoNCiAgICAvLyDmuIXnqbrpgInmi6nnirbmgIENCiAgICBoYW5kbGVDbGVhclNlbGVjdGlvbigpIHsNCiAgICAgIHRoaXMuc2VsZWN0ZWRSb3cgPSBudWxsOw0KICAgICAgdGhpcy5pZHMgPSBbXTsNCiAgICAgIHRoaXMuc2VsZWN0ZWRVc2VySWQgPSBudWxsOw0KICAgIH0sDQoNCiAgICAvL+S4iuS8oOWbvueJhw0KICAgIGhhbmRsZVBpY3R1cmVDYXJkUHJldmlldyhmaWxlKSB7DQogICAgICB2YXIgbG9jYWxVcmwgPSB3aW5kb3cubG9jYXRpb24uaG9zdDsNCiAgICAgIGlmIChmaWxlLnVybCAhPSBudWxsKSB7DQogICAgICAgIGlmIChsb2NhbFVybCA9PSAiMTcyLjEuMjAzLjIzOjgwOTkiKSB7DQogICAgICAgICAgZmlsZS51cmwgPSBmaWxlLnVybC5yZXBsYWNlKCJ5ZHh0LmNpdGljc3RlZWwuY29tOjgwOTkiLCAiMTcyLjEuMjAzLjIzOjgwOTkiKTsNCiAgICAgICAgfQ0KICAgICAgICB3aW5kb3cub3BlbihmaWxlLnVybCk7DQogICAgICB9DQogICAgICBpZiAoZmlsZS5yZXNwb25zZSAmJiBmaWxlLnJlc3BvbnNlLnVybCkgew0KICAgICAgICBpZiAobG9jYWxVcmwgPT0gIjE3Mi4xLjIwMy4yMzo4MDk5Iikgew0KICAgICAgICAgIGxldCB0bXBVcmwgPSBmaWxlLnJlc3BvbnNlLnVybC5yZXBsYWNlKCJ5ZHh0LmNpdGljc3RlZWwuY29tOjgwOTkiLCAiMTcyLjEuMjAzLjIzOjgwOTkiKTsNCiAgICAgICAgICB3aW5kb3cub3Blbih0bXBVcmwpOw0KICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgIHdpbmRvdy5vcGVuKGZpbGUucmVzcG9uc2UudXJsKTsNCiAgICAgICAgfQ0KICAgICAgfQ0KICAgIH0sDQoNCiAgICBoYW5kbGVQaWN0dXJlVXAoZmlsZSwgZmlsZUxpc3QpIHsNCiAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoIuWbvueJh+S4iumZkOS4ujEiKTsNCiAgICB9LA0KDQogICAgaGFuZGxlUGljdHVyZVVwZGF0ZVN1Y2Nlc3MocmVzcG9uc2UsIGZpbGUsIGZpbGVMaXN0KSB7DQogICAgICBsZXQgdGhhdCA9IHRoaXM7DQogICAgICBpZiAocmVzcG9uc2UuY29kZSA9PSAyMDApIHsNCiAgICAgICAgdGhhdC5maWxlTGlzdC5wdXNoKHsgdXJsOiByZXNwb25zZS51cmwsIG5hbWU6IGZpbGUubmFtZSB9KTsNCiAgICAgICAgdGhhdC5maWxlTGlzdFRvUGljdHVyZSgpOw0KICAgICAgfSBlbHNlIHsNCiAgICAgICAgdGhhdC5yZW1vdmVGYWlsZWRGaWxlKGZpbGUpOw0KICAgICAgICB0aGF0LiRtZXNzYWdlLmVycm9yKHJlc3BvbnNlLm1zZyk7DQogICAgICB9DQogICAgICB0aGF0LnVwbG9hZC5pc1VwbG9hZGluZyA9IGZhbHNlOw0KICAgIH0sDQogICAgLy/lm77niYfkuIrkvKDkuK3lpITnkIYNCiAgICBoYW5kbGVQaWN0dXJlVXBsb2FkUHJvZ3Jlc3MoKSB7DQogICAgICB0aGlzLnVwbG9hZC5pc1VwbG9hZGluZyA9IHRydWU7DQogICAgfSwNCg0KICAgIGhhbmRsZVBpY3R1cmVSZW1vdmUoZmlsZSwgZmlsZUxpc3QpIHsNCiAgICAgIGZvciAobGV0IGkgPSAwOyBpIDwgdGhpcy5maWxlTGlzdC5sZW5ndGg7IGkrKykgew0KICAgICAgICBpZiAodGhpcy5maWxlTGlzdFtpXS51cmwgPT0gZmlsZS51cmwpIHsNCiAgICAgICAgICB0aGlzLmZpbGVMaXN0LnNwbGljZShpLCAxKTsNCiAgICAgICAgfQ0KICAgICAgfQ0KICAgICAgdGhpcy5maWxlTGlzdFRvUGljdHVyZSgpOw0KICAgIH0sDQogICAgZmlsZUxpc3RUb1BpY3R1cmUoKSB7DQogICAgICBsZXQgZmlsZVVybCA9IFtdOw0KICAgICAgdGhpcy5maWxlTGlzdC5mb3JFYWNoKChpdGVtKSA9PiB7DQogICAgICAgIGZpbGVVcmwucHVzaCh7IHVybDogaXRlbS51cmwgfSk7DQogICAgICB9KTsNCiAgICAgIGlmIChmaWxlVXJsLmxlbmd0aCA+IDApDQogICAgICAgIHRoaXMuZm9ybS5maWxlVXJsID0gSlNPTi5zdHJpbmdpZnkoZmlsZVVybCkudG9TdHJpbmcoKTsNCiAgICAgIGVsc2UgdGhpcy5mb3JtLmZpbGVVcmwgPSAiW10iOw0KICAgIH0sDQogICAgcmVtb3ZlRmFpbGVkRmlsZShmaWxlKSB7DQogICAgICAvLyDku44gZmlsZUxpc3Qg5Lit56e76Zmk5aSx6LSl55qE5paH5Lu2DQogICAgICB0aGlzLmZpbGVMaXN0ID0gdGhpcy5maWxlTGlzdC5maWx0ZXIoaXRlbSA9PiBpdGVtICE9PSBmaWxlKTsNCiAgICB9LA0KDQogICAgLy8g5oqA5pyv5Y2P6K6uDQogICAgaGFuZGxlVGVjaFByZXZpZXcoZmlsZSkgew0KICAgICAgdGhpcy5oYW5kbGVGaWxlUHJldmlldyhmaWxlKTsNCiAgICB9LA0KDQogICAgaGFuZGxlVGVjaFVwbG9hZFByb2dyZXNzKGV2ZW50LCBmaWxlLCBmaWxlTGlzdCkgew0KICAgICAgdGhpcy51cGxvYWQuaXNVcGxvYWRpbmcgPSB0cnVlOw0KICAgIH0sDQoNCiAgICBoYW5kbGVUZWNoRXhjZWVkKGZpbGVzLCBmaWxlTGlzdCkgew0KICAgICAgdGhpcy4kbWVzc2FnZS53YXJuaW5nKGDmioDmnK/ljY/orq7lj6rog73kuIrkvKDkuIDkuKrmlofku7bvvIzlvZPliY3pgInmi6nkuoYgJHtmaWxlcy5sZW5ndGh9IOS4quaWh+S7tmApOw0KICAgIH0sDQoNCiAgICBoYW5kbGVUZWNoVXBsb2FkU3VjY2VzcyhyZXNwb25zZSwgZmlsZSwgZmlsZUxpc3QpIHsNCiAgICAgIHRoaXMudXBsb2FkLmlzVXBsb2FkaW5nID0gZmFsc2U7DQogICAgICBpZiAocmVzcG9uc2UuY29kZSA9PT0gMjAwKSB7DQogICAgICAgIHRoaXMudGVjaEZpbGVMaXN0ID0gW3sgdXJsOiByZXNwb25zZS51cmwsIG5hbWU6IGZpbGUubmFtZSB9XTsNCiAgICAgICAgdGhpcy5mb3JtLnRlY2huaWNhbEFncmVlbWVudCA9IEpTT04uc3RyaW5naWZ5KHRoaXMudGVjaEZpbGVMaXN0KTsNCiAgICAgIH0gZWxzZSB7DQogICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IocmVzcG9uc2UubXNnKTsNCiAgICAgICAgdGhpcy5yZW1vdmVGYWlsZWRGaWxlKGZpbGUsIHRoaXMudGVjaEZpbGVMaXN0KTsNCiAgICAgIH0NCiAgICB9LA0KDQogICAgaGFuZGxlVGVjaFJlbW92ZShmaWxlLCBmaWxlTGlzdCkgew0KICAgICAgdGhpcy50ZWNoRmlsZUxpc3QgPSBbXTsNCiAgICAgIHRoaXMuZm9ybS50ZWNobmljYWxBZ3JlZW1lbnQgPSAiIjsNCiAgICB9LA0KDQogICAgIC8vIOmAmueUqOaWh+S7tumihOiniOaWueazlQ0KICAgICBoYW5kbGVGaWxlUHJldmlldyhmaWxlKSB7DQogICAgICBsZXQgdXJsID0gZmlsZS51cmwgfHwgKGZpbGUucmVzcG9uc2UgJiYgZmlsZS5yZXNwb25zZS51cmwpOw0KICAgICAgaWYgKCF1cmwpIHJldHVybjsNCg0KICAgICAgLy8g5pys5Zyw5byA5Y+R546v5aKD5pu/5o2iDQogICAgICBpZiAod2luZG93LmxvY2F0aW9uLmhvc3QgPT09ICIxNzIuMS4yMDMuMjM6ODA5OSIpIHsNCiAgICAgICAgdXJsID0gdXJsLnJlcGxhY2UoInlkeHQuY2l0aWNzdGVlbC5jb206ODA5OSIsICIxNzIuMS4yMDMuMjM6ODA5OSIpOw0KICAgICAgfQ0KDQogICAgICB3aW5kb3cub3Blbih1cmwpOw0KICAgIH0sDQoNCiAgICAvLyDku47mjIflrprmlofku7bliJfooajkuK3np7vpmaTlpLHotKXmlofku7YNCiAgICByZW1vdmVGYWlsZWRGaWxlKGZpbGUsIHRhcmdldExpc3QpIHsNCiAgICAgIHRhcmdldExpc3QgPSB0YXJnZXRMaXN0LmZpbHRlcihpdGVtID0+IGl0ZW0udWlkICE9PSBmaWxlLnVpZCk7DQogICAgfSwNCg0KICAgIC8qKiDlj5HotbflrqHmibkgKi8NCiAgICBpbml0aWF0ZUFwcHJvdmFsKCkgew0KICAgICAgLy/liKTmlq3mmK/lkKbmnInpgInkuK3nmoTooYwgdGhpcy5zZWxlY3RlZFJvdw0KICAgICAgaWYgKHRoaXMuc2VsZWN0ZWRSb3cgPT0gbnVsbCkgew0KICAgICAgICB0aGlzLiRtZXNzYWdlLndhcm5pbmcoIuivt+mAieaLqeS4gOadoeiusOW9le+8gSIpOw0KICAgICAgICByZXR1cm47DQogICAgICB9DQogICAgICB0aGlzLnNob3dEaWFsb2cgPSB0cnVlOw0KICAgIH0sDQoNCiAgICAvKirmn6XnnIvor6bmg4Xmk43kvZwgKi8NCiAgICBoYW5kbGVEZXRhaWwocm93KSB7DQogICAgICBjb25zb2xlLmxvZyhyb3cpOw0KICAgICAgdGhpcy4kcm91dGVyLnB1c2goIi9kcmF3aW5nL3RlY2huaWNhbERldGFpbC8iICsgcm93LmlkKTsNCiAgICB9LA0KDQogICAgLyoqIOaQnOe0ouaMiemSruaTjeS9nCAqLw0KICAgIGhhbmRsZVF1ZXJ5KCkgew0KICAgICAgdGhpcy5xdWVyeVBhcmFtcy5wYWdlTnVtID0gMTsNCiAgICAgIHRoaXMuZ2V0TGlzdCgpOw0KICAgIH0sDQogICAgLyoq5pCc57Si5a6h5qC45Lq65pON5L2cICovDQogICAgaGFuZGxlVXNlclF1ZXJ5KCkgew0KICAgICAgdGhpcy5hcHByb3ZlclBhcmFtcy5wYWdlTnVtID0xOw0KICAgICAgdGhpcy5nZXRVc2VyTGlzdCgpDQogICAgfSwNCg0KICAgIC8qKiDmkJzntKLnianmlpnnvJbnoIHooaggKi8NCiAgICBzZWFyY2hCeU1hdGVyaWFsc0NvZGUoKSB7DQogICAgICB0aGlzLnRibFF1ZXJ5UGFyYW1zLnBhZ2VOdW0gPSAxOw0KICAgICAgdGhpcy5nZXRNYXRlcmlhbExpc3QoKTsNCiAgICAgIHRoaXMuaXNUYmxNb2RhbFZpc2libGUgPSB0cnVlOw0KICAgIH0sDQoNCiAgICAvLyDmkJzntKLnianmlpnnvJbnoIENCiAgICBzZWFyY2hNYXRlcmlhbHNDb2RlKCkgew0KICAgICAgdGhpcy5zZWFyY2hCeU1hdGVyaWFsc0NvZGUoKTsgLy8g5Zyo5omT5byA5a+56K+d5qGG55qE5ZCM5pe26I635Y+W5pWw5o2uDQogICAgfSwNCg0KICAgIC8v5paw5aKe5pe25pCc57Si54mp5paZ57yW56CBDQogICAgb3Blbk1hdGVyaWFsc0NvZGVEaWFsb2coKSB7DQogICAgICB0aGlzLmlzVGJsTW9kYWxWaXNpYmxlID0gdHJ1ZTsNCiAgICAgIHRoaXMudGJsUXVlcnlQYXJhbXMucGFnZU51bSA9IDE7DQogICAgICB0aGlzLmdldE1hdGVyaWFsTGlzdCgpOw0KICAgIH0sDQoNCiAgICAvKiog6YeN572u5oyJ6ZKu5pON5L2cICovDQogICAgcmVzZXRRdWVyeSgpIHsNCiAgICAgIHRoaXMucmVzZXRGb3JtKCJxdWVyeUZvcm0iKTsNCiAgICAgIHRoaXMuaGFuZGxlUXVlcnkoKTsNCiAgICAgIHRoaXMuZ2V0TGlzdCgpOw0KICAgIH0sDQoNCiAgICAvKirph43nva7lrqHmoLjkurrmk43kvZwgKi8NCiAgICByZXNldFVzZXJRdWVyeSgpIHsNCiAgICAgIHRoaXMucmVzZXRGb3JtKCdxdWVyeUZvcm0nKQ0KICAgICAgdGhpcy5hcHByb3ZlclBhcmFtcy51c2VyTmFtZSA9ICcnOw0KICAgICAgdGhpcy5hcHByb3ZlclBhcmFtcy5uaWNrTmFtZSA9ICcnOw0KICAgICAgdGhpcy5oYW5kbGVVc2VyUXVlcnkoKTsNCiAgICAgIHRoaXMuZ2V0VXNlckxpc3QoKTsNCiAgICAgIHRoaXMuaGFuZGxlQ2xlYXJTZWxlY3Rpb24oKTsNCiAgICB9LA0KDQogICAgLyoqIOmHjee9rueJqeaWmeS7o+eggeWIl+ihqOaMiemSruaTjeS9nCAqLw0KICAgIHJlc2V0VGJsUXVlcnkoKSB7DQogICAgICB0aGlzLnRibFF1ZXJ5UGFyYW1zID0gew0KICAgICAgICBwYWdlTnVtOiAxLA0KICAgICAgICBwYWdlU2l6ZTogMTAsDQogICAgICAgIG1hdGVyaWFsc0NvZGU6IG51bGwsDQogICAgICAgIGNyZWF0ZURhdGVUaW1lOiBudWxsLA0KICAgICAgICBtYXRlcmlhbHNOYW1lOiBudWxsLA0KICAgICAgICBzcGVjaU1vZGVsOiBudWxsDQogICAgICB9Ow0KICAgICAgLy8gdGhpcy5nZXRNYXRlcmlhbExpc3QoKTsNCiAgICB9LA0KDQogICAgLyoqIOaWsOWinuaMiemSruaTjeS9nCAqLw0KICAgIGhhbmRsZUFkZCgpIHsNCiAgICAgIHRoaXMucmVzZXQoKTsNCiAgICAgIHRoaXMub3BlbiA9IHRydWU7DQogICAgICB0aGlzLnRpdGxlID0gIua3u+WKoOaKgOacr+WNj+iuriI7DQogICAgfSwNCiAgICAvKiog5L+u5pS55oyJ6ZKu5pON5L2cICovDQogICAgaGFuZGxlVXBkYXRlKHJvdykgew0KICAgICAgdGhpcy5yZXNldCgpOw0KICAgICAgY29uc29sZS5sb2cocm93KTsNCiAgICAgIGNvbnN0IGlkID0gcm93LmlkIHx8IHRoaXMuaWRzDQogICAgICBnZXREcmF3aW5nKGlkKS50aGVuKHJlc3BvbnNlID0+IHsNCiAgICAgICAgdGhpcy5mb3JtID0gcmVzcG9uc2UuZGF0YTsNCiAgICAgICAgdGhpcy5vcGVuID0gdHJ1ZTsNCiAgICAgICAgdGhpcy50aXRsZSA9ICLkv67mlLnmioDmnK/ljY/orq4iOw0KICAgICAgfSk7DQogICAgICBpZiAocm93LmZpbGVVcmwpIHsNCiAgICAgICAgdGhpcy5maWxlTGlzdCA9IEpTT04ucGFyc2Uocm93LmZpbGVVcmwpOw0KICAgICAgfQ0KICAgICAgZWxzZSB7DQogICAgICAgIHRoaXMuZmlsZUxpc3QgPSBbXQ0KICAgICAgfQ0KICAgIH0sDQoNCiAgICAvLyDlhbPpl63mjInpkq7ngrnlh7vkuovku7YNCiAgICBjbG9zZURpYWxvZygpIHsNCiAgICAgIHRoaXMucmVzZXRUYmxRdWVyeSgpOyAvLyDph43nva7nianmlpnnvJbnoIHliJfooajmkJzntKLmnaHku7YNCiAgICAgIHRoaXMuc2VsZWN0ZWRNYXRlcmlhbCA9IG51bGw7IC8vIOa4heepuuW3sumAieaLqeeahOeJqeaWmQ0KICAgICAgdGhpcy5pc1RibENsb3NlID0gZmFsc2U7IC8vIOWFs+mXreWvueivneahhg0KICAgICAgdGhpcy5pc1RibE1vZGFsVmlzaWJsZSA9IGZhbHNlOyAvLyDlhbPpl63lj6bkuIDkuKrlr7nor53moYYNCiAgICAgIHRoaXMuc2hvd0RpYWxvZyA9IGZhbHNlOw0KICAgICAgdGhpcy5zZWxlY3RlZFVzZXJJZCA9IG51bGw7DQogICAgfSwNCg0KICAgIC8v5o6o6YCB57uZ5a6h5qC45Lq656Gu5a6a5oyJ6ZKuDQogICAgVXNlclNlbGVjdEZvcm0oKSB7DQogICAgICBpZiAodGhpcy5zZWxlY3RlZFVzZXJJZCAhPT0gbnVsbCkgew0KICAgICAgICAvLyDlsIblrqHmoLjkurrnmoQgdXNlcklkIOWPkemAgeWIsOWQjuerrw0KICAgICAgICBsZXQgcGFyYW0gPSB7DQogICAgICAgICAgYXBwcm92ZVVzZXJJZDogdGhpcy5zZWxlY3RlZFVzZXJJZCwNCiAgICAgICAgICBjaXRpY3NYY3RnRHJhd2luZzogdGhpcy5zZWxlY3RlZFJvdw0KICAgICAgICB9Ow0KICAgICAgICBzZW5kQXBwcm92ZShwYXJhbSkudGhlbigoKSA9PiB7DQogICAgICAgICAgdGhpcy4kbWVzc2FnZSh7DQogICAgICAgICAgICB0eXBlOiAnc3VjY2VzcycsDQogICAgICAgICAgICBtZXNzYWdlOiAn5Y+R6LW35a6h5om55oiQ5Yqf77yBJw0KICAgICAgICAgIH0pOw0KICAgICAgICAgIC8vIOa4heepuumAieaLqeeKtuaAgQ0KICAgICAgICAgIHRoaXMuaGFuZGxlQ2xlYXJTZWxlY3Rpb24oKTsNCiAgICAgICAgICB0aGlzLmNsb3NlRGlhbG9nKCk7DQogICAgICAgICAgdGhpcy5nZXRMaXN0KCk7DQogICAgICAgIH0pLmNhdGNoKGVycm9yID0+IHsNCiAgICAgICAgICB0aGlzLiRtZXNzYWdlKHsNCiAgICAgICAgICAgIHR5cGU6ICdlcnJvcicsDQogICAgICAgICAgICBtZXNzYWdlOiBlcnJvci5yZXNwb25zZS5kYXRhLm1lc3NhZ2UgfHwgJ+WPkei1t+WuoeaJueWksei0pe+8gScNCiAgICAgICAgICB9KTsNCiAgICAgICAgfSk7DQogICAgICB9IGVsc2Ugew0KICAgICAgICB0aGlzLiRtZXNzYWdlLndhcm5pbmcoJ+ivt+iHs+WwkemAieaLqeS4gOS4quWuoeaguOS6uu+8gScpOw0KICAgICAgfQ0KDQogICAgfSwNCg0KICAgIC8qKiDmj5DkuqTmjInpkq4gKi8NCiAgICBzdWJtaXRGb3JtKCkgew0KICAgICAgdGhpcy4kcmVmc1siZm9ybSJdLnZhbGlkYXRlKHZhbGlkID0+IHsNCiAgICAgICAgaWYgKHZhbGlkKSB7DQogICAgICAgICAgLy/liKTmlq3mmK/lkKbkuIrkvKDkuobmlofku7bvvIhmb3JtLmZpbGVVcmzvvIkNCiAgICAgICAgICBpZiAodGhpcy5mb3JtLnRlY2huaWNhbEFncmVlbWVudCA9PSBudWxsIHx8IHRoaXMuZm9ybS50ZWNobmljYWxBZ3JlZW1lbnQgPT0gJ1tdJykgew0KICAgICAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcigi6K+35LiK5Lyg5paH5Lu2Iik7DQogICAgICAgICAgICByZXR1cm47DQogICAgICAgICAgfQ0KICAgICAgICAgIGlmICh0aGlzLmZvcm0uaWQgIT0gbnVsbCkgew0KICAgICAgICAgICAgdXBkYXRlRHJhd2luZyh0aGlzLmZvcm0pLnRoZW4ocmVzcG9uc2UgPT4gew0KICAgICAgICAgICAgICB0aGlzLiRtb2RhbC5tc2dTdWNjZXNzKCLkv67mlLnmiJDlip8iKTsNCiAgICAgICAgICAgICAgdGhpcy5vcGVuID0gZmFsc2U7DQogICAgICAgICAgICAgIHRoaXMuZ2V0TGlzdCgpOw0KICAgICAgICAgICAgfSk7DQogICAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICAgIGFkZFRlY2huaWNhbEFncmVlbWVudCh0aGlzLmZvcm0pLnRoZW4ocmVzcG9uc2UgPT4gew0KICAgICAgICAgICAgICB0aGlzLiRtb2RhbC5tc2dTdWNjZXNzKCLmlrDlop7miJDlip8iKTsNCiAgICAgICAgICAgICAgdGhpcy5vcGVuID0gZmFsc2U7DQogICAgICAgICAgICAgIHRoaXMuZ2V0TGlzdCgpOw0KICAgICAgICAgICAgfSk7DQogICAgICAgICAgfQ0KICAgICAgICB9DQogICAgICB9KTsNCiAgICB9LA0KICAgIC8qKiDliKDpmaTmjInpkq7mk43kvZwgKi8NCiAgICBoYW5kbGVEZWxldGUocm93KSB7DQogICAgICBjb25zdCBpZCA9IHJvdy5pZCB8fCB0aGlzLmlkczsNCiAgICAgIHRoaXMuJGNvbmZpcm0oYOaYr+WQpuehruiupOWIoOmZpOivpeaKgOacr+WNj+iuru+8n2AsICLorablkYoiLCB7DQogICAgICAgIGNvbmZpcm1CdXR0b25UZXh0OiAi56Gu5a6aIiwNCiAgICAgICAgY2FuY2VsQnV0dG9uVGV4dDogIuWPlua2iCIsDQogICAgICAgIHR5cGU6ICJ3YXJuaW5nIg0KICAgICAgfSkudGhlbihmdW5jdGlvbigpIHsNCiAgICAgICAgcmV0dXJuIGRlbERyYXdpbmcoaWQpOw0KICAgICAgICB9KS50aGVuKCgpID0+IHsNCiAgICAgICAgICB0aGlzLmdldExpc3QoKTsNCiAgICAgICAgICB0aGlzLm1zZ1N1Y2Nlc3MoIuWIoOmZpOaIkOWKnyIpOw0KICAgICAgICB9KQ0KICAgICAgfSwNCg0KICAgIC8qKiDlr7zlh7rmjInpkq7mk43kvZwgKi8NCiAgICBoYW5kbGVFeHBvcnQoKSB7DQogICAgICBjb25zdCBxdWVyeVBhcmFtcyA9IHRoaXMucXVlcnlQYXJhbXM7DQogICAgICB0aGlzLiRjb25maXJtKCfmmK/lkKbnoa7orqTlr7zlh7rmiYDmnInlm77nurjlupPmlbDmja7pobnvvJ8nLCAi6K2m5ZGKIiwgew0KICAgICAgICBjb25maXJtQnV0dG9uVGV4dDogIuehruWumiIsDQogICAgICAgIGNhbmNlbEJ1dHRvblRleHQ6ICLlj5bmtogiLA0KICAgICAgICB0eXBlOiAid2FybmluZyINCiAgICAgIH0pLnRoZW4oZnVuY3Rpb24gKCkgew0KICAgICAgICByZXR1cm4gZXhwb3J0RHJhd2luZyhxdWVyeVBhcmFtcyk7DQogICAgICB9KS50aGVuKHJlc3BvbnNlID0+IHsNCiAgICAgICAgdGhpcy5kb3dubG9hZChyZXNwb25zZS5tc2cpOw0KICAgICAgfSkNCiAgICB9DQogIH0NCn07DQo="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA2JA;AACA;AACA;;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;;;AAIA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/drawing/technical", "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <el-form :model=\"queryParams\" ref=\"queryForm\" size=\"small\" :inline=\"true\" v-show=\"showSearch\" label-width=\"80px\">\r\n      <el-form-item label=\"物料编码\" prop=\"materialsCode\">\r\n        <el-input v-model=\"queryParams.materialsCode\" clearable readonly>\r\n          <el-button slot=\"append\" icon=\"el-icon-search\" size=\"mini\" @click=\"searchMaterialsCode\"></el-button>\r\n          <el-button slot=\"append\" icon=\"el-icon-delete\" size=\"mini\" @click=\"clearSearchMaterialsCode\"></el-button>\r\n        </el-input>\r\n      </el-form-item>\r\n      <el-form-item label=\"创建人\" prop=\"createUserNo\">\r\n        <el-input v-model=\"queryParams.createUserNo\" placeholder=\"请输入创建人\" clearable @keyup.enter.native=\"handleQuery\" />\r\n      </el-form-item>\r\n\r\n      <el-form-item>\r\n        <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleQuery\">搜索</el-button>\r\n        <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\r\n      </el-form-item>\r\n    </el-form>\r\n\r\n    <el-row :gutter=\"10\" class=\"mb8\">\r\n      <el-col :span=\"1.5\">\r\n        <el-button type=\"primary\" plain icon=\"el-icon-plus\" size=\"mini\" @click=\"handleAdd\">新增</el-button>\r\n      </el-col>\r\n      <!-- <el-col :span=\"1.5\">\r\n        <el-button type=\"primary\" plain icon=\"el-icon-success\" size=\"mini\" @click=\"initiateApproval\">发起审批</el-button>\r\n      </el-col> -->\r\n      <el-col :span=\"1.5\">\r\n        <el-button type=\"warning\" plain icon=\"el-icon-download\" size=\"mini\" @click=\"handleExport\">导出</el-button>\r\n      </el-col>\r\n      <right-toolbar :showSearch.sync=\"showSearch\" @queryTable=\"getList\"></right-toolbar>\r\n    </el-row>\r\n\r\n    <el-table v-loading=\"loading\" :data=\"drawingList\" ref=\"multipleTable\" @selection-change=\"handleSelectionChange\">\r\n      <el-table-column label=\"物料编码\" align=\"center\" prop=\"materialsCode\" />\r\n      <el-table-column label=\"创建人\" align=\"center\" prop=\"createUserNo\" />\r\n      <el-table-column label=\"创建时间\" align=\"center\" prop=\"createDateTime\" width=\"180\">\r\n        <template slot-scope=\"scope\">\r\n          <span>{{ formatTime(scope.row.createDateTime) }}</span>\r\n        </template>\r\n      </el-table-column>\r\n\r\n      <el-table-column label=\"操作\" align=\"center\" class-name=\"small-padding fixed-width\">\r\n        <template slot-scope=\"scope\">\r\n          <el-button plain v-if=\"scope.row.showModifyBtn\" size=\"mini\" type=\"text\" icon=\"el-icon-edit\"\r\n            @click=\"handleUpdate(scope.row)\">修改</el-button>\r\n          <el-button size=\"mini\" type=\"text\" icon=\"el-icon-edit\" @click=\"handleDetail(scope.row)\">详情</el-button>\r\n          <el-button size=\"mini\" type=\"text\" icon=\"el-icon-delete\" @click=\"handleDelete(scope.row)\">删除</el-button>\r\n        </template>\r\n      </el-table-column>\r\n    </el-table>\r\n\r\n    <pagination v-show=\"total > 0\" :total=\"total\" :page.sync=\"queryParams.pageNum\" :limit.sync=\"queryParams.pageSize\"\r\n      @pagination=\"getList\" />\r\n\r\n    <!--新增图纸时物料编码对话框-->\r\n    <el-dialog title=\"物料代码列表\" :visible.sync=\"isTblModalVisible\" width=\"800px\">\r\n      <!-- 添加物料编码搜索表单 -->\r\n      <el-form :inline=\"true\" :model=\"tblQueryParams\" class=\"demo-form-inline\">\r\n        <el-form-item label=\"物料代码\">\r\n          <el-input v-model=\"tblQueryParams.itemId\" placeholder=\"请输入物料代码\" clearable></el-input>\r\n        </el-form-item>\r\n        <el-form-item>\r\n          <el-button type=\"primary\" icon=\"el-icon-search\" @click=\"searchByMaterialsCode\">搜索</el-button>\r\n        </el-form-item>\r\n        <el-form-item>\r\n          <el-button @click=\"resetTblQuery\">重置</el-button>\r\n        </el-form-item>\r\n      </el-form>\r\n\r\n      <!-- 新增图纸时物料编码列表展示 -->\r\n      <el-table :data=\"tblList\" stripe highlight-current-row @row-click=\"handleMaterialsRowClick\">\r\n        <el-table-column label=\"选择\" width=\"50\" align=\"center\">\r\n          <template slot-scope=\"scope\">\r\n            <el-radio v-model=\"selectedMaterial\" :label=\"scope.row\" @change=\"handleMaterialsRowClick(scope.row)\" >\r\n              <span></span>\r\n            </el-radio>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column label=\"物料代码\" align=\"center\" prop=\"itemId\" />\r\n        <el-table-column label=\"物料名称\" align=\"center\" prop=\"itemName\" />\r\n        <el-table-column label=\"型号规格\" align=\"center\" prop=\"itemModel\" />\r\n        <el-table-column label=\"状态\" align=\"center\">\r\n          <template v-slot:default=\"{ row }\">\r\n            <!-- 使用三元运算符判断 stateId 的值 -->\r\n            {{ row.stateId === 'A' ? '可用' : '不可用' }}\r\n          </template>\r\n        </el-table-column>\r\n        <!-- <el-table-column label=\"创建时间\" align=\"center\" prop=\"createDateTime\" /> -->\r\n        <el-table-column label=\"创建时间\" align=\"center\" prop=\"recCreateTime\" width=\"180\">\r\n          <template slot-scope=\"scope\">\r\n            <span>{{ formatStringTime(scope.row.recCreateTime) }}</span>\r\n          </template>\r\n        </el-table-column>\r\n      </el-table>\r\n\r\n      <pagination v-show=\"totalTbl > 0\" :total=\"totalTbl\" :page.sync=\"tblQueryParams.pageNum\"\r\n        :limit.sync=\"tblQueryParams.pageSize\" @pagination=\"getMaterialList\" />\r\n\r\n      <span slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button type=\"primary\" @click=\"queryMaterialCode\">确 定</el-button>\r\n        <el-button @click=\"isTblModalVisible = false\">关闭</el-button>\r\n      </span>\r\n    </el-dialog>\r\n\r\n    <!-- 添加或修改图纸库对话框 -->\r\n    <el-dialog :title=\"title\" :visible.sync=\"open\" width=\"1000px\" append-to-body>\r\n      <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" label-width=\"100px\" label-position=\"top\">\r\n        <el-row :gutter=\"20\">\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"物料编码\" prop=\"materialsCode\">\r\n              <el-input v-model=\"form.materialsCode\" clearable readonly>\r\n                <el-button v-if=\"!form.id\" slot=\"append\" icon=\"el-icon-search\" size=\"mini\" @click=\"openMaterialsCodeDialog\"></el-button>\r\n                <el-button v-if=\"!form.id\" slot=\"append\" icon=\"el-icon-delete\" size=\"mini\" @click=\"clearMaterialsCode\"></el-button>\r\n              </el-input>\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n     <!-- 附件和技术协议上传（水平排列） -->\r\n     <el-form-item>\r\n        <el-row :gutter=\"20\">\r\n          <!-- 技术协议上传 -->\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"技术协议\" prop=\"technicalAgreement\" label-width=\"80px\">\r\n              <el-upload\r\n                ref=\"techUpload\"\r\n                list-type=\"file\"\r\n                accept=\".pdf\"\r\n                :on-preview=\"handleTechPreview\"\r\n                :limit=\"1\"\r\n                :file-list=\"techFileList\"\r\n                :on-exceed=\"handleTechExceed\"\r\n                :on-progress=\"handleTechUploadProgress\"\r\n                :on-success=\"handleTechUploadSuccess\"\r\n                :on-remove=\"handleTechRemove\"\r\n                :action=\"upload.url\"\r\n                :disabled=\"upload.isUploading\">\r\n                <el-button size=\"small\" type=\"primary\">点击上传</el-button>\r\n                <div slot=\"tip\" class=\"el-upload__tip\" style=\"font-weight: bold; color: #D22630;\">\r\n                    因兴澄保密政策，请申请外发后，上传解密后的pdf文件，若直接上传加密后的pdf文件，将上传失败！\r\n                </div>\r\n              </el-upload>\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n      </el-form-item>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button type=\"primary\" @click=\"submitForm\">确 定</el-button>\r\n        <el-button @click=\"cancel\">取 消</el-button>\r\n      </div>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { listDrawing, getDrawing, delDrawing, addDrawing,addTechnicalAgreement, updateDrawing, exportDrawing, sendApprove } from \"@/api/drawing/drawing\";\r\nimport { listMaterialInfo } from \"@/api/materialInfo/materialInfo\";\r\nimport { getListByRoleKey } from '@/api/system/role';\r\n\r\n\r\nexport default {\r\n  name: \"Technical\",\r\n  data() {\r\n    return {\r\n      // 遮罩层\r\n      loading: false,\r\n      ids: [],\r\n      // 显示搜索条件\r\n      showSearch: true,\r\n      // 总条数\r\n      total: 0,\r\n      userTotal: 0,\r\n      totalTbl: 0,\r\n      // 图纸库表格数据\r\n      drawingList: [],\r\n      // 物料编码列表数据\r\n      tblList: [],\r\n      userList: [],\r\n      // 控制全选复选框的状态\r\n      selectAll: false,\r\n      // 用于保存选中的行数据\r\n      selectedRow: null,\r\n      // 保存选中的物料编码数据\r\n      selectedMaterial: null,\r\n      selectedUserId: null,\r\n      selectedUser: null,\r\n      // 弹出层标题\r\n      title: \"\",\r\n      // 是否显示弹出层\r\n      open: false,\r\n      //附件列表\r\n      fileList: [],\r\n      //技术协议列表\r\n      techFileList:[],\r\n      //跟踪是否选择了“内部图纸”\r\n      isInternalDrawing: false,\r\n      isExternalDrawing: false,\r\n      isExternalReviewerVisible: false,\r\n      isTblModalVisible: false,\r\n      isTblClose: false,\r\n      showDialog: false,\r\n      tblTitle: '选择物料编码',\r\n      upload: {\r\n        //设置上传的请求头部\r\n        headers: {},\r\n        // 上传的地址\r\n        url: process.env.VUE_APP_BASE_API + \"/app/common/uploadDecryptPdfMinio\",\r\n        isUploading: false,\r\n      },\r\n      // 查询参数\r\n      queryParams: {\r\n        pageNum: 1,\r\n        pageSize: 10,\r\n        suitEquipment: null, drawingNo: '', materialsCode: '', drawingType: null, drawingStatus: null, drawingResource: null, speciModel: null, reviewer: null, designerInner: null, reviewerOuter: null, drawingCompanyOuter: null, tenantId: null, addFactory: null, remarks: null, attachmentUuid: null, instanceId: null, isNoticeCx: null, objectVersionNumber: null, flowStatus: null, createUserNo: null, createDateTime: null, updateUserNo: null, updateDateTime: null, companyId: null, attachment: null, fileUrl: null\r\n      },\r\n      tblQueryParams: {\r\n        pageNum: 1,\r\n        pageSize: 10,\r\n        materialsCode: '', createDateTime: null, materialsName: null, speciModel: null\r\n      },\r\n      //查询审核人参数\r\n      approverParams:{\r\n        pageNum: 1,\r\n        pageSize: 10,\r\n        userName: '',\r\n        nickName: undefined,\r\n        roleKey: undefined\r\n      },\r\n      // 表单参数\r\n      form: {\r\n        drawingResource: '', // 默认情况下没有选择任何图纸来源\r\n        designerInner: '',\r\n        reviewerOuter: '',\r\n        drawingCompanyOuter: '',\r\n        reviewerOuter: '',\r\n        speciModelReadonly: false,\r\n        materialsCode: '',\r\n        speciModel: '',\r\n        drawingType: '',\r\n        fileUrl: '',\r\n      },\r\n      // 表单校验\r\n      rules: {\r\n        drawingNo: [{ required: true, message: '请输入图纸号', trigger: 'blur' }],\r\n        materialsCode: [{ required: true, message: '请输入物料编码', trigger: 'blur' }],\r\n        drawingType: [{ required: true, message: '请选择图纸类型', trigger: 'change' }],\r\n        drawingResource: [{ required: true, message: '请选择图纸来源', trigger: 'change' }]\r\n      },\r\n    };\r\n  },\r\n  created() {\r\n    this.getList();\r\n    this.getUserList();\r\n  },\r\n  methods: {\r\n\r\n    //时间格式化\r\n    formatTime(isoTimeString) {\r\n      if (!isoTimeString) return '';\r\n\r\n      // 解析 ISO 8601 时间字符串\r\n      const date = new Date(isoTimeString);\r\n\r\n      // 提取年月日时分秒\r\n      const year = date.getFullYear();\r\n      const month = String(date.getMonth() + 1).padStart(2, '0'); // 月份是从0开始的，所以加1\r\n      const day = String(date.getDate()).padStart(2, '0');\r\n      const hours = String(date.getHours()).padStart(2, '0');\r\n      const minutes = String(date.getMinutes()).padStart(2, '0');\r\n      const seconds = String(date.getSeconds()).padStart(2, '0');\r\n\r\n      // 返回格式化的字符串\r\n      return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;\r\n\r\n    },\r\n     // 字符串时间格式化\r\n     formatStringTime(time) {\r\n      if (typeof time === \"string\") {\r\n        return (\r\n          time.substring(0, 4) +\r\n          \"-\" +\r\n          time.substring(4, 6) +\r\n          \"-\" +\r\n          time.substring(6, 8) +\r\n          \" \" +\r\n          time.substring(8, 10) +\r\n          \":\" +\r\n          time.substring(10, 12) +\r\n          \":\" +\r\n          time.substring(12, 14)\r\n        );\r\n      } else {\r\n        return time;\r\n      }\r\n    },\r\n    tableRowClassName({ row, rowIndex }) {\r\n      if (row.status == '1') {\r\n        return 'warning-row'\r\n      }\r\n      return ''\r\n    },\r\n\r\n    /** 查询图纸库列表 */\r\n    getList() {\r\n      this.loading = true;\r\n      listDrawing(this.queryParams).then(response => {\r\n        this.drawingList = response.rows;\r\n        this.total = response.total;\r\n        this.loading = false;\r\n      });\r\n    },\r\n    /**查询物料编码列表 */\r\n    getMaterialList() {\r\n      this.loading = true;\r\n      listMaterialInfo(this.tblQueryParams).then(response => {\r\n        this.tblList = response.rows;\r\n        this.totalTbl = response.total;\r\n        this.loading = false;\r\n      });\r\n    },\r\n    /**查询用户列表 */\r\n    getUserList() {\r\n      this.loading = true\r\n      this.approverParams.roleKey = 'drawing.approver.factory';\r\n      getListByRoleKey(this.approverParams).then((response) => {\r\n          this.userList = response.rows\r\n          this.userTotal = response.total\r\n          this.loading = false\r\n        }\r\n      )\r\n    },\r\n    // 取消按钮\r\n    cancel() {\r\n      this.open = false;\r\n      this.reset();\r\n    },\r\n    clearMaterialsCode() {\r\n      this.form.materialsCode = ''; // 清空物料编码\r\n      this.form.speciModel = '';\r\n    },\r\n    clearSearchMaterialsCode() {\r\n      this.queryParams.materialsCode = '';\r\n    },\r\n    // 表单重置\r\n    reset() {\r\n      this.form = {\r\n        id: null, originalId: null, suitEquipment: null, drawingNo: null, materialsCode: null, drawingType: null, drawingStatus: null, drawingResource: null, speciModel: null, reviewer: null,\r\n        designerInner: null, reviewerOuter: null, drawingCompanyOuter: null, tenantId: null, addFactory: null, remarks: null,\r\n        attachmentUuid: null, instanceId: null, isNoticeCx: null, objectVersionNumber: null, flowStatus: null, createUserNo: null, createDateTime: null,\r\n        updateUserNo: null, updateDateTime: null, companyId: null, attachment: null, fileUrl: null\r\n      };\r\n      this.resetForm(\"form\");\r\n      this.fileList = [];\r\n      this.techFileList = [];\r\n    },\r\n    onDrawingResourceChange(value) {\r\n      if (value === '2') { // 内部图纸\r\n        this.isInternalDrawing = true;\r\n        this.isExternalDrawing = false;\r\n        this.isExternalReviewerVisible = false;\r\n      } else if (value === '1') { // 外来图纸\r\n        this.isInternalDrawing = false;\r\n        this.isExternalDrawing = true;\r\n        this.isExternalReviewerVisible = true;\r\n      } else { // 如果没有选择任何值\r\n        this.isInternalDrawing = false;\r\n        this.isExternalDrawing = false;\r\n        this.isExternalReviewerVisible = false;\r\n      }\r\n    },\r\n    // 物料编码列表的选择变化\r\n    handleTblSelectionChange(rows) {\r\n      this.selectedMaterial = rows[0];\r\n    },\r\n    //用于查询图纸审批单\r\n    queryMaterialCode() {\r\n      console.log(this.selectedMaterial)\r\n      if (this.selectedMaterial) {\r\n        if (this.open) {\r\n          //新增或修改时\r\n          //判断是否可用\r\n          if(this.selectedMaterial.stateId !== 'A'){\r\n            this.$message.warning('该物料编码已停用，请重新选择！');\r\n            return;\r\n          }\r\n          this.form.materialsCode = this.selectedMaterial.itemId;\r\n          this.form.speciModel = this.selectedMaterial.itemModel;\r\n          this.form.speciModelReadonly = true; // 设置规格型号输入框为只读\r\n        } else {\r\n          //搜索时\r\n          this.queryParams.materialsCode = this.selectedMaterial.itemId;\r\n        }\r\n\r\n\r\n        this.resetTblQuery(); // 重置物料编码列表搜索条件\r\n        this.selectedMaterial = null; // 清空已选择的物料\r\n        this.isTblModalVisible = false; // 关闭对话框\r\n      } else {\r\n        // 如果没有选择任何物料，则提示用户\r\n        this.$message.warning('请选择一条物料编码记录！');\r\n      }\r\n    },\r\n    //\r\n\r\n\r\n    // 处理搜索部分物料编码列表的确定按钮\r\n    TblSelectForm() {\r\n      if (this.selectedMaterial) {\r\n        // 如果选择了物料，则更新表单中的物料编码和规格型号\r\n        this.queryParams.materialsCode = this.selectedMaterial.materialsCode;\r\n        this.resetTblQuery(); // 重置物料编码列表搜索条件\r\n        this.selectedMaterial = null; // 清空已选择的物料\r\n        this.isTblClose = false; // 关闭对话框\r\n        this.isTblModalVisible = false; // 关闭另一个对话框\r\n      } else {\r\n        // 如果没有选择任何物料，则提示用户\r\n        this.$message.warning('请选择一条物料编码记录！');\r\n      }\r\n    },\r\n\r\n    toggleSelection() {\r\n      this.$refs.multipleTable.toggleAllSelection();\r\n    },\r\n\r\n    //图纸单选\r\n    handleSelectionChange(row) {\r\n      if (this.selectedRow === row) {\r\n        this.selectedRow = null; // 如果当前选中的行再次被点击，则取消选中\r\n      } else {\r\n        this.selectedRow = row; // 否则，选中该行\r\n      }\r\n    },\r\n\r\n    //物料单选\r\n    handleMaterialsRowClick(row) {\r\n        this.selectedMaterial = row;\r\n    },\r\n\r\n    handleApproverRowClick(row) {\r\n      // 单击行时触发\r\n        this.selectedUserId = row.userName;\r\n    },\r\n\r\n\r\n\r\n    // 清空选择状态\r\n    handleClearSelection() {\r\n      this.selectedRow = null;\r\n      this.ids = [];\r\n      this.selectedUserId = null;\r\n    },\r\n\r\n    //上传图片\r\n    handlePictureCardPreview(file) {\r\n      var localUrl = window.location.host;\r\n      if (file.url != null) {\r\n        if (localUrl == \"************:8099\") {\r\n          file.url = file.url.replace(\"ydxt.citicsteel.com:8099\", \"************:8099\");\r\n        }\r\n        window.open(file.url);\r\n      }\r\n      if (file.response && file.response.url) {\r\n        if (localUrl == \"************:8099\") {\r\n          let tmpUrl = file.response.url.replace(\"ydxt.citicsteel.com:8099\", \"************:8099\");\r\n          window.open(tmpUrl);\r\n        } else {\r\n          window.open(file.response.url);\r\n        }\r\n      }\r\n    },\r\n\r\n    handlePictureUp(file, fileList) {\r\n      this.$message.error(\"图片上限为1\");\r\n    },\r\n\r\n    handlePictureUpdateSuccess(response, file, fileList) {\r\n      let that = this;\r\n      if (response.code == 200) {\r\n        that.fileList.push({ url: response.url, name: file.name });\r\n        that.fileListToPicture();\r\n      } else {\r\n        that.removeFailedFile(file);\r\n        that.$message.error(response.msg);\r\n      }\r\n      that.upload.isUploading = false;\r\n    },\r\n    //图片上传中处理\r\n    handlePictureUploadProgress() {\r\n      this.upload.isUploading = true;\r\n    },\r\n\r\n    handlePictureRemove(file, fileList) {\r\n      for (let i = 0; i < this.fileList.length; i++) {\r\n        if (this.fileList[i].url == file.url) {\r\n          this.fileList.splice(i, 1);\r\n        }\r\n      }\r\n      this.fileListToPicture();\r\n    },\r\n    fileListToPicture() {\r\n      let fileUrl = [];\r\n      this.fileList.forEach((item) => {\r\n        fileUrl.push({ url: item.url });\r\n      });\r\n      if (fileUrl.length > 0)\r\n        this.form.fileUrl = JSON.stringify(fileUrl).toString();\r\n      else this.form.fileUrl = \"[]\";\r\n    },\r\n    removeFailedFile(file) {\r\n      // 从 fileList 中移除失败的文件\r\n      this.fileList = this.fileList.filter(item => item !== file);\r\n    },\r\n\r\n    // 技术协议\r\n    handleTechPreview(file) {\r\n      this.handleFilePreview(file);\r\n    },\r\n\r\n    handleTechUploadProgress(event, file, fileList) {\r\n      this.upload.isUploading = true;\r\n    },\r\n\r\n    handleTechExceed(files, fileList) {\r\n      this.$message.warning(`技术协议只能上传一个文件，当前选择了 ${files.length} 个文件`);\r\n    },\r\n\r\n    handleTechUploadSuccess(response, file, fileList) {\r\n      this.upload.isUploading = false;\r\n      if (response.code === 200) {\r\n        this.techFileList = [{ url: response.url, name: file.name }];\r\n        this.form.technicalAgreement = JSON.stringify(this.techFileList);\r\n      } else {\r\n        this.$message.error(response.msg);\r\n        this.removeFailedFile(file, this.techFileList);\r\n      }\r\n    },\r\n\r\n    handleTechRemove(file, fileList) {\r\n      this.techFileList = [];\r\n      this.form.technicalAgreement = \"\";\r\n    },\r\n\r\n     // 通用文件预览方法\r\n     handleFilePreview(file) {\r\n      let url = file.url || (file.response && file.response.url);\r\n      if (!url) return;\r\n\r\n      // 本地开发环境替换\r\n      if (window.location.host === \"************:8099\") {\r\n        url = url.replace(\"ydxt.citicsteel.com:8099\", \"************:8099\");\r\n      }\r\n\r\n      window.open(url);\r\n    },\r\n\r\n    // 从指定文件列表中移除失败文件\r\n    removeFailedFile(file, targetList) {\r\n      targetList = targetList.filter(item => item.uid !== file.uid);\r\n    },\r\n\r\n    /** 发起审批 */\r\n    initiateApproval() {\r\n      //判断是否有选中的行 this.selectedRow\r\n      if (this.selectedRow == null) {\r\n        this.$message.warning(\"请选择一条记录！\");\r\n        return;\r\n      }\r\n      this.showDialog = true;\r\n    },\r\n\r\n    /**查看详情操作 */\r\n    handleDetail(row) {\r\n      console.log(row);\r\n      this.$router.push(\"/drawing/technicalDetail/\" + row.id);\r\n    },\r\n\r\n    /** 搜索按钮操作 */\r\n    handleQuery() {\r\n      this.queryParams.pageNum = 1;\r\n      this.getList();\r\n    },\r\n    /**搜索审核人操作 */\r\n    handleUserQuery() {\r\n      this.approverParams.pageNum =1;\r\n      this.getUserList()\r\n    },\r\n\r\n    /** 搜索物料编码表 */\r\n    searchByMaterialsCode() {\r\n      this.tblQueryParams.pageNum = 1;\r\n      this.getMaterialList();\r\n      this.isTblModalVisible = true;\r\n    },\r\n\r\n    // 搜索物料编码\r\n    searchMaterialsCode() {\r\n      this.searchByMaterialsCode(); // 在打开对话框的同时获取数据\r\n    },\r\n\r\n    //新增时搜索物料编码\r\n    openMaterialsCodeDialog() {\r\n      this.isTblModalVisible = true;\r\n      this.tblQueryParams.pageNum = 1;\r\n      this.getMaterialList();\r\n    },\r\n\r\n    /** 重置按钮操作 */\r\n    resetQuery() {\r\n      this.resetForm(\"queryForm\");\r\n      this.handleQuery();\r\n      this.getList();\r\n    },\r\n\r\n    /**重置审核人操作 */\r\n    resetUserQuery() {\r\n      this.resetForm('queryForm')\r\n      this.approverParams.userName = '';\r\n      this.approverParams.nickName = '';\r\n      this.handleUserQuery();\r\n      this.getUserList();\r\n      this.handleClearSelection();\r\n    },\r\n\r\n    /** 重置物料代码列表按钮操作 */\r\n    resetTblQuery() {\r\n      this.tblQueryParams = {\r\n        pageNum: 1,\r\n        pageSize: 10,\r\n        materialsCode: null,\r\n        createDateTime: null,\r\n        materialsName: null,\r\n        speciModel: null\r\n      };\r\n      // this.getMaterialList();\r\n    },\r\n\r\n    /** 新增按钮操作 */\r\n    handleAdd() {\r\n      this.reset();\r\n      this.open = true;\r\n      this.title = \"添加技术协议\";\r\n    },\r\n    /** 修改按钮操作 */\r\n    handleUpdate(row) {\r\n      this.reset();\r\n      console.log(row);\r\n      const id = row.id || this.ids\r\n      getDrawing(id).then(response => {\r\n        this.form = response.data;\r\n        this.open = true;\r\n        this.title = \"修改技术协议\";\r\n      });\r\n      if (row.fileUrl) {\r\n        this.fileList = JSON.parse(row.fileUrl);\r\n      }\r\n      else {\r\n        this.fileList = []\r\n      }\r\n    },\r\n\r\n    // 关闭按钮点击事件\r\n    closeDialog() {\r\n      this.resetTblQuery(); // 重置物料编码列表搜索条件\r\n      this.selectedMaterial = null; // 清空已选择的物料\r\n      this.isTblClose = false; // 关闭对话框\r\n      this.isTblModalVisible = false; // 关闭另一个对话框\r\n      this.showDialog = false;\r\n      this.selectedUserId = null;\r\n    },\r\n\r\n    //推送给审核人确定按钮\r\n    UserSelectForm() {\r\n      if (this.selectedUserId !== null) {\r\n        // 将审核人的 userId 发送到后端\r\n        let param = {\r\n          approveUserId: this.selectedUserId,\r\n          citicsXctgDrawing: this.selectedRow\r\n        };\r\n        sendApprove(param).then(() => {\r\n          this.$message({\r\n            type: 'success',\r\n            message: '发起审批成功！'\r\n          });\r\n          // 清空选择状态\r\n          this.handleClearSelection();\r\n          this.closeDialog();\r\n          this.getList();\r\n        }).catch(error => {\r\n          this.$message({\r\n            type: 'error',\r\n            message: error.response.data.message || '发起审批失败！'\r\n          });\r\n        });\r\n      } else {\r\n        this.$message.warning('请至少选择一个审核人！');\r\n      }\r\n\r\n    },\r\n\r\n    /** 提交按钮 */\r\n    submitForm() {\r\n      this.$refs[\"form\"].validate(valid => {\r\n        if (valid) {\r\n          //判断是否上传了文件（form.fileUrl）\r\n          if (this.form.technicalAgreement == null || this.form.technicalAgreement == '[]') {\r\n            this.$message.error(\"请上传文件\");\r\n            return;\r\n          }\r\n          if (this.form.id != null) {\r\n            updateDrawing(this.form).then(response => {\r\n              this.$modal.msgSuccess(\"修改成功\");\r\n              this.open = false;\r\n              this.getList();\r\n            });\r\n          } else {\r\n            addTechnicalAgreement(this.form).then(response => {\r\n              this.$modal.msgSuccess(\"新增成功\");\r\n              this.open = false;\r\n              this.getList();\r\n            });\r\n          }\r\n        }\r\n      });\r\n    },\r\n    /** 删除按钮操作 */\r\n    handleDelete(row) {\r\n      const id = row.id || this.ids;\r\n      this.$confirm(`是否确认删除该技术协议？`, \"警告\", {\r\n        confirmButtonText: \"确定\",\r\n        cancelButtonText: \"取消\",\r\n        type: \"warning\"\r\n      }).then(function() {\r\n        return delDrawing(id);\r\n        }).then(() => {\r\n          this.getList();\r\n          this.msgSuccess(\"删除成功\");\r\n        })\r\n      },\r\n\r\n    /** 导出按钮操作 */\r\n    handleExport() {\r\n      const queryParams = this.queryParams;\r\n      this.$confirm('是否确认导出所有图纸库数据项？', \"警告\", {\r\n        confirmButtonText: \"确定\",\r\n        cancelButtonText: \"取消\",\r\n        type: \"warning\"\r\n      }).then(function () {\r\n        return exportDrawing(queryParams);\r\n      }).then(response => {\r\n        this.download(response.msg);\r\n      })\r\n    }\r\n  }\r\n};\r\n</script>\r\n"]}]}