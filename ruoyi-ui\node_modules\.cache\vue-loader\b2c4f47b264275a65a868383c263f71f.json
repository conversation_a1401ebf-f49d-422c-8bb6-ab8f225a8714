{"remainingRequest": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\src\\views\\suppPunishment\\punishmentMeasure-module.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\src\\views\\suppPunishment\\punishmentMeasure-module.vue", "mtime": 1755499162062}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["punishmentMeasure-module.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAyFA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA", "file": "punishmentMeasure-module.vue", "sourceRoot": "src/views/suppPunishment", "sourcesContent": ["<template>\r\n  <el-dialog\r\n    title=\"处罚措施选择\"\r\n    :visible.sync=\"visible\"\r\n    width=\"380px\"\r\n    append-to-body\r\n    @close=\"handleClose\"\r\n  >\r\n    <!-- 处罚措施选项 -->\r\n    <div class=\"measure-options\">\r\n      <el-checkbox-group v-model=\"selectedMeasures\" @change=\"handleMeasureChange\">\r\n        <!-- 处罚金额 -->\r\n        <div class=\"measure-item\">\r\n          <el-checkbox label=\"penalty\" class=\"measure-checkbox\">\r\n            <span class=\"measure-text\">\r\n              处罚\r\n              <el-input-number\r\n                v-model=\"penaltyAmount\"\r\n                controls-position=\"right\"\r\n                :min=\"0\"\r\n                :max=\"9999999\"\r\n                size=\"small\"\r\n                class=\"inline-input-number\"\r\n                @change=\"updateMeasureText\"\r\n                @focus=\"checkPenalty\"\r\n              />\r\n              元\r\n            </span>\r\n          </el-checkbox>\r\n        </div>\r\n\r\n        <!-- 降级 -->\r\n        <div class=\"measure-item\">\r\n          <el-checkbox label=\"downgrade\" class=\"measure-checkbox\">\r\n            <span class=\"measure-text\">降级</span>\r\n          </el-checkbox>\r\n        </div>\r\n\r\n        <!-- 淘汰（禁用） -->\r\n        <div class=\"measure-item\">\r\n          <el-checkbox label=\"eliminate\" class=\"measure-checkbox\">\r\n            <span class=\"measure-text\">淘汰（禁用）</span>\r\n          </el-checkbox>\r\n        </div>\r\n\r\n        <!-- 暂缓 -->\r\n        <div class=\"measure-item\">\r\n          <el-checkbox label=\"suspend\" class=\"measure-checkbox\">\r\n            <span class=\"measure-text\">\r\n              暂缓\r\n              <el-input-number\r\n                v-model=\"suspendMonths\"\r\n                controls-position=\"right\"\r\n                :min=\"0\"\r\n                :max=\"999\"\r\n                size=\"small\"\r\n                class=\"inline-input-number\"\r\n                @change=\"updateMeasureText\"\r\n                @focus=\"checkSuspend\"\r\n              />\r\n              月\r\n            </span>\r\n          </el-checkbox>\r\n        </div>\r\n      </el-checkbox-group>\r\n    </div>\r\n\r\n    <!-- 预览区域 -->\r\n    <div class=\"preview-area\">\r\n      <el-form-item label=\"预览结果：\">\r\n        <el-input\r\n          v-model=\"previewText\"\r\n          type=\"textarea\"\r\n          :rows=\"3\"\r\n          readonly\r\n          placeholder=\"选择处罚措施后将在此显示预览\"\r\n        />\r\n      </el-form-item>\r\n    </div>\r\n\r\n    <!-- 底部按钮 -->\r\n    <div slot=\"footer\" class=\"dialog-footer\">\r\n      <el-button type=\"primary\" @click=\"handleConfirm\">确 定</el-button>\r\n      <el-button @click=\"handleClose\">取 消</el-button>\r\n    </div>\r\n  </el-dialog>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  name: \"PunishmentMeasureDialog\",\r\n  data() {\r\n    return {\r\n      // 是否显示弹出层\r\n      visible: false,\r\n      // 选中的处罚措施\r\n      selectedMeasures: [],\r\n      // 处罚金额\r\n      penaltyAmount: 0,\r\n      // 暂缓月数\r\n      suspendMonths: 0,\r\n      // 预览文本\r\n      previewText: ''\r\n    };\r\n  },\r\n  methods: {\r\n    /** 显示弹窗 */\r\n    show(currentValue = '') {\r\n      this.visible = true;\r\n      this.parseCurrentValue(currentValue);\r\n      this.updatePreview();\r\n    },\r\n    \r\n    /** 隐藏弹窗 */\r\n    hide() {\r\n      this.visible = false;\r\n    },\r\n    \r\n    /** 关闭弹窗 */\r\n    handleClose() {\r\n      this.visible = false;\r\n      this.reset();\r\n    },\r\n    \r\n    /** 重置数据 */\r\n    reset() {\r\n      this.selectedMeasures = [];\r\n      this.penaltyAmount = 0;\r\n      this.suspendMonths = 0;\r\n      this.previewText = '';\r\n    },\r\n    \r\n    /** 解析当前值 */\r\n    parseCurrentValue(value) {\r\n      if (!value) {\r\n        this.reset();\r\n        return;\r\n      }\r\n      \r\n      this.selectedMeasures = [];\r\n      \r\n      // 解析处罚金额\r\n      const penaltyMatch = value.match(/处罚(\\d+)元/);\r\n      if (penaltyMatch) {\r\n        this.selectedMeasures.push('penalty');\r\n        this.penaltyAmount = parseInt(penaltyMatch[1]);\r\n      }\r\n\r\n      // 解析降级\r\n      if (value.includes('降级')) {\r\n        this.selectedMeasures.push('downgrade');\r\n      }\r\n\r\n      // 解析淘汰\r\n      if (value.includes('淘汰（禁用）')) {\r\n        this.selectedMeasures.push('eliminate');\r\n      }\r\n\r\n      // 解析暂缓\r\n      const suspendMatch = value.match(/暂缓(\\d+)月/);\r\n      if (suspendMatch) {\r\n        this.selectedMeasures.push('suspend');\r\n        this.suspendMonths = parseInt(suspendMatch[1]);\r\n      }\r\n    },\r\n    \r\n    /** 处罚措施变化 */\r\n    handleMeasureChange() {\r\n      this.updatePreview();\r\n    },\r\n    \r\n    /** 更新措施文本 */\r\n    updateMeasureText() {\r\n      this.updatePreview();\r\n    },\r\n\r\n    /** 点击处罚金额输入框时自动选中 */\r\n    checkPenalty() {\r\n      if (!this.selectedMeasures.includes('penalty')) {\r\n        this.selectedMeasures.push('penalty');\r\n        this.updatePreview();\r\n      }\r\n    },\r\n\r\n    /** 点击暂缓月数输入框时自动选中 */\r\n    checkSuspend() {\r\n      if (!this.selectedMeasures.includes('suspend')) {\r\n        this.selectedMeasures.push('suspend');\r\n        this.updatePreview();\r\n      }\r\n    },\r\n    \r\n    /** 更新预览 */\r\n    updatePreview() {\r\n      const measures = [];\r\n\r\n      if (this.selectedMeasures.includes('penalty') && this.penaltyAmount > 0) {\r\n        measures.push(`处罚${this.penaltyAmount}元`);\r\n      }\r\n\r\n      if (this.selectedMeasures.includes('downgrade')) {\r\n        measures.push('降级');\r\n      }\r\n\r\n      if (this.selectedMeasures.includes('eliminate')) {\r\n        measures.push('淘汰（禁用）');\r\n      }\r\n\r\n      if (this.selectedMeasures.includes('suspend') && this.suspendMonths > 0) {\r\n        measures.push(`暂缓${this.suspendMonths}月`);\r\n      }\r\n\r\n      this.previewText = measures.join('；');\r\n    },\r\n    \r\n    /** 确认选择 */\r\n    handleConfirm() {\r\n      this.updatePreview();\r\n\r\n      // 验证是否至少选择了一条处罚措施\r\n      if (this.selectedMeasures.length === 0) {\r\n        this.$message.warning('请至少选择一条处罚措施');\r\n        return;\r\n      }\r\n\r\n      // 验证有数值要求的措施是否填写了数值\r\n      if (this.selectedMeasures.includes('penalty') && (!this.penaltyAmount || this.penaltyAmount <= 0)) {\r\n        this.$message.warning('请输入处罚金额');\r\n        return;\r\n      }\r\n\r\n      if (this.selectedMeasures.includes('suspend') && (!this.suspendMonths || this.suspendMonths <= 0)) {\r\n        this.$message.warning('请输入暂缓月数');\r\n        return;\r\n      }\r\n\r\n      this.$emit('select', this.previewText);\r\n      this.handleClose();\r\n    }\r\n  }\r\n};\r\n</script>\r\n\r\n<style scoped>\r\n/* 处罚措施选项样式 */\r\n.measure-options {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.measure-item {\r\n  margin-bottom: 15px;\r\n  padding: 0;\r\n}\r\n\r\n.measure-checkbox {\r\n  width: 100%;\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.measure-text {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n}\r\n\r\n.inline-input {\r\n  width: 120px;\r\n}\r\n\r\n.inline-input ::v-deep .el-input__inner {\r\n  height: 28px;\r\n  line-height: 28px;\r\n}\r\n\r\n.inline-input-number {\r\n  width: 120px;\r\n}\r\n\r\n/* 预览区域样式 */\r\n.preview-area {\r\n  margin-top: 20px;\r\n  padding-top: 20px;\r\n  border-top: 1px solid #e4e7ed;\r\n}\r\n\r\n/* 弹窗标题居中 */\r\n::v-deep .el-dialog__header {\r\n  text-align: center;\r\n  background: #f8f9fa;\r\n  border-bottom: 1px solid #e9ecef;\r\n}\r\n\r\n::v-deep .el-dialog__title {\r\n  text-align: center;\r\n  width: 100%;\r\n  display: block;\r\n  font-weight: 600;\r\n  color: #303133;\r\n}\r\n\r\n/* 弹窗内容区域样式 */\r\n::v-deep .el-dialog__body {\r\n  padding: 20px;\r\n}\r\n\r\n/* 复选框组样式 */\r\n::v-deep .el-checkbox-group {\r\n  width: 100%;\r\n}\r\n\r\n::v-deep .el-checkbox {\r\n  margin-right: 0;\r\n  margin-bottom: 0;\r\n}\r\n\r\n::v-deep .el-checkbox__label {\r\n  display: flex;\r\n  align-items: center;\r\n  width: 100%;\r\n  font-size: 14px;\r\n  color: #606266;\r\n}\r\n\r\n::v-deep .el-checkbox__input.is-checked + .el-checkbox__label {\r\n  color: #409EFF;\r\n}\r\n</style>\r\n"]}]}