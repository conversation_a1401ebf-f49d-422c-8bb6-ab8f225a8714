{"remainingRequest": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\src\\views\\suppPunishment\\punishmentMeasure-module.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\src\\views\\suppPunishment\\punishmentMeasure-module.vue", "mtime": 1755499162062}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\babel.config.js", "mtime": 1688548084091}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["name", "data", "visible", "selectedMeasures", "penaltyAmount", "suspend<PERSON><PERSON><PERSON>", "previewText", "methods", "show", "currentValue", "arguments", "length", "undefined", "parseCurrentValue", "updatePreview", "hide", "handleClose", "reset", "value", "penaltyMatch", "match", "push", "parseInt", "includes", "suspendMatch", "handleMeasureChange", "updateMeasureText", "checkPenalty", "checkSuspend", "measures", "concat", "join", "handleConfirm", "$message", "warning", "$emit"], "sources": ["src/views/suppPunishment/punishmentMeasure-module.vue"], "sourcesContent": ["<template>\r\n  <el-dialog\r\n    title=\"处罚措施选择\"\r\n    :visible.sync=\"visible\"\r\n    width=\"380px\"\r\n    append-to-body\r\n    @close=\"handleClose\"\r\n  >\r\n    <!-- 处罚措施选项 -->\r\n    <div class=\"measure-options\">\r\n      <el-checkbox-group v-model=\"selectedMeasures\" @change=\"handleMeasureChange\">\r\n        <!-- 处罚金额 -->\r\n        <div class=\"measure-item\">\r\n          <el-checkbox label=\"penalty\" class=\"measure-checkbox\">\r\n            <span class=\"measure-text\">\r\n              处罚\r\n              <el-input-number\r\n                v-model=\"penaltyAmount\"\r\n                controls-position=\"right\"\r\n                :min=\"0\"\r\n                :max=\"9999999\"\r\n                size=\"small\"\r\n                class=\"inline-input-number\"\r\n                @change=\"updateMeasureText\"\r\n                @focus=\"checkPenalty\"\r\n              />\r\n              元\r\n            </span>\r\n          </el-checkbox>\r\n        </div>\r\n\r\n        <!-- 降级 -->\r\n        <div class=\"measure-item\">\r\n          <el-checkbox label=\"downgrade\" class=\"measure-checkbox\">\r\n            <span class=\"measure-text\">降级</span>\r\n          </el-checkbox>\r\n        </div>\r\n\r\n        <!-- 淘汰（禁用） -->\r\n        <div class=\"measure-item\">\r\n          <el-checkbox label=\"eliminate\" class=\"measure-checkbox\">\r\n            <span class=\"measure-text\">淘汰（禁用）</span>\r\n          </el-checkbox>\r\n        </div>\r\n\r\n        <!-- 暂缓 -->\r\n        <div class=\"measure-item\">\r\n          <el-checkbox label=\"suspend\" class=\"measure-checkbox\">\r\n            <span class=\"measure-text\">\r\n              暂缓\r\n              <el-input-number\r\n                v-model=\"suspendMonths\"\r\n                controls-position=\"right\"\r\n                :min=\"0\"\r\n                :max=\"999\"\r\n                size=\"small\"\r\n                class=\"inline-input-number\"\r\n                @change=\"updateMeasureText\"\r\n                @focus=\"checkSuspend\"\r\n              />\r\n              月\r\n            </span>\r\n          </el-checkbox>\r\n        </div>\r\n      </el-checkbox-group>\r\n    </div>\r\n\r\n    <!-- 预览区域 -->\r\n    <div class=\"preview-area\">\r\n      <el-form-item label=\"预览结果：\">\r\n        <el-input\r\n          v-model=\"previewText\"\r\n          type=\"textarea\"\r\n          :rows=\"3\"\r\n          readonly\r\n          placeholder=\"选择处罚措施后将在此显示预览\"\r\n        />\r\n      </el-form-item>\r\n    </div>\r\n\r\n    <!-- 底部按钮 -->\r\n    <div slot=\"footer\" class=\"dialog-footer\">\r\n      <el-button type=\"primary\" @click=\"handleConfirm\">确 定</el-button>\r\n      <el-button @click=\"handleClose\">取 消</el-button>\r\n    </div>\r\n  </el-dialog>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  name: \"PunishmentMeasureDialog\",\r\n  data() {\r\n    return {\r\n      // 是否显示弹出层\r\n      visible: false,\r\n      // 选中的处罚措施\r\n      selectedMeasures: [],\r\n      // 处罚金额\r\n      penaltyAmount: 0,\r\n      // 暂缓月数\r\n      suspendMonths: 0,\r\n      // 预览文本\r\n      previewText: ''\r\n    };\r\n  },\r\n  methods: {\r\n    /** 显示弹窗 */\r\n    show(currentValue = '') {\r\n      this.visible = true;\r\n      this.parseCurrentValue(currentValue);\r\n      this.updatePreview();\r\n    },\r\n    \r\n    /** 隐藏弹窗 */\r\n    hide() {\r\n      this.visible = false;\r\n    },\r\n    \r\n    /** 关闭弹窗 */\r\n    handleClose() {\r\n      this.visible = false;\r\n      this.reset();\r\n    },\r\n    \r\n    /** 重置数据 */\r\n    reset() {\r\n      this.selectedMeasures = [];\r\n      this.penaltyAmount = 0;\r\n      this.suspendMonths = 0;\r\n      this.previewText = '';\r\n    },\r\n    \r\n    /** 解析当前值 */\r\n    parseCurrentValue(value) {\r\n      if (!value) {\r\n        this.reset();\r\n        return;\r\n      }\r\n      \r\n      this.selectedMeasures = [];\r\n      \r\n      // 解析处罚金额\r\n      const penaltyMatch = value.match(/处罚(\\d+)元/);\r\n      if (penaltyMatch) {\r\n        this.selectedMeasures.push('penalty');\r\n        this.penaltyAmount = parseInt(penaltyMatch[1]);\r\n      }\r\n\r\n      // 解析降级\r\n      if (value.includes('降级')) {\r\n        this.selectedMeasures.push('downgrade');\r\n      }\r\n\r\n      // 解析淘汰\r\n      if (value.includes('淘汰（禁用）')) {\r\n        this.selectedMeasures.push('eliminate');\r\n      }\r\n\r\n      // 解析暂缓\r\n      const suspendMatch = value.match(/暂缓(\\d+)月/);\r\n      if (suspendMatch) {\r\n        this.selectedMeasures.push('suspend');\r\n        this.suspendMonths = parseInt(suspendMatch[1]);\r\n      }\r\n    },\r\n    \r\n    /** 处罚措施变化 */\r\n    handleMeasureChange() {\r\n      this.updatePreview();\r\n    },\r\n    \r\n    /** 更新措施文本 */\r\n    updateMeasureText() {\r\n      this.updatePreview();\r\n    },\r\n\r\n    /** 点击处罚金额输入框时自动选中 */\r\n    checkPenalty() {\r\n      if (!this.selectedMeasures.includes('penalty')) {\r\n        this.selectedMeasures.push('penalty');\r\n        this.updatePreview();\r\n      }\r\n    },\r\n\r\n    /** 点击暂缓月数输入框时自动选中 */\r\n    checkSuspend() {\r\n      if (!this.selectedMeasures.includes('suspend')) {\r\n        this.selectedMeasures.push('suspend');\r\n        this.updatePreview();\r\n      }\r\n    },\r\n    \r\n    /** 更新预览 */\r\n    updatePreview() {\r\n      const measures = [];\r\n\r\n      if (this.selectedMeasures.includes('penalty') && this.penaltyAmount > 0) {\r\n        measures.push(`处罚${this.penaltyAmount}元`);\r\n      }\r\n\r\n      if (this.selectedMeasures.includes('downgrade')) {\r\n        measures.push('降级');\r\n      }\r\n\r\n      if (this.selectedMeasures.includes('eliminate')) {\r\n        measures.push('淘汰（禁用）');\r\n      }\r\n\r\n      if (this.selectedMeasures.includes('suspend') && this.suspendMonths > 0) {\r\n        measures.push(`暂缓${this.suspendMonths}月`);\r\n      }\r\n\r\n      this.previewText = measures.join('；');\r\n    },\r\n    \r\n    /** 确认选择 */\r\n    handleConfirm() {\r\n      this.updatePreview();\r\n\r\n      // 验证是否至少选择了一条处罚措施\r\n      if (this.selectedMeasures.length === 0) {\r\n        this.$message.warning('请至少选择一条处罚措施');\r\n        return;\r\n      }\r\n\r\n      // 验证有数值要求的措施是否填写了数值\r\n      if (this.selectedMeasures.includes('penalty') && (!this.penaltyAmount || this.penaltyAmount <= 0)) {\r\n        this.$message.warning('请输入处罚金额');\r\n        return;\r\n      }\r\n\r\n      if (this.selectedMeasures.includes('suspend') && (!this.suspendMonths || this.suspendMonths <= 0)) {\r\n        this.$message.warning('请输入暂缓月数');\r\n        return;\r\n      }\r\n\r\n      this.$emit('select', this.previewText);\r\n      this.handleClose();\r\n    }\r\n  }\r\n};\r\n</script>\r\n\r\n<style scoped>\r\n/* 处罚措施选项样式 */\r\n.measure-options {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.measure-item {\r\n  margin-bottom: 15px;\r\n  padding: 0;\r\n}\r\n\r\n.measure-checkbox {\r\n  width: 100%;\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.measure-text {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n}\r\n\r\n.inline-input {\r\n  width: 120px;\r\n}\r\n\r\n.inline-input ::v-deep .el-input__inner {\r\n  height: 28px;\r\n  line-height: 28px;\r\n}\r\n\r\n.inline-input-number {\r\n  width: 120px;\r\n}\r\n\r\n/* 预览区域样式 */\r\n.preview-area {\r\n  margin-top: 20px;\r\n  padding-top: 20px;\r\n  border-top: 1px solid #e4e7ed;\r\n}\r\n\r\n/* 弹窗标题居中 */\r\n::v-deep .el-dialog__header {\r\n  text-align: center;\r\n  background: #f8f9fa;\r\n  border-bottom: 1px solid #e9ecef;\r\n}\r\n\r\n::v-deep .el-dialog__title {\r\n  text-align: center;\r\n  width: 100%;\r\n  display: block;\r\n  font-weight: 600;\r\n  color: #303133;\r\n}\r\n\r\n/* 弹窗内容区域样式 */\r\n::v-deep .el-dialog__body {\r\n  padding: 20px;\r\n}\r\n\r\n/* 复选框组样式 */\r\n::v-deep .el-checkbox-group {\r\n  width: 100%;\r\n}\r\n\r\n::v-deep .el-checkbox {\r\n  margin-right: 0;\r\n  margin-bottom: 0;\r\n}\r\n\r\n::v-deep .el-checkbox__label {\r\n  display: flex;\r\n  align-items: center;\r\n  width: 100%;\r\n  font-size: 14px;\r\n  color: #606266;\r\n}\r\n\r\n::v-deep .el-checkbox__input.is-checked + .el-checkbox__label {\r\n  color: #409EFF;\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCAyFA;EACAA,IAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACA;MACAC,OAAA;MACA;MACAC,gBAAA;MACA;MACAC,aAAA;MACA;MACAC,aAAA;MACA;MACAC,WAAA;IACA;EACA;EACAC,OAAA;IACA,WACAC,IAAA,WAAAA,KAAA;MAAA,IAAAC,YAAA,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA;MACA,KAAAR,OAAA;MACA,KAAAW,iBAAA,CAAAJ,YAAA;MACA,KAAAK,aAAA;IACA;IAEA,WACAC,IAAA,WAAAA,KAAA;MACA,KAAAb,OAAA;IACA;IAEA,WACAc,WAAA,WAAAA,YAAA;MACA,KAAAd,OAAA;MACA,KAAAe,KAAA;IACA;IAEA,WACAA,KAAA,WAAAA,MAAA;MACA,KAAAd,gBAAA;MACA,KAAAC,aAAA;MACA,KAAAC,aAAA;MACA,KAAAC,WAAA;IACA;IAEA,YACAO,iBAAA,WAAAA,kBAAAK,KAAA;MACA,KAAAA,KAAA;QACA,KAAAD,KAAA;QACA;MACA;MAEA,KAAAd,gBAAA;;MAEA;MACA,IAAAgB,YAAA,GAAAD,KAAA,CAAAE,KAAA;MACA,IAAAD,YAAA;QACA,KAAAhB,gBAAA,CAAAkB,IAAA;QACA,KAAAjB,aAAA,GAAAkB,QAAA,CAAAH,YAAA;MACA;;MAEA;MACA,IAAAD,KAAA,CAAAK,QAAA;QACA,KAAApB,gBAAA,CAAAkB,IAAA;MACA;;MAEA;MACA,IAAAH,KAAA,CAAAK,QAAA;QACA,KAAApB,gBAAA,CAAAkB,IAAA;MACA;;MAEA;MACA,IAAAG,YAAA,GAAAN,KAAA,CAAAE,KAAA;MACA,IAAAI,YAAA;QACA,KAAArB,gBAAA,CAAAkB,IAAA;QACA,KAAAhB,aAAA,GAAAiB,QAAA,CAAAE,YAAA;MACA;IACA;IAEA,aACAC,mBAAA,WAAAA,oBAAA;MACA,KAAAX,aAAA;IACA;IAEA,aACAY,iBAAA,WAAAA,kBAAA;MACA,KAAAZ,aAAA;IACA;IAEA,qBACAa,YAAA,WAAAA,aAAA;MACA,UAAAxB,gBAAA,CAAAoB,QAAA;QACA,KAAApB,gBAAA,CAAAkB,IAAA;QACA,KAAAP,aAAA;MACA;IACA;IAEA,qBACAc,YAAA,WAAAA,aAAA;MACA,UAAAzB,gBAAA,CAAAoB,QAAA;QACA,KAAApB,gBAAA,CAAAkB,IAAA;QACA,KAAAP,aAAA;MACA;IACA;IAEA,WACAA,aAAA,WAAAA,cAAA;MACA,IAAAe,QAAA;MAEA,SAAA1B,gBAAA,CAAAoB,QAAA,oBAAAnB,aAAA;QACAyB,QAAA,CAAAR,IAAA,gBAAAS,MAAA,MAAA1B,aAAA;MACA;MAEA,SAAAD,gBAAA,CAAAoB,QAAA;QACAM,QAAA,CAAAR,IAAA;MACA;MAEA,SAAAlB,gBAAA,CAAAoB,QAAA;QACAM,QAAA,CAAAR,IAAA;MACA;MAEA,SAAAlB,gBAAA,CAAAoB,QAAA,oBAAAlB,aAAA;QACAwB,QAAA,CAAAR,IAAA,gBAAAS,MAAA,MAAAzB,aAAA;MACA;MAEA,KAAAC,WAAA,GAAAuB,QAAA,CAAAE,IAAA;IACA;IAEA,WACAC,aAAA,WAAAA,cAAA;MACA,KAAAlB,aAAA;;MAEA;MACA,SAAAX,gBAAA,CAAAQ,MAAA;QACA,KAAAsB,QAAA,CAAAC,OAAA;QACA;MACA;;MAEA;MACA,SAAA/B,gBAAA,CAAAoB,QAAA,sBAAAnB,aAAA,SAAAA,aAAA;QACA,KAAA6B,QAAA,CAAAC,OAAA;QACA;MACA;MAEA,SAAA/B,gBAAA,CAAAoB,QAAA,sBAAAlB,aAAA,SAAAA,aAAA;QACA,KAAA4B,QAAA,CAAAC,OAAA;QACA;MACA;MAEA,KAAAC,KAAA,gBAAA7B,WAAA;MACA,KAAAU,WAAA;IACA;EACA;AACA", "ignoreList": []}]}