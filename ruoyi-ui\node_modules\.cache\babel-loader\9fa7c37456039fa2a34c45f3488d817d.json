{"remainingRequest": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\src\\views\\suppPunishment\\suppInfo-module.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\src\\views\\suppPunishment\\suppInfo-module.vue", "mtime": 1755499162063}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\babel.config.js", "mtime": 1688548084091}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_suppInfo", "require", "name", "data", "visible", "loading", "total", "suppList", "queryParams", "pageNum", "pageSize", "suppId", "suppName", "methods", "show", "reset<PERSON><PERSON>y", "getList", "hide", "handleClose", "reset", "_this", "listSuppInfo", "then", "response", "rows", "catch", "handleQuery", "resetForm", "handleRowClick", "row", "handleRowDoubleClick", "handleSelect", "$emit"], "sources": ["src/views/suppPunishment/suppInfo-module.vue"], "sourcesContent": ["<template>\r\n  <el-dialog\r\n    title=\"供应商信息查询\"\r\n    :visible.sync=\"visible\"\r\n    width=\"900px\"\r\n    append-to-body\r\n    @close=\"handleClose\"\r\n  >\r\n    <!-- 查询条件 -->\r\n    <el-form :model=\"queryParams\" ref=\"queryForm\" :inline=\"true\" label-width=\"100px\">\r\n      <el-form-item label=\"供应商代码\" prop=\"suppId\">\r\n        <el-input\r\n          v-model=\"queryParams.suppId\"\r\n          placeholder=\"请输入供应商代码\"\r\n          clearable\r\n          size=\"small\"\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"供应商名称\" prop=\"suppName\">\r\n        <el-input\r\n          v-model=\"queryParams.suppName\"\r\n          placeholder=\"请输入供应商名称\"\r\n          clearable\r\n          size=\"small\"\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item >\r\n        <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleQuery\">搜索</el-button>\r\n        <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\r\n      </el-form-item>\r\n    </el-form>\r\n\r\n    <!-- 供应商列表 -->\r\n    <el-table \r\n      v-loading=\"loading\" \r\n      :data=\"suppList\" \r\n      @row-click=\"handleRowClick\"\r\n      @row-dblclick=\"handleRowDoubleClick\"\r\n      highlight-current-row\r\n      style=\"cursor: pointer;\"\r\n    >\r\n      <el-table-column label=\"供应商代码\" align=\"center\" prop=\"suppId\" width=\"120\" />\r\n      <el-table-column label=\"供应商名称\" align=\"center\" prop=\"suppName\" />\r\n      <el-table-column label=\"供应商中文简称\" align=\"center\" prop=\"suppShortName\" />\r\n      <el-table-column label=\"供应商地址\" align=\"center\" prop=\"suppAddress\" />\r\n      <el-table-column label=\"操作\" align=\"center\" width=\"100\">\r\n        <template slot-scope=\"scope\">\r\n          <el-button\r\n            size=\"mini\"\r\n            type=\"text\"\r\n            icon=\"el-icon-check\"\r\n            @click=\"handleSelect(scope.row)\"\r\n          >选择</el-button>\r\n        </template>\r\n      </el-table-column>\r\n    </el-table>\r\n\r\n    <!-- 分页 -->\r\n    <pagination\r\n      v-show=\"total > 0\"\r\n      :total=\"total\"\r\n      :page.sync=\"queryParams.pageNum\"\r\n      :limit.sync=\"queryParams.pageSize\"\r\n      @pagination=\"getList\"\r\n    />\r\n\r\n    <!-- 底部按钮 -->\r\n    <div slot=\"footer\" class=\"dialog-footer\">\r\n      <el-button @click=\"handleClose\">取 消</el-button>\r\n    </div>\r\n  </el-dialog>\r\n</template>\r\n\r\n<script>\r\nimport { listSuppInfo } from \"@/api/suppPunishment/suppInfo\";\r\n\r\nexport default {\r\n  name: \"SuppInfoDialog\",\r\n  data() {\r\n    return {\r\n      // 是否显示弹出层\r\n      visible: false,\r\n      // 遮罩层\r\n      loading: true,\r\n      // 总条数\r\n      total: 0,\r\n      // 供应商信息表格数据\r\n      suppList: [],\r\n      // 查询参数\r\n      queryParams: {\r\n        pageNum: 1,\r\n        pageSize: 10,\r\n        suppId: null,\r\n        suppName: null\r\n      }\r\n    };\r\n  },\r\n  methods: {\r\n    /** 显示弹窗 */\r\n    show() {\r\n      this.visible = true;\r\n      this.resetQuery();\r\n      this.getList();\r\n    },\r\n    \r\n    /** 隐藏弹窗 */\r\n    hide() {\r\n      this.visible = false;\r\n    },\r\n    \r\n    /** 关闭弹窗 */\r\n    handleClose() {\r\n      this.visible = false;\r\n      this.reset();\r\n    },\r\n    \r\n    /** 重置数据 */\r\n    reset() {\r\n      this.suppList = [];\r\n      this.total = 0;\r\n      this.queryParams = {\r\n        pageNum: 1,\r\n        pageSize: 10,\r\n        suppId: null,\r\n        suppName: null\r\n      };\r\n    },\r\n    \r\n    /** 查询供应商信息列表 */\r\n    getList() {\r\n      this.loading = true;\r\n      listSuppInfo(this.queryParams).then(response => {\r\n        this.suppList = response.rows;\r\n        this.total = response.total;\r\n        this.loading = false;\r\n      }).catch(() => {\r\n        this.loading = false;\r\n      });\r\n    },\r\n    \r\n    /** 搜索按钮操作 */\r\n    handleQuery() {\r\n      this.queryParams.pageNum = 1;\r\n      this.getList();\r\n    },\r\n    \r\n    /** 重置按钮操作 */\r\n    resetQuery() {\r\n      this.resetForm(\"queryForm\");\r\n      this.handleQuery();\r\n    },\r\n    \r\n    /** 行点击事件 */\r\n    handleRowClick(row) {\r\n      // 可以在这里添加行选中效果\r\n    },\r\n    \r\n    /** 行双击事件 */\r\n    handleRowDoubleClick(row) {\r\n      this.handleSelect(row);\r\n    },\r\n    \r\n    /** 选择供应商 */\r\n    handleSelect(row) {\r\n      this.$emit('select', row);\r\n      this.handleClose();\r\n    }\r\n  }\r\n};\r\n</script>\r\n\r\n<style scoped>\r\n/* 表格行悬停效果 */\r\n::v-deep .el-table tbody tr:hover {\r\n  background-color: #f5f7fa;\r\n}\r\n\r\n/* 查询表单样式 - 输入框左对齐，按钮右对齐 */\r\n.el-form--inline {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 20px;\r\n}\r\n\r\n/* 输入框区域左对齐 */\r\n.el-form--inline .el-form-item:not(:last-child) {\r\n  margin-right: 15px;\r\n  margin-bottom: 0;\r\n}\r\n\r\n/* 按钮区域右对齐 */\r\n.el-form--inline .el-form-item:last-child {\r\n  margin-left: auto;\r\n  margin-right: 0;\r\n  margin-bottom: 0;\r\n}\r\n\r\n/* 统一输入框宽度 */\r\n.el-form--inline .el-form-item .el-input {\r\n  width: 200px;\r\n}\r\n\r\n/* 弹窗标题居中 */\r\n::v-deep .el-dialog__header {\r\n  text-align: center;\r\n}\r\n\r\n::v-deep .el-dialog__title {\r\n  text-align: center;\r\n  width: 100%;\r\n  display: block;\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;AA4EA,IAAAA,SAAA,GAAAC,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCAEA;EACAC,IAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACA;MACAC,OAAA;MACA;MACAC,OAAA;MACA;MACAC,KAAA;MACA;MACAC,QAAA;MACA;MACAC,WAAA;QACAC,OAAA;QACAC,QAAA;QACAC,MAAA;QACAC,QAAA;MACA;IACA;EACA;EACAC,OAAA;IACA,WACAC,IAAA,WAAAA,KAAA;MACA,KAAAV,OAAA;MACA,KAAAW,UAAA;MACA,KAAAC,OAAA;IACA;IAEA,WACAC,IAAA,WAAAA,KAAA;MACA,KAAAb,OAAA;IACA;IAEA,WACAc,WAAA,WAAAA,YAAA;MACA,KAAAd,OAAA;MACA,KAAAe,KAAA;IACA;IAEA,WACAA,KAAA,WAAAA,MAAA;MACA,KAAAZ,QAAA;MACA,KAAAD,KAAA;MACA,KAAAE,WAAA;QACAC,OAAA;QACAC,QAAA;QACAC,MAAA;QACAC,QAAA;MACA;IACA;IAEA,gBACAI,OAAA,WAAAA,QAAA;MAAA,IAAAI,KAAA;MACA,KAAAf,OAAA;MACA,IAAAgB,sBAAA,OAAAb,WAAA,EAAAc,IAAA,WAAAC,QAAA;QACAH,KAAA,CAAAb,QAAA,GAAAgB,QAAA,CAAAC,IAAA;QACAJ,KAAA,CAAAd,KAAA,GAAAiB,QAAA,CAAAjB,KAAA;QACAc,KAAA,CAAAf,OAAA;MACA,GAAAoB,KAAA;QACAL,KAAA,CAAAf,OAAA;MACA;IACA;IAEA,aACAqB,WAAA,WAAAA,YAAA;MACA,KAAAlB,WAAA,CAAAC,OAAA;MACA,KAAAO,OAAA;IACA;IAEA,aACAD,UAAA,WAAAA,WAAA;MACA,KAAAY,SAAA;MACA,KAAAD,WAAA;IACA;IAEA,YACAE,cAAA,WAAAA,eAAAC,GAAA;MACA;IAAA,CACA;IAEA,YACAC,oBAAA,WAAAA,qBAAAD,GAAA;MACA,KAAAE,YAAA,CAAAF,GAAA;IACA;IAEA,YACAE,YAAA,WAAAA,aAAAF,GAAA;MACA,KAAAG,KAAA,WAAAH,GAAA;MACA,KAAAX,WAAA;IACA;EACA;AACA", "ignoreList": []}]}