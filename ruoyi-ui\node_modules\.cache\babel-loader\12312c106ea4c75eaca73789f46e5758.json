{"remainingRequest": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\src\\views\\leave\\material\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\src\\views\\leave\\material\\index.vue", "mtime": 1755499098414}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\babel.config.js", "mtime": 1688548084091}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:InVzZSBzdHJpY3QiOwoKT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsICJfX2VzTW9kdWxlIiwgewogIHZhbHVlOiB0cnVlCn0pOwpleHBvcnRzLmRlZmF1bHQgPSB2b2lkIDA7CnJlcXVpcmUoImNvcmUtanMvbW9kdWxlcy9lcy5hcnJheS5tYXAuanMiKTsKcmVxdWlyZSgiY29yZS1qcy9tb2R1bGVzL2VzLm9iamVjdC50by1zdHJpbmcuanMiKTsKcmVxdWlyZSgiY29yZS1qcy9tb2R1bGVzL2VzbmV4dC5pdGVyYXRvci5jb25zdHJ1Y3Rvci5qcyIpOwpyZXF1aXJlKCJjb3JlLWpzL21vZHVsZXMvZXNuZXh0Lml0ZXJhdG9yLm1hcC5qcyIpOwp2YXIgX21hdGVyaWFsID0gcmVxdWlyZSgiQC9hcGkvbGVhdmUvbWF0ZXJpYWwiKTsKdmFyIF9qc0JlYXV0aWZ5ID0gcmVxdWlyZSgianMtYmVhdXRpZnkiKTsKLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KdmFyIF9kZWZhdWx0ID0gZXhwb3J0cy5kZWZhdWx0ID0gewogIG5hbWU6ICJNYXRlcmlhbCIsCiAgY29tcG9uZW50czoge30sCiAgZGF0YTogZnVuY3Rpb24gZGF0YSgpIHsKICAgIHJldHVybiB7CiAgICAgIC8vIOmBrue9qeWxggogICAgICBsb2FkaW5nOiB0cnVlLAogICAgICAvLyDpgInkuK3mlbDnu4QKICAgICAgaWRzOiBbXSwKICAgICAgLy8g6Z2e5Y2V5Liq56aB55SoCiAgICAgIHNpbmdsZTogdHJ1ZSwKICAgICAgLy8g6Z2e5aSa5Liq56aB55SoCiAgICAgIG11bHRpcGxlOiB0cnVlLAogICAgICAvLyDmmL7npLrmkJzntKLmnaHku7YKICAgICAgc2hvd1NlYXJjaDogdHJ1ZSwKICAgICAgLy8g5oC75p2h5pWwCiAgICAgIHRvdGFsOiAwLAogICAgICAvLyDlh7rpl6jor4HnianotYTooajmoLzmlbDmja4KICAgICAgbWF0ZXJpYWxMaXN0OiBbXSwKICAgICAgLy8g5by55Ye65bGC5qCH6aKYCiAgICAgIHRpdGxlOiAiIiwKICAgICAgLy8g5piv5ZCm5pi+56S65by55Ye65bGCCiAgICAgIG9wZW46IGZhbHNlLAogICAgICAvLyBvcGVuRGV0YWlsOiBmYWxzZSwKICAgICAgLy8g5p+l6K+i5Y+C5pWwCiAgICAgIHF1ZXJ5UGFyYW1zOiB7CiAgICAgICAgcGFnZU51bTogMSwKICAgICAgICBwYWdlU2l6ZTogMTAsCiAgICAgICAgbWF0ZXJpYWxOYW1lOiBudWxsLAogICAgICAgIG1hdGVyaWFsU3BlYzogbnVsbCwKICAgICAgICBtZWFzdXJlVW5pdDogbnVsbCwKICAgICAgICBtZWFzdXJlRmxhZzogbnVsbCwKICAgICAgICBtYXRlcmlhbFR5cGU6IG51bGwsCiAgICAgICAgcXVlcnlXb3JkOiBudWxsLAogICAgICAgIGNyZWF0ZUJ5OiBudWxsLAogICAgICAgIGNyZWF0ZVRpbWU6IG51bGwsCiAgICAgICAgdXBkYXRlQnk6IG51bGwsCiAgICAgICAgdXBkYXRlVGltZTogbnVsbCwKICAgICAgICByZW1hcms6IG51bGwKICAgICAgfSwKICAgICAgLy8g6KGo5Y2V5Y+C5pWwCiAgICAgIGZvcm06IHt9LAogICAgICAvLyDooajljZXmoKHpqowKICAgICAgcnVsZXM6IHt9CiAgICB9OwogIH0sCiAgY3JlYXRlZDogZnVuY3Rpb24gY3JlYXRlZCgpIHsKICAgIHRoaXMuZ2V0TGlzdCgpOwogIH0sCiAgbWV0aG9kczogewogICAgLyoqIOafpeivouWHuumXqOivgeeJqei1hOWIl+ihqCAqL2dldExpc3Q6IGZ1bmN0aW9uIGdldExpc3QoKSB7CiAgICAgIHZhciBfdGhpcyA9IHRoaXM7CiAgICAgIHRoaXMubG9hZGluZyA9IHRydWU7CiAgICAgICgwLCBfbWF0ZXJpYWwubGlzdE1hdGVyaWFsKSh0aGlzLnF1ZXJ5UGFyYW1zKS50aGVuKGZ1bmN0aW9uIChyZXNwb25zZSkgewogICAgICAgIF90aGlzLm1hdGVyaWFsTGlzdCA9IHJlc3BvbnNlLnJvd3M7CiAgICAgICAgX3RoaXMudG90YWwgPSByZXNwb25zZS50b3RhbDsKICAgICAgICBfdGhpcy5sb2FkaW5nID0gZmFsc2U7CiAgICAgIH0pOwogICAgfSwKICAgIC8vIOWPlua2iOaMiemSrgogICAgY2FuY2VsOiBmdW5jdGlvbiBjYW5jZWwoKSB7CiAgICAgIHRoaXMub3BlbiA9IGZhbHNlOwogICAgICB0aGlzLnJlc2V0KCk7CiAgICB9LAogICAgLy8gZGV0YWlsQ2FuY2VsKCkgewogICAgLy8gICB0aGlzLm9wZW5EZXRhaWwgPSBmYWxzZTsKICAgIC8vIH0sCiAgICAvLyDooajljZXph43nva4KICAgIHJlc2V0OiBmdW5jdGlvbiByZXNldCgpIHsKICAgICAgdGhpcy5mb3JtID0gewogICAgICAgIGlkOiBudWxsLAogICAgICAgIG1hdGVyaWFsTmFtZTogbnVsbCwKICAgICAgICBtYXRlcmlhbFNwZWM6IG51bGwsCiAgICAgICAgbWVhc3VyZVVuaXQ6IG51bGwsCiAgICAgICAgbWVhc3VyZUZsYWc6IG51bGwsCiAgICAgICAgcmVtYXJrOiBudWxsLAogICAgICAgIGNyZWF0ZVRpbWU6IG51bGwsCiAgICAgICAgY3JlYXRlQnk6IG51bGwsCiAgICAgICAgdXBkYXRlVGltZTogbnVsbCwKICAgICAgICB1cGRhdGVCeTogbnVsbCwKICAgICAgICBtYXRlcmlhbFR5cGU6IG51bGwsCiAgICAgICAgcXVlcnlXb3JkOiBudWxsCiAgICAgIH07CiAgICAgIHRoaXMucmVzZXRGb3JtKCJmb3JtIik7CiAgICB9LAogICAgLyoqIOaQnOe0ouaMiemSruaTjeS9nCAqL2hhbmRsZVF1ZXJ5OiBmdW5jdGlvbiBoYW5kbGVRdWVyeSgpIHsKICAgICAgdGhpcy5xdWVyeVBhcmFtcy5wYWdlTnVtID0gMTsKICAgICAgdGhpcy5nZXRMaXN0KCk7CiAgICB9LAogICAgLyoqIOmHjee9ruaMiemSruaTjeS9nCAqL3Jlc2V0UXVlcnk6IGZ1bmN0aW9uIHJlc2V0UXVlcnkoKSB7CiAgICAgIHRoaXMucmVzZXRGb3JtKCJxdWVyeUZvcm0iKTsKICAgICAgdGhpcy5oYW5kbGVRdWVyeSgpOwogICAgfSwKICAgIC8vIOWkmumAieahhumAieS4reaVsOaNrgogICAgaGFuZGxlU2VsZWN0aW9uQ2hhbmdlOiBmdW5jdGlvbiBoYW5kbGVTZWxlY3Rpb25DaGFuZ2Uoc2VsZWN0aW9uKSB7CiAgICAgIHRoaXMuaWRzID0gc2VsZWN0aW9uLm1hcChmdW5jdGlvbiAoaXRlbSkgewogICAgICAgIHJldHVybiBpdGVtLmlkOwogICAgICB9KTsKICAgICAgdGhpcy5zaW5nbGUgPSBzZWxlY3Rpb24ubGVuZ3RoICE9PSAxOwogICAgICB0aGlzLm11bHRpcGxlID0gIXNlbGVjdGlvbi5sZW5ndGg7CiAgICB9LAogICAgLyoqIOaWsOWinuaMiemSruaTjeS9nCAqL2hhbmRsZUFkZDogZnVuY3Rpb24gaGFuZGxlQWRkKCkgewogICAgICB0aGlzLnJlc2V0KCk7CiAgICAgIHRoaXMub3BlbiA9IHRydWU7CiAgICAgIHRoaXMudGl0bGUgPSAi5re75Yqg5Ye66Zeo6K+B54mp6LWEIjsKICAgIH0sCiAgICAvKiog5L+u5pS55oyJ6ZKu5pON5L2cICovaGFuZGxlVXBkYXRlOiBmdW5jdGlvbiBoYW5kbGVVcGRhdGUocm93KSB7CiAgICAgIHZhciBfdGhpczIgPSB0aGlzOwogICAgICB0aGlzLnJlc2V0KCk7CiAgICAgIHZhciBpZCA9IHJvdy5pZCB8fCB0aGlzLmlkczsKICAgICAgKDAsIF9tYXRlcmlhbC5nZXRNYXRlcmlhbCkoaWQpLnRoZW4oZnVuY3Rpb24gKHJlc3BvbnNlKSB7CiAgICAgICAgX3RoaXMyLmZvcm0gPSByZXNwb25zZS5kYXRhOwogICAgICAgIF90aGlzMi5vcGVuID0gdHJ1ZTsKICAgICAgICBfdGhpczIudGl0bGUgPSAi5L+u5pS55Ye66Zeo6K+B54mp6LWEIjsKICAgICAgfSk7CiAgICB9LAogICAgLy8gLyoqIOivpuaDheaMiemSruaTjeS9nCAqLwogICAgLy8gaGFuZGxlRGV0YWlsKHJvdykgewogICAgLy8gICB0aGlzLmZvcm0gPSBKU09OLnBhcnNlKEpTT04uc3RyaW5naWZ5KHJvdykpOwogICAgLy8gICB0aGlzLm9wZW5EZXRhaWwgPSB0cnVlOwogICAgLy8gICB0aGlzLnRpdGxlID0gIueJqeaWmeinhOagvOeuoeeQhiI7CiAgICAvLyB9LAogICAgLyoqIOaPkOS6pOaMiemSriAqLwogICAgc3VibWl0Rm9ybTogZnVuY3Rpb24gc3VibWl0Rm9ybSgpIHsKICAgICAgdmFyIF90aGlzMyA9IHRoaXM7CiAgICAgIHRoaXMuJHJlZnNbImZvcm0iXS52YWxpZGF0ZShmdW5jdGlvbiAodmFsaWQpIHsKICAgICAgICBpZiAodmFsaWQpIHsKICAgICAgICAgIGlmIChfdGhpczMuZm9ybS5pZCAhPSBudWxsKSB7CiAgICAgICAgICAgICgwLCBfbWF0ZXJpYWwudXBkYXRlTWF0ZXJpYWwpKF90aGlzMy5mb3JtKS50aGVuKGZ1bmN0aW9uIChyZXNwb25zZSkgewogICAgICAgICAgICAgIF90aGlzMy5tc2dTdWNjZXNzKCLkv67mlLnmiJDlip8iKTsKICAgICAgICAgICAgICBfdGhpczMub3BlbiA9IGZhbHNlOwogICAgICAgICAgICAgIF90aGlzMy5nZXRMaXN0KCk7CiAgICAgICAgICAgIH0pOwogICAgICAgICAgfSBlbHNlIHsKICAgICAgICAgICAgKDAsIF9tYXRlcmlhbC5hZGRNYXRlcmlhbCkoX3RoaXMzLmZvcm0pLnRoZW4oZnVuY3Rpb24gKHJlc3BvbnNlKSB7CiAgICAgICAgICAgICAgX3RoaXMzLm1zZ1N1Y2Nlc3MoIuaWsOWinuaIkOWKnyIpOwogICAgICAgICAgICAgIF90aGlzMy5vcGVuID0gZmFsc2U7CiAgICAgICAgICAgICAgX3RoaXMzLmdldExpc3QoKTsKICAgICAgICAgICAgfSk7CiAgICAgICAgICB9CiAgICAgICAgfQogICAgICB9KTsKICAgIH0sCiAgICAvKiog5Yig6Zmk5oyJ6ZKu5pON5L2cICovaGFuZGxlRGVsZXRlOiBmdW5jdGlvbiBoYW5kbGVEZWxldGUocm93KSB7CiAgICAgIHZhciBfdGhpczQgPSB0aGlzOwogICAgICB2YXIgaWRzID0gcm93Lm1hdGVyaWFsTmFtZSB8fCB0aGlzLmlkczsKICAgICAgdGhpcy4kY29uZmlybSgn5piv5ZCm56Gu6K6k5bqf5byD54mp6LWE5ZCN56ew5Li6IicgKyBpZHMgKyAnIueahOaVsOaNrumhuT8nLCAi6K2m5ZGKIiwgewogICAgICAgIGNvbmZpcm1CdXR0b25UZXh0OiAi56Gu5a6aIiwKICAgICAgICBjYW5jZWxCdXR0b25UZXh0OiAi5Y+W5raIIiwKICAgICAgICB0eXBlOiAid2FybmluZyIKICAgICAgfSkudGhlbihmdW5jdGlvbiAoKSB7CiAgICAgICAgcmV0dXJuICgwLCBfbWF0ZXJpYWwuZGVsTWF0ZXJpYWwpKGlkcyk7CiAgICAgIH0pLnRoZW4oZnVuY3Rpb24gKCkgewogICAgICAgIF90aGlzNC5nZXRMaXN0KCk7CiAgICAgICAgX3RoaXM0Lm1zZ1N1Y2Nlc3MoIuWIoOmZpOaIkOWKnyIpOwogICAgICB9KTsKICAgIH0sCiAgICAvKiog5a+85Ye65oyJ6ZKu5pON5L2cICovaGFuZGxlRXhwb3J0OiBmdW5jdGlvbiBoYW5kbGVFeHBvcnQoKSB7CiAgICAgIHZhciBfdGhpczUgPSB0aGlzOwogICAgICB2YXIgcXVlcnlQYXJhbXMgPSB0aGlzLnF1ZXJ5UGFyYW1zOwogICAgICB0aGlzLiRjb25maXJtKCfmmK/lkKbnoa7orqTlr7zlh7rmiYDmnInlh7rpl6jor4HnianotYTmlbDmja7pobk/JywgIuitpuWRiiIsIHsKICAgICAgICBjb25maXJtQnV0dG9uVGV4dDogIuehruWumiIsCiAgICAgICAgY2FuY2VsQnV0dG9uVGV4dDogIuWPlua2iCIsCiAgICAgICAgdHlwZTogIndhcm5pbmciCiAgICAgIH0pLnRoZW4oZnVuY3Rpb24gKCkgewogICAgICAgIHJldHVybiAoMCwgX21hdGVyaWFsLmV4cG9ydE1hdGVyaWFsKShxdWVyeVBhcmFtcyk7CiAgICAgIH0pLnRoZW4oZnVuY3Rpb24gKHJlc3BvbnNlKSB7CiAgICAgICAgX3RoaXM1LmRvd25sb2FkKHJlc3BvbnNlLm1zZyk7CiAgICAgIH0pOwogICAgfQogIH0KfTs="}, {"version": 3, "names": ["_material", "require", "_jsBeautify", "name", "components", "data", "loading", "ids", "single", "multiple", "showSearch", "total", "materialList", "title", "open", "queryParams", "pageNum", "pageSize", "materialName", "materialSpec", "measureUnit", "measureFlag", "materialType", "queryWord", "createBy", "createTime", "updateBy", "updateTime", "remark", "form", "rules", "created", "getList", "methods", "_this", "listMaterial", "then", "response", "rows", "cancel", "reset", "id", "resetForm", "handleQuery", "reset<PERSON><PERSON>y", "handleSelectionChange", "selection", "map", "item", "length", "handleAdd", "handleUpdate", "row", "_this2", "getMaterial", "submitForm", "_this3", "$refs", "validate", "valid", "updateMaterial", "msgSuccess", "addMaterial", "handleDelete", "_this4", "$confirm", "confirmButtonText", "cancelButtonText", "type", "delMaterial", "handleExport", "_this5", "exportMaterial", "download", "msg"], "sources": ["src/views/leave/material/index.vue"], "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <el-form :model=\"queryParams\" ref=\"queryForm\" :inline=\"true\" v-show=\"showSearch\" label-width=\"110px\">\r\n      <el-form-item label=\"物料名称\" prop=\"materialName\">\r\n        <el-input v-model=\"queryParams.materialName\" placeholder=\"请输入物料名称\" clearable size=\"small\"\r\n          @keyup.enter.native=\"handleQuery\" />\r\n      </el-form-item>\r\n      <el-form-item label=\"是否计质量标记\" prop=\"materialType\">\r\n        <el-select v-model=\"queryParams.materialType\" placeholder=\"请选择是否计质量标记\">\r\n          <el-option label=\"计量\" value=\"1\" />\r\n          <el-option label=\"不计量\" value=\"0\" />\r\n        </el-select>\r\n      </el-form-item>\r\n      <!-- <el-form-item label=\"计量单位\" prop=\"measureUnit\">\r\n        <el-input v-model=\"queryParams.measureUnit\" placeholder=\"请输入计量单位\" clearable size=\"small\"\r\n          @keyup.enter.native=\"handleQuery\" />\r\n      </el-form-item> -->\r\n      <el-form-item label=\"是否贵重金属\" prop=\"materialType\">\r\n        <el-select v-model=\"queryParams.materialType\" placeholder=\"请选择是否贵重金属\">\r\n          <el-option label=\"是\" value=\"1\" />\r\n          <el-option label=\"否\" value=\"0\" />\r\n        </el-select>\r\n      </el-form-item>\r\n      <el-form-item>\r\n        <el-button type=\"cyan\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleQuery\">搜索</el-button>\r\n        <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\r\n      </el-form-item>\r\n    </el-form>\r\n\r\n    <el-row :gutter=\"10\" class=\"mb8\">\r\n      <el-col :span=\"1.5\">\r\n        <el-button type=\"primary\" icon=\"el-icon-plus\" size=\"mini\" @click=\"handleAdd\"\r\n          >新增</el-button>\r\n      </el-col>\r\n      <el-col :span=\"1.5\">\r\n        <el-button type=\"warning\" icon=\"el-icon-download\" size=\"mini\" @click=\"handleExport\"\r\n          >导出</el-button>\r\n      </el-col>\r\n      <right-toolbar :showSearch.sync=\"showSearch\" @queryTable=\"getList\"></right-toolbar>\r\n    </el-row>\r\n\r\n    <el-table v-loading=\"loading\" :data=\"materialList\" @selection-change=\"handleSelectionChange\">\r\n      <el-table-column type=\"selection\" width=\"55\" align=\"center\" />\r\n      <el-table-column label=\"物资名称\" align=\"center\" prop=\"materialName\" />\r\n      <!-- <el-table-column label=\"计量单位\" align=\"center\" prop=\"measureUnit\" /> -->\r\n      <el-table-column label=\"计质量标记\" align=\"center\" prop=\"measureFlag\">\r\n        <template slot-scope=\"scope\">\r\n          <el-tag v-if=\"scope.row.measureFlag == '1'\" type=\"success\">只质量</el-tag>\r\n          <el-tag v-if=\"scope.row.measureFlag == '0'\" type=\"warning\">只质检</el-tag>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"贵重金属\" align=\"center\" prop=\"materialType\">\r\n        <template slot-scope=\"scope\">\r\n          <el-tag v-if=\"scope.row.materialType == '1'\" type=\"success\">是</el-tag>\r\n          <el-tag v-if=\"scope.row.materialType == '0'\" type=\"warning\">否</el-tag>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"拼音头\" align=\"center\" prop=\"queryWord\" />\r\n      <el-table-column label=\"备注\" align=\"center\" prop=\"remark\" />\r\n      <el-table-column label=\"创建人\" align=\"center\" prop=\"createBy\" width=\"180\"/>\r\n      <el-table-column label=\"创建时间\" align=\"center\" prop=\"createTime\"/>\r\n      </el-table-column>\r\n      <el-table-column label=\"操作\" align=\"center\" class-name=\"small-padding fixed-width\">\r\n        <template slot-scope=\"scope\">\r\n          <!-- <el-button size=\"mini\" type=\"text\" icon=\"el-icon-edit\" @click=\"handleDetail(scope.row)\">详情</el-button> -->\r\n          <el-button type=\"text\" icon=\"el-icon-edit\" size=\"mini\" @click=\"handleUpdate(scope.row)\">修改</el-button>\r\n          <el-button size=\"text\" type=\"text\" icon=\"el-icon-delete\" @click=\"handleDelete(scope.row)\">废弃</el-button>\r\n        </template>\r\n      </el-table-column>\r\n    </el-table>\r\n\r\n    <pagination v-show=\"total > 0\" :total=\"total\" :page.sync=\"queryParams.pageNum\" :limit.sync=\"queryParams.pageSize\"\r\n      @pagination=\"getList\" />\r\n\r\n    <!-- 添加或修改出门证物资对话框 -->\r\n    <el-dialog :title=\"title\" :visible.sync=\"open\" width=\"500px\" append-to-body>\r\n      <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" label-width=\"90px\">\r\n        <el-form-item label=\"物资名称\" prop=\"materialName\">\r\n          <el-input v-model=\"form.materialName\" placeholder=\"请输入物资名称\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"计质量标记\" prop=\"drawingType\">\r\n          <el-select v-model=\"form.measureFlag\" placeholder=\"请选择计质量标记\">\r\n            <el-option label=\"只计量\" :value=\"1\" />\r\n            <el-option label=\"只质检\" :value=\"0\" />\r\n          </el-select>\r\n        </el-form-item>\r\n        <el-form-item label=\"拼音头\" prop=\"queryWord\">\r\n          <el-input v-model=\"form.queryWord\" placeholder=\"请输入拼音头\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"贵重金属\" prop=\"materialType\">\r\n          <el-select v-model=\"form.materialType\" placeholder=\"请选择是否贵重金属\">\r\n            <el-option label=\"是\" :value=\"1\" />\r\n            <el-option label=\"否\" :value=\"0\" />\r\n          </el-select>\r\n        </el-form-item>\r\n        <el-form-item label=\"备注\" prop=\"remark\">\r\n          <el-input v-model=\"form.remark\" placeholder=\"请输入备注\" />\r\n        </el-form-item>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button type=\"primary\" @click=\"submitForm\">确 定</el-button>\r\n        <el-button @click=\"cancel\">取 消</el-button>\r\n      </div>\r\n    </el-dialog>\r\n    <!-- \r\n    <el-dialog title=\"物料规格管理\" :visible.sync=\"openDetail\" width=\"600px\" append-to-body>\r\n      <el-form :model=\"form\" label-width=\"120px\">\r\n        <el-form-item label=\"物料名称：\" prop=\"materialName\">\r\n          {{ form.materialName }}\r\n        </el-form-item>\r\n        <el-form-item label=\"计质量标记：\" prop=\"drawingType\">\r\n          {{ form.drawingType == 0 ? '只质检' : '只质量' }}\r\n        </el-form-item>\r\n        <el-form-item label=\"拼音头：\" prop=\"queryWord\">\r\n          {{ form.queryWord }}\r\n        </el-form-item>\r\n        <el-form-item label=\"贵重金属：\" prop=\"materialType\">\r\n          {{ form.materialType == 0 ? '否' : '是' }}\r\n        </el-form-item>\r\n      </el-form>\r\n    </el-dialog> -->\r\n\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { listMaterial, getMaterial, delMaterial, addMaterial, updateMaterial, exportMaterial } from \"@/api/leave/material\";\r\nimport { js } from \"js-beautify\";\r\n\r\nexport default {\r\n  name: \"Material\",\r\n  components: {\r\n  },\r\n  data() {\r\n    return {\r\n      // 遮罩层\r\n      loading: true,\r\n      // 选中数组\r\n      ids: [],\r\n      // 非单个禁用\r\n      single: true,\r\n      // 非多个禁用\r\n      multiple: true,\r\n      // 显示搜索条件\r\n      showSearch: true,\r\n      // 总条数\r\n      total: 0,\r\n      // 出门证物资表格数据\r\n      materialList: [],\r\n      // 弹出层标题\r\n      title: \"\",\r\n      // 是否显示弹出层\r\n      open: false,\r\n      // openDetail: false,\r\n      // 查询参数\r\n      queryParams: {\r\n        pageNum: 1,\r\n        pageSize: 10,\r\n        materialName: null,\r\n        materialSpec: null,\r\n        measureUnit: null,\r\n        measureFlag: null,\r\n        materialType: null,\r\n        queryWord: null,\r\n        createBy: null,\r\n        createTime: null,\r\n        updateBy: null,\r\n        updateTime: null,\r\n        remark: null\r\n      },\r\n      // 表单参数\r\n      form: {},\r\n      // 表单校验\r\n      rules: {\r\n      }\r\n    };\r\n  },\r\n  created() {\r\n    this.getList();\r\n  },\r\n  methods: {\r\n    /** 查询出门证物资列表 */\r\n    getList() {\r\n      this.loading = true;\r\n      listMaterial(this.queryParams).then(response => {\r\n        this.materialList = response.rows;\r\n        this.total = response.total;\r\n        this.loading = false;\r\n      });\r\n    },\r\n    // 取消按钮\r\n    cancel() {\r\n      this.open = false;\r\n      this.reset();\r\n    },\r\n    // detailCancel() {\r\n    //   this.openDetail = false;\r\n    // },\r\n    // 表单重置\r\n    reset() {\r\n      this.form = {\r\n        id: null,\r\n        materialName: null,\r\n        materialSpec: null,\r\n        measureUnit: null,\r\n        measureFlag: null,\r\n        remark: null,\r\n        createTime: null,\r\n        createBy: null,\r\n        updateTime: null,\r\n        updateBy: null,\r\n        materialType: null,\r\n        queryWord: null\r\n      };\r\n      this.resetForm(\"form\");\r\n    },\r\n    /** 搜索按钮操作 */\r\n    handleQuery() {\r\n      this.queryParams.pageNum = 1;\r\n      this.getList();\r\n    },\r\n    /** 重置按钮操作 */\r\n    resetQuery() {\r\n      this.resetForm(\"queryForm\");\r\n      this.handleQuery();\r\n    },\r\n    // 多选框选中数据\r\n    handleSelectionChange(selection) {\r\n      this.ids = selection.map(item => item.id)\r\n      this.single = selection.length !== 1\r\n      this.multiple = !selection.length\r\n    },\r\n    /** 新增按钮操作 */\r\n    handleAdd() {\r\n      this.reset();\r\n      this.open = true;\r\n      this.title = \"添加出门证物资\";\r\n    },\r\n    /** 修改按钮操作 */\r\n    handleUpdate(row) {\r\n      this.reset();\r\n      const id = row.id || this.ids\r\n      getMaterial(id).then(response => {\r\n        this.form = response.data;\r\n        this.open = true;\r\n        this.title = \"修改出门证物资\";\r\n      });\r\n    },\r\n\r\n    // /** 详情按钮操作 */\r\n    // handleDetail(row) {\r\n    //   this.form = JSON.parse(JSON.stringify(row));\r\n    //   this.openDetail = true;\r\n    //   this.title = \"物料规格管理\";\r\n    // },\r\n    /** 提交按钮 */\r\n    submitForm() {\r\n      this.$refs[\"form\"].validate(valid => {\r\n        if (valid) {\r\n          if (this.form.id != null) {\r\n            updateMaterial(this.form).then(response => {\r\n              this.msgSuccess(\"修改成功\");\r\n              this.open = false;\r\n              this.getList();\r\n            });\r\n          } else {\r\n            addMaterial(this.form).then(response => {\r\n              this.msgSuccess(\"新增成功\");\r\n              this.open = false;\r\n              this.getList();\r\n            });\r\n          }\r\n        }\r\n      });\r\n    },\r\n    /** 删除按钮操作 */\r\n    handleDelete(row) {\r\n      const ids = row.materialName || this.ids;\r\n      this.$confirm('是否确认废弃物资名称为\"' + ids + '\"的数据项?', \"警告\", {\r\n        confirmButtonText: \"确定\",\r\n        cancelButtonText: \"取消\",\r\n        type: \"warning\"\r\n      }).then(function () {\r\n        return delMaterial(ids);\r\n      }).then(() => {\r\n        this.getList();\r\n        this.msgSuccess(\"删除成功\");\r\n      })\r\n    },\r\n    /** 导出按钮操作 */\r\n    handleExport() {\r\n      const queryParams = this.queryParams;\r\n      this.$confirm('是否确认导出所有出门证物资数据项?', \"警告\", {\r\n        confirmButtonText: \"确定\",\r\n        cancelButtonText: \"取消\",\r\n        type: \"warning\"\r\n      }).then(function () {\r\n        return exportMaterial(queryParams);\r\n      }).then(response => {\r\n        this.download(response.msg);\r\n      })\r\n    }\r\n  }\r\n};\r\n</script>\r\n"], "mappings": ";;;;;;;;;;AA8HA,IAAAA,SAAA,GAAAC,OAAA;AACA,IAAAC,WAAA,GAAAD,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCAEA;EACAE,IAAA;EACAC,UAAA,GACA;EACAC,IAAA,WAAAA,KAAA;IACA;MACA;MACAC,OAAA;MACA;MACAC,GAAA;MACA;MACAC,MAAA;MACA;MACAC,QAAA;MACA;MACAC,UAAA;MACA;MACAC,KAAA;MACA;MACAC,YAAA;MACA;MACAC,KAAA;MACA;MACAC,IAAA;MACA;MACA;MACAC,WAAA;QACAC,OAAA;QACAC,QAAA;QACAC,YAAA;QACAC,YAAA;QACAC,WAAA;QACAC,WAAA;QACAC,YAAA;QACAC,SAAA;QACAC,QAAA;QACAC,UAAA;QACAC,QAAA;QACAC,UAAA;QACAC,MAAA;MACA;MACA;MACAC,IAAA;MACA;MACAC,KAAA,GACA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA,KAAAC,OAAA;EACA;EACAC,OAAA;IACA,gBACAD,OAAA,WAAAA,QAAA;MAAA,IAAAE,KAAA;MACA,KAAA5B,OAAA;MACA,IAAA6B,sBAAA,OAAApB,WAAA,EAAAqB,IAAA,WAAAC,QAAA;QACAH,KAAA,CAAAtB,YAAA,GAAAyB,QAAA,CAAAC,IAAA;QACAJ,KAAA,CAAAvB,KAAA,GAAA0B,QAAA,CAAA1B,KAAA;QACAuB,KAAA,CAAA5B,OAAA;MACA;IACA;IACA;IACAiC,MAAA,WAAAA,OAAA;MACA,KAAAzB,IAAA;MACA,KAAA0B,KAAA;IACA;IACA;IACA;IACA;IACA;IACAA,KAAA,WAAAA,MAAA;MACA,KAAAX,IAAA;QACAY,EAAA;QACAvB,YAAA;QACAC,YAAA;QACAC,WAAA;QACAC,WAAA;QACAO,MAAA;QACAH,UAAA;QACAD,QAAA;QACAG,UAAA;QACAD,QAAA;QACAJ,YAAA;QACAC,SAAA;MACA;MACA,KAAAmB,SAAA;IACA;IACA,aACAC,WAAA,WAAAA,YAAA;MACA,KAAA5B,WAAA,CAAAC,OAAA;MACA,KAAAgB,OAAA;IACA;IACA,aACAY,UAAA,WAAAA,WAAA;MACA,KAAAF,SAAA;MACA,KAAAC,WAAA;IACA;IACA;IACAE,qBAAA,WAAAA,sBAAAC,SAAA;MACA,KAAAvC,GAAA,GAAAuC,SAAA,CAAAC,GAAA,WAAAC,IAAA;QAAA,OAAAA,IAAA,CAAAP,EAAA;MAAA;MACA,KAAAjC,MAAA,GAAAsC,SAAA,CAAAG,MAAA;MACA,KAAAxC,QAAA,IAAAqC,SAAA,CAAAG,MAAA;IACA;IACA,aACAC,SAAA,WAAAA,UAAA;MACA,KAAAV,KAAA;MACA,KAAA1B,IAAA;MACA,KAAAD,KAAA;IACA;IACA,aACAsC,YAAA,WAAAA,aAAAC,GAAA;MAAA,IAAAC,MAAA;MACA,KAAAb,KAAA;MACA,IAAAC,EAAA,GAAAW,GAAA,CAAAX,EAAA,SAAAlC,GAAA;MACA,IAAA+C,qBAAA,EAAAb,EAAA,EAAAL,IAAA,WAAAC,QAAA;QACAgB,MAAA,CAAAxB,IAAA,GAAAQ,QAAA,CAAAhC,IAAA;QACAgD,MAAA,CAAAvC,IAAA;QACAuC,MAAA,CAAAxC,KAAA;MACA;IACA;IAEA;IACA;IACA;IACA;IACA;IACA;IACA;IACA0C,UAAA,WAAAA,WAAA;MAAA,IAAAC,MAAA;MACA,KAAAC,KAAA,SAAAC,QAAA,WAAAC,KAAA;QACA,IAAAA,KAAA;UACA,IAAAH,MAAA,CAAA3B,IAAA,CAAAY,EAAA;YACA,IAAAmB,wBAAA,EAAAJ,MAAA,CAAA3B,IAAA,EAAAO,IAAA,WAAAC,QAAA;cACAmB,MAAA,CAAAK,UAAA;cACAL,MAAA,CAAA1C,IAAA;cACA0C,MAAA,CAAAxB,OAAA;YACA;UACA;YACA,IAAA8B,qBAAA,EAAAN,MAAA,CAAA3B,IAAA,EAAAO,IAAA,WAAAC,QAAA;cACAmB,MAAA,CAAAK,UAAA;cACAL,MAAA,CAAA1C,IAAA;cACA0C,MAAA,CAAAxB,OAAA;YACA;UACA;QACA;MACA;IACA;IACA,aACA+B,YAAA,WAAAA,aAAAX,GAAA;MAAA,IAAAY,MAAA;MACA,IAAAzD,GAAA,GAAA6C,GAAA,CAAAlC,YAAA,SAAAX,GAAA;MACA,KAAA0D,QAAA,kBAAA1D,GAAA;QACA2D,iBAAA;QACAC,gBAAA;QACAC,IAAA;MACA,GAAAhC,IAAA;QACA,WAAAiC,qBAAA,EAAA9D,GAAA;MACA,GAAA6B,IAAA;QACA4B,MAAA,CAAAhC,OAAA;QACAgC,MAAA,CAAAH,UAAA;MACA;IACA;IACA,aACAS,YAAA,WAAAA,aAAA;MAAA,IAAAC,MAAA;MACA,IAAAxD,WAAA,QAAAA,WAAA;MACA,KAAAkD,QAAA;QACAC,iBAAA;QACAC,gBAAA;QACAC,IAAA;MACA,GAAAhC,IAAA;QACA,WAAAoC,wBAAA,EAAAzD,WAAA;MACA,GAAAqB,IAAA,WAAAC,QAAA;QACAkC,MAAA,CAAAE,QAAA,CAAApC,QAAA,CAAAqC,GAAA;MACA;IACA;EACA;AACA", "ignoreList": []}]}