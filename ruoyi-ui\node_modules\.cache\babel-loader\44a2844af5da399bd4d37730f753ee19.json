{"remainingRequest": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\src\\views\\drawing\\technical\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\src\\views\\drawing\\technical\\index.vue", "mtime": 1755499162048}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\babel.config.js", "mtime": 1688548084091}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_drawing", "require", "_materialInfo", "_role", "_methods", "name", "data", "loading", "ids", "showSearch", "total", "userTotal", "totalTbl", "drawingList", "tblList", "userList", "selectAll", "selectedRow", "selectedMaterial", "selectedUserId", "selected<PERSON>ser", "title", "open", "fileList", "techFileList", "isInternalDrawing", "isExternalDrawing", "isExternalReviewerVisible", "isTblModalVisible", "isTblClose", "showDialog", "tblTitle", "upload", "headers", "url", "process", "env", "VUE_APP_BASE_API", "isUploading", "queryParams", "pageNum", "pageSize", "suitEquipment", "drawingNo", "materialsCode", "drawingType", "drawingStatus", "drawingResource", "speciModel", "reviewer", "designerInner", "reviewerOuter", "drawingCompanyOuter", "tenantId", "addFactory", "remarks", "attachmentUuid", "instanceId", "isNoticeCx", "objectVersionNumber", "flowStatus", "createUserNo", "createDateTime", "updateUserNo", "updateDateTime", "companyId", "attachment", "fileUrl", "tblQueryParams", "materialsName", "approver<PERSON><PERSON><PERSON>", "userName", "nick<PERSON><PERSON>", "undefined", "<PERSON><PERSON><PERSON>", "form", "_defineProperty2", "default", "rules", "required", "message", "trigger", "created", "getList", "getUserList", "methods", "formatTime", "isoTimeString", "date", "Date", "year", "getFullYear", "month", "String", "getMonth", "padStart", "day", "getDate", "hours", "getHours", "minutes", "getMinutes", "seconds", "getSeconds", "concat", "formatStringTime", "time", "substring", "tableRowClassName", "_ref", "row", "rowIndex", "status", "_this", "listDrawing", "then", "response", "rows", "getMaterialList", "_this2", "listMaterialInfo", "_this3", "getListByRole<PERSON>ey", "cancel", "reset", "clearMaterialsCode", "clearSearchMaterialsCode", "id", "originalId", "resetForm", "onDrawingResourceChange", "value", "handleTblSelectionChange", "queryMaterialCode", "console", "log", "stateId", "$message", "warning", "itemId", "itemModel", "speciModelReadonly", "resetTblQuery", "TblSelectForm", "toggleSelection", "$refs", "multipleTable", "toggleAllSelection", "handleSelectionChange", "handleMaterialsRowClick", "handleApproverRowClick", "handleClearSelection", "handlePictureCardPreview", "file", "localUrl", "window", "location", "host", "replace", "tmpUrl", "handlePictureUp", "error", "handlePictureUpdateSuccess", "that", "code", "push", "fileListToPicture", "removeFailedFile", "msg", "handlePictureUploadProgress", "handlePictureRemove", "i", "length", "splice", "for<PERSON>ach", "item", "JSON", "stringify", "toString", "filter", "handleTechPreview", "handleFilePreview", "handleTechUploadProgress", "event", "handleTechExceed", "files", "handleTechUploadSuccess", "technicalAgreement", "handleTechRemove", "targetList", "uid", "initiateApproval", "handleDetail", "$router", "handleQuery", "handleUserQuery", "searchByMaterialsCode", "searchMaterialsCode", "openMaterialsCodeDialog", "reset<PERSON><PERSON>y", "resetUser<PERSON><PERSON>y", "handleAdd", "handleUpdate", "_this4", "getDrawing", "parse", "closeDialog", "UserSelectForm", "_this5", "param", "approveUserId", "citicsXctgDrawing", "sendApprove", "type", "catch", "submitForm", "_this6", "validate", "valid", "updateDrawing", "$modal", "msgSuccess", "addTechnicalAgreement", "handleDelete", "_this7", "$confirm", "confirmButtonText", "cancelButtonText", "delDrawing", "handleExport", "_this8", "exportDrawing", "download"], "sources": ["src/views/drawing/technical/index.vue"], "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <el-form :model=\"queryParams\" ref=\"queryForm\" size=\"small\" :inline=\"true\" v-show=\"showSearch\" label-width=\"80px\">\r\n      <el-form-item label=\"物料编码\" prop=\"materialsCode\">\r\n        <el-input v-model=\"queryParams.materialsCode\" clearable readonly>\r\n          <el-button slot=\"append\" icon=\"el-icon-search\" size=\"mini\" @click=\"searchMaterialsCode\"></el-button>\r\n          <el-button slot=\"append\" icon=\"el-icon-delete\" size=\"mini\" @click=\"clearSearchMaterialsCode\"></el-button>\r\n        </el-input>\r\n      </el-form-item>\r\n      <el-form-item label=\"创建人\" prop=\"createUserNo\">\r\n        <el-input v-model=\"queryParams.createUserNo\" placeholder=\"请输入创建人\" clearable @keyup.enter.native=\"handleQuery\" />\r\n      </el-form-item>\r\n\r\n      <el-form-item>\r\n        <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleQuery\">搜索</el-button>\r\n        <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\r\n      </el-form-item>\r\n    </el-form>\r\n\r\n    <el-row :gutter=\"10\" class=\"mb8\">\r\n      <el-col :span=\"1.5\">\r\n        <el-button type=\"primary\" plain icon=\"el-icon-plus\" size=\"mini\" @click=\"handleAdd\">新增</el-button>\r\n      </el-col>\r\n      <!-- <el-col :span=\"1.5\">\r\n        <el-button type=\"primary\" plain icon=\"el-icon-success\" size=\"mini\" @click=\"initiateApproval\">发起审批</el-button>\r\n      </el-col> -->\r\n      <el-col :span=\"1.5\">\r\n        <el-button type=\"warning\" plain icon=\"el-icon-download\" size=\"mini\" @click=\"handleExport\">导出</el-button>\r\n      </el-col>\r\n      <right-toolbar :showSearch.sync=\"showSearch\" @queryTable=\"getList\"></right-toolbar>\r\n    </el-row>\r\n\r\n    <el-table v-loading=\"loading\" :data=\"drawingList\" ref=\"multipleTable\" @selection-change=\"handleSelectionChange\">\r\n      <el-table-column label=\"物料编码\" align=\"center\" prop=\"materialsCode\" />\r\n      <el-table-column label=\"创建人\" align=\"center\" prop=\"createUserNo\" />\r\n      <el-table-column label=\"创建时间\" align=\"center\" prop=\"createDateTime\" width=\"180\">\r\n        <template slot-scope=\"scope\">\r\n          <span>{{ formatTime(scope.row.createDateTime) }}</span>\r\n        </template>\r\n      </el-table-column>\r\n\r\n      <el-table-column label=\"操作\" align=\"center\" class-name=\"small-padding fixed-width\">\r\n        <template slot-scope=\"scope\">\r\n          <el-button plain v-if=\"scope.row.showModifyBtn\" size=\"mini\" type=\"text\" icon=\"el-icon-edit\"\r\n            @click=\"handleUpdate(scope.row)\">修改</el-button>\r\n          <el-button size=\"mini\" type=\"text\" icon=\"el-icon-edit\" @click=\"handleDetail(scope.row)\">详情</el-button>\r\n          <el-button size=\"mini\" type=\"text\" icon=\"el-icon-delete\" @click=\"handleDelete(scope.row)\">删除</el-button>\r\n        </template>\r\n      </el-table-column>\r\n    </el-table>\r\n\r\n    <pagination v-show=\"total > 0\" :total=\"total\" :page.sync=\"queryParams.pageNum\" :limit.sync=\"queryParams.pageSize\"\r\n      @pagination=\"getList\" />\r\n\r\n    <!--新增图纸时物料编码对话框-->\r\n    <el-dialog title=\"物料代码列表\" :visible.sync=\"isTblModalVisible\" width=\"800px\">\r\n      <!-- 添加物料编码搜索表单 -->\r\n      <el-form :inline=\"true\" :model=\"tblQueryParams\" class=\"demo-form-inline\">\r\n        <el-form-item label=\"物料代码\">\r\n          <el-input v-model=\"tblQueryParams.itemId\" placeholder=\"请输入物料代码\" clearable></el-input>\r\n        </el-form-item>\r\n        <el-form-item>\r\n          <el-button type=\"primary\" icon=\"el-icon-search\" @click=\"searchByMaterialsCode\">搜索</el-button>\r\n        </el-form-item>\r\n        <el-form-item>\r\n          <el-button @click=\"resetTblQuery\">重置</el-button>\r\n        </el-form-item>\r\n      </el-form>\r\n\r\n      <!-- 新增图纸时物料编码列表展示 -->\r\n      <el-table :data=\"tblList\" stripe highlight-current-row @row-click=\"handleMaterialsRowClick\">\r\n        <el-table-column label=\"选择\" width=\"50\" align=\"center\">\r\n          <template slot-scope=\"scope\">\r\n            <el-radio v-model=\"selectedMaterial\" :label=\"scope.row\" @change=\"handleMaterialsRowClick(scope.row)\" >\r\n              <span></span>\r\n            </el-radio>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column label=\"物料代码\" align=\"center\" prop=\"itemId\" />\r\n        <el-table-column label=\"物料名称\" align=\"center\" prop=\"itemName\" />\r\n        <el-table-column label=\"型号规格\" align=\"center\" prop=\"itemModel\" />\r\n        <el-table-column label=\"状态\" align=\"center\">\r\n          <template v-slot:default=\"{ row }\">\r\n            <!-- 使用三元运算符判断 stateId 的值 -->\r\n            {{ row.stateId === 'A' ? '可用' : '不可用' }}\r\n          </template>\r\n        </el-table-column>\r\n        <!-- <el-table-column label=\"创建时间\" align=\"center\" prop=\"createDateTime\" /> -->\r\n        <el-table-column label=\"创建时间\" align=\"center\" prop=\"recCreateTime\" width=\"180\">\r\n          <template slot-scope=\"scope\">\r\n            <span>{{ formatStringTime(scope.row.recCreateTime) }}</span>\r\n          </template>\r\n        </el-table-column>\r\n      </el-table>\r\n\r\n      <pagination v-show=\"totalTbl > 0\" :total=\"totalTbl\" :page.sync=\"tblQueryParams.pageNum\"\r\n        :limit.sync=\"tblQueryParams.pageSize\" @pagination=\"getMaterialList\" />\r\n\r\n      <span slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button type=\"primary\" @click=\"queryMaterialCode\">确 定</el-button>\r\n        <el-button @click=\"isTblModalVisible = false\">关闭</el-button>\r\n      </span>\r\n    </el-dialog>\r\n\r\n    <!-- 添加或修改图纸库对话框 -->\r\n    <el-dialog :title=\"title\" :visible.sync=\"open\" width=\"1000px\" append-to-body>\r\n      <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" label-width=\"100px\" label-position=\"top\">\r\n        <el-row :gutter=\"20\">\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"物料编码\" prop=\"materialsCode\">\r\n              <el-input v-model=\"form.materialsCode\" clearable readonly>\r\n                <el-button v-if=\"!form.id\" slot=\"append\" icon=\"el-icon-search\" size=\"mini\" @click=\"openMaterialsCodeDialog\"></el-button>\r\n                <el-button v-if=\"!form.id\" slot=\"append\" icon=\"el-icon-delete\" size=\"mini\" @click=\"clearMaterialsCode\"></el-button>\r\n              </el-input>\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n     <!-- 附件和技术协议上传（水平排列） -->\r\n     <el-form-item>\r\n        <el-row :gutter=\"20\">\r\n          <!-- 技术协议上传 -->\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"技术协议\" prop=\"technicalAgreement\" label-width=\"80px\">\r\n              <el-upload\r\n                ref=\"techUpload\"\r\n                list-type=\"file\"\r\n                accept=\".pdf\"\r\n                :on-preview=\"handleTechPreview\"\r\n                :limit=\"1\"\r\n                :file-list=\"techFileList\"\r\n                :on-exceed=\"handleTechExceed\"\r\n                :on-progress=\"handleTechUploadProgress\"\r\n                :on-success=\"handleTechUploadSuccess\"\r\n                :on-remove=\"handleTechRemove\"\r\n                :action=\"upload.url\"\r\n                :disabled=\"upload.isUploading\">\r\n                <el-button size=\"small\" type=\"primary\">点击上传</el-button>\r\n                <div slot=\"tip\" class=\"el-upload__tip\" style=\"font-weight: bold; color: #D22630;\">\r\n                    因兴澄保密政策，请申请外发后，上传解密后的pdf文件，若直接上传加密后的pdf文件，将上传失败！\r\n                </div>\r\n              </el-upload>\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n      </el-form-item>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button type=\"primary\" @click=\"submitForm\">确 定</el-button>\r\n        <el-button @click=\"cancel\">取 消</el-button>\r\n      </div>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { listDrawing, getDrawing, delDrawing, addDrawing,addTechnicalAgreement, updateDrawing, exportDrawing, sendApprove } from \"@/api/drawing/drawing\";\r\nimport { listMaterialInfo } from \"@/api/materialInfo/materialInfo\";\r\nimport { getListByRoleKey } from '@/api/system/role';\r\n\r\n\r\nexport default {\r\n  name: \"Technical\",\r\n  data() {\r\n    return {\r\n      // 遮罩层\r\n      loading: false,\r\n      ids: [],\r\n      // 显示搜索条件\r\n      showSearch: true,\r\n      // 总条数\r\n      total: 0,\r\n      userTotal: 0,\r\n      totalTbl: 0,\r\n      // 图纸库表格数据\r\n      drawingList: [],\r\n      // 物料编码列表数据\r\n      tblList: [],\r\n      userList: [],\r\n      // 控制全选复选框的状态\r\n      selectAll: false,\r\n      // 用于保存选中的行数据\r\n      selectedRow: null,\r\n      // 保存选中的物料编码数据\r\n      selectedMaterial: null,\r\n      selectedUserId: null,\r\n      selectedUser: null,\r\n      // 弹出层标题\r\n      title: \"\",\r\n      // 是否显示弹出层\r\n      open: false,\r\n      //附件列表\r\n      fileList: [],\r\n      //技术协议列表\r\n      techFileList:[],\r\n      //跟踪是否选择了“内部图纸”\r\n      isInternalDrawing: false,\r\n      isExternalDrawing: false,\r\n      isExternalReviewerVisible: false,\r\n      isTblModalVisible: false,\r\n      isTblClose: false,\r\n      showDialog: false,\r\n      tblTitle: '选择物料编码',\r\n      upload: {\r\n        //设置上传的请求头部\r\n        headers: {},\r\n        // 上传的地址\r\n        url: process.env.VUE_APP_BASE_API + \"/app/common/uploadDecryptPdfMinio\",\r\n        isUploading: false,\r\n      },\r\n      // 查询参数\r\n      queryParams: {\r\n        pageNum: 1,\r\n        pageSize: 10,\r\n        suitEquipment: null, drawingNo: '', materialsCode: '', drawingType: null, drawingStatus: null, drawingResource: null, speciModel: null, reviewer: null, designerInner: null, reviewerOuter: null, drawingCompanyOuter: null, tenantId: null, addFactory: null, remarks: null, attachmentUuid: null, instanceId: null, isNoticeCx: null, objectVersionNumber: null, flowStatus: null, createUserNo: null, createDateTime: null, updateUserNo: null, updateDateTime: null, companyId: null, attachment: null, fileUrl: null\r\n      },\r\n      tblQueryParams: {\r\n        pageNum: 1,\r\n        pageSize: 10,\r\n        materialsCode: '', createDateTime: null, materialsName: null, speciModel: null\r\n      },\r\n      //查询审核人参数\r\n      approverParams:{\r\n        pageNum: 1,\r\n        pageSize: 10,\r\n        userName: '',\r\n        nickName: undefined,\r\n        roleKey: undefined\r\n      },\r\n      // 表单参数\r\n      form: {\r\n        drawingResource: '', // 默认情况下没有选择任何图纸来源\r\n        designerInner: '',\r\n        reviewerOuter: '',\r\n        drawingCompanyOuter: '',\r\n        reviewerOuter: '',\r\n        speciModelReadonly: false,\r\n        materialsCode: '',\r\n        speciModel: '',\r\n        drawingType: '',\r\n        fileUrl: '',\r\n      },\r\n      // 表单校验\r\n      rules: {\r\n        drawingNo: [{ required: true, message: '请输入图纸号', trigger: 'blur' }],\r\n        materialsCode: [{ required: true, message: '请输入物料编码', trigger: 'blur' }],\r\n        drawingType: [{ required: true, message: '请选择图纸类型', trigger: 'change' }],\r\n        drawingResource: [{ required: true, message: '请选择图纸来源', trigger: 'change' }]\r\n      },\r\n    };\r\n  },\r\n  created() {\r\n    this.getList();\r\n    this.getUserList();\r\n  },\r\n  methods: {\r\n\r\n    //时间格式化\r\n    formatTime(isoTimeString) {\r\n      if (!isoTimeString) return '';\r\n\r\n      // 解析 ISO 8601 时间字符串\r\n      const date = new Date(isoTimeString);\r\n\r\n      // 提取年月日时分秒\r\n      const year = date.getFullYear();\r\n      const month = String(date.getMonth() + 1).padStart(2, '0'); // 月份是从0开始的，所以加1\r\n      const day = String(date.getDate()).padStart(2, '0');\r\n      const hours = String(date.getHours()).padStart(2, '0');\r\n      const minutes = String(date.getMinutes()).padStart(2, '0');\r\n      const seconds = String(date.getSeconds()).padStart(2, '0');\r\n\r\n      // 返回格式化的字符串\r\n      return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;\r\n\r\n    },\r\n     // 字符串时间格式化\r\n     formatStringTime(time) {\r\n      if (typeof time === \"string\") {\r\n        return (\r\n          time.substring(0, 4) +\r\n          \"-\" +\r\n          time.substring(4, 6) +\r\n          \"-\" +\r\n          time.substring(6, 8) +\r\n          \" \" +\r\n          time.substring(8, 10) +\r\n          \":\" +\r\n          time.substring(10, 12) +\r\n          \":\" +\r\n          time.substring(12, 14)\r\n        );\r\n      } else {\r\n        return time;\r\n      }\r\n    },\r\n    tableRowClassName({ row, rowIndex }) {\r\n      if (row.status == '1') {\r\n        return 'warning-row'\r\n      }\r\n      return ''\r\n    },\r\n\r\n    /** 查询图纸库列表 */\r\n    getList() {\r\n      this.loading = true;\r\n      listDrawing(this.queryParams).then(response => {\r\n        this.drawingList = response.rows;\r\n        this.total = response.total;\r\n        this.loading = false;\r\n      });\r\n    },\r\n    /**查询物料编码列表 */\r\n    getMaterialList() {\r\n      this.loading = true;\r\n      listMaterialInfo(this.tblQueryParams).then(response => {\r\n        this.tblList = response.rows;\r\n        this.totalTbl = response.total;\r\n        this.loading = false;\r\n      });\r\n    },\r\n    /**查询用户列表 */\r\n    getUserList() {\r\n      this.loading = true\r\n      this.approverParams.roleKey = 'drawing.approver.factory';\r\n      getListByRoleKey(this.approverParams).then((response) => {\r\n          this.userList = response.rows\r\n          this.userTotal = response.total\r\n          this.loading = false\r\n        }\r\n      )\r\n    },\r\n    // 取消按钮\r\n    cancel() {\r\n      this.open = false;\r\n      this.reset();\r\n    },\r\n    clearMaterialsCode() {\r\n      this.form.materialsCode = ''; // 清空物料编码\r\n      this.form.speciModel = '';\r\n    },\r\n    clearSearchMaterialsCode() {\r\n      this.queryParams.materialsCode = '';\r\n    },\r\n    // 表单重置\r\n    reset() {\r\n      this.form = {\r\n        id: null, originalId: null, suitEquipment: null, drawingNo: null, materialsCode: null, drawingType: null, drawingStatus: null, drawingResource: null, speciModel: null, reviewer: null,\r\n        designerInner: null, reviewerOuter: null, drawingCompanyOuter: null, tenantId: null, addFactory: null, remarks: null,\r\n        attachmentUuid: null, instanceId: null, isNoticeCx: null, objectVersionNumber: null, flowStatus: null, createUserNo: null, createDateTime: null,\r\n        updateUserNo: null, updateDateTime: null, companyId: null, attachment: null, fileUrl: null\r\n      };\r\n      this.resetForm(\"form\");\r\n      this.fileList = [];\r\n      this.techFileList = [];\r\n    },\r\n    onDrawingResourceChange(value) {\r\n      if (value === '2') { // 内部图纸\r\n        this.isInternalDrawing = true;\r\n        this.isExternalDrawing = false;\r\n        this.isExternalReviewerVisible = false;\r\n      } else if (value === '1') { // 外来图纸\r\n        this.isInternalDrawing = false;\r\n        this.isExternalDrawing = true;\r\n        this.isExternalReviewerVisible = true;\r\n      } else { // 如果没有选择任何值\r\n        this.isInternalDrawing = false;\r\n        this.isExternalDrawing = false;\r\n        this.isExternalReviewerVisible = false;\r\n      }\r\n    },\r\n    // 物料编码列表的选择变化\r\n    handleTblSelectionChange(rows) {\r\n      this.selectedMaterial = rows[0];\r\n    },\r\n    //用于查询图纸审批单\r\n    queryMaterialCode() {\r\n      console.log(this.selectedMaterial)\r\n      if (this.selectedMaterial) {\r\n        if (this.open) {\r\n          //新增或修改时\r\n          //判断是否可用\r\n          if(this.selectedMaterial.stateId !== 'A'){\r\n            this.$message.warning('该物料编码已停用，请重新选择！');\r\n            return;\r\n          }\r\n          this.form.materialsCode = this.selectedMaterial.itemId;\r\n          this.form.speciModel = this.selectedMaterial.itemModel;\r\n          this.form.speciModelReadonly = true; // 设置规格型号输入框为只读\r\n        } else {\r\n          //搜索时\r\n          this.queryParams.materialsCode = this.selectedMaterial.itemId;\r\n        }\r\n\r\n\r\n        this.resetTblQuery(); // 重置物料编码列表搜索条件\r\n        this.selectedMaterial = null; // 清空已选择的物料\r\n        this.isTblModalVisible = false; // 关闭对话框\r\n      } else {\r\n        // 如果没有选择任何物料，则提示用户\r\n        this.$message.warning('请选择一条物料编码记录！');\r\n      }\r\n    },\r\n    //\r\n\r\n\r\n    // 处理搜索部分物料编码列表的确定按钮\r\n    TblSelectForm() {\r\n      if (this.selectedMaterial) {\r\n        // 如果选择了物料，则更新表单中的物料编码和规格型号\r\n        this.queryParams.materialsCode = this.selectedMaterial.materialsCode;\r\n        this.resetTblQuery(); // 重置物料编码列表搜索条件\r\n        this.selectedMaterial = null; // 清空已选择的物料\r\n        this.isTblClose = false; // 关闭对话框\r\n        this.isTblModalVisible = false; // 关闭另一个对话框\r\n      } else {\r\n        // 如果没有选择任何物料，则提示用户\r\n        this.$message.warning('请选择一条物料编码记录！');\r\n      }\r\n    },\r\n\r\n    toggleSelection() {\r\n      this.$refs.multipleTable.toggleAllSelection();\r\n    },\r\n\r\n    //图纸单选\r\n    handleSelectionChange(row) {\r\n      if (this.selectedRow === row) {\r\n        this.selectedRow = null; // 如果当前选中的行再次被点击，则取消选中\r\n      } else {\r\n        this.selectedRow = row; // 否则，选中该行\r\n      }\r\n    },\r\n\r\n    //物料单选\r\n    handleMaterialsRowClick(row) {\r\n        this.selectedMaterial = row;\r\n    },\r\n\r\n    handleApproverRowClick(row) {\r\n      // 单击行时触发\r\n        this.selectedUserId = row.userName;\r\n    },\r\n\r\n\r\n\r\n    // 清空选择状态\r\n    handleClearSelection() {\r\n      this.selectedRow = null;\r\n      this.ids = [];\r\n      this.selectedUserId = null;\r\n    },\r\n\r\n    //上传图片\r\n    handlePictureCardPreview(file) {\r\n      var localUrl = window.location.host;\r\n      if (file.url != null) {\r\n        if (localUrl == \"************:8099\") {\r\n          file.url = file.url.replace(\"ydxt.citicsteel.com:8099\", \"************:8099\");\r\n        }\r\n        window.open(file.url);\r\n      }\r\n      if (file.response && file.response.url) {\r\n        if (localUrl == \"************:8099\") {\r\n          let tmpUrl = file.response.url.replace(\"ydxt.citicsteel.com:8099\", \"************:8099\");\r\n          window.open(tmpUrl);\r\n        } else {\r\n          window.open(file.response.url);\r\n        }\r\n      }\r\n    },\r\n\r\n    handlePictureUp(file, fileList) {\r\n      this.$message.error(\"图片上限为1\");\r\n    },\r\n\r\n    handlePictureUpdateSuccess(response, file, fileList) {\r\n      let that = this;\r\n      if (response.code == 200) {\r\n        that.fileList.push({ url: response.url, name: file.name });\r\n        that.fileListToPicture();\r\n      } else {\r\n        that.removeFailedFile(file);\r\n        that.$message.error(response.msg);\r\n      }\r\n      that.upload.isUploading = false;\r\n    },\r\n    //图片上传中处理\r\n    handlePictureUploadProgress() {\r\n      this.upload.isUploading = true;\r\n    },\r\n\r\n    handlePictureRemove(file, fileList) {\r\n      for (let i = 0; i < this.fileList.length; i++) {\r\n        if (this.fileList[i].url == file.url) {\r\n          this.fileList.splice(i, 1);\r\n        }\r\n      }\r\n      this.fileListToPicture();\r\n    },\r\n    fileListToPicture() {\r\n      let fileUrl = [];\r\n      this.fileList.forEach((item) => {\r\n        fileUrl.push({ url: item.url });\r\n      });\r\n      if (fileUrl.length > 0)\r\n        this.form.fileUrl = JSON.stringify(fileUrl).toString();\r\n      else this.form.fileUrl = \"[]\";\r\n    },\r\n    removeFailedFile(file) {\r\n      // 从 fileList 中移除失败的文件\r\n      this.fileList = this.fileList.filter(item => item !== file);\r\n    },\r\n\r\n    // 技术协议\r\n    handleTechPreview(file) {\r\n      this.handleFilePreview(file);\r\n    },\r\n\r\n    handleTechUploadProgress(event, file, fileList) {\r\n      this.upload.isUploading = true;\r\n    },\r\n\r\n    handleTechExceed(files, fileList) {\r\n      this.$message.warning(`技术协议只能上传一个文件，当前选择了 ${files.length} 个文件`);\r\n    },\r\n\r\n    handleTechUploadSuccess(response, file, fileList) {\r\n      this.upload.isUploading = false;\r\n      if (response.code === 200) {\r\n        this.techFileList = [{ url: response.url, name: file.name }];\r\n        this.form.technicalAgreement = JSON.stringify(this.techFileList);\r\n      } else {\r\n        this.$message.error(response.msg);\r\n        this.removeFailedFile(file, this.techFileList);\r\n      }\r\n    },\r\n\r\n    handleTechRemove(file, fileList) {\r\n      this.techFileList = [];\r\n      this.form.technicalAgreement = \"\";\r\n    },\r\n\r\n     // 通用文件预览方法\r\n     handleFilePreview(file) {\r\n      let url = file.url || (file.response && file.response.url);\r\n      if (!url) return;\r\n\r\n      // 本地开发环境替换\r\n      if (window.location.host === \"************:8099\") {\r\n        url = url.replace(\"ydxt.citicsteel.com:8099\", \"************:8099\");\r\n      }\r\n\r\n      window.open(url);\r\n    },\r\n\r\n    // 从指定文件列表中移除失败文件\r\n    removeFailedFile(file, targetList) {\r\n      targetList = targetList.filter(item => item.uid !== file.uid);\r\n    },\r\n\r\n    /** 发起审批 */\r\n    initiateApproval() {\r\n      //判断是否有选中的行 this.selectedRow\r\n      if (this.selectedRow == null) {\r\n        this.$message.warning(\"请选择一条记录！\");\r\n        return;\r\n      }\r\n      this.showDialog = true;\r\n    },\r\n\r\n    /**查看详情操作 */\r\n    handleDetail(row) {\r\n      console.log(row);\r\n      this.$router.push(\"/drawing/technicalDetail/\" + row.id);\r\n    },\r\n\r\n    /** 搜索按钮操作 */\r\n    handleQuery() {\r\n      this.queryParams.pageNum = 1;\r\n      this.getList();\r\n    },\r\n    /**搜索审核人操作 */\r\n    handleUserQuery() {\r\n      this.approverParams.pageNum =1;\r\n      this.getUserList()\r\n    },\r\n\r\n    /** 搜索物料编码表 */\r\n    searchByMaterialsCode() {\r\n      this.tblQueryParams.pageNum = 1;\r\n      this.getMaterialList();\r\n      this.isTblModalVisible = true;\r\n    },\r\n\r\n    // 搜索物料编码\r\n    searchMaterialsCode() {\r\n      this.searchByMaterialsCode(); // 在打开对话框的同时获取数据\r\n    },\r\n\r\n    //新增时搜索物料编码\r\n    openMaterialsCodeDialog() {\r\n      this.isTblModalVisible = true;\r\n      this.tblQueryParams.pageNum = 1;\r\n      this.getMaterialList();\r\n    },\r\n\r\n    /** 重置按钮操作 */\r\n    resetQuery() {\r\n      this.resetForm(\"queryForm\");\r\n      this.handleQuery();\r\n      this.getList();\r\n    },\r\n\r\n    /**重置审核人操作 */\r\n    resetUserQuery() {\r\n      this.resetForm('queryForm')\r\n      this.approverParams.userName = '';\r\n      this.approverParams.nickName = '';\r\n      this.handleUserQuery();\r\n      this.getUserList();\r\n      this.handleClearSelection();\r\n    },\r\n\r\n    /** 重置物料代码列表按钮操作 */\r\n    resetTblQuery() {\r\n      this.tblQueryParams = {\r\n        pageNum: 1,\r\n        pageSize: 10,\r\n        materialsCode: null,\r\n        createDateTime: null,\r\n        materialsName: null,\r\n        speciModel: null\r\n      };\r\n      // this.getMaterialList();\r\n    },\r\n\r\n    /** 新增按钮操作 */\r\n    handleAdd() {\r\n      this.reset();\r\n      this.open = true;\r\n      this.title = \"添加技术协议\";\r\n    },\r\n    /** 修改按钮操作 */\r\n    handleUpdate(row) {\r\n      this.reset();\r\n      console.log(row);\r\n      const id = row.id || this.ids\r\n      getDrawing(id).then(response => {\r\n        this.form = response.data;\r\n        this.open = true;\r\n        this.title = \"修改技术协议\";\r\n      });\r\n      if (row.fileUrl) {\r\n        this.fileList = JSON.parse(row.fileUrl);\r\n      }\r\n      else {\r\n        this.fileList = []\r\n      }\r\n    },\r\n\r\n    // 关闭按钮点击事件\r\n    closeDialog() {\r\n      this.resetTblQuery(); // 重置物料编码列表搜索条件\r\n      this.selectedMaterial = null; // 清空已选择的物料\r\n      this.isTblClose = false; // 关闭对话框\r\n      this.isTblModalVisible = false; // 关闭另一个对话框\r\n      this.showDialog = false;\r\n      this.selectedUserId = null;\r\n    },\r\n\r\n    //推送给审核人确定按钮\r\n    UserSelectForm() {\r\n      if (this.selectedUserId !== null) {\r\n        // 将审核人的 userId 发送到后端\r\n        let param = {\r\n          approveUserId: this.selectedUserId,\r\n          citicsXctgDrawing: this.selectedRow\r\n        };\r\n        sendApprove(param).then(() => {\r\n          this.$message({\r\n            type: 'success',\r\n            message: '发起审批成功！'\r\n          });\r\n          // 清空选择状态\r\n          this.handleClearSelection();\r\n          this.closeDialog();\r\n          this.getList();\r\n        }).catch(error => {\r\n          this.$message({\r\n            type: 'error',\r\n            message: error.response.data.message || '发起审批失败！'\r\n          });\r\n        });\r\n      } else {\r\n        this.$message.warning('请至少选择一个审核人！');\r\n      }\r\n\r\n    },\r\n\r\n    /** 提交按钮 */\r\n    submitForm() {\r\n      this.$refs[\"form\"].validate(valid => {\r\n        if (valid) {\r\n          //判断是否上传了文件（form.fileUrl）\r\n          if (this.form.technicalAgreement == null || this.form.technicalAgreement == '[]') {\r\n            this.$message.error(\"请上传文件\");\r\n            return;\r\n          }\r\n          if (this.form.id != null) {\r\n            updateDrawing(this.form).then(response => {\r\n              this.$modal.msgSuccess(\"修改成功\");\r\n              this.open = false;\r\n              this.getList();\r\n            });\r\n          } else {\r\n            addTechnicalAgreement(this.form).then(response => {\r\n              this.$modal.msgSuccess(\"新增成功\");\r\n              this.open = false;\r\n              this.getList();\r\n            });\r\n          }\r\n        }\r\n      });\r\n    },\r\n    /** 删除按钮操作 */\r\n    handleDelete(row) {\r\n      const id = row.id || this.ids;\r\n      this.$confirm(`是否确认删除该技术协议？`, \"警告\", {\r\n        confirmButtonText: \"确定\",\r\n        cancelButtonText: \"取消\",\r\n        type: \"warning\"\r\n      }).then(function() {\r\n        return delDrawing(id);\r\n        }).then(() => {\r\n          this.getList();\r\n          this.msgSuccess(\"删除成功\");\r\n        })\r\n      },\r\n\r\n    /** 导出按钮操作 */\r\n    handleExport() {\r\n      const queryParams = this.queryParams;\r\n      this.$confirm('是否确认导出所有图纸库数据项？', \"警告\", {\r\n        confirmButtonText: \"确定\",\r\n        cancelButtonText: \"取消\",\r\n        type: \"warning\"\r\n      }).then(function () {\r\n        return exportDrawing(queryParams);\r\n      }).then(response => {\r\n        this.download(response.msg);\r\n      })\r\n    }\r\n  }\r\n};\r\n</script>\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;AA2JA,IAAAA,QAAA,GAAAC,OAAA;AACA,IAAAC,aAAA,GAAAD,OAAA;AACA,IAAAE,KAAA,GAAAF,OAAA;AAAA,IAAAG,QAAA,E;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCAGA;EACAC,IAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACA;MACAC,OAAA;MACAC,GAAA;MACA;MACAC,UAAA;MACA;MACAC,KAAA;MACAC,SAAA;MACAC,QAAA;MACA;MACAC,WAAA;MACA;MACAC,OAAA;MACAC,QAAA;MACA;MACAC,SAAA;MACA;MACAC,WAAA;MACA;MACAC,gBAAA;MACAC,cAAA;MACAC,YAAA;MACA;MACAC,KAAA;MACA;MACAC,IAAA;MACA;MACAC,QAAA;MACA;MACAC,YAAA;MACA;MACAC,iBAAA;MACAC,iBAAA;MACAC,yBAAA;MACAC,iBAAA;MACAC,UAAA;MACAC,UAAA;MACAC,QAAA;MACAC,MAAA;QACA;QACAC,OAAA;QACA;QACAC,GAAA,EAAAC,OAAA,CAAAC,GAAA,CAAAC,gBAAA;QACAC,WAAA;MACA;MACA;MACAC,WAAA;QACAC,OAAA;QACAC,QAAA;QACAC,aAAA;QAAAC,SAAA;QAAAC,aAAA;QAAAC,WAAA;QAAAC,aAAA;QAAAC,eAAA;QAAAC,UAAA;QAAAC,QAAA;QAAAC,aAAA;QAAAC,aAAA;QAAAC,mBAAA;QAAAC,QAAA;QAAAC,UAAA;QAAAC,OAAA;QAAAC,cAAA;QAAAC,UAAA;QAAAC,UAAA;QAAAC,mBAAA;QAAAC,UAAA;QAAAC,YAAA;QAAAC,cAAA;QAAAC,YAAA;QAAAC,cAAA;QAAAC,SAAA;QAAAC,UAAA;QAAAC,OAAA;MACA;MACAC,cAAA;QACA5B,OAAA;QACAC,QAAA;QACAG,aAAA;QAAAkB,cAAA;QAAAO,aAAA;QAAArB,UAAA;MACA;MACA;MACAsB,cAAA;QACA9B,OAAA;QACAC,QAAA;QACA8B,QAAA;QACAC,QAAA,EAAAC,SAAA;QACAC,OAAA,EAAAD;MACA;MACA;MACAE,IAAA,MAAAC,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA;QACA9B,eAAA;QAAA;QACAG,aAAA;QACAC,aAAA;QACAC,mBAAA;MAAA,oBACA,2BACA,yBACA,mBACA,oBACA,gBACA,GACA;MACA;MACA0B,KAAA;QACAnC,SAAA;UAAAoC,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;QACArC,aAAA;UAAAmC,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;QACApC,WAAA;UAAAkC,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;QACAlC,eAAA;UAAAgC,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;MACA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA,KAAAC,OAAA;IACA,KAAAC,WAAA;EACA;EACAC,OAAA,GAAAjF,QAAA;IAEA;IACAkF,UAAA,WAAAA,WAAAC,aAAA;MACA,KAAAA,aAAA;;MAEA;MACA,IAAAC,IAAA,OAAAC,IAAA,CAAAF,aAAA;;MAEA;MACA,IAAAG,IAAA,GAAAF,IAAA,CAAAG,WAAA;MACA,IAAAC,KAAA,GAAAC,MAAA,CAAAL,IAAA,CAAAM,QAAA,QAAAC,QAAA;MACA,IAAAC,GAAA,GAAAH,MAAA,CAAAL,IAAA,CAAAS,OAAA,IAAAF,QAAA;MACA,IAAAG,KAAA,GAAAL,MAAA,CAAAL,IAAA,CAAAW,QAAA,IAAAJ,QAAA;MACA,IAAAK,OAAA,GAAAP,MAAA,CAAAL,IAAA,CAAAa,UAAA,IAAAN,QAAA;MACA,IAAAO,OAAA,GAAAT,MAAA,CAAAL,IAAA,CAAAe,UAAA,IAAAR,QAAA;;MAEA;MACA,UAAAS,MAAA,CAAAd,IAAA,OAAAc,MAAA,CAAAZ,KAAA,OAAAY,MAAA,CAAAR,GAAA,OAAAQ,MAAA,CAAAN,KAAA,OAAAM,MAAA,CAAAJ,OAAA,OAAAI,MAAA,CAAAF,OAAA;IAEA;IACA;IACAG,gBAAA,WAAAA,iBAAAC,IAAA;MACA,WAAAA,IAAA;QACA,OACAA,IAAA,CAAAC,SAAA,SACA,MACAD,IAAA,CAAAC,SAAA,SACA,MACAD,IAAA,CAAAC,SAAA,SACA,MACAD,IAAA,CAAAC,SAAA,UACA,MACAD,IAAA,CAAAC,SAAA,WACA,MACAD,IAAA,CAAAC,SAAA;MAEA;QACA,OAAAD,IAAA;MACA;IACA;IACAE,iBAAA,WAAAA,kBAAAC,IAAA;MAAA,IAAAC,GAAA,GAAAD,IAAA,CAAAC,GAAA;QAAAC,QAAA,GAAAF,IAAA,CAAAE,QAAA;MACA,IAAAD,GAAA,CAAAE,MAAA;QACA;MACA;MACA;IACA;IAEA,cACA7B,OAAA,WAAAA,QAAA;MAAA,IAAA8B,KAAA;MACA,KAAA1G,OAAA;MACA,IAAA2G,oBAAA,OAAA3E,WAAA,EAAA4E,IAAA,WAAAC,QAAA;QACAH,KAAA,CAAApG,WAAA,GAAAuG,QAAA,CAAAC,IAAA;QACAJ,KAAA,CAAAvG,KAAA,GAAA0G,QAAA,CAAA1G,KAAA;QACAuG,KAAA,CAAA1G,OAAA;MACA;IACA;IACA,cACA+G,eAAA,WAAAA,gBAAA;MAAA,IAAAC,MAAA;MACA,KAAAhH,OAAA;MACA,IAAAiH,8BAAA,OAAApD,cAAA,EAAA+C,IAAA,WAAAC,QAAA;QACAG,MAAA,CAAAzG,OAAA,GAAAsG,QAAA,CAAAC,IAAA;QACAE,MAAA,CAAA3G,QAAA,GAAAwG,QAAA,CAAA1G,KAAA;QACA6G,MAAA,CAAAhH,OAAA;MACA;IACA;IACA,YACA6E,WAAA,WAAAA,YAAA;MAAA,IAAAqC,MAAA;MACA,KAAAlH,OAAA;MACA,KAAA+D,cAAA,CAAAI,OAAA;MACA,IAAAgD,sBAAA,OAAApD,cAAA,EAAA6C,IAAA,WAAAC,QAAA;QACAK,MAAA,CAAA1G,QAAA,GAAAqG,QAAA,CAAAC,IAAA;QACAI,MAAA,CAAA9G,SAAA,GAAAyG,QAAA,CAAA1G,KAAA;QACA+G,MAAA,CAAAlH,OAAA;MACA,CACA;IACA;IACA;IACAoH,MAAA,WAAAA,OAAA;MACA,KAAArG,IAAA;MACA,KAAAsG,KAAA;IACA;IACAC,kBAAA,WAAAA,mBAAA;MACA,KAAAlD,IAAA,CAAA/B,aAAA;MACA,KAAA+B,IAAA,CAAA3B,UAAA;IACA;IACA8E,wBAAA,WAAAA,yBAAA;MACA,KAAAvF,WAAA,CAAAK,aAAA;IACA;IACA;IACAgF,KAAA,WAAAA,MAAA;MACA,KAAAjD,IAAA;QACAoD,EAAA;QAAAC,UAAA;QAAAtF,aAAA;QAAAC,SAAA;QAAAC,aAAA;QAAAC,WAAA;QAAAC,aAAA;QAAAC,eAAA;QAAAC,UAAA;QAAAC,QAAA;QACAC,aAAA;QAAAC,aAAA;QAAAC,mBAAA;QAAAC,QAAA;QAAAC,UAAA;QAAAC,OAAA;QACAC,cAAA;QAAAC,UAAA;QAAAC,UAAA;QAAAC,mBAAA;QAAAC,UAAA;QAAAC,YAAA;QAAAC,cAAA;QACAC,YAAA;QAAAC,cAAA;QAAAC,SAAA;QAAAC,UAAA;QAAAC,OAAA;MACA;MACA,KAAA8D,SAAA;MACA,KAAA1G,QAAA;MACA,KAAAC,YAAA;IACA;IACA0G,uBAAA,WAAAA,wBAAAC,KAAA;MACA,IAAAA,KAAA;QAAA;QACA,KAAA1G,iBAAA;QACA,KAAAC,iBAAA;QACA,KAAAC,yBAAA;MACA,WAAAwG,KAAA;QAAA;QACA,KAAA1G,iBAAA;QACA,KAAAC,iBAAA;QACA,KAAAC,yBAAA;MACA;QAAA;QACA,KAAAF,iBAAA;QACA,KAAAC,iBAAA;QACA,KAAAC,yBAAA;MACA;IACA;IACA;IACAyG,wBAAA,WAAAA,yBAAAf,IAAA;MACA,KAAAnG,gBAAA,GAAAmG,IAAA;IACA;IACA;IACAgB,iBAAA,WAAAA,kBAAA;MACAC,OAAA,CAAAC,GAAA,MAAArH,gBAAA;MACA,SAAAA,gBAAA;QACA,SAAAI,IAAA;UACA;UACA;UACA,SAAAJ,gBAAA,CAAAsH,OAAA;YACA,KAAAC,QAAA,CAAAC,OAAA;YACA;UACA;UACA,KAAA/D,IAAA,CAAA/B,aAAA,QAAA1B,gBAAA,CAAAyH,MAAA;UACA,KAAAhE,IAAA,CAAA3B,UAAA,QAAA9B,gBAAA,CAAA0H,SAAA;UACA,KAAAjE,IAAA,CAAAkE,kBAAA;QACA;UACA;UACA,KAAAtG,WAAA,CAAAK,aAAA,QAAA1B,gBAAA,CAAAyH,MAAA;QACA;QAGA,KAAAG,aAAA;QACA,KAAA5H,gBAAA;QACA,KAAAU,iBAAA;MACA;QACA;QACA,KAAA6G,QAAA,CAAAC,OAAA;MACA;IACA;IACA;IAGA;IACAK,aAAA,WAAAA,cAAA;MACA,SAAA7H,gBAAA;QACA;QACA,KAAAqB,WAAA,CAAAK,aAAA,QAAA1B,gBAAA,CAAA0B,aAAA;QACA,KAAAkG,aAAA;QACA,KAAA5H,gBAAA;QACA,KAAAW,UAAA;QACA,KAAAD,iBAAA;MACA;QACA;QACA,KAAA6G,QAAA,CAAAC,OAAA;MACA;IACA;IAEAM,eAAA,WAAAA,gBAAA;MACA,KAAAC,KAAA,CAAAC,aAAA,CAAAC,kBAAA;IACA;IAEA;IACAC,qBAAA,WAAAA,sBAAAtC,GAAA;MACA,SAAA7F,WAAA,KAAA6F,GAAA;QACA,KAAA7F,WAAA;MACA;QACA,KAAAA,WAAA,GAAA6F,GAAA;MACA;IACA;IAEA;IACAuC,uBAAA,WAAAA,wBAAAvC,GAAA;MACA,KAAA5F,gBAAA,GAAA4F,GAAA;IACA;IAEAwC,sBAAA,WAAAA,uBAAAxC,GAAA;MACA;MACA,KAAA3F,cAAA,GAAA2F,GAAA,CAAAvC,QAAA;IACA;IAIA;IACAgF,oBAAA,WAAAA,qBAAA;MACA,KAAAtI,WAAA;MACA,KAAAT,GAAA;MACA,KAAAW,cAAA;IACA;IAEA;IACAqI,wBAAA,WAAAA,yBAAAC,IAAA;MACA,IAAAC,QAAA,GAAAC,MAAA,CAAAC,QAAA,CAAAC,IAAA;MACA,IAAAJ,IAAA,CAAAvH,GAAA;QACA,IAAAwH,QAAA;UACAD,IAAA,CAAAvH,GAAA,GAAAuH,IAAA,CAAAvH,GAAA,CAAA4H,OAAA;QACA;QACAH,MAAA,CAAArI,IAAA,CAAAmI,IAAA,CAAAvH,GAAA;MACA;MACA,IAAAuH,IAAA,CAAArC,QAAA,IAAAqC,IAAA,CAAArC,QAAA,CAAAlF,GAAA;QACA,IAAAwH,QAAA;UACA,IAAAK,MAAA,GAAAN,IAAA,CAAArC,QAAA,CAAAlF,GAAA,CAAA4H,OAAA;UACAH,MAAA,CAAArI,IAAA,CAAAyI,MAAA;QACA;UACAJ,MAAA,CAAArI,IAAA,CAAAmI,IAAA,CAAArC,QAAA,CAAAlF,GAAA;QACA;MACA;IACA;IAEA8H,eAAA,WAAAA,gBAAAP,IAAA,EAAAlI,QAAA;MACA,KAAAkH,QAAA,CAAAwB,KAAA;IACA;IAEAC,0BAAA,WAAAA,2BAAA9C,QAAA,EAAAqC,IAAA,EAAAlI,QAAA;MACA,IAAA4I,IAAA;MACA,IAAA/C,QAAA,CAAAgD,IAAA;QACAD,IAAA,CAAA5I,QAAA,CAAA8I,IAAA;UAAAnI,GAAA,EAAAkF,QAAA,CAAAlF,GAAA;UAAA7B,IAAA,EAAAoJ,IAAA,CAAApJ;QAAA;QACA8J,IAAA,CAAAG,iBAAA;MACA;QACAH,IAAA,CAAAI,gBAAA,CAAAd,IAAA;QACAU,IAAA,CAAA1B,QAAA,CAAAwB,KAAA,CAAA7C,QAAA,CAAAoD,GAAA;MACA;MACAL,IAAA,CAAAnI,MAAA,CAAAM,WAAA;IACA;IACA;IACAmI,2BAAA,WAAAA,4BAAA;MACA,KAAAzI,MAAA,CAAAM,WAAA;IACA;IAEAoI,mBAAA,WAAAA,oBAAAjB,IAAA,EAAAlI,QAAA;MACA,SAAAoJ,CAAA,MAAAA,CAAA,QAAApJ,QAAA,CAAAqJ,MAAA,EAAAD,CAAA;QACA,SAAApJ,QAAA,CAAAoJ,CAAA,EAAAzI,GAAA,IAAAuH,IAAA,CAAAvH,GAAA;UACA,KAAAX,QAAA,CAAAsJ,MAAA,CAAAF,CAAA;QACA;MACA;MACA,KAAAL,iBAAA;IACA;IACAA,iBAAA,WAAAA,kBAAA;MACA,IAAAnG,OAAA;MACA,KAAA5C,QAAA,CAAAuJ,OAAA,WAAAC,IAAA;QACA5G,OAAA,CAAAkG,IAAA;UAAAnI,GAAA,EAAA6I,IAAA,CAAA7I;QAAA;MACA;MACA,IAAAiC,OAAA,CAAAyG,MAAA,MACA,KAAAjG,IAAA,CAAAR,OAAA,GAAA6G,IAAA,CAAAC,SAAA,CAAA9G,OAAA,EAAA+G,QAAA,QACA,KAAAvG,IAAA,CAAAR,OAAA;IACA;IACAoG,gBAAA,WAAAA,iBAAAd,IAAA;MACA;MACA,KAAAlI,QAAA,QAAAA,QAAA,CAAA4J,MAAA,WAAAJ,IAAA;QAAA,OAAAA,IAAA,KAAAtB,IAAA;MAAA;IACA;IAEA;IACA2B,iBAAA,WAAAA,kBAAA3B,IAAA;MACA,KAAA4B,iBAAA,CAAA5B,IAAA;IACA;IAEA6B,wBAAA,WAAAA,yBAAAC,KAAA,EAAA9B,IAAA,EAAAlI,QAAA;MACA,KAAAS,MAAA,CAAAM,WAAA;IACA;IAEAkJ,gBAAA,WAAAA,iBAAAC,KAAA,EAAAlK,QAAA;MACA,KAAAkH,QAAA,CAAAC,OAAA,iHAAAlC,MAAA,CAAAiF,KAAA,CAAAb,MAAA;IACA;IAEAc,uBAAA,WAAAA,wBAAAtE,QAAA,EAAAqC,IAAA,EAAAlI,QAAA;MACA,KAAAS,MAAA,CAAAM,WAAA;MACA,IAAA8E,QAAA,CAAAgD,IAAA;QACA,KAAA5I,YAAA;UAAAU,GAAA,EAAAkF,QAAA,CAAAlF,GAAA;UAAA7B,IAAA,EAAAoJ,IAAA,CAAApJ;QAAA;QACA,KAAAsE,IAAA,CAAAgH,kBAAA,GAAAX,IAAA,CAAAC,SAAA,MAAAzJ,YAAA;MACA;QACA,KAAAiH,QAAA,CAAAwB,KAAA,CAAA7C,QAAA,CAAAoD,GAAA;QACA,KAAAD,gBAAA,CAAAd,IAAA,OAAAjI,YAAA;MACA;IACA;IAEAoK,gBAAA,WAAAA,iBAAAnC,IAAA,EAAAlI,QAAA;MACA,KAAAC,YAAA;MACA,KAAAmD,IAAA,CAAAgH,kBAAA;IACA;IAEA;IACAN,iBAAA,WAAAA,kBAAA5B,IAAA;MACA,IAAAvH,GAAA,GAAAuH,IAAA,CAAAvH,GAAA,IAAAuH,IAAA,CAAArC,QAAA,IAAAqC,IAAA,CAAArC,QAAA,CAAAlF,GAAA;MACA,KAAAA,GAAA;;MAEA;MACA,IAAAyH,MAAA,CAAAC,QAAA,CAAAC,IAAA;QACA3H,GAAA,GAAAA,GAAA,CAAA4H,OAAA;MACA;MAEAH,MAAA,CAAArI,IAAA,CAAAY,GAAA;IACA;EAAA,OAAA0C,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,EAAAzE,QAAA,+BAAAmK,iBAGAd,IAAA,EAAAoC,UAAA;IACAA,UAAA,GAAAA,UAAA,CAAAV,MAAA,WAAAJ,IAAA;MAAA,OAAAA,IAAA,CAAAe,GAAA,KAAArC,IAAA,CAAAqC,GAAA;IAAA;EACA,iCAGAC,iBAAA;IACA;IACA,SAAA9K,WAAA;MACA,KAAAwH,QAAA,CAAAC,OAAA;MACA;IACA;IACA,KAAA5G,UAAA;EACA,6BAGAkK,aAAAlF,GAAA;IACAwB,OAAA,CAAAC,GAAA,CAAAzB,GAAA;IACA,KAAAmF,OAAA,CAAA5B,IAAA,+BAAAvD,GAAA,CAAAiB,EAAA;EACA,4BAGAmE,YAAA;IACA,KAAA3J,WAAA,CAAAC,OAAA;IACA,KAAA2C,OAAA;EACA,gCAEAgH,gBAAA;IACA,KAAA7H,cAAA,CAAA9B,OAAA;IACA,KAAA4C,WAAA;EACA,sCAGAgH,sBAAA;IACA,KAAAhI,cAAA,CAAA5B,OAAA;IACA,KAAA8E,eAAA;IACA,KAAA1F,iBAAA;EACA,oCAGAyK,oBAAA;IACA,KAAAD,qBAAA;EACA,wCAGAE,wBAAA;IACA,KAAA1K,iBAAA;IACA,KAAAwC,cAAA,CAAA5B,OAAA;IACA,KAAA8E,eAAA;EACA,2BAGAiF,WAAA;IACA,KAAAtE,SAAA;IACA,KAAAiE,WAAA;IACA,KAAA/G,OAAA;EACA,+BAGAqH,eAAA;IACA,KAAAvE,SAAA;IACA,KAAA3D,cAAA,CAAAC,QAAA;IACA,KAAAD,cAAA,CAAAE,QAAA;IACA,KAAA2H,eAAA;IACA,KAAA/G,WAAA;IACA,KAAAmE,oBAAA;EACA,QAAA3E,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,EAAAzE,QAAA,4BAGA0I,cAAA;IACA,KAAA1E,cAAA;MACA5B,OAAA;MACAC,QAAA;MACAG,aAAA;MACAkB,cAAA;MACAO,aAAA;MACArB,UAAA;IACA;IACA;EACA,0BAGAyJ,UAAA;IACA,KAAA7E,KAAA;IACA,KAAAtG,IAAA;IACA,KAAAD,KAAA;EACA,6BAEAqL,aAAA5F,GAAA;IAAA,IAAA6F,MAAA;IACA,KAAA/E,KAAA;IACAU,OAAA,CAAAC,GAAA,CAAAzB,GAAA;IACA,IAAAiB,EAAA,GAAAjB,GAAA,CAAAiB,EAAA,SAAAvH,GAAA;IACA,IAAAoM,mBAAA,EAAA7E,EAAA,EAAAZ,IAAA,WAAAC,QAAA;MACAuF,MAAA,CAAAhI,IAAA,GAAAyC,QAAA,CAAA9G,IAAA;MACAqM,MAAA,CAAArL,IAAA;MACAqL,MAAA,CAAAtL,KAAA;IACA;IACA,IAAAyF,GAAA,CAAA3C,OAAA;MACA,KAAA5C,QAAA,GAAAyJ,IAAA,CAAA6B,KAAA,CAAA/F,GAAA,CAAA3C,OAAA;IACA,OACA;MACA,KAAA5C,QAAA;IACA;EACA,4BAGAuL,YAAA;IACA,KAAAhE,aAAA;IACA,KAAA5H,gBAAA;IACA,KAAAW,UAAA;IACA,KAAAD,iBAAA;IACA,KAAAE,UAAA;IACA,KAAAX,cAAA;EACA,+BAGA4L,eAAA;IAAA,IAAAC,MAAA;IACA,SAAA7L,cAAA;MACA;MACA,IAAA8L,KAAA;QACAC,aAAA,OAAA/L,cAAA;QACAgM,iBAAA,OAAAlM;MACA;MACA,IAAAmM,oBAAA,EAAAH,KAAA,EAAA9F,IAAA;QACA6F,MAAA,CAAAvE,QAAA;UACA4E,IAAA;UACArI,OAAA;QACA;QACA;QACAgI,MAAA,CAAAzD,oBAAA;QACAyD,MAAA,CAAAF,WAAA;QACAE,MAAA,CAAA7H,OAAA;MACA,GAAAmI,KAAA,WAAArD,KAAA;QACA+C,MAAA,CAAAvE,QAAA;UACA4E,IAAA;UACArI,OAAA,EAAAiF,KAAA,CAAA7C,QAAA,CAAA9G,IAAA,CAAA0E,OAAA;QACA;MACA;IACA;MACA,KAAAyD,QAAA,CAAAC,OAAA;IACA;EAEA,2BAGA6E,WAAA;IAAA,IAAAC,MAAA;IACA,KAAAvE,KAAA,SAAAwE,QAAA,WAAAC,KAAA;MACA,IAAAA,KAAA;QACA;QACA,IAAAF,MAAA,CAAA7I,IAAA,CAAAgH,kBAAA,YAAA6B,MAAA,CAAA7I,IAAA,CAAAgH,kBAAA;UACA6B,MAAA,CAAA/E,QAAA,CAAAwB,KAAA;UACA;QACA;QACA,IAAAuD,MAAA,CAAA7I,IAAA,CAAAoD,EAAA;UACA,IAAA4F,sBAAA,EAAAH,MAAA,CAAA7I,IAAA,EAAAwC,IAAA,WAAAC,QAAA;YACAoG,MAAA,CAAAI,MAAA,CAAAC,UAAA;YACAL,MAAA,CAAAlM,IAAA;YACAkM,MAAA,CAAArI,OAAA;UACA;QACA;UACA,IAAA2I,8BAAA,EAAAN,MAAA,CAAA7I,IAAA,EAAAwC,IAAA,WAAAC,QAAA;YACAoG,MAAA,CAAAI,MAAA,CAAAC,UAAA;YACAL,MAAA,CAAAlM,IAAA;YACAkM,MAAA,CAAArI,OAAA;UACA;QACA;MACA;IACA;EACA,6BAEA4I,aAAAjH,GAAA;IAAA,IAAAkH,MAAA;IACA,IAAAjG,EAAA,GAAAjB,GAAA,CAAAiB,EAAA,SAAAvH,GAAA;IACA,KAAAyN,QAAA;MACAC,iBAAA;MACAC,gBAAA;MACAd,IAAA;IACA,GAAAlG,IAAA;MACA,WAAAiH,mBAAA,EAAArG,EAAA;IACA,GAAAZ,IAAA;MACA6G,MAAA,CAAA7I,OAAA;MACA6I,MAAA,CAAAH,UAAA;IACA;EACA,6BAGAQ,aAAA;IAAA,IAAAC,MAAA;IACA,IAAA/L,WAAA,QAAAA,WAAA;IACA,KAAA0L,QAAA;MACAC,iBAAA;MACAC,gBAAA;MACAd,IAAA;IACA,GAAAlG,IAAA;MACA,WAAAoH,sBAAA,EAAAhM,WAAA;IACA,GAAA4E,IAAA,WAAAC,QAAA;MACAkH,MAAA,CAAAE,QAAA,CAAApH,QAAA,CAAAoD,GAAA;IACA;EACA;AAEA", "ignoreList": []}]}