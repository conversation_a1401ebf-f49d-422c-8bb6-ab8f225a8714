{"remainingRequest": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\src\\views\\leave\\plan\\task.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\src\\views\\leave\\plan\\task.vue", "mtime": 1755505501444}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\babel.config.js", "mtime": 1688548084091}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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<PERSON>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"}, {"version": 3, "names": ["_task", "require", "_plan", "_xctgDriverCar", "_elementUi", "_qrcodejs", "_interopRequireDefault", "name", "data", "factoryConfirmDialogVisible", "factoryConfirmForm", "companyName", "taskNo", "applyNo", "planNo", "taskType", "unloadingWorkNo", "unloadingTime", "spec1Length", "spec2Width", "totals", "total", "totalUnit", "processType", "heatNo", "steelGrade", "axles", "remark", "taskStatus", "carNum", "stockOutSpec1Length", "stockOutSpec2Width", "stockOutTotals", "stockOutTotalUnit", "stockOutTotal", "stockOutProcessType", "stockOutHeatNo", "stockOutSteelGrade", "stockOutAxles", "stockOutRemark", "handledMaterialName", "sourceCompany", "receiveCompany", "showDropdown", "extraOption", "deductWeight", "optionDialogVisible", "searchForm", "optionList", "editDoorManStatus", "editFactoryStatus", "driverInfo", "id", "idCard", "phone", "gender", "company", "photo", "driverLicenseImgs", "vehicleLicenseImgs", "carInfo", "taskMaterials", "taskLogs", "isdoorMan", "dispatchId", "taskInfoForm", "measureFlag", "backupTaskMaterials", "selectedOption", "planForm", "processTypeOptions", "filteredProcessTypeOptions", "searchProcessTypeQuery", "directSupplyPlanList", "editingRow", "selectedRows", "directSupplyParams", "computed", "displayProcessTypeOptions", "hasSelectedItems", "length", "materialNames", "map", "item", "materialName", "join", "materialSpecs", "materialSpec", "created", "console", "log", "initPage", "activated", "methods", "resetTaskInfoForm", "_this$$route$query", "$route", "query", "planType", "error", "$message", "validDoorMan", "initializeData", "getDirectSupplyPlanAndTask", "_this", "leaveTask0", "directSupplyTaskNo", "getDirectSupplyPlanAndTaskDetail", "then", "res", "code", "rows", "message", "catch", "err", "_this2", "$store", "getters", "roles", "for<PERSON>ach", "_this3", "_asyncToGenerator2", "default", "_regenerator2", "m", "_callee", "_t", "w", "_context", "n", "p", "getTaskInfo", "getTaskmaterialList", "getPlanInfo", "uploadFactoryConfirmForm", "getTaskLogList", "getProcessType", "v", "a", "_this4", "doorman<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "planNum", "doormanReceiveNumIn", "gross", "secGross", "<PERSON><PERSON><PERSON>", "tare", "Date", "openNewWindow", "newWindowUrl", "window", "open", "getDirectSupplyList", "_this5", "_callee2", "leavePlan", "_iterator", "_step", "leavePlanMaterial", "response", "_t2", "_t3", "_context2", "getDirectSupplyPlans", "_createForOfIteratorHelper2", "s", "done", "value", "getPlanMaterials", "e", "f", "filterProcessType", "filter", "includes", "_this6", "getProcessList", "processname", "label", "_this7", "_callee3", "_t4", "_context3", "detailPlan", "openFactoryConfirmDialog", "submitFactoryConfirm", "_this8", "submitData", "isDirectSupply", "leaveTask", "leaveTaskMaterial", "directSupplyTask", "secGrossTime", "sex", "mobilePhone", "idCardNo", "vehicleEmissionStandards", "faceImg", "drivingLicenseImg", "driverLicenseImg", "directSupplyTaskMaterialList", "handleUnload", "success", "submitStockOutConfirm", "_this9", "handleStockOut", "handleFactoryConfirm", "_this0", "warning", "leaveTaskLog", "logType", "info", "factoryTaskInfo", "param", "taskMaterialList", "leaveLog", "addLeaveLogAndEditTaskMaterialsAndUpdateTask", "handleDoorManConfirm", "_this1", "doorManTaskInfo", "leaveTime", "toISOString", "slice", "replace", "enterTime", "handleDoorManMeasureConfirm", "_this10", "creatQrCode", "qrCode<PERSON>ontent", "$refs", "qrCode", "innerHTML", "YSqrCode", "QRCode", "text", "width", "height", "colorDark", "colorLight", "correctLevel", "CorrectLevel", "H", "_this11", "taskLog", "getTaskLogs", "logs", "finishedLogs", "otherLogs", "concat", "_toConsumableArray2", "_this12", "_callee4", "leaveMaterial", "_t5", "_context4", "getTaskmaterials", "editDoorManRow", "row", "_backup", "JSON", "parse", "stringify", "editFactoryRow", "backupMaterials", "cancelDoorManEdit", "Object", "assign", "cancelFactoryEdit", "saveDoorManRowIn", "_this13", "_iterator2", "_step2", "saveDoorManRow", "_this14", "_iterator3", "_step3", "saveFactoryRow", "_this15", "_callee5", "_t6", "_context5", "getTask", "licensePlateColor", "$nextTick", "getStatusText", "standard", "standardMap", "getPlanStatusText", "getEmissionStandardsText", "getEmissionStandardsTagType", "typeMap", "getMaterialStatusText", "status", "statusMap", "getMaterialStatusType", "getLogColor", "logTypeColorMap", "type", "cancel", "$router", "go", "getTaskDetail", "handleShowDropdownChange", "val", "openOptionDialog", "_this16", "loadOptions", "optionTable", "clearSelection", "handleOptionSelection", "selection", "lastSelected", "toggleRowSelection", "confirmOptionSelection", "_this17", "planStatus", "getBusinessCategoryText", "category", "categoryMap", "searchOptions", "_this18", "searchPlanNo", "toLowerCase", "searchApplyNo", "searchReceiveCompany", "toString", "matchPlanNo", "matchApplyNo", "matchReceiveCompany", "resetSearch", "getTaskTypeText", "handleSelectionChange", "handleNonMeasureFactoryConfirm", "_this19", "isHandled", "factoryReceiveNum", "openNewTaskWindow", "BigInt", "url"], "sources": ["src/views/leave/plan/task.vue"], "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <el-card class=\"box-card\">\r\n      <div slot=\"header\" class=\"card-header\" style=\"display: flex; align-items: center; justify-content: flex-start;\">\r\n        <h2>派车任务详情</h2>\r\n        <el-tag size=\"medium\" style=\"margin-left: 20px; margin-top: 10px;\">\r\n          任务状态： {{ getStatusText(taskInfoForm.taskStatus) }}\r\n        </el-tag>\r\n      </div>\r\n\r\n      <!-- 任务流程图部分 -->\r\n      <div class=\"section-container\">\r\n        <div class=\"section-title\">任务流程</div>\r\n        <div class=\"process-flow-container\">\r\n          <!-- <img style=\"width: 100%; max-height: 400px; object-fit: contain;\" :src=\"require('@/assets/images/task-flow-chart.png')\" /> -->\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 通行证二维码部分 -->\r\n      <div class=\"section-container\">\r\n        <div class=\"section-title\">通行证二维码</div>\r\n        <div class=\"qrcode-container\">\r\n          <div ref=\"qrCode\" class=\"qrcode\"></div>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 司机信息部分 -->\r\n      <div class=\"section-container\">\r\n        <div class=\"section-title\">司机信息</div>\r\n        <el-descriptions :column=\"2\" border>\r\n          <el-descriptions-item label=\"姓名\">\r\n            <template slot=\"label\"><i class=\"el-icon-user\"></i> 姓名</template>\r\n            {{ taskInfoForm.driverName }}\r\n          </el-descriptions-item>\r\n          <el-descriptions-item label=\"手机号\">\r\n            <template slot=\"label\"><i class=\"el-icon-mobile-phone\"></i> 手机号</template>\r\n            {{ taskInfoForm.mobilePhone }}\r\n          </el-descriptions-item>\r\n\r\n          <el-descriptions-item label=\"身份证号\">\r\n            <template slot=\"label\"><i class=\"el-icon-document\"></i> 身份证号</template>\r\n            {{ taskInfoForm.idCardNo }}\r\n          </el-descriptions-item>\r\n          <el-descriptions-item label=\"性别\">\r\n            <template slot=\"label\"><i class=\"el-icon-user\"></i> 性别</template>\r\n            {{ taskInfoForm.sex === 1 ? '男' : '女' }}\r\n          </el-descriptions-item>\r\n\r\n          <el-descriptions-item label=\"所属单位\">\r\n            <template slot=\"label\"><i class=\"el-icon-office-building\"></i> 所属单位</template>\r\n            {{ taskInfoForm.companyName }}\r\n          </el-descriptions-item>\r\n        </el-descriptions>\r\n\r\n        <!-- 司机照片和证件照片 -->\r\n        <div class=\"driver-photos\"\r\n          v-if=\"driverInfo.photo || driverInfo.driverLicenseImgs || driverInfo.vehicleLicenseImgs\">\r\n          <div class=\"photo-item\" v-if=\"driverInfo.photo\">\r\n            <h4><i class=\"el-icon-picture-outline\"></i> 司机照片</h4>\r\n            <div class=\"photo-container\">\r\n              <!-- <img :src=\"taskInfoForm.faceImg\" alt=\"司机照片\"> -->\r\n\r\n              <el-image style=\"width: 200px; height: 200px\" :src=\"taskInfoForm.faceImg\" fit=\"contain\" fallback=\"\"\r\n                :preview-src-list=\"[taskInfoForm.faceImg]\">\r\n                <template #error>\r\n                  <div style=\"width: 100%; height: 100%;\"></div> <!-- 空白区域 -->\r\n                </template>\r\n              </el-image>\r\n            </div>\r\n          </div>\r\n          <div class=\"photo-item\" v-if=\"driverInfo.driverLicenseImgs\">\r\n            <h4><i class=\"el-icon-picture-outline\"></i> 驾驶证照片</h4>\r\n            <div class=\"photo-container\">\r\n              <!-- <img :src=\"taskInfoForm.driverLicenseImg\" alt=\"驾驶证照片\"> -->\r\n\r\n              <el-image style=\"width: 200px; height: 200px\" :src=\"taskInfoForm.driverLicenseImg\" fit=\"contain\"\r\n                fallback=\"\" :preview-src-list=\"[taskInfoForm.driverLicenseImg]\">\r\n                <template #error>\r\n                  <div style=\"width: 100%; height: 100%;\"></div> <!-- 空白区域 -->\r\n                </template>\r\n              </el-image>\r\n            </div>\r\n          </div>\r\n          <div class=\"photo-item\" v-if=\"driverInfo.vehicleLicenseImgs\">\r\n            <h4><i class=\"el-icon-picture-outline\"></i> 行驶证照片</h4>\r\n            <div class=\"photo-container\">\r\n              <!-- <img :src=\"taskInfoForm.drivingLicenseImg\" alt=\"行驶证照片\"> -->\r\n\r\n              <el-image style=\"width: 200px; height: 200px\" :src=\"taskInfoForm.drivingLicenseImg\" fit=\"contain\"\r\n                fallback=\"\" :preview-src-list=\"[taskInfoForm.drivingLicenseImg]\">\r\n                <template #error>\r\n                  <div style=\"width: 100%; height: 100%;\"></div> <!-- 空白区域 -->\r\n                </template>\r\n              </el-image>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 车辆信息部分 -->\r\n      <div class=\"section-container\">\r\n        <div class=\"section-title\">车辆信息</div>\r\n        <el-descriptions :column=\"2\" border>\r\n          <el-descriptions-item label=\"车牌号\" v-if=\"taskInfoForm.carNum != null\">\r\n            <template slot=\"label\"><i class=\"el-icon-truck\"></i> 车牌号</template>\r\n            <el-tag type=\"primary\">{{ taskInfoForm.carNum }}</el-tag>\r\n          </el-descriptions-item>\r\n          <el-descriptions-item label=\"车牌颜色\" v-if=\"taskInfoForm.licensePlateColor != null\">\r\n            <template slot=\"label\"><i class=\"el-icon-takeaway-box\"></i> 车牌颜色</template>\r\n            {{ taskInfoForm.licensePlateColor }}\r\n          </el-descriptions-item>\r\n\r\n          <el-descriptions-item label=\"车辆道路运输证号\" v-if=\"taskInfoForm.trailerId != null\">\r\n            <template slot=\"label\"><i class=\"el-icon-document\"></i> 运输证号</template>\r\n            {{ taskInfoForm.trailerId }}\r\n          </el-descriptions-item>\r\n          <el-descriptions-item label=\"挂车号牌\" v-if=\"taskInfoForm.trailerNumber\">\r\n            <template slot=\"label\"><i class=\"el-icon-truck\"></i> 挂车号牌</template>\r\n            <el-tag type=\"info\">{{ taskInfoForm.trailerNumber }}</el-tag>\r\n          </el-descriptions-item>\r\n\r\n          <el-descriptions-item label=\"挂车道路运输证号\" v-if=\"taskInfoForm.trailerId\">\r\n            <template slot=\"label\"><i class=\"el-icon-document\"></i> 挂车运输证号</template>\r\n            {{ taskInfoForm.trailerId }}\r\n          </el-descriptions-item>\r\n          <el-descriptions-item label=\"轴型\" v-if=\"taskInfoForm.axisType != null\">\r\n            <template slot=\"label\"><i class=\"el-icon-data-line\"></i> 轴型</template>\r\n            {{ taskInfoForm.axisType }}\r\n          </el-descriptions-item>\r\n\r\n          <el-descriptions-item label=\"货车自重\" v-if=\"taskInfoForm.driverWeight != null\">\r\n            <template slot=\"label\"><i class=\"el-icon-heavy-rain\"></i> 货车自重</template>\r\n            {{ taskInfoForm.driverWeight }} kg\r\n          </el-descriptions-item>\r\n          <el-descriptions-item label=\"车货总质量限值\" v-if=\"taskInfoForm.maxWeight != null\">\r\n            <template slot=\"label\"><i class=\"el-icon-opportunity\"></i> 总质量限值</template>\r\n            {{ taskInfoForm.maxWeight }} kg\r\n          </el-descriptions-item>\r\n\r\n          <el-descriptions-item label=\"车辆排放标准\" v-if=\"taskInfoForm.vehicleEmissionStandards != null\">\r\n            <template slot=\"label\"><i class=\"el-icon-magic-stick\"></i> 排放标准</template>\r\n            <el-tag :type=\"getEmissionStandardsTagType(taskInfoForm.vehicleEmissionStandards)\">\r\n              {{ getEmissionStandardsText(taskInfoForm.vehicleEmissionStandards) }}\r\n            </el-tag>\r\n          </el-descriptions-item>\r\n          <el-descriptions-item label=\"发动机号\" v-if=\"taskInfoForm.engineNumber != null\">\r\n            <template slot=\"label\"><i class=\"el-icon-set-up\"></i> 发动机号</template>\r\n            {{ taskInfoForm.engineNumber }}\r\n          </el-descriptions-item>\r\n\r\n          <el-descriptions-item label=\"车辆识别代码\" v-if=\"taskInfoForm.vinNumber != null\">\r\n            <template slot=\"label\"><i class=\"el-icon-document-checked\"></i> 车辆识别代码</template>\r\n            {{ taskInfoForm.vinNumber }}\r\n          </el-descriptions-item>\r\n        </el-descriptions>\r\n      </div>\r\n\r\n      <!-- 任务物资列表部分 -->\r\n      <div class=\"section-container\">\r\n        <div class=\"section-title\">物资列表</div>\r\n        <el-table :data=\"taskMaterials\" style=\"width: 100%\" border @selection-change=\"handleSelectionChange\">\r\n          <!-- <el-table-column type=\"selection\" width=\"55\" v-if=\"measureFlag == 0\">\r\n          </el-table-column> -->\r\n          <el-table-column type=\"index\" width=\"50\" label=\"序号\">\r\n          </el-table-column>\r\n          <el-table-column prop=\"materialName\" label=\"物资名称\" width=\"150\">\r\n          </el-table-column>\r\n          <el-table-column prop=\"materialSpec\" label=\"物资型号规格\" width=\"150\">\r\n          </el-table-column>\r\n          <!-- v-if=\"measureFlag == 0\" -->\r\n          <el-table-column prop=\"planNum\" label=\"计划数量\" width=\"120\">\r\n          </el-table-column>\r\n          <el-table-column prop=\"measureUnit\" label=\"单位\" width=\"120\">\r\n          </el-table-column>\r\n          <el-table-column prop=\"doormanReceiveNum\" label=\"门卫出厂确认数量\" width=\"230\"\r\n            v-if=\"taskInfoForm.taskStatus >= 4 && measureFlag == 0 && taskInfoForm.taskType != 2\">\r\n            <template slot-scope=\"scope\">\r\n              <el-input-number v-model=\"scope.row.doormanReceiveNum\" :min=\"0\" controls-position=\"right\"\r\n                :disabled=\"!isdoorMan && taskInfoForm.taskStatus !== 4\" />\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column prop=\"doormanReceiveNumIn\" label=\"门卫入厂确认数量\" width=\"230\"\r\n            v-if=\"measureFlag == 0 && taskInfoForm.taskType !== 1 && taskInfoForm.taskStatus >= 5\">\r\n            <template slot-scope=\"scope\">\r\n              <el-input-number v-model=\"scope.row.doormanReceiveNumIn\" :min=\"0\" controls-position=\"right\"\r\n                :disabled=\"!isdoorMan && taskInfoForm.taskStatus !== 5\" />\r\n            </template>\r\n          </el-table-column>\r\n          <!-- v-if=\"measureFlag == 0 && taskInfoForm.taskType == 2\" -->\r\n          <el-table-column prop=\"doormanReceiveNum\" label=\"分厂确认数量\" width=\"230\"\r\n            v-if=\"measureFlag == 0 && taskInfoForm.taskStatus > 7 && (taskInfoForm.taskType == 3 || taskInfoForm.taskType == 2)\">\r\n            <!-- <template slot-scope=\"scope\">\r\n              <el-input v-model=\"scope.row.doormanReceiveNum\" :min=\"0\" controls-position=\"right\" disabled />\r\n            </template> -->\r\n          </el-table-column>\r\n          <el-table-column prop=\"remark\" label=\"备注\">\r\n          </el-table-column>\r\n\r\n          <!-- v-if=\"taskInfoForm.taskStatus == 4 || taskInfoForm.taskStatus == 5 && (measureFlag == 0 && taskInfoForm.taskType == 2 && taskInfoForm.taskStatus == 7)\" -->\r\n          <!-- <el-table-column v-if=\"measureFlag == 0 && (taskInfoForm.taskStatus == 4 || taskInfoForm.taskStatus == 5)\"\r\n            label=\"操作\" width=\"200\" fixed=\"right\">\r\n            <template slot-scope=\"scope\">\r\n              <div style=\"display: flex; flex-wrap: wrap; gap: 4px;\">\r\n\r\n                <div v-if=\"editingRow === scope.row\">\r\n                  <el-button size=\"mini\" type=\"success\" @click=\"saveDoorManRow(scope.row)\">保存</el-button>\r\n                  <el-button size=\"mini\" @click=\"cancelDoorManEdit(scope.row)\">取消</el-button>\r\n                </div>\r\n\r\n                <div v-else>\r\n                  <el-button v-hasPermi=\"['leave:task:doorManConfirm']\" size=\"mini\" type=\"primary\"\r\n                    @click=\"editDoorManRow(scope.row)\">门卫编辑</el-button>\r\n                </div>\r\n              </div>\r\n            </template>\r\n          </el-table-column> -->\r\n        </el-table>\r\n\r\n        <div class=\"btn-wrapper\" v-if=\"measureFlag == 0 && taskInfoForm.taskStatus == 4\">\r\n          <el-button type=\"primary\" size=\"medium\" @click=\"saveDoorManRow\" class=\"dispatch-btn\">\r\n            <!-- :disabled=\"!hasSelectedItems\" -->\r\n            门卫出厂确认\r\n          </el-button>\r\n        </div>\r\n        <div class=\"btn-wrapper\" v-if=\"measureFlag == 0 && taskInfoForm.taskStatus == 5\">\r\n          <el-button type=\"primary\" size=\"medium\" @click=\"saveDoorManRowIn\" class=\"dispatch-btn\">\r\n            <!-- :disabled=\"!hasSelectedItems\" -->\r\n            门卫入厂确认\r\n          </el-button>\r\n        </div>\r\n        <div class=\"button-container\" v-if=\"measureFlag == 0 && taskInfoForm.taskStatus == 7\">\r\n          <el-button type=\"primary\" @click=\"handleNonMeasureFactoryConfirm\">\r\n            分厂确认\r\n          </el-button>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"section-container\" v-if=\"measureFlag == 1\">\r\n        <div class=\"section-title\">计量信息</div>\r\n        <div class=\"info-footer\" style=\"margin-top: 20px;\" v-if=\"measureFlag == 1\">\r\n          <el-descriptions :column=\"3\" border>\r\n            <el-descriptions-item label=\"皮重\" :label-style=\"{ width: '200px' }\" v-if=\"taskInfoForm.tare != null\">\r\n              {{ taskInfoForm.tare + ' 吨' }}\r\n            </el-descriptions-item>\r\n            <el-descriptions-item label=\"毛重\" :label-style=\"{ width: '200px' }\" v-if=\"taskInfoForm.gross != null\">\r\n              {{ taskInfoForm.gross + ' 吨' }}\r\n            </el-descriptions-item>\r\n            <el-descriptions-item label=\"净重\" :label-style=\"{ width: '200px' }\" v-if=\"taskInfoForm.netWeight != null\">\r\n              {{ taskInfoForm.netWeight + ' 吨' }}\r\n            </el-descriptions-item>\r\n            <el-descriptions-item label=\"皮重时间\" :label-style=\"{ width: '200px' }\" v-if=\"taskInfoForm.tareTime != null\">\r\n              {{ taskInfoForm.tareTime }}\r\n            </el-descriptions-item>\r\n            <el-descriptions-item label=\"毛重时间\" :label-style=\"{ width: '200px' }\" v-if=\"taskInfoForm.grossTime != null\">\r\n              {{ taskInfoForm.grossTime }}\r\n            </el-descriptions-item>\r\n            <el-descriptions-item label=\"净重时间\" :label-style=\"{ width: '200px' }\" v-if=\"taskInfoForm.netWeight != null\">\r\n              {{ taskInfoForm.grossTime }}\r\n            </el-descriptions-item>\r\n            <el-descriptions-item label=\"皮重（复磅）\" :label-style=\"{ width: '200px' }\" v-if=\"taskInfoForm.secTare != null\">\r\n              {{ taskInfoForm.secTare + ' 吨' }}\r\n            </el-descriptions-item>\r\n            <el-descriptions-item label=\"毛重（复磅）\" :label-style=\"{ width: '200px' }\" v-if=\"taskInfoForm.secGross != null\">\r\n              {{ taskInfoForm.secGross + ' 吨' }}\r\n            </el-descriptions-item>\r\n            <el-descriptions-item label=\"净重（复磅）\" :label-style=\"{ width: '200px' }\"\r\n              v-if=\"taskInfoForm.secNetWeight != null\">\r\n              {{ taskInfoForm.secNetWeight + ' 吨' }}\r\n            </el-descriptions-item>\r\n            <el-descriptions-item label=\"皮重时间（复磅）\" :label-style=\"{ width: '200px' }\"\r\n              v-if=\"taskInfoForm.secTareTime != null\">\r\n              {{ taskInfoForm.secTareTime }}\r\n            </el-descriptions-item>\r\n            <el-descriptions-item label=\"毛重时间（复磅）\" :label-style=\"{ width: '200px' }\"\r\n              v-if=\"taskInfoForm.secGrossTime != null\">\r\n              {{ taskInfoForm.secGrossTime }}\r\n            </el-descriptions-item>\r\n            <el-descriptions-item label=\"净重时间（复磅）\" :label-style=\"{ width: '200px' }\"\r\n              v-if=\"taskInfoForm.secNetWeightTime != null\">\r\n              {{ taskInfoForm.secNetWeightTime }}\r\n            </el-descriptions-item>\r\n          </el-descriptions>\r\n          <!-- v-if=\"taskInfoForm.taskStatus == 4 || taskInfoForm.taskStatus == 5\" -->\r\n          <div class=\"btn-wrapper\" v-if=\"measureFlag == 1 && taskInfoForm.taskStatus == 4\">\r\n            <el-button type=\"primary\" size=\"medium\" @click=\"handleDoorManMeasureConfirm\" class=\"dispatch-btn\">\r\n              门卫出厂确认\r\n            </el-button>\r\n          </div>\r\n          <div class=\"btn-wrapper\" v-if=\"measureFlag == 1 && taskInfoForm.taskStatus == 5\">\r\n            <el-button type=\"primary\" size=\"medium\" @click=\"handleDoorManMeasureConfirm\" class=\"dispatch-btn\">\r\n              门卫入厂确认\r\n            </el-button>\r\n          </div>\r\n          <!-- 新增分厂确认按钮 -->\r\n          <!-- <div class=\"btn-wrapper\">\r\n            <el-button type=\"primary\" size=\"medium\" @click=\"openFactoryConfirmDialog\" class=\"dispatch-btn\">\r\n              分厂确认\r\n            </el-button>\r\n          </div> -->\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 可编辑的出库信息表单 -->\r\n      <div class=\"section-container\" v-if=\"measureFlag == 1 && taskInfoForm.taskStatus == 2\">\r\n        <div class=\"section-title\">出库信息</div>\r\n\r\n        <el-form :model=\"factoryConfirmForm\" label-width=\"120px\">\r\n          <el-row :gutter=\"20\">\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"计划号\">\r\n                <el-input v-model=\"factoryConfirmForm.planNo\" disabled></el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"车牌号\">\r\n                <el-input v-model=\"factoryConfirmForm.carNum\" placeholder=\"请输入车牌号\" disabled></el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n\r\n          <el-row :gutter=\"20\">\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"物资名称\">\r\n                <el-input :value=\"taskMaterials.map(item => item.materialName).join(' ')\" disabled></el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"皮重(t)\">\r\n                <el-input :value=\"taskInfoForm.tare\" disabled></el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n\r\n          <el-row :gutter=\"20\" v-if=\"planForm.planType == 3\">\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"物料规格\">\r\n                <el-input :value=\"taskMaterials.map(item => item.materialSpec).join(' ')\" disabled></el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n\r\n          <el-row :gutter=\"20\">\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"供货单位\">\r\n                <el-input v-model=\"factoryConfirmForm.sourceCompany\" placeholder=\"请输入车牌号\" disabled></el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"收货单位\">\r\n                <el-input v-model=\"factoryConfirmForm.receiveCompany\" disabled></el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n\r\n          <el-row :gutter=\"20\">\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"规格\" v-if=\"taskInfoForm.taskType\">\r\n                <el-input v-model=\"factoryConfirmForm.stockOutSpec1Length\" placeholder=\"请输入规格\"></el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n\r\n          <el-row :gutter=\"20\">\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"总数\">\r\n                <el-input v-model=\"factoryConfirmForm.stockOutTotal\" placeholder=\"请输入总数\"></el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"总数单位\">\r\n                <el-select v-model=\"factoryConfirmForm.stockOutTotalUnit\" placeholder=\"请选择总数单位\">\r\n                  <el-option label=\"件\" value=\"件\"></el-option>\r\n                  <el-option label=\"支\" value=\"支\"></el-option>\r\n                  <el-option label=\"张\" value=\"张\"></el-option>\r\n                </el-select>\r\n              </el-form-item>\r\n            </el-col>\r\n\r\n          </el-row>\r\n\r\n          <el-row :gutter=\"20\">\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"加工类型\">\r\n                <el-select v-model=\"factoryConfirmForm.stockOutProcessType\" placeholder=\"请选择加工类型\" filterable\r\n                  :filter-method=\"filterProcessType\">\r\n                  <el-option v-for=\"item in filteredProcessTypeOptions\" :key=\"item.value\" :label=\"item.label\"\r\n                    :value=\"item.value\"></el-option>\r\n                </el-select>\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n\r\n          <el-row :gutter=\"20\">\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"炉号/批号\">\r\n                <el-input v-model=\"factoryConfirmForm.stockOutHeatNo\" placeholder=\"请输入炉号\"></el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"钢种\">\r\n                <el-input v-model=\"factoryConfirmForm.stockOutSteelGrade\" placeholder=\"请输入钢种\"></el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n\r\n          <el-row :gutter=\"20\">\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"轴数\">\r\n                <el-select v-model=\"factoryConfirmForm.stockOutAxles\" placeholder=\"请选择轴数\">\r\n                  <el-option label=\"2\" value=\"2\"></el-option>\r\n                  <el-option label=\"3\" value=\"3\"></el-option>\r\n                  <el-option label=\"4\" value=\"4\"></el-option>\r\n                  <el-option label=\"5\" value=\"5\"></el-option>\r\n                  <el-option label=\"6轴标准\" value=\"6轴标准\"></el-option>\r\n                  <el-option label=\"6轴非标准\" value=\"6轴非标准\"></el-option>\r\n                </el-select>\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n\r\n          <el-form-item label=\"出库备注\">\r\n            <el-input type=\"textarea\" v-model=\"factoryConfirmForm.stockOutRemark\" placeholder=\"请输入出库备注\"></el-input>\r\n          </el-form-item>\r\n        </el-form>\r\n\r\n        <div class=\"btn-wrapper\">\r\n          <el-button type=\"primary\" @click=\"submitStockOutConfirm\" size=\"medium\" class=\"dispatch-btn\">确认出库</el-button>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 只读的出库信息表单 -->\r\n      <div class=\"section-container\"\r\n        v-if=\"measureFlag == 1 && taskInfoForm.taskStatus > 2 && taskInfoForm.taskType !== 2 && taskInfoForm.isDirectSupply != 3\">\r\n        <div class=\"section-title\">出库信息</div>\r\n\r\n        <el-form :model=\"taskInfoForm\" label-width=\"120px\">\r\n          <el-row :gutter=\"20\">\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"计划号\">\r\n                <el-input :value=\"taskInfoForm.planNo\" disabled></el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"车牌号\">\r\n                <el-input :value=\"taskInfoForm.carNum\" disabled></el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n\r\n          <el-row :gutter=\"20\">\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"物资名称\">\r\n                <el-input :value=\"materialNames\" disabled></el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"皮重(t)\">\r\n                <el-input :value=\"taskInfoForm.tare\" disabled></el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n\r\n          <el-row :gutter=\"20\" v-if=\"planForm.planType == 3\">\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"物料规格\">\r\n                <el-input :value=\"materialSpecs\" disabled></el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n\r\n          <el-row :gutter=\"20\">\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"供货单位\">\r\n                <el-input :value=\"planForm.sourceCompany\" disabled></el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"收货单位\">\r\n                <el-input :value=\"planForm.receiveCompany\" disabled></el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n\r\n          <el-row :gutter=\"20\">\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"规格\">\r\n                <el-input :value=\"taskInfoForm.stockOutSpec1Length\" disabled></el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n\r\n          <el-row :gutter=\"20\">\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"总数\">\r\n                <el-input :value=\"taskInfoForm.stockOutTotals\" disabled></el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n\r\n          <el-row :gutter=\"20\" v-if=\"taskInfoForm.taskType == 2\">\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"加工类型\">\r\n                <el-input :value=\"taskInfoForm.stockOutProcessType\" disabled></el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n\r\n          <el-row :gutter=\"20\">\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"炉号/批号\">\r\n                <el-input :value=\"taskInfoForm.stockOutHeatNo\" disabled></el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"钢种\">\r\n                <el-input :value=\"taskInfoForm.stockOutSteelGrade\" disabled></el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n\r\n          <el-row :gutter=\"20\">\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"轴数\">\r\n                <el-input :value=\"taskInfoForm.stockOutAxles\" disabled></el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n\r\n          <el-form-item label=\"备注\">\r\n            <el-input type=\"textarea\" :value=\"taskInfoForm.stockOutRemark\" disabled></el-input>\r\n          </el-form-item>\r\n        </el-form>\r\n      </div>\r\n\r\n      <!-- 可编辑的入库信息表单 -->\r\n      <div class=\"section-container\" v-if=\"measureFlag == 1 && taskInfoForm.taskStatus == 7\">\r\n        <div class=\"section-title\">入库信息</div>\r\n\r\n        <el-form :model=\"factoryConfirmForm\" label-width=\"120px\">\r\n          <el-row :gutter=\"20\">\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"计划号\">\r\n                <el-input v-model=\"factoryConfirmForm.planNo\" disabled></el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"车牌号\">\r\n                <el-input v-model=\"factoryConfirmForm.carNum\" placeholder=\"请输入车牌号\" disabled></el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n\r\n          <el-row :gutter=\"20\">\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"物资名称\">\r\n                <el-input :value=\"materialNames\" disabled></el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"12\">\r\n              <!-- <div\r\n                v-if=\"taskInfoForm.isDirectSupply == 0 || taskInfoForm.isDirectSupply == null || taskInfoForm.isDirectSupply == ''\">\r\n                <el-form-item label=\"毛重(t)\">\r\n                  <el-input v-model=\"factoryConfirmForm.secGross\" placeholder=\"\" disabled></el-input>\r\n                </el-form-item>\r\n              </div>\r\n\r\n              <div v-if=\"taskInfoForm.isDirectSupply == 1\">\r\n                <el-form-item label=\"毛重(t)\">\r\n                  <el-input v-model=\"factoryConfirmForm.gross\" placeholder=\"\" disabled></el-input>\r\n                </el-form-item>\r\n              </div> -->\r\n\r\n              <el-form-item label=\"毛重(t)\">\r\n                <el-input :value=\"taskInfoForm.secGross\" disabled></el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n\r\n          <el-row :gutter=\"20\">\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"供货单位\">\r\n                <el-input v-model=\"factoryConfirmForm.sourceCompany\" placeholder=\"请输入车牌号\" disabled></el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"收货单位\">\r\n                <el-input v-model=\"factoryConfirmForm.receiveCompany\" disabled></el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n\r\n          <el-row :gutter=\"20\">\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"加工类型\">\r\n                <el-select v-model=\"factoryConfirmForm.processType\" placeholder=\"请选择加工类型\" filterable\r\n                  :filter-method=\"filterProcessType\">\r\n                  <el-option v-for=\"item in filteredProcessTypeOptions\" :key=\"item.value\" :label=\"item.label\"\r\n                    :value=\"item.value\"></el-option>\r\n                </el-select>\r\n              </el-form-item>\r\n            </el-col>\r\n\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"钢种\">\r\n                <el-input v-model=\"factoryConfirmForm.steelGrade\" placeholder=\"请输入钢种\"></el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n\r\n          <el-row :gutter=\"20\">\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"规格\">\r\n                <el-input v-model=\"factoryConfirmForm.spec1Length\" placeholder=\"请输入规格\"></el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n\r\n          <el-row :gutter=\"20\">\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"总数\">\r\n                <el-input v-model=\"factoryConfirmForm.total\" placeholder=\"请输入总数\"></el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"总数单位\">\r\n                <el-select v-model=\"factoryConfirmForm.totalUnit\" placeholder=\"请选择总数单位\">\r\n                  <el-option label=\"件\" value=\"件\"></el-option>\r\n                  <el-option label=\"支\" value=\"支\"></el-option>\r\n                  <el-option label=\"张\" value=\"张\"></el-option>\r\n                </el-select>\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n\r\n          <el-row :gutter=\"20\">\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"炉号/批号\">\r\n                <el-input v-model=\"factoryConfirmForm.heatNo\" placeholder=\"请输入炉号/批号\"></el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"扣重(t)\">\r\n                <el-input v-model=\"factoryConfirmForm.deductWeight\" placeholder=\"请输入扣重\"></el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n\r\n          <el-form-item label=\"出库备注\">\r\n            <el-input type=\"textarea\" v-model=\"factoryConfirmForm.remark\" placeholder=\"请输入出库备注\"></el-input>\r\n          </el-form-item>\r\n\r\n          <el-form-item label=\"是否直供\" v-if=\"taskInfoForm.taskType == 2\">\r\n            <el-checkbox v-model=\"factoryConfirmForm.showDropdown\" @change=\"handleShowDropdownChange\">是否直供</el-checkbox>\r\n          </el-form-item>\r\n\r\n          <el-form-item v-if=\"factoryConfirmForm.showDropdown\" label=\"直供申请单号\">\r\n            <el-input v-model=\"factoryConfirmForm.extraOption\" placeholder=\"请选择直供申请单号\" readonly style=\"width: 300px;\">\r\n              <el-button slot=\"append\" icon=\"el-icon-search\" @click=\"openOptionDialog\"></el-button>\r\n            </el-input>\r\n          </el-form-item>\r\n        </el-form>\r\n\r\n        <div class=\"btn-wrapper\">\r\n          <el-button type=\"primary\" @click=\"submitFactoryConfirm\" size=\"medium\" class=\"dispatch-btn\">确认入库</el-button>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 只读的入库信息表单 -->\r\n      <div class=\"section-container\"\r\n        v-if=\"measureFlag == 1 && taskInfoForm.taskStatus > 7 && taskInfoForm.taskType !== 1\">\r\n        <div class=\"section-title\">入库信息</div>\r\n\r\n        <el-form :model=\"taskInfoForm\" label-width=\"120px\">\r\n          <el-row :gutter=\"20\">\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"计划号\">\r\n                <el-input :value=\"taskInfoForm.planNo\" disabled></el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"车牌号\">\r\n                <el-input :value=\"taskInfoForm.carNum\" disabled></el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n\r\n          <el-row :gutter=\"20\">\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"物资名称\">\r\n                <el-input :value=\"materialNames\" disabled></el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"12\">\r\n              <!-- <div\r\n                v-if=\"taskInfoForm.isDirectSupply == 0 || taskInfoForm.isDirectSupply == null || taskInfoForm.isDirectSupply == ''\">\r\n                <el-form-item label=\"毛重(t)\">\r\n                  <el-input :value=\"taskInfoForm.secGross\" disabled></el-input>\r\n                </el-form-item>\r\n              </div>\r\n\r\n              <div v-if=\"taskInfoForm.isDirectSupply == 1\">\r\n                <el-form-item label=\"毛重(t)\">\r\n                  <el-input :value=\"taskInfoForm.gross\" disabled></el-input>\r\n                </el-form-item>\r\n              </div> -->\r\n\r\n              <el-form-item label=\"毛重(t)\">\r\n                <el-input :value=\"taskInfoForm.secGross\" disabled></el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n\r\n          <el-row :gutter=\"20\">\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"供货单位\">\r\n                <el-input :value=\"planForm.sourceCompany\" disabled></el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"收货单位\">\r\n                <el-input :value=\"planForm.receiveCompany\" disabled></el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n\r\n          <el-row :gutter=\"20\">\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"加工类型\">\r\n                <el-input :value=\"taskInfoForm.processType\" disabled></el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"钢种\">\r\n                <el-input :value=\"taskInfoForm.steelGrade\" disabled></el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n\r\n          <el-row :gutter=\"20\">\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"规格\">\r\n                <el-input :value=\"taskInfoForm.spec1Length\" disabled></el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n\r\n          <el-row :gutter=\"20\">\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"总数\">\r\n                <el-input :value=\"taskInfoForm.totals\" disabled></el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"炉号/批号\">\r\n                <el-input :value=\"taskInfoForm.heatNo\" disabled></el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n\r\n          <el-row :gutter=\"20\">\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"扣重\">\r\n                <el-input :value=\"taskInfoForm.deductWeight\" disabled></el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n\r\n          <el-form-item label=\"备注\">\r\n            <el-input type=\"textarea\" :value=\"taskInfoForm.remark\" disabled></el-input>\r\n          </el-form-item>\r\n\r\n\r\n          <el-form-item v-if=\"taskInfoForm.directSupplyTaskNo\" label=\"对应申请单号\">\r\n            <el-input :value=\"taskInfoForm.directSupplyTaskNo\" disabled style=\"width: 300px;\"></el-input>\r\n            <el-button style=\"margin-left: 10px; font-size: 14px; padding: 5px 10px;\" type=\"primary\"\r\n              @click=\"openNewTaskWindow\">直供对应任务号</el-button>\r\n          </el-form-item>\r\n        </el-form>\r\n      </div>\r\n\r\n      <!-- 日志列表部分 -->\r\n      <div class=\"section-container\">\r\n        <div class=\"section-title\">任务日志</div>\r\n        <el-timeline>\r\n          <el-timeline-item v-for=\"(log, index) in taskLogs\" :key=\"index\" :timestamp=\"log.createTime\"\r\n            :color=\"getLogColor(log)\">\r\n            {{ log.info }}\r\n          </el-timeline-item>\r\n        </el-timeline>\r\n      </div>\r\n\r\n      <div class=\"form-footer\">\r\n        <el-button @click=\"cancel\">返 回</el-button>\r\n      </div>\r\n    </el-card>\r\n\r\n    <!-- 选项弹窗 -->\r\n    <el-dialog title=\"选择直供申请单号\" :visible.sync=\"optionDialogVisible\" width=\"1600px\">\r\n      <el-form :inline=\"true\" :model=\"searchForm\" class=\"demo-form-inline\">\r\n        <el-form-item label=\"计划号\">\r\n          <el-input v-model=\"searchForm.planNo\" placeholder=\"请输入计划号\"></el-input>\r\n        </el-form-item>\r\n        <el-form-item label=\"申请编号\">\r\n          <el-input v-model=\"searchForm.applyNo\" placeholder=\"请输入申请编号\"></el-input>\r\n        </el-form-item>\r\n        <el-form-item label=\"收货单位\">\r\n          <el-input v-model=\"searchForm.receiveCompany\" placeholder=\"请输入收货单位\"></el-input>\r\n        </el-form-item>\r\n        <el-form-item>\r\n          <el-button type=\"primary\" @click=\"searchOptions\">查询</el-button>\r\n          <el-button @click=\"resetSearch\">重置</el-button>\r\n          <el-button style=\"margin-left: 10px; font-size: 14px; padding: 5px 10px;\" type=\"primary\"\r\n            @click=\"openNewWindow\">直供对应任务号\r\n          </el-button>\r\n        </el-form-item>\r\n      </el-form>\r\n      <el-table :data=\"optionList\" style=\"width: 100%\" @selection-change=\"handleOptionSelection\" ref=\"optionTable\">\r\n        <el-table-column type=\"selection\" width=\"55\" />\r\n        <el-table-column prop=\"planNo\" label=\"计划号\" width=\"150\" />\r\n        <el-table-column prop=\"applyNo\" label=\"申请编号\" width=\"150\" />\r\n        <el-table-column prop=\"materialName\" label=\"物资名称\" width=\"150\" />\r\n        <el-table-column prop=\"materialSpec\" label=\"物料规格\" width=\"120\" />\r\n        <el-table-column prop=\"sourceCompany\" label=\"申请单位\" width=\"150\" />\r\n        <el-table-column prop=\"receiveCompany\" label=\"收货单位\" width=\"150\" />\r\n        <el-table-column prop=\"plannedAmount\" label=\"计划量/t\" width=\"150\" />\r\n        <el-table-column prop=\"startTime\" label=\"开始时间\" width=\"160\">\r\n          <template slot-scope=\"scope\">\r\n            {{ parseTime(scope.row.create_time) }}\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column prop=\"endTime\" label=\"结束时间\" width=\"160\">\r\n          <template slot-scope=\"scope\">\r\n            {{ parseTime(scope.row.create_time) }}\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column prop=\"planStatus\" label=\"状态\" width=\"150\" />\r\n      </el-table>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"optionDialogVisible = false\">取消</el-button>\r\n        <el-button type=\"primary\" @click=\"confirmOptionSelection\">确认</el-button>\r\n      </div>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { getTask, getTaskmaterials, getProcessList, getDirectSupplyPlans, getDirectSupplyPlanAndTaskDetail, handleUnload, handleStockOut, isAllowDispatch, getPlanMaterials, editTaskmaterials, getTaskLogs, addLeaveLog, updateTask, addLeaveLogAndEditTaskMaterialsAndUpdateTask } from \"@/api/leave/task\";\r\nimport { detailPlan } from \"@/api/leave/plan\";\r\nimport { listXctgDriverCar, getXctgDriverCar, delXctgDriverCar, addXctgDriverCar, updateXctgDriverCar, exportXctgDriverCar } from \"@/api/truck/common/xctgDriverCar\";\r\nimport { Message } from \"element-ui\";\r\nimport QRCode from \"qrcodejs2\";\r\n\r\n\r\nexport default {\r\n  name: \"DispatchTaskDetail\",\r\n  data() {\r\n    return {\r\n      factoryConfirmDialogVisible: false,\r\n      factoryConfirmForm: {\r\n        companyName: '',\r\n        taskNo: '',\r\n        applyNo: '',\r\n        planNo: '',\r\n        taskType: null,\r\n        unloadingWorkNo: '',\r\n        unloadingTime: null,\r\n        spec1Length: null,\r\n        spec2Width: null,\r\n        totals: '',\r\n        total: '',\r\n        totalUnit: '',\r\n        processType: '',\r\n        heatNo: '',\r\n        steelGrade: '',\r\n        axles: '',\r\n        remark: '',\r\n        taskStatus: 9, // 完成状态\r\n        carNum: '', // 车牌号\r\n        // 出库信息\r\n        stockOutSpec1Length: null,\r\n        stockOutSpec2Width: null,\r\n        stockOutTotals: '',\r\n        stockOutTotalUnit: '',\r\n        stockOutTotal: '',\r\n        stockOutProcessType: '',\r\n        stockOutHeatNo: '',\r\n        stockOutSteelGrade: '',\r\n        stockOutAxles: '',\r\n        stockOutRemark: '',\r\n        handledMaterialName: '',\r\n        sourceCompany: '',\r\n        receiveCompany: '',\r\n        showDropdown: false,\r\n        extraOption: '',\r\n        deductWeight: null, // 添加扣重字段\r\n      },\r\n      optionDialogVisible: false,\r\n      searchForm: {\r\n        planNo: '',\r\n        applyNo: '',\r\n        receiveCompany: ''\r\n      },\r\n      optionList: [],\r\n      editDoorManStatus: false,\r\n      editFactoryStatus: false,\r\n      // 司机信息\r\n      driverInfo: {\r\n        id: 1,\r\n        name: '王小明',\r\n        idCard: '110101199001010001',\r\n        phone: '13800138000',\r\n        gender: '1',\r\n        company: '北京运输有限公司',\r\n        photo: 'https://via.placeholder.com/150',\r\n        driverLicenseImgs: 'https://via.placeholder.com/300x200',\r\n        vehicleLicenseImgs: 'https://via.placeholder.com/300x200'\r\n      },\r\n\r\n      // 车辆信息\r\n      carInfo: {},\r\n\r\n      // 任务物资列表\r\n      taskMaterials: [],\r\n\r\n      // 任务日志列表\r\n      taskLogs: [],\r\n\r\n      // 申请编号\r\n      applyNo: null,\r\n\r\n      isdoorMan: false,\r\n\r\n      // 派车任务ID\r\n      dispatchId: null,\r\n\r\n      taskInfoForm: {},\r\n\r\n      measureFlag: null,\r\n\r\n      backupTaskMaterials: null,\r\n      taskNo: null,\r\n\r\n      selectedOption: null,\r\n\r\n      planForm: {},\r\n\r\n      processTypeOptions: [], // 动态加载的加工类型选项\r\n\r\n      filteredProcessTypeOptions: [], // 过滤后的加工类型选项\r\n\r\n      searchProcessTypeQuery: '',// 搜索框的值\r\n\r\n      directSupplyPlanList: [], // 直供计划列表\r\n\r\n      editingRow: null,\r\n\r\n      selectedRows: [], // 添加选中行数据数组\r\n\r\n      directSupplyParams: {}\r\n    };\r\n  },\r\n\r\n  computed: {\r\n    displayProcessTypeOptions() {\r\n      return this.searchProcessTypeQuery ? this.filteredProcessTypeOptions : this.processTypeOptions;\r\n    },\r\n\r\n    // 是否有选中的项\r\n    hasSelectedItems() {\r\n      return this.selectedRows.length > 0;\r\n    },\r\n\r\n    // 添加计算属性\r\n    materialNames() {\r\n      return this.taskMaterials.map(item => item.materialName).join(' ');\r\n    },\r\n\r\n    materialSpecs() {\r\n      return this.taskMaterials.map(item => item.materialSpec).join(' ');\r\n    }\r\n  },\r\n\r\n  created() {\r\n    console.log(\"created执行\");\r\n    // 如果不是 keep-alive 组件，created 会在页面刷新时执行\r\n    this.initPage();\r\n  },\r\n\r\n  activated() {\r\n    console.log(\"activated执行\");\r\n    // 调用统一的页面初始化方法\r\n    this.initPage();\r\n  },\r\n\r\n  methods: {\r\n    // 页面初始化方法，统一处理路由参数获取和数据加载\r\n    initPage() {\r\n      console.log(\"initPage执行\");\r\n      this.resetTaskInfoForm();\r\n      // 获取路由参数\r\n      const { dispatchId, applyNo, measureFlag, planType, taskNo } = this.$route.query;\r\n\r\n      // 验证必要参数是否存在\r\n      if (!dispatchId || !applyNo || !taskNo) {\r\n        console.error('缺少必要的路由参数:', { dispatchId, applyNo, taskNo });\r\n        this.$message.error('页面参数缺失，请从列表页面重新进入');\r\n        // 可以选择跳转回列表页面\r\n        // this.$router.push('/leave/plan');\r\n        return;\r\n      }\r\n\r\n      this.dispatchId = dispatchId;\r\n      this.applyNo = applyNo;\r\n      this.measureFlag = measureFlag;\r\n      console.log(\"this.measureFlag\", this.measureFlag)\r\n      this.planType = planType;\r\n      this.taskNo = taskNo;\r\n      console.log(\"taskNo\", this.taskNo);\r\n      this.validDoorMan();\r\n\r\n      // 使用 async/await 确保按顺序执行\r\n      this.initializeData();\r\n    },\r\n\r\n    getDirectSupplyPlanAndTask() {\r\n\r\n\r\n      let leaveTask0 = {\r\n        taskNo: this.taskInfoForm.directSupplyTaskNo\r\n      }\r\n\r\n      getDirectSupplyPlanAndTaskDetail(leaveTask0).then(res => {\r\n        console.log(\"getDirectSupplyPlanAndTaskDetail\", res)\r\n        if (res.code == 200) {\r\n          this.directSupplyParams.dispatchId = res.rows[0].id;\r\n          this.directSupplyParams.applyNo = res.rows[0].applyNo;\r\n          this.directSupplyParams.taskNo = res.rows[0].taskNo;\r\n          this.directSupplyParams.measureFlag = res.rows[1].measureFlag;\r\n          this.directSupplyParams.planType = res.rows[1].planType;\r\n        } else {\r\n          this.$message.error(res.message || '获取计划列表失败');\r\n        }\r\n      }).catch(err => {\r\n        console.error('getDirectSupplyPlanAndTaskDetail error:', err);\r\n        this.$message.error('网络异常，稍后重试');\r\n        throw err;\r\n      });\r\n\r\n    },\r\n\r\n    validDoorMan() {\r\n      this.$store.getters.roles.forEach(item => {\r\n        if (item == 'leave.quard') {\r\n          this.isdoorMan = true;\r\n        }\r\n      });\r\n      console.log(\"isdoorMan\", this.isdoorMan)\r\n    },\r\n    async initializeData() {\r\n      try {\r\n        // 等待所有异步操作完成\r\n        await this.getTaskInfo();\r\n        await this.getTaskmaterialList(this.taskNo);\r\n        await this.getPlanInfo(this.applyNo);\r\n\r\n        // 在所有数据加载完成后执行\r\n        this.uploadFactoryConfirmForm();\r\n\r\n        // 其他初始化操作\r\n        this.getTaskLogList(this.taskNo);\r\n        this.getProcessType();\r\n\r\n        //查询直供对应计划、任务详情\r\n        this.getDirectSupplyPlanAndTask();\r\n      } catch (error) {\r\n        console.error('Error initializing data:', error);\r\n        this.$message.error('数据加载失败，请刷新页面重试');\r\n      }\r\n    },\r\n\r\n    uploadFactoryConfirmForm() {\r\n      // 赋值后，初始化每个元素的 doormanReceiveNum 和 doormanReceiveNumIn\r\n      this.taskMaterials.forEach(item => {\r\n        item.doormanReceiveNum = item.planNum;\r\n        console.log(\"item.planType\", this.planForm.planType);\r\n        if (this.planForm.planType == 2 || this.planForm.planType == 3) {\r\n          item.doormanReceiveNumIn = item.planNum;\r\n        }\r\n      });\r\n\r\n      let handledMaterialName = this.taskMaterials.map(item => item.materialName).join(' ');\r\n      let materialSpecs = this.taskMaterials.map(item => item.materialSpec).join(' ');\r\n      // 初始化表单数据\r\n      this.factoryConfirmForm = {\r\n        companyName: this.taskInfoForm.companyName,\r\n        gross: this.taskInfoForm.gross,\r\n        secGross: this.taskInfoForm.secGross,\r\n        driverName: this.taskInfoForm.driverName,\r\n        tare: this.taskInfoForm.tare,\r\n        taskNo: this.taskNo,\r\n        applyNo: this.applyNo,\r\n        planNo: this.taskInfoForm.planNo,\r\n        unloadingWorkNo: '',\r\n        unloadingTime: new Date(),\r\n        spec1Length: null,\r\n        spec2Width: null,\r\n        totals: '',\r\n        total: '',\r\n        totalUnit: '',\r\n        processType: '',\r\n        heatNo: '',\r\n        steelGrade: '',\r\n        axles: '',\r\n        remark: '',\r\n        taskStatus: 9,\r\n        carNum: this.taskInfoForm.carNum, // 初始化车牌号\r\n        handledMaterialName: handledMaterialName,\r\n        materialSpecs: materialSpecs,\r\n        sourceCompany: this.planForm.sourceCompany,\r\n        receiveCompany: this.planForm.receiveCompany,\r\n        showDropdown: false, // 是否启用额外选项\r\n        extraOption: '', // 额外选项的值\r\n        // 出库信息\r\n        stockOutSpec1Length: null,\r\n        stockOutSpec2Width: null,\r\n        stockOutTotals: '',\r\n        stockOutTotalUnit: '',\r\n        stockOutTotal: '',\r\n        stockOutProcessType: '',\r\n        stockOutHeatNo: '',\r\n        stockOutSteelGrade: '',\r\n        stockOutAxles: '',\r\n        stockOutRemark: '',\r\n        deductWeight: null, // 添加扣重字段初始化\r\n      };\r\n    },\r\n\r\n    openNewWindow() {\r\n      const newWindowUrl = 'http://localhost/leave/leavePlanList'; // 替换为实际要跳转的页面 URL\r\n      window.open(newWindowUrl, '_blank'); // 打开新窗口并跳转至指定 URL\r\n    },\r\n    //获取可以直供的计划\r\n    async getDirectSupplyList() {\r\n      try {\r\n        let leavePlan = {\r\n          sourceCompany: this.planForm.sourceCompany,\r\n          planType: 3,\r\n        }\r\n        console.log(\"获取可以直供的计划\", leavePlan)\r\n\r\n        const res = await getDirectSupplyPlans(leavePlan);\r\n        console.log(\"getDirectSupplyPlans\", res)\r\n        if (res.code == 200) {\r\n          this.directSupplyPlanList = res.rows;\r\n          //查询每个计划的物资\r\n          for (const item of this.directSupplyPlanList) {\r\n            console.log(\"item\", item)\r\n            let leavePlanMaterial = {\r\n              applyNo: item.applyNo\r\n            };\r\n            const response = await getPlanMaterials(leavePlanMaterial);\r\n            if (response.code == 200) {\r\n              console.log(\"getPlanMaterials\", response)\r\n              item.materialName = response.rows[0].materialName;\r\n              item.materialSpec = response.rows[0].materialSpec;\r\n            } else {\r\n              this.$message.error(response.message || '获取计划物资失败');\r\n            }\r\n          }\r\n        } else {\r\n          this.$message.error(res.message || '获取计划列表失败');\r\n        }\r\n      } catch (err) {\r\n        console.error('getDirectSupplyPlans error:', err);\r\n        this.$message.error('网络异常，稍后重试');\r\n        throw err;\r\n      }\r\n    },\r\n    filterProcessType(query) {\r\n      this.searchProcessTypeQuery = query;\r\n\r\n      if (this.searchProcessTypeQuery) {\r\n        console.log(\"processTypeOptions\", this.processTypeOptions)\r\n\r\n        this.filteredProcessTypeOptions = this.processTypeOptions.filter(item =>\r\n          item.value.includes(query)\r\n        );\r\n      } else {\r\n\r\n        this.filteredProcessTypeOptions = this.processTypeOptions;\r\n      }\r\n    },\r\n    getProcessType() {\r\n      getProcessList().then(res => {\r\n        console.log(\"getProcessList\", res)\r\n        if (res.code == 200) {\r\n          this.processTypeOptions = res.rows.map(item => ({\r\n            value: item.processname,\r\n            label: item.processname\r\n          }));\r\n          this.filteredProcessTypeOptions = this.processTypeOptions; // 初始化过滤后的选项\r\n        } else {\r\n          this.$message.error(res.message || '获取加工类型失败');\r\n        }\r\n      }).catch(err => {\r\n        console.error('getProcessList error:', err);\r\n        this.$message.error('网络异常，稍后重试');\r\n      });\r\n    },\r\n    async getPlanInfo(applyNo) {\r\n      try {\r\n        const response = await detailPlan(applyNo);\r\n        console.log(\"detailPlan\", response);\r\n        this.planForm = response.data;\r\n        await this.getDirectSupplyList();\r\n        return response;\r\n      } catch (error) {\r\n        console.error('getPlanInfo error:', error);\r\n        throw error;\r\n      }\r\n    },\r\n    openFactoryConfirmDialog() {\r\n      let handledMaterialName = this.taskMaterials.map(item => item.materialName).join(' ');\r\n      // 初始化表单数据\r\n      this.factoryConfirmForm = {\r\n        companyName: this.taskInfoForm.companyName,\r\n        gross: this.taskInfoForm.gross,\r\n        secGross: this.taskInfoForm.secGross,\r\n        tare: this.taskInfoForm.tare,\r\n        taskNo: this.taskNo,\r\n        applyNo: this.applyNo,\r\n        planNo: this.taskInfoForm.planNo,\r\n        unloadingWorkNo: '',\r\n        unloadingTime: new Date(),\r\n        spec1Length: null,\r\n        spec2Width: null,\r\n        totals: '',\r\n        total: '',\r\n        totalUnit: '',\r\n        processType: '',\r\n        heatNo: '',\r\n        steelGrade: '',\r\n        axles: '',\r\n        remark: '',\r\n        taskStatus: 9,\r\n        carNum: this.taskInfoForm.carNum, // 初始化车牌号\r\n        handledMaterialName: handledMaterialName,\r\n        sourceCompany: this.planForm.sourceCompany,\r\n        receiveCompany: this.planForm.receiveCompany,\r\n        showDropdown: false, // 是否启用额外选项\r\n        extraOption: '', // 额外选项的值\r\n        // 出库信息\r\n        stockOutSpec1Length: null,\r\n        stockOutSpec2Width: null,\r\n        stockOutTotals: '',\r\n        stockOutTotalUnit: '',\r\n        stockOutTotal: '',\r\n        stockOutProcessType: '',\r\n        stockOutHeatNo: '',\r\n        stockOutSteelGrade: '',\r\n        stockOutAxles: '',\r\n        stockOutRemark: '',\r\n        deductWeight: null, // 添加扣重字段初始化\r\n      };\r\n      this.factoryConfirmDialogVisible = true;\r\n    },\r\n    submitFactoryConfirm() {\r\n      if (this.factoryConfirmForm.showDropdown == true) {\r\n        if (this.factoryConfirmForm.extraOption == null || this.factoryConfirmForm.extraOption == '') {\r\n          this.$message.error('请选择额外选项');\r\n          return;\r\n        }\r\n      }\r\n\r\n      let submitData = {};\r\n      if (this.taskInfoForm.isDirectSupply == 3) {\r\n        // 构建提交数据\r\n        submitData = {\r\n          leaveTask: {\r\n            id: this.dispatchId,\r\n            taskNo: this.taskNo,\r\n            applyNo: this.applyNo,\r\n            //入库信息\r\n            spec1Length: this.factoryConfirmForm.spec1Length,\r\n            spec2Width: this.factoryConfirmForm.spec2Width,\r\n            totals: this.factoryConfirmForm.total + this.factoryConfirmForm.totalUnit,\r\n            processType: this.factoryConfirmForm.processType,\r\n            heatNo: this.factoryConfirmForm.heatNo,\r\n            steelGrade: this.factoryConfirmForm.steelGrade,\r\n            axles: this.factoryConfirmForm.axles,\r\n            remark: this.factoryConfirmForm.remark,\r\n            carNum: this.taskInfoForm.carNum,\r\n            driverName: this.taskInfoForm.driverName,\r\n            isDirectSupply:3,\r\n            \r\n            deductWeight: this.factoryConfirmForm.deductWeight, // 添加扣重字段\r\n            \r\n            // 出库信息\r\n            stockOutSpec1Length: this.factoryConfirmForm.stockOutSpec1Length,\r\n            stockOutSpec2Width: this.factoryConfirmForm.stockOutSpec2Width,\r\n            stockOutTotals: this.factoryConfirmForm.stockOutTotal + this.factoryConfirmForm.stockOutTotalUnit,\r\n            stockOutProcessType: this.factoryConfirmForm.stockOutProcessType,\r\n            stockOutHeatNo: this.factoryConfirmForm.stockOutHeatNo,\r\n            stockOutSteelGrade: this.factoryConfirmForm.stockOutSteelGrade,\r\n            stockOutAxles: this.factoryConfirmForm.stockOutAxles,\r\n            stockOutRemark: this.factoryConfirmForm.stockOutRemark,\r\n            // 更改任务状态: 9\r\n            // todo 任务状态如何变化\r\n            taskStatus: 8,\r\n            taskType: this.taskInfoForm.taskType,\r\n          },\r\n          leavePlan: this.planForm,\r\n          leaveTaskMaterial: this.taskMaterials[0],\r\n        };\r\n      } else {\r\n        // 构建提交数据\r\n        submitData = {\r\n          leaveTask: {\r\n            id: this.dispatchId,\r\n            taskNo: this.taskNo,\r\n            applyNo: this.applyNo,\r\n            //入库信息\r\n            spec1Length: this.factoryConfirmForm.spec1Length,\r\n            spec2Width: this.factoryConfirmForm.spec2Width,\r\n            totals: this.factoryConfirmForm.total + this.factoryConfirmForm.totalUnit,\r\n            processType: this.factoryConfirmForm.processType,\r\n            heatNo: this.factoryConfirmForm.heatNo,\r\n            steelGrade: this.factoryConfirmForm.steelGrade,\r\n            axles: this.factoryConfirmForm.axles,\r\n            remark: this.factoryConfirmForm.remark,\r\n            carNum: this.taskInfoForm.carNum,\r\n            driverName: this.taskInfoForm.driverName,\r\n            isDirectSupply: 0, // 默认不是直供\r\n            deductWeight: this.factoryConfirmForm.deductWeight, // 添加扣重字段\r\n            directSupplyTaskNo: this.factoryConfirmForm.extraOption,\r\n            // 出库信息\r\n            stockOutSpec1Length: this.factoryConfirmForm.stockOutSpec1Length,\r\n            stockOutSpec2Width: this.factoryConfirmForm.stockOutSpec2Width,\r\n            stockOutTotals: this.factoryConfirmForm.stockOutTotal + this.factoryConfirmForm.stockOutTotalUnit,\r\n            stockOutProcessType: this.factoryConfirmForm.stockOutProcessType,\r\n            stockOutHeatNo: this.factoryConfirmForm.stockOutHeatNo,\r\n            stockOutSteelGrade: this.factoryConfirmForm.stockOutSteelGrade,\r\n            stockOutAxles: this.factoryConfirmForm.stockOutAxles,\r\n            stockOutRemark: this.factoryConfirmForm.stockOutRemark,\r\n            // 更改任务状态: 9\r\n            // todo 任务状态如何变化\r\n            taskStatus: 8,\r\n            taskType: this.taskInfoForm.taskType,\r\n          },\r\n          leavePlan: this.planForm,\r\n          leaveTaskMaterial: this.taskMaterials[0],\r\n        };\r\n      }\r\n\r\n\r\n\r\n      let directSupplyTask = {\r\n        //taskNo后台雪花生成\r\n        applyNo: this.factoryConfirmForm.extraOption,\r\n        taskType: 3,\r\n        taskStatus: 7,\r\n        secGross: this.taskInfoForm.secGross,\r\n        secGrossTime: this.taskInfoForm.secGrossTime,\r\n        planNo: this.taskInfoForm.planNo,\r\n        driverName: this.taskInfoForm.driverName,\r\n        sex: this.taskInfoForm.sex,\r\n        mobilePhone: this.taskInfoForm.mobilePhone,\r\n        idCardNo: this.taskInfoForm.idCardNo,\r\n        carNum: this.taskInfoForm.carNum,\r\n        vehicleEmissionStandards: this.taskInfoForm.vehicleEmissionStandards,\r\n        faceImg: this.taskInfoForm.faceImg,\r\n        drivingLicenseImg: this.taskInfoForm.drivingLicenseImg,\r\n        driverLicenseImg: this.taskInfoForm.driverLicenseImg,\r\n        companyName: this.taskInfoForm.companyName,\r\n        isDirectSupply:3\r\n      };\r\n\r\n      let directSupplyTaskMaterialList = this.taskMaterials;\r\n\r\n      if (this.factoryConfirmForm.showDropdown == true && this.factoryConfirmForm.extraOption != null && this.factoryConfirmForm.extraOption != '') {\r\n        submitData.leaveTask.isDirectSupply = 1; // 设置为直供\r\n        submitData.directSupplyTask = directSupplyTask;\r\n        submitData.directSupplyTaskMaterialList = directSupplyTaskMaterialList;\r\n      }\r\n\r\n      handleUnload(submitData).then(res => {\r\n        console.log(\"handleUnload\", res)\r\n        if (res.code == 200) {\r\n          this.$message.success('确认入库成功');\r\n          this.factoryConfirmDialogVisible = false;\r\n          this.getTaskLogList(this.taskNo);\r\n          this.getTaskInfo();\r\n        } else {\r\n          // 其他失败原因\r\n          this.$message.error(res.message || '确认入库失败');\r\n        }\r\n      }).catch(err => {\r\n        console.error('handleDirectSupply error:', err);\r\n        this.$message.error('网络异常，稍后重试');\r\n      });\r\n    },\r\n\r\n    submitStockOutConfirm() {\r\n\r\n      // 判断用户角色权限\r\n      const roles = this.$store.getters.roles;\r\n      if (!roles.includes('leave.unloading')) {\r\n        this.$message.error('您没有确认出库权限');\r\n        return;\r\n      }\r\n      // 构建提交数据\r\n      let submitData = {\r\n        leaveTask: {\r\n          //todo 计量系统补充信息待完善\r\n          id: this.dispatchId,\r\n          taskNo: this.taskNo,\r\n          applyNo: this.applyNo,\r\n          // 出库信息\r\n          stockOutSpec1Length: this.factoryConfirmForm.stockOutSpec1Length,\r\n          stockOutSpec2Width: this.factoryConfirmForm.stockOutSpec2Width,\r\n          stockOutTotals: this.factoryConfirmForm.stockOutTotal + this.factoryConfirmForm.stockOutTotalUnit,\r\n          stockOutProcessType: this.factoryConfirmForm.stockOutProcessType,\r\n          stockOutHeatNo: this.factoryConfirmForm.stockOutHeatNo,\r\n          stockOutSteelGrade: this.factoryConfirmForm.stockOutSteelGrade,\r\n          stockOutAxles: this.factoryConfirmForm.stockOutAxles,\r\n          stockOutRemark: this.factoryConfirmForm.stockOutRemark,\r\n          // 更改任务状态: 9\r\n          taskStatus: 3,\r\n          carNum: this.taskInfoForm.carNum,\r\n        },\r\n        leavePlan: this.planForm,\r\n        leaveTaskMaterial: this.taskMaterials[0],\r\n      };\r\n\r\n      handleStockOut(submitData).then(res => {\r\n        console.log(\"handleStockOut\", res)\r\n        if (res.code == 200) {\r\n          this.$message.success('确认出库成功');\r\n          this.factoryConfirmDialogVisible = false;\r\n          this.getTaskLogList(this.taskNo);\r\n          this.getTaskInfo();\r\n        } else {\r\n          // 其他失败原因\r\n          this.$message.error(res.message || '确认出库失败');\r\n        }\r\n      }).catch(err => {\r\n        console.error('handleDirectSupply error:', err);\r\n        this.$message.error('网络异常，稍后重试');\r\n      });\r\n    },\r\n\r\n    handleFactoryConfirm() {\r\n      if (this.editFactoryStatus) {\r\n        this.$message.warning('请先保存');\r\n        return\r\n      }\r\n\r\n\r\n      //todo\r\n      //生成派车日志\r\n      let leaveTaskLog = {};\r\n      leaveTaskLog.logType = 2;\r\n      leaveTaskLog.taskNo = this.taskNo;\r\n      leaveTaskLog.applyNo = this.applyNo;\r\n      leaveTaskLog.info = '分厂确认数量';\r\n\r\n\r\n      let factoryTaskInfo = {}\r\n      //todo 出入场\r\n      factoryTaskInfo.id = this.taskInfoForm.id\r\n      factoryTaskInfo.unloadingWorkNo = '卸货人占位符'\r\n      factoryTaskInfo.unloadingTime = new Date()\r\n      factoryTaskInfo.taskStatus = 9\r\n\r\n      let param = {};\r\n      param.taskMaterialList = this.taskMaterials;\r\n      param.leaveLog = leaveTaskLog;\r\n      param.leaveTask = factoryTaskInfo;\r\n      param.measureFlag = this.measureFlag;\r\n\r\n      addLeaveLogAndEditTaskMaterialsAndUpdateTask(param).then(res => {\r\n        console.log(\"addLeaveLogAndEditTaskMaterialsAndUpdateTask\", res)\r\n        if (res.code == 200) {\r\n          this.$message.success('分厂确认成功');\r\n          this.getTaskLogList(this.taskNo);\r\n          this.getTaskInfo();\r\n        } else {\r\n          // 其他失败原因\r\n          this.$message.error(res.message || '分厂确认成功');\r\n        }\r\n      }).catch(err => {\r\n        console.error('handleFactoryConfirm error:', err);\r\n        this.$message.error('网络异常，稍后重试');\r\n      });\r\n    },\r\n\r\n\r\n    handleDoorManConfirm() {\r\n      if (this.editDoorManStatus) {\r\n        this.$message.warning('请先保存');\r\n        return\r\n      }\r\n\r\n      let leaveTaskLog = {};\r\n      leaveTaskLog.logType = 2;\r\n      leaveTaskLog.taskNo = this.taskNo;\r\n      leaveTaskLog.applyNo = this.applyNo;\r\n      leaveTaskLog.info = '门卫确认数量';\r\n\r\n\r\n\r\n      let doorManTaskInfo = {}\r\n      doorManTaskInfo.id = this.taskInfoForm.id\r\n      if (this.taskInfoForm.taskType == 1) {\r\n        doorManTaskInfo.taskStatus = 9\r\n        doorManTaskInfo.leaveTime = new Date().toISOString().slice(0, 19).replace('T', ' ')\r\n        //离厂大门\r\n      } else if (this.taskInfoForm.taskType == 2 && this.measureFlag == 0) {\r\n        doorManTaskInfo.taskStatus = 7\r\n        doorManTaskInfo.enterTime = new Date().toISOString().slice(0, 19).replace('T', ' ')\r\n        //出厂大门\r\n      } else if (this.taskInfoForm.taskType == 2 && this.measureFlag == 1) {\r\n        doorManTaskInfo.taskStatus = 6\r\n        doorManTaskInfo.enterTime = new Date().toISOString().slice(0, 19).replace('T', ' ')\r\n        //出厂大门\r\n      } else if (this.taskInfoForm.taskType == 3 && this.taskInfoForm.taskStatus == 4) {\r\n        doorManTaskInfo.taskStatus = 5\r\n        doorManTaskInfo.leaveTime = new Date().toISOString().slice(0, 19).replace('T', ' ')\r\n        //离厂大门\r\n      } else if (this.taskInfoForm.taskType == 3 && this.measureFlag == 0 && this.taskInfoForm.taskStatus == 5) {\r\n        doorManTaskInfo.taskStatus = 7\r\n        doorManTaskInfo.enterTime = new Date().toISOString().slice(0, 19).replace('T', ' ')\r\n        //出厂大门\r\n      } else if (this.taskInfoForm.taskType == 3 && this.measureFlag == 1 && this.taskInfoForm.taskStatus == 5) {\r\n        doorManTaskInfo.taskStatus = 6\r\n        doorManTaskInfo.enterTime = new Date().toISOString().slice(0, 19).replace('T', ' ')\r\n        //出厂大门\r\n      }\r\n\r\n      let param = {};\r\n      param.taskMaterialList = this.taskMaterials;\r\n      param.leaveLog = leaveTaskLog;\r\n      param.leaveTask = doorManTaskInfo;\r\n      param.measureFlag = this.measureFlag;\r\n\r\n      addLeaveLogAndEditTaskMaterialsAndUpdateTask(param).then(res => {\r\n        console.log(\"addLeaveLogAndEditTaskMaterialsAndUpdateTask\", res)\r\n        if (res.code == 200) {\r\n          this.$message.success('门卫确认成功');\r\n          this.getTaskLogList(this.taskNo);\r\n          this.getTaskInfo();\r\n        } else {\r\n          // 其他失败原因\r\n          this.$message.error(res.message || '门卫确认成功');\r\n        }\r\n      }).catch(err => {\r\n        console.error('handleDoorManConfirm error:', err);\r\n        this.$message.error('网络异常，稍后重试');\r\n      });\r\n\r\n      // this.taskMaterials.map(item => {\r\n      //   editTaskmaterials(item);\r\n      // })\r\n      //todo\r\n      // let leaveTaskLog = {};\r\n      // leaveTaskLog.logType = 2;\r\n      leaveTaskLog.taskNo = this.taskNo;\r\n      leaveTaskLog.applyNo = this.applyNo;\r\n      leaveTaskLog.info = '门卫确认数量';\r\n      // addLeaveLog(leaveTaskLog);\r\n      // this.getTaskLogList(this.taskNo);\r\n\r\n      // let doorManTaskInfo = {}\r\n      // doorManTaskInfo.id = this.taskInfoForm.id\r\n      // if (this.taskInfoForm.taskType == 1) {\r\n      //   doorManTaskInfo.taskStatus = 9\r\n      //   doorManTaskInfo.leaveTime = new Date()\r\n      //   //离厂大门\r\n      // } else if (this.taskInfoForm.taskType == 2 && this.measureFlag == 0) {\r\n      //   doorManTaskInfo.taskStatus = 7\r\n      //   doorManTaskInfo.enterTime = new Date()\r\n      //   //出厂大门\r\n      // } else if (this.taskInfoForm.taskType == 2 && this.measureFlag == 1) {\r\n      //   doorManTaskInfo.taskStatus = 6\r\n      //   doorManTaskInfo.enterTime = new Date()\r\n      //   //出厂大门\r\n      // } else if (this.taskInfoForm.taskType == 3 && this.taskInfoForm.taskStatus == 4) {\r\n      //   doorManTaskInfo.taskStatus = 5\r\n      //   doorManTaskInfo.leaveTime = new Date()\r\n      //   //离厂大门\r\n      // } else if (this.taskInfoForm.taskType == 3 && this.measureFlag == 0 && this.taskInfoForm.taskStatus == 5) {\r\n      //   doorManTaskInfo.taskStatus = 7\r\n      //   doorManTaskInfo.enterTime = new Date()\r\n      //   //出厂大门\r\n      // } else if (this.taskInfoForm.taskType == 3 && this.measureFlag == 1 && this.taskInfoForm.taskStatus == 5) {\r\n      //   doorManTaskInfo.taskStatus = 6\r\n      //   doorManTaskInfo.enterTime = new Date()\r\n      //   //出厂大门\r\n      // }\r\n      // updateTask(doorManTaskInfo);\r\n      // this.$message.success('门卫确认成功');\r\n\r\n      // setTimeout(() => {\r\n      //   this.getTaskInfo();\r\n      // }, 500)\r\n\r\n    },\r\n\r\n    handleDoorManMeasureConfirm() {\r\n      // 判断用户角色权限\r\n      const roles = this.$store.getters.roles;\r\n      if (!roles.includes('leave.guard')) {\r\n        this.$message.error('您没有门卫出厂确认权限');\r\n        return;\r\n      }\r\n\r\n      let leaveTaskLog = {};\r\n      leaveTaskLog.logType = 2;\r\n      leaveTaskLog.taskNo = this.taskNo;\r\n      leaveTaskLog.applyNo = this.applyNo;\r\n      if (this.taskInfoForm.taskStatus == 4) {\r\n        leaveTaskLog.info = '门卫出厂确认，确认物资：' + this.taskMaterials.map(item => item.materialName).join('、 ');\r\n      } else {\r\n        leaveTaskLog.info = '门卫入厂确认，确认物资：' + this.taskMaterials.map(item => item.materialName).join('、 ');\r\n      }\r\n\r\n      let doorManTaskInfo = {}\r\n      doorManTaskInfo.id = this.taskInfoForm.id\r\n      if (this.taskInfoForm.taskType == 1) {\r\n        doorManTaskInfo.taskStatus = 9\r\n        doorManTaskInfo.leaveTime = new Date().toISOString().slice(0, 19).replace('T', ' ')\r\n        //离厂大门\r\n      } else if (this.taskInfoForm.taskType == 2 && this.measureFlag == 0) {\r\n        doorManTaskInfo.taskStatus = 7\r\n        doorManTaskInfo.enterTime = new Date().toISOString().slice(0, 19).replace('T', ' ')\r\n        //出厂大门\r\n      } else if (this.taskInfoForm.taskType == 2 && this.measureFlag == 1) {\r\n        doorManTaskInfo.taskStatus = 6\r\n        doorManTaskInfo.enterTime = new Date().toISOString().slice(0, 19).replace('T', ' ')\r\n        //出厂大门\r\n      } else if (this.taskInfoForm.taskType == 3 && this.taskInfoForm.taskStatus == 4) {\r\n        doorManTaskInfo.taskStatus = 5\r\n        doorManTaskInfo.leaveTime = new Date().toISOString().slice(0, 19).replace('T', ' ')\r\n        //离厂大门\r\n      } else if (this.taskInfoForm.taskType == 3 && this.measureFlag == 0 && this.taskInfoForm.taskStatus == 5) {\r\n        doorManTaskInfo.taskStatus = 7\r\n        doorManTaskInfo.enterTime = new Date().toISOString().slice(0, 19).replace('T', ' ')\r\n        //出厂大门\r\n      } else if (this.taskInfoForm.taskType == 3 && this.measureFlag == 1 && this.taskInfoForm.taskStatus == 5) {\r\n        doorManTaskInfo.taskStatus = 6\r\n        doorManTaskInfo.enterTime = new Date().toISOString().slice(0, 19).replace('T', ' ')\r\n        //出厂大门\r\n      }\r\n\r\n      let param = {};\r\n      param.taskMaterialList = this.taskMaterials;\r\n      param.leaveLog = leaveTaskLog;\r\n      param.leaveTask = doorManTaskInfo;\r\n      param.measureFlag = this.measureFlag;\r\n\r\n      addLeaveLogAndEditTaskMaterialsAndUpdateTask(param).then(res => {\r\n        console.log(\"addLeaveLogAndEditTaskMaterialsAndUpdateTask\", res)\r\n        if (res.code == 200) {\r\n          this.$message.success('门卫确认成功');\r\n          this.getTaskLogList(this.taskNo);\r\n          this.getTaskInfo();\r\n        } else {\r\n          // 其他失败原因\r\n          this.$message.error(res.message || '门卫确认成功');\r\n        }\r\n      }).catch(err => {\r\n        console.error('handleDoorManConfirm error:', err);\r\n        this.$message.error('网络异常，稍后重试');\r\n      });\r\n      //todo\r\n\r\n    },\r\n    // 生成二维码\r\n    creatQrCode() {\r\n      if (this.taskInfoForm.qrCodeContent) {\r\n        this.$refs.qrCode.innerHTML = \"\";\r\n        var YSqrCode = new QRCode(this.$refs.qrCode, {\r\n          text: this.taskInfoForm.qrCodeContent, // 需要转换为二维码的内容\r\n          width: 120,\r\n          height: 120,\r\n          colorDark: \"#000000\",\r\n          colorLight: \"#ffffff\",\r\n          correctLevel: QRCode.CorrectLevel.H,\r\n        });\r\n      }\r\n    },\r\n    getTaskLogList(taskNo) {\r\n      let taskLog = {};\r\n      taskLog.taskNo = taskNo\r\n      getTaskLogs(taskLog).then(response => {\r\n        console.log(\"getTaskLogs\", response);\r\n        // this.taskLogs = response.rows;\r\n        let logs = response.rows || [];\r\n        // 找出包含\"任务完成\"的日志\r\n        const finishedLogs = logs.filter(log => log.info && log.info.includes('任务完成'));\r\n        const otherLogs = logs.filter(log => !(log.info && log.info.includes('任务完成')));\r\n        // 先放\"任务完成\"，再放其他\r\n        this.taskLogs = [...finishedLogs, ...otherLogs];\r\n      })\r\n\r\n    },\r\n    async getTaskmaterialList(taskNo) {\r\n      try {\r\n        console.log(\"getTaskmaterialList\");\r\n        let leaveMaterial = {};\r\n        leaveMaterial.taskNo = taskNo;\r\n        const response = await getTaskmaterials(leaveMaterial);\r\n        this.taskMaterials = response.rows;\r\n        // 赋值后，初始化每个元素的 doormanReceiveNum 和 doormanReceiveNumIn\r\n        this.taskMaterials.forEach(item => {\r\n          item.doormanReceiveNum = item.planNum;\r\n          console.log(\"item.planType\", this.planForm.planType);\r\n          if (this.planForm.planType == 2 || this.planForm.planType == 3) {\r\n            item.doormanReceiveNumIn = item.planNum;\r\n          }\r\n        });\r\n        console.log(\"taskMaterials\", this.taskMaterials);\r\n        return response;\r\n      } catch (error) {\r\n        console.error('getTaskmaterialList error:', error);\r\n        throw error;\r\n      }\r\n    },\r\n    editDoorManRow(row) {\r\n      row._backup = JSON.parse(JSON.stringify(row));//深拷贝\r\n      this.editingRow = row;\r\n      this.editDoorManStatus = true;\r\n      console.log(\"this.editDoorManRow\", row);\r\n    },\r\n    editFactoryRow() {\r\n      this.backupMaterials = JSON.parse(JSON.stringify(this.taskMaterials));//深拷贝\r\n      this.editFactoryStatus = true;\r\n    },\r\n    cancelDoorManEdit(row) {\r\n      //深拷贝\r\n      if (row._backup) {\r\n        // 恢复备份数据\r\n        Object.assign(row, row._backup);\r\n        delete row._backup; // 删除备份数据\r\n      };\r\n      this.editingRow = null; // 清空当前编辑行\r\n      this.editDoorManStatus = false;\r\n    },\r\n    cancelFactoryEdit() {\r\n      this.taskMaterials = JSON.parse(JSON.stringify(this.backupMaterials));//深拷贝\r\n      console.log(\"this.taskMaterials\", this.taskMaterials);\r\n      this.editFactoryStatus = false;\r\n    },\r\n\r\n    saveDoorManRowIn() {\r\n      // 判断用户角色权限\r\n      const roles = this.$store.getters.roles;\r\n      if (!roles.includes('leave.guard')) {\r\n        this.$message.error('您没有门卫出厂确认权限');\r\n        return;\r\n      }\r\n\r\n      if (this.taskMaterials.length == 0) {\r\n        console.log(\"taskMaterials\", this.taskMaterials);\r\n        this.$message.warning('物资异常');\r\n        return\r\n      }\r\n\r\n      // 校验doormanReceiveNumIn是否等于planNum\r\n      for (const item of this.taskMaterials) {\r\n        if (item.doormanReceiveNumIn !== item.planNum) {\r\n          this.$message.warning(`物资\"${item.materialName}\"的门卫入厂确认数量(${item.doormanReceiveNumIn})与计划数量(${item.planNum})不一致，请检查`);\r\n          return;\r\n        }\r\n      }\r\n\r\n      let leaveTaskLog = {};\r\n      leaveTaskLog.logType = 2;\r\n      leaveTaskLog.taskNo = this.taskNo;\r\n      leaveTaskLog.applyNo = this.applyNo;\r\n      leaveTaskLog.info = '门卫入厂确认，确认物资：' + this.taskMaterials.map(item => item.materialName).join('、 ');\r\n\r\n      let doorManTaskInfo = {}\r\n      doorManTaskInfo.id = this.taskInfoForm.id;\r\n      if (this.taskInfoForm.taskType == 1) {\r\n        doorManTaskInfo.taskStatus = 9\r\n        doorManTaskInfo.leaveTime = new Date().toISOString().slice(0, 19).replace('T', ' ')\r\n        //离厂大门\r\n      } else if (this.taskInfoForm.taskType == 2 && this.measureFlag == 0) {\r\n        doorManTaskInfo.taskStatus = 7\r\n        doorManTaskInfo.enterTime = new Date().toISOString().slice(0, 19).replace('T', ' ')\r\n        //出厂大门\r\n      } else if (this.taskInfoForm.taskType == 2 && this.measureFlag == 1) {\r\n        doorManTaskInfo.taskStatus = 6\r\n        doorManTaskInfo.enterTime = new Date().toISOString().slice(0, 19).replace('T', ' ')\r\n        //出厂大门\r\n      } else if (this.taskInfoForm.taskType == 3 && this.taskInfoForm.taskStatus == 4) {\r\n        doorManTaskInfo.taskStatus = 5\r\n        doorManTaskInfo.leaveTime = new Date().toISOString().slice(0, 19).replace('T', ' ')\r\n        //离厂大门\r\n      } else if (this.taskInfoForm.taskType == 3 && this.measureFlag == 0 && this.taskInfoForm.taskStatus == 5) {\r\n        doorManTaskInfo.taskStatus = 7\r\n        doorManTaskInfo.enterTime = new Date().toISOString().slice(0, 19).replace('T', ' ')\r\n        //出厂大门\r\n      } else if (this.taskInfoForm.taskType == 3 && this.measureFlag == 1 && this.taskInfoForm.taskStatus == 5) {\r\n        doorManTaskInfo.taskStatus = 6\r\n        doorManTaskInfo.enterTime = new Date().toISOString().slice(0, 19).replace('T', ' ')\r\n        //出厂大门\r\n      }\r\n\r\n      let param = {\r\n        taskMaterialList: this.taskMaterials,\r\n        leaveLog: leaveTaskLog,\r\n        leaveTask: doorManTaskInfo,\r\n        measureFlag: this.measureFlag\r\n      };\r\n\r\n      console.log(\"addLeaveLogAndEditTaskMaterialsAndUpdateTask\", param, this.taskInfoForm.taskType);\r\n\r\n\r\n      addLeaveLogAndEditTaskMaterialsAndUpdateTask(param).then(res => {\r\n        console.log(\"addLeaveLogAndEditTaskMaterialsAndUpdateTask\", res)\r\n        if (res.code == 200) {\r\n          this.$message.success('门卫确认成功');\r\n          this.getTaskLogList(this.taskNo);\r\n          this.getTaskInfo();\r\n        } else {\r\n          // 其他失败原因\r\n          this.$message.error(res.message || '门卫确认成功');\r\n        }\r\n      }).catch(err => {\r\n        console.error('handleDoorManConfirm error:', err);\r\n        this.$message.error('网络异常，稍后重试');\r\n      });\r\n\r\n      this.editDoorManStatus = false;\r\n    },\r\n\r\n    saveDoorManRow() {\r\n      // 判断用户角色权限\r\n      const roles = this.$store.getters.roles;\r\n      console.log(\"roles\", roles);\r\n      if (!roles.includes('leave.guard')) {\r\n        this.$message.error('您没有门卫出厂确认权限');\r\n        return;\r\n      }\r\n\r\n      if (this.taskMaterials.length == 0) {\r\n        console.log(\"taskMaterials\", this.taskMaterials);\r\n        this.$message.warning('物资异常');\r\n        return\r\n      }\r\n\r\n      // 校验doormanReceiveNum是否等于planNum\r\n      for (const item of this.taskMaterials) {\r\n        if (item.doormanReceiveNum !== item.planNum) {\r\n          this.$message.warning(`物资\"${item.materialName}\"的门卫确认数量(${item.doormanReceiveNum})与计划数量(${item.planNum})不一致，请检查`);\r\n          return;\r\n        }\r\n      }\r\n\r\n      let leaveTaskLog = {};\r\n      leaveTaskLog.logType = 2;\r\n      leaveTaskLog.taskNo = this.taskNo;\r\n      leaveTaskLog.applyNo = this.applyNo;\r\n      leaveTaskLog.info = '门卫出厂确认，确认物资：' + this.taskMaterials.map(item => item.materialName).join('、 ');\r\n\r\n      let doorManTaskInfo = {}\r\n      doorManTaskInfo.id = this.taskInfoForm.id\r\n      if (this.taskInfoForm.taskType == 1) {\r\n        doorManTaskInfo.taskStatus = 9\r\n        doorManTaskInfo.leaveTime = new Date().toISOString().slice(0, 19).replace('T', ' ')\r\n        //离厂大门\r\n      } else if (this.taskInfoForm.taskType == 2 && this.measureFlag == 0) {\r\n        doorManTaskInfo.taskStatus = 7\r\n        doorManTaskInfo.enterTime = new Date().toISOString().slice(0, 19).replace('T', ' ')\r\n        //出厂大门\r\n      } else if (this.taskInfoForm.taskType == 2 && this.measureFlag == 1) {\r\n        doorManTaskInfo.taskStatus = 6\r\n        doorManTaskInfo.enterTime = new Date().toISOString().slice(0, 19).replace('T', ' ')\r\n        //出厂大门\r\n      } else if (this.taskInfoForm.taskType == 3 && this.taskInfoForm.taskStatus == 4) {\r\n        doorManTaskInfo.taskStatus = 5\r\n        doorManTaskInfo.leaveTime = new Date().toISOString().slice(0, 19).replace('T', ' ')\r\n        //离厂大门\r\n      } else if (this.taskInfoForm.taskType == 3 && this.measureFlag == 0 && this.taskInfoForm.taskStatus == 5) {\r\n        doorManTaskInfo.taskStatus = 7\r\n        doorManTaskInfo.enterTime = new Date().toISOString().slice(0, 19).replace('T', ' ')\r\n        //出厂大门\r\n      } else if (this.taskInfoForm.taskType == 3 && this.measureFlag == 1 && this.taskInfoForm.taskStatus == 5) {\r\n        doorManTaskInfo.taskStatus = 6\r\n        doorManTaskInfo.enterTime = new Date().toISOString().slice(0, 19).replace('T', ' ')\r\n        //出厂大门\r\n      }\r\n\r\n      let param = {\r\n        taskMaterialList: this.taskMaterials,\r\n        leaveLog: leaveTaskLog,\r\n        leaveTask: doorManTaskInfo,\r\n        measureFlag: this.measureFlag\r\n      };\r\n\r\n      console.log(\"addLeaveLogAndEditTaskMaterialsAndUpdateTask\", param, this.taskInfoForm.taskType);\r\n\r\n\r\n      addLeaveLogAndEditTaskMaterialsAndUpdateTask(param).then(res => {\r\n        console.log(\"addLeaveLogAndEditTaskMaterialsAndUpdateTask\", res)\r\n        if (res.code == 200) {\r\n          this.$message.success('门卫确认成功');\r\n          this.getTaskLogList(this.taskNo);\r\n          this.getTaskInfo();\r\n        } else {\r\n          // 其他失败原因\r\n          this.$message.error(res.message || '门卫确认成功');\r\n        }\r\n      }).catch(err => {\r\n        console.error('handleDoorManConfirm error:', err);\r\n        this.$message.error('网络异常，稍后重试');\r\n      });\r\n\r\n      this.editDoorManStatus = false;\r\n    },\r\n\r\n\r\n    saveFactoryRow() {\r\n\r\n      this.editFactoryStatus = false;\r\n    },\r\n\r\n    resetTaskInfoForm() {\r\n      this.taskInfoForm = {};\r\n    },\r\n\r\n    async getTaskInfo() {\r\n      try {\r\n        const response = await getTask(this.dispatchId);\r\n        this.taskInfoForm = response.data;\r\n        console.log(\"this.taskInfoForm\", this.taskInfoForm);\r\n        if (this.taskInfoForm.licensePlateColor == 1) {\r\n          this.taskInfoForm.licensePlateColor = '蓝色'\r\n        } else if (this.taskInfoForm.licensePlateColor == 2) {\r\n          this.taskInfoForm.licensePlateColor = '绿色'\r\n        } else if (this.taskInfoForm.licensePlateColor == 3) {\r\n          this.taskInfoForm.licensePlateColor = '黄'\r\n        } else if (this.taskInfoForm.licensePlateColor == 4) {\r\n          this.taskInfoForm.licensePlateColor = '黄绿色'\r\n        }\r\n        console.log(\"this.taskInfoForm\", this.taskInfoForm);\r\n        // 生成二维码\r\n        this.$nextTick(() => {\r\n          this.creatQrCode();\r\n        });\r\n        return response;\r\n      } catch (error) {\r\n        console.error('getTaskInfo error:', error);\r\n        throw error;\r\n      }\r\n    },\r\n\r\n\r\n    getStatusText(standard) {\r\n      const standardMap = {\r\n        1: '待过皮重',\r\n        2: '待装货',\r\n        3: '待过毛重',\r\n        4: '待出厂',\r\n        5: '待返厂',\r\n        6: '待过毛重(复磅)',\r\n        7: '待卸货',\r\n        8: '待过皮重(复磅)',\r\n        9: '完成'\r\n      };\r\n      return standardMap[standard] || '未知';\r\n    },\r\n\r\n    //计划状态\r\n    getPlanStatusText(standard) {\r\n      const standardMap = {\r\n        1: '待分厂审批',\r\n        2: '待分厂复审',\r\n        3: '待生产指挥中心审批',\r\n        4: '审批完成',\r\n        5: '已出厂',\r\n        6: '部分收货',\r\n        7: '已完成',\r\n        11: '驳回',\r\n        12: '废弃',\r\n        13: '过期',\r\n      };\r\n      return standardMap[standard] || '未知';\r\n    },\r\n    // 获取排放标准文本\r\n    getEmissionStandardsText(standard) {\r\n      const standardMap = {\r\n        1: '国五',\r\n        2: '国六',\r\n        3: '新能源'\r\n      };\r\n      return standardMap[standard] || '未知';\r\n    },\r\n\r\n    // 获取排放标准标签类型\r\n    getEmissionStandardsTagType(standard) {\r\n      const typeMap = {\r\n        1: 'warning',  // 国五\r\n        2: 'success',  // 国六\r\n        3: 'primary'   // 新能源\r\n      };\r\n      return typeMap[standard] || 'info';\r\n    },\r\n\r\n    // 获取物资状态文本\r\n    getMaterialStatusText(status) {\r\n      const statusMap = {\r\n        1: '待装载',\r\n        2: '已装载',\r\n        3: '已签收',\r\n        4: '异常'\r\n      };\r\n      return statusMap[status] || '未知状态';\r\n    },\r\n\r\n    // 获取物资状态标签类型\r\n    getMaterialStatusType(status) {\r\n      const typeMap = {\r\n        1: 'info',     // 待装载\r\n        2: 'warning',  // 已装载\r\n        3: 'success',  // 已签收\r\n        4: 'danger'    // 异常\r\n      };\r\n      return typeMap[status] || 'info';\r\n    },\r\n\r\n    // 获取日志颜色\r\n    getLogColor(log) {\r\n      const logTypeColorMap = {\r\n        1: '#409EFF', // 创建\r\n        2: '#E6A23C', // 更新\r\n        3: '#67C23A', // 完成\r\n        4: '#F56C6C', // 异常\r\n        5: '#909399'  // 其他\r\n      };\r\n      return logTypeColorMap[log.type] || '#409EFF';\r\n    },\r\n\r\n    // 返回按钮\r\n    cancel() {\r\n      this.$router.go(-1);\r\n    },\r\n\r\n    // 获取任务详情数据\r\n    getTaskDetail(dispatchId) {\r\n      // 实际项目中这里需要调用API获取数据\r\n      // getDispatchTaskDetail(dispatchId).then(response => {\r\n      //   const { driverInfo, carInfo, taskMaterials, taskLogs } = response.data;\r\n      //   this.driverInfo = driverInfo;\r\n      //   this.carInfo = carInfo;\r\n      //   this.taskMaterials = taskMaterials;\r\n      //   this.taskLogs = taskLogs;\r\n      // });\r\n    },\r\n    handleShowDropdownChange(val) {\r\n      if (!val) {\r\n        this.factoryConfirmForm.extraOption = '';\r\n      }\r\n    },\r\n    openOptionDialog() {\r\n      this.optionDialogVisible = true;\r\n      this.loadOptions();\r\n      // 重置选中状态\r\n      this.selectedOption = null;\r\n      this.$nextTick(() => {\r\n        if (this.$refs.optionTable) {\r\n          this.$refs.optionTable.clearSelection();\r\n        }\r\n      });\r\n    },\r\n    handleOptionSelection(selection) {\r\n      // 只保留最后选中的一项\r\n      if (selection.length > 1) {\r\n        const lastSelected = selection[selection.length - 1];\r\n        this.$refs.optionTable.clearSelection();\r\n        this.$refs.optionTable.toggleRowSelection(lastSelected, true);\r\n        this.selectedOption = lastSelected;\r\n      } else {\r\n        this.selectedOption = selection[0];\r\n      }\r\n    },\r\n    confirmOptionSelection() {\r\n      if (!this.selectedOption) {\r\n        this.$message.warning('请选择一个选项');\r\n        return;\r\n      }\r\n\r\n      this.factoryConfirmForm.extraOption = this.selectedOption.applyNo;\r\n\r\n      // let dispatchInfo = {};\r\n      // dispatchInfo.carNum = this.taskInfoForm.carNum;\r\n      // dispatchInfo.isDirectSupply = 1;\r\n\r\n      // isAllowDispatch(dispatchInfo).then(response => {\r\n      //   let row = response.data;\r\n      //   if (row > 0) {\r\n      //     this.$message.error(\"当前车有正在执行的任务\")\r\n      //     return;\r\n      //   } else {\r\n      //     this.optionDialogVisible = false;\r\n      //     this.$message.success('选项已确认');\r\n      //   }\r\n      //   console.log(\"this.isAllowDispatch\", response);\r\n      // }).catch(err => {\r\n      //   console.error('dispatch error:', err);\r\n      //   this.$message.error('网络异常，稍后重试');\r\n      // });\r\n\r\n      this.optionDialogVisible = false;\r\n      this.$message.success('选项已确认');\r\n\r\n\r\n\r\n    },\r\n    loadOptions() {\r\n      // 这里应该调用API获取leave_plan表的数据\r\n      this.optionList = this.directSupplyPlanList; // 使用直供计划列表作为选项数据\\\r\n      this.optionList.forEach(item => {\r\n        item.planStatus = this.getPlanStatusText(item.planStatus);\r\n      });\r\n      console.log(\"optionList\", this.optionList)\r\n    },\r\n    getBusinessCategoryText(category) {\r\n      const categoryMap = {\r\n        1: '通用（出厂不返回）',\r\n        11: '通用（出厂返回）',\r\n        12: '委外加工（出厂返回）',\r\n        21: '有计划量计量（跨区调拨）',\r\n        22: '短期（跨区调拨）',\r\n        23: '钢板（圆钢）（跨区调拨）',\r\n        31: '通用（退货申请）'\r\n      };\r\n      return categoryMap[category] || '未知类型';\r\n    },\r\n    searchOptions() {\r\n      // 取出并转小写\r\n      const searchPlanNo = (this.searchForm.planNo || '').toLowerCase();\r\n      const searchApplyNo = (this.searchForm.applyNo || '').toLowerCase();\r\n      const searchReceiveCompany = (this.searchForm.receiveCompany || '').toLowerCase();\r\n\r\n      // 过滤\r\n      this.optionList = this.directSupplyPlanList.filter(item => {\r\n        const planNo = (item.planNo || '').toString().toLowerCase();\r\n        const applyNo = (item.applyNo || '').toString().toLowerCase();\r\n        const receiveCompany = (item.receiveCompany || '').toString().toLowerCase();\r\n\r\n        // 为空不作为条件\r\n        const matchPlanNo = !searchPlanNo || planNo.includes(searchPlanNo);\r\n        const matchApplyNo = !searchApplyNo || applyNo.includes(searchApplyNo);\r\n        const matchReceiveCompany = !searchReceiveCompany || receiveCompany.includes(searchReceiveCompany);\r\n\r\n        return matchPlanNo && matchApplyNo && matchReceiveCompany;\r\n      });\r\n\r\n      // 更新状态显示\r\n      this.optionList.forEach(item => {\r\n        item.planStatus = this.getPlanStatusText(item.planStatus);\r\n      });\r\n    },\r\n    resetSearch() {\r\n      this.searchForm = {\r\n        planNo: '',\r\n        applyNo: '',\r\n        receiveCompany: ''\r\n      };\r\n      this.loadOptions(); // 重新加载所有数据\r\n    },\r\n    getTaskTypeText(type) {\r\n      const typeMap = {\r\n        1: '出厂',\r\n        2: '返厂',\r\n        3: '跨区调拨'\r\n      };\r\n      return typeMap[type] || '未知';\r\n    },\r\n    // // 判断行是否可选\r\n    // isSelectable(row) {\r\n    //   // 当门卫确认数量不为0时，该行可选\r\n    //   return row.doormanReceiveNum > 0 && this.taskInfoForm.taskStatus !== 9;\r\n    // },\r\n\r\n    // 表格选择变化时的处理函数\r\n    handleSelectionChange(selection) {\r\n      this.selectedRows = selection;\r\n    },\r\n\r\n    // 处理非计量分厂确认\r\n    handleNonMeasureFactoryConfirm() {\r\n      const roles = this.$store.getters.roles;\r\n      if (!roles.includes('leave.unloading')) {\r\n        this.$message.error('您没有门卫出厂确认权限');\r\n        return;\r\n      }\r\n      let isHandled = false;\r\n      this.selectedRows.forEach(item => {\r\n        if (item.doormanReceiveNum !== item.planNum) {\r\n          this.$message.warning('门卫确认数量和计划数量不一致，请检查');\r\n          isHandled = true;\r\n        }\r\n      });\r\n\r\n      if (isHandled) {\r\n        return;\r\n      }\r\n\r\n      // if (this.selectedRows.length === 0) {\r\n      //   this.$message.warning('请选择需要确认的物资');\r\n      //   return;\r\n      // }\r\n\r\n      // 生成派车日志\r\n      let leaveTaskLog = {\r\n        logType: 2,\r\n        taskNo: this.taskNo,\r\n        applyNo: this.applyNo,\r\n        info: '分厂接收确认，确认物资：' + this.taskMaterials.map(item => item.materialName).join('、 ')\r\n      };\r\n\r\n      // 构建任务信息\r\n      let factoryTaskInfo = {\r\n        id: this.taskInfoForm.id,\r\n        unloadingWorkNo: '卸货人占位符',//后端updateLeaveTask方法\r\n        unloadingTime: new Date(),\r\n        taskStatus: 9\r\n      };\r\n\r\n      this.selectedRows.forEach(item => {\r\n        // 设置非计量分厂确认数量\r\n        item.factoryReceiveNum = item.doormanReceiveNum;\r\n      });\r\n\r\n      // 构建请求参数\r\n      let param = {\r\n        taskMaterialList: this.selectedRows, // 使用选中的行数据\r\n        leaveLog: leaveTaskLog,\r\n        leaveTask: factoryTaskInfo,\r\n        measureFlag: this.measureFlag\r\n      };\r\n\r\n      // 发送请求\r\n      addLeaveLogAndEditTaskMaterialsAndUpdateTask(param).then(res => {\r\n        if (res.code == 200) {\r\n          this.$message.success('非计量分厂确认成功');\r\n          this.getTaskLogList(this.taskNo);\r\n          this.getTaskInfo();\r\n          // 清空选中状态\r\n          this.selectedRows = [];\r\n        } else {\r\n          this.$message.error(res.message || '非计量分厂确认失败');\r\n        }\r\n      }).catch(err => {\r\n        console.error('handleNonMeasureFactoryConfirm error:', err);\r\n        this.$message.error('网络异常，稍后重试');\r\n      });\r\n    },\r\n    openNewTaskWindow() {\r\n      console.log(\"openNewTaskWindow\", this.directSupplyParams);\r\n      let dispatchId = this.directSupplyParams.dispatchId;\r\n      let applyNo = BigInt(this.directSupplyParams.applyNo);\r\n      let measureFlag = this.directSupplyParams.measureFlag;\r\n      let planType = this.directSupplyParams.planType;\r\n      let taskNo = BigInt(this.directSupplyParams.taskNo);\r\n      const url = `http://localhost/leave/plan/task?dispatchId=${dispatchId}&applyNo=${applyNo}&measureFlag=${measureFlag}&planType=${planType}&taskNo=${taskNo}`;\r\n      window.open(url, '_blank');\r\n    },\r\n  }\r\n};\r\n</script>\r\n\r\n<style scoped>\r\n.btn-wrapper {\r\n  display: flex;\r\n  justify-content: center;\r\n}\r\n\r\n.dispatch-btn {\r\n  margin-left: 15px;\r\n}\r\n\r\n.app-container {\r\n  padding: 20px;\r\n}\r\n\r\n.qrcode-container {\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  padding: 20px;\r\n}\r\n\r\n.qrcode {\r\n  margin: 0 auto;\r\n}\r\n\r\n.box-card {\r\n  margin-bottom: 20px;\r\n  border-radius: 5px;\r\n  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.card-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n}\r\n\r\n.section-container {\r\n  margin-bottom: 30px;\r\n  border-radius: 8px;\r\n  background: #fff;\r\n  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);\r\n  overflow: hidden;\r\n  border: 1px solid #ebeef5;\r\n}\r\n\r\n.section-container:nth-child(1) {\r\n  border-top: 4px solid #F56C6C;\r\n  /* 通行证二维码模块 - 红色 */\r\n}\r\n\r\n.section-container:nth-child(2) {\r\n  border-top: 4px solid #409EFF;\r\n  /* 司机信息模块 - 蓝色 */\r\n}\r\n\r\n.section-container:nth-child(3) {\r\n  border-top: 4px solid #67C23A;\r\n  /* 车辆信息模块 - 绿色 */\r\n}\r\n\r\n.section-container:nth-child(4) {\r\n  border-top: 4px solid #E6A23C;\r\n  /* 物资列表模块 - 橙色 */\r\n}\r\n\r\n.section-container:nth-child(5) {\r\n  border-top: 4px solid #909399;\r\n  /* 日志列表模块 - 灰色 */\r\n}\r\n\r\n.section-title {\r\n  font-size: 16px;\r\n  font-weight: bold;\r\n  padding: 15px 20px;\r\n  margin-bottom: 15px;\r\n  border-bottom: 1px solid #ebeef5;\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  background: #fafafa;\r\n  position: relative;\r\n  padding-left: 30px;\r\n}\r\n\r\n.section-title::before {\r\n  content: '';\r\n  width: 4px;\r\n  height: 16px;\r\n  background: currentColor;\r\n  position: absolute;\r\n  left: 15px;\r\n  top: 50%;\r\n  transform: translateY(-50%);\r\n  border-radius: 2px;\r\n}\r\n\r\n.section-container:nth-child(1) .section-title {\r\n  color: #F56C6C;\r\n}\r\n\r\n.section-container:nth-child(2) .section-title {\r\n  color: #409EFF;\r\n}\r\n\r\n.section-container:nth-child(3) .section-title {\r\n  color: #67C23A;\r\n}\r\n\r\n.section-container:nth-child(4) .section-title {\r\n  color: #E6A23C;\r\n}\r\n\r\n.section-container:nth-child(5) .section-title {\r\n  color: #909399;\r\n}\r\n\r\n.section-container .el-descriptions,\r\n.section-container .el-table,\r\n.section-container .el-timeline {\r\n  padding: 0 20px 20px;\r\n}\r\n\r\n.form-footer {\r\n  margin-top: 30px;\r\n  text-align: center;\r\n}\r\n\r\n.driver-photos {\r\n  padding: 0 20px 20px;\r\n  display: flex;\r\n  gap: 20px;\r\n  flex-wrap: wrap;\r\n}\r\n\r\n.photo-item {\r\n  width: 300px;\r\n  border: 1px solid #ebeef5;\r\n  border-radius: 4px;\r\n  overflow: hidden;\r\n  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);\r\n}\r\n\r\n.photo-item h4 {\r\n  padding: 10px;\r\n  background: #f5f7fa;\r\n  margin: 0;\r\n  border-bottom: 1px solid #ebeef5;\r\n}\r\n\r\n.photo-container {\r\n  padding: 10px;\r\n  display: flex;\r\n  justify-content: center;\r\n}\r\n\r\n.photo-container img {\r\n  max-width: 100%;\r\n  max-height: 200px;\r\n  object-fit: contain;\r\n}\r\n\r\n.button-container {\r\n  margin-top: 20px;\r\n  text-align: center;\r\n}\r\n</style>\r\n\r\n<style lang=\"scss\">\r\n.el-table {\r\n  border-radius: 4px;\r\n  overflow: hidden;\r\n\r\n  th {\r\n    background-color: #fafafa !important;\r\n    color: #606266;\r\n    font-weight: bold;\r\n  }\r\n\r\n  td {\r\n    padding: 12px 0;\r\n  }\r\n}\r\n\r\n\r\n\r\n.el-timeline {\r\n  padding: 20px !important;\r\n\r\n  .el-timeline-item__node {\r\n    width: 12px;\r\n    height: 12px;\r\n  }\r\n\r\n  .el-timeline-item__content {\r\n    padding: 0 0 0 25px;\r\n  }\r\n}\r\n\r\n.el-descriptions {\r\n  .el-descriptions-item__label {\r\n    background-color: #fafafa;\r\n  }\r\n}\r\n\r\n.el-tag {\r\n  border-radius: 12px;\r\n  padding: 0 10px;\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAy1BA,IAAAA,KAAA,GAAAC,OAAA;AACA,IAAAC,KAAA,GAAAD,OAAA;AACA,IAAAE,cAAA,GAAAF,OAAA;AACA,IAAAG,UAAA,GAAAH,OAAA;AACA,IAAAI,SAAA,GAAAC,sBAAA,CAAAL,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCAGA;EACAM,IAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,2BAAA;MACAC,kBAAA;QACAC,WAAA;QACAC,MAAA;QACAC,OAAA;QACAC,MAAA;QACAC,QAAA;QACAC,eAAA;QACAC,aAAA;QACAC,WAAA;QACAC,UAAA;QACAC,MAAA;QACAC,KAAA;QACAC,SAAA;QACAC,WAAA;QACAC,MAAA;QACAC,UAAA;QACAC,KAAA;QACAC,MAAA;QACAC,UAAA;QAAA;QACAC,MAAA;QAAA;QACA;QACAC,mBAAA;QACAC,kBAAA;QACAC,cAAA;QACAC,iBAAA;QACAC,aAAA;QACAC,mBAAA;QACAC,cAAA;QACAC,kBAAA;QACAC,aAAA;QACAC,cAAA;QACAC,mBAAA;QACAC,aAAA;QACAC,cAAA;QACAC,YAAA;QACAC,WAAA;QACAC,YAAA;MACA;MACAC,mBAAA;MACAC,UAAA;QACAjC,MAAA;QACAD,OAAA;QACA6B,cAAA;MACA;MACAM,UAAA;MACAC,iBAAA;MACAC,iBAAA;MACA;MACAC,UAAA;QACAC,EAAA;QACA7C,IAAA;QACA8C,MAAA;QACAC,KAAA;QACAC,MAAA;QACAC,OAAA;QACAC,KAAA;QACAC,iBAAA;QACAC,kBAAA;MACA;MAEA;MACAC,OAAA;MAEA;MACAC,aAAA;MAEA;MACAC,QAAA;MAEA;MACAjD,OAAA;MAEAkD,SAAA;MAEA;MACAC,UAAA;MAEAC,YAAA;MAEAC,WAAA;MAEAC,mBAAA;MACAvD,MAAA;MAEAwD,cAAA;MAEAC,QAAA;MAEAC,kBAAA;MAAA;;MAEAC,0BAAA;MAAA;;MAEAC,sBAAA;MAAA;;MAEAC,oBAAA;MAAA;;MAEAC,UAAA;MAEAC,YAAA;MAAA;;MAEAC,kBAAA;IACA;EACA;EAEAC,QAAA;IACAC,yBAAA,WAAAA,0BAAA;MACA,YAAAN,sBAAA,QAAAD,0BAAA,QAAAD,kBAAA;IACA;IAEA;IACAS,gBAAA,WAAAA,iBAAA;MACA,YAAAJ,YAAA,CAAAK,MAAA;IACA;IAEA;IACAC,aAAA,WAAAA,cAAA;MACA,YAAApB,aAAA,CAAAqB,GAAA,WAAAC,IAAA;QAAA,OAAAA,IAAA,CAAAC,YAAA;MAAA,GAAAC,IAAA;IACA;IAEAC,aAAA,WAAAA,cAAA;MACA,YAAAzB,aAAA,CAAAqB,GAAA,WAAAC,IAAA;QAAA,OAAAA,IAAA,CAAAI,YAAA;MAAA,GAAAF,IAAA;IACA;EACA;EAEAG,OAAA,WAAAA,QAAA;IACAC,OAAA,CAAAC,GAAA;IACA;IACA,KAAAC,QAAA;EACA;EAEAC,SAAA,WAAAA,UAAA;IACAH,OAAA,CAAAC,GAAA;IACA;IACA,KAAAC,QAAA;EACA;EAEAE,OAAA;IACA;IACAF,QAAA,WAAAA,SAAA;MACAF,OAAA,CAAAC,GAAA;MACA,KAAAI,iBAAA;MACA;MACA,IAAAC,kBAAA,QAAAC,MAAA,CAAAC,KAAA;QAAAjC,UAAA,GAAA+B,kBAAA,CAAA/B,UAAA;QAAAnD,OAAA,GAAAkF,kBAAA,CAAAlF,OAAA;QAAAqD,WAAA,GAAA6B,kBAAA,CAAA7B,WAAA;QAAAgC,QAAA,GAAAH,kBAAA,CAAAG,QAAA;QAAAtF,MAAA,GAAAmF,kBAAA,CAAAnF,MAAA;;MAEA;MACA,KAAAoD,UAAA,KAAAnD,OAAA,KAAAD,MAAA;QACA6E,OAAA,CAAAU,KAAA;UAAAnC,UAAA,EAAAA,UAAA;UAAAnD,OAAA,EAAAA,OAAA;UAAAD,MAAA,EAAAA;QAAA;QACA,KAAAwF,QAAA,CAAAD,KAAA;QACA;QACA;QACA;MACA;MAEA,KAAAnC,UAAA,GAAAA,UAAA;MACA,KAAAnD,OAAA,GAAAA,OAAA;MACA,KAAAqD,WAAA,GAAAA,WAAA;MACAuB,OAAA,CAAAC,GAAA,0BAAAxB,WAAA;MACA,KAAAgC,QAAA,GAAAA,QAAA;MACA,KAAAtF,MAAA,GAAAA,MAAA;MACA6E,OAAA,CAAAC,GAAA,gBAAA9E,MAAA;MACA,KAAAyF,YAAA;;MAEA;MACA,KAAAC,cAAA;IACA;IAEAC,0BAAA,WAAAA,2BAAA;MAAA,IAAAC,KAAA;MAGA,IAAAC,UAAA;QACA7F,MAAA,OAAAqD,YAAA,CAAAyC;MACA;MAEA,IAAAC,sCAAA,EAAAF,UAAA,EAAAG,IAAA,WAAAC,GAAA;QACApB,OAAA,CAAAC,GAAA,qCAAAmB,GAAA;QACA,IAAAA,GAAA,CAAAC,IAAA;UACAN,KAAA,CAAA5B,kBAAA,CAAAZ,UAAA,GAAA6C,GAAA,CAAAE,IAAA,IAAA3D,EAAA;UACAoD,KAAA,CAAA5B,kBAAA,CAAA/D,OAAA,GAAAgG,GAAA,CAAAE,IAAA,IAAAlG,OAAA;UACA2F,KAAA,CAAA5B,kBAAA,CAAAhE,MAAA,GAAAiG,GAAA,CAAAE,IAAA,IAAAnG,MAAA;UACA4F,KAAA,CAAA5B,kBAAA,CAAAV,WAAA,GAAA2C,GAAA,CAAAE,IAAA,IAAA7C,WAAA;UACAsC,KAAA,CAAA5B,kBAAA,CAAAsB,QAAA,GAAAW,GAAA,CAAAE,IAAA,IAAAb,QAAA;QACA;UACAM,KAAA,CAAAJ,QAAA,CAAAD,KAAA,CAAAU,GAAA,CAAAG,OAAA;QACA;MACA,GAAAC,KAAA,WAAAC,GAAA;QACAzB,OAAA,CAAAU,KAAA,4CAAAe,GAAA;QACAV,KAAA,CAAAJ,QAAA,CAAAD,KAAA;QACA,MAAAe,GAAA;MACA;IAEA;IAEAb,YAAA,WAAAA,aAAA;MAAA,IAAAc,MAAA;MACA,KAAAC,MAAA,CAAAC,OAAA,CAAAC,KAAA,CAAAC,OAAA,WAAApC,IAAA;QACA,IAAAA,IAAA;UACAgC,MAAA,CAAApD,SAAA;QACA;MACA;MACA0B,OAAA,CAAAC,GAAA,mBAAA3B,SAAA;IACA;IACAuC,cAAA,WAAAA,eAAA;MAAA,IAAAkB,MAAA;MAAA,WAAAC,kBAAA,CAAAC,OAAA,mBAAAC,aAAA,CAAAD,OAAA,IAAAE,CAAA,UAAAC,QAAA;QAAA,IAAAC,EAAA;QAAA,WAAAH,aAAA,CAAAD,OAAA,IAAAK,CAAA,WAAAC,QAAA;UAAA,kBAAAA,QAAA,CAAAC,CAAA;YAAA;cAAAD,QAAA,CAAAE,CAAA;cAAAF,QAAA,CAAAC,CAAA;cAAA,OAGAT,MAAA,CAAAW,WAAA;YAAA;cAAAH,QAAA,CAAAC,CAAA;cAAA,OACAT,MAAA,CAAAY,mBAAA,CAAAZ,MAAA,CAAA5G,MAAA;YAAA;cAAAoH,QAAA,CAAAC,CAAA;cAAA,OACAT,MAAA,CAAAa,WAAA,CAAAb,MAAA,CAAA3G,OAAA;YAAA;cAEA;cACA2G,MAAA,CAAAc,wBAAA;;cAEA;cACAd,MAAA,CAAAe,cAAA,CAAAf,MAAA,CAAA5G,MAAA;cACA4G,MAAA,CAAAgB,cAAA;;cAEA;cACAhB,MAAA,CAAAjB,0BAAA;cAAAyB,QAAA,CAAAC,CAAA;cAAA;YAAA;cAAAD,QAAA,CAAAE,CAAA;cAAAJ,EAAA,GAAAE,QAAA,CAAAS,CAAA;cAEAhD,OAAA,CAAAU,KAAA,6BAAA2B,EAAA;cACAN,MAAA,CAAApB,QAAA,CAAAD,KAAA;YAAA;cAAA,OAAA6B,QAAA,CAAAU,CAAA;UAAA;QAAA,GAAAb,OAAA;MAAA;IAEA;IAEAS,wBAAA,WAAAA,yBAAA;MAAA,IAAAK,MAAA;MACA;MACA,KAAA9E,aAAA,CAAA0D,OAAA,WAAApC,IAAA;QACAA,IAAA,CAAAyD,iBAAA,GAAAzD,IAAA,CAAA0D,OAAA;QACApD,OAAA,CAAAC,GAAA,kBAAAiD,MAAA,CAAAtE,QAAA,CAAA6B,QAAA;QACA,IAAAyC,MAAA,CAAAtE,QAAA,CAAA6B,QAAA,SAAAyC,MAAA,CAAAtE,QAAA,CAAA6B,QAAA;UACAf,IAAA,CAAA2D,mBAAA,GAAA3D,IAAA,CAAA0D,OAAA;QACA;MACA;MAEA,IAAArG,mBAAA,QAAAqB,aAAA,CAAAqB,GAAA,WAAAC,IAAA;QAAA,OAAAA,IAAA,CAAAC,YAAA;MAAA,GAAAC,IAAA;MACA,IAAAC,aAAA,QAAAzB,aAAA,CAAAqB,GAAA,WAAAC,IAAA;QAAA,OAAAA,IAAA,CAAAI,YAAA;MAAA,GAAAF,IAAA;MACA;MACA,KAAA3E,kBAAA;QACAC,WAAA,OAAAsD,YAAA,CAAAtD,WAAA;QACAoI,KAAA,OAAA9E,YAAA,CAAA8E,KAAA;QACAC,QAAA,OAAA/E,YAAA,CAAA+E,QAAA;QACAC,UAAA,OAAAhF,YAAA,CAAAgF,UAAA;QACAC,IAAA,OAAAjF,YAAA,CAAAiF,IAAA;QACAtI,MAAA,OAAAA,MAAA;QACAC,OAAA,OAAAA,OAAA;QACAC,MAAA,OAAAmD,YAAA,CAAAnD,MAAA;QACAE,eAAA;QACAC,aAAA,MAAAkI,IAAA;QACAjI,WAAA;QACAC,UAAA;QACAC,MAAA;QACAC,KAAA;QACAC,SAAA;QACAC,WAAA;QACAC,MAAA;QACAC,UAAA;QACAC,KAAA;QACAC,MAAA;QACAC,UAAA;QACAC,MAAA,OAAAoC,YAAA,CAAApC,MAAA;QAAA;QACAW,mBAAA,EAAAA,mBAAA;QACA8C,aAAA,EAAAA,aAAA;QACA7C,aAAA,OAAA4B,QAAA,CAAA5B,aAAA;QACAC,cAAA,OAAA2B,QAAA,CAAA3B,cAAA;QACAC,YAAA;QAAA;QACAC,WAAA;QAAA;QACA;QACAd,mBAAA;QACAC,kBAAA;QACAC,cAAA;QACAC,iBAAA;QACAC,aAAA;QACAC,mBAAA;QACAC,cAAA;QACAC,kBAAA;QACAC,aAAA;QACAC,cAAA;QACAM,YAAA;MACA;IACA;IAEAuG,aAAA,WAAAA,cAAA;MACA,IAAAC,YAAA;MACAC,MAAA,CAAAC,IAAA,CAAAF,YAAA;IACA;IACA;IACAG,mBAAA,WAAAA,oBAAA;MAAA,IAAAC,MAAA;MAAA,WAAAhC,kBAAA,CAAAC,OAAA,mBAAAC,aAAA,CAAAD,OAAA,IAAAE,CAAA,UAAA8B,SAAA;QAAA,IAAAC,SAAA,EAAA9C,GAAA,EAAA+C,SAAA,EAAAC,KAAA,EAAA1E,IAAA,EAAA2E,iBAAA,EAAAC,QAAA,EAAAC,GAAA,EAAAC,GAAA;QAAA,WAAAtC,aAAA,CAAAD,OAAA,IAAAK,CAAA,WAAAmC,SAAA;UAAA,kBAAAA,SAAA,CAAAjC,CAAA;YAAA;cAAAiC,SAAA,CAAAhC,CAAA;cAEAyB,SAAA;gBACAlH,aAAA,EAAAgH,MAAA,CAAApF,QAAA,CAAA5B,aAAA;gBACAyD,QAAA;cACA;cACAT,OAAA,CAAAC,GAAA,cAAAiE,SAAA;cAAAO,SAAA,CAAAjC,CAAA;cAAA,OAEA,IAAAkC,0BAAA,EAAAR,SAAA;YAAA;cAAA9C,GAAA,GAAAqD,SAAA,CAAAzB,CAAA;cACAhD,OAAA,CAAAC,GAAA,yBAAAmB,GAAA;cAAA,MACAA,GAAA,CAAAC,IAAA;gBAAAoD,SAAA,CAAAjC,CAAA;gBAAA;cAAA;cACAwB,MAAA,CAAAhF,oBAAA,GAAAoC,GAAA,CAAAE,IAAA;cACA;cAAA6C,SAAA,OAAAQ,2BAAA,CAAA1C,OAAA,EACA+B,MAAA,CAAAhF,oBAAA;cAAAyF,SAAA,CAAAhC,CAAA;cAAA0B,SAAA,CAAAS,CAAA;YAAA;cAAA,KAAAR,KAAA,GAAAD,SAAA,CAAA3B,CAAA,IAAAqC,IAAA;gBAAAJ,SAAA,CAAAjC,CAAA;gBAAA;cAAA;cAAA9C,IAAA,GAAA0E,KAAA,CAAAU,KAAA;cACA9E,OAAA,CAAAC,GAAA,SAAAP,IAAA;cACA2E,iBAAA;gBACAjJ,OAAA,EAAAsE,IAAA,CAAAtE;cACA;cAAAqJ,SAAA,CAAAjC,CAAA;cAAA,OACA,IAAAuC,sBAAA,EAAAV,iBAAA;YAAA;cAAAC,QAAA,GAAAG,SAAA,CAAAzB,CAAA;cACA,IAAAsB,QAAA,CAAAjD,IAAA;gBACArB,OAAA,CAAAC,GAAA,qBAAAqE,QAAA;gBACA5E,IAAA,CAAAC,YAAA,GAAA2E,QAAA,CAAAhD,IAAA,IAAA3B,YAAA;gBACAD,IAAA,CAAAI,YAAA,GAAAwE,QAAA,CAAAhD,IAAA,IAAAxB,YAAA;cACA;gBACAkE,MAAA,CAAArD,QAAA,CAAAD,KAAA,CAAA4D,QAAA,CAAA/C,OAAA;cACA;YAAA;cAAAkD,SAAA,CAAAjC,CAAA;cAAA;YAAA;cAAAiC,SAAA,CAAAjC,CAAA;cAAA;YAAA;cAAAiC,SAAA,CAAAhC,CAAA;cAAA8B,GAAA,GAAAE,SAAA,CAAAzB,CAAA;cAAAmB,SAAA,CAAAa,CAAA,CAAAT,GAAA;YAAA;cAAAE,SAAA,CAAAhC,CAAA;cAAA0B,SAAA,CAAAc,CAAA;cAAA,OAAAR,SAAA,CAAAQ,CAAA;YAAA;cAAAR,SAAA,CAAAjC,CAAA;cAAA;YAAA;cAGAwB,MAAA,CAAArD,QAAA,CAAAD,KAAA,CAAAU,GAAA,CAAAG,OAAA;YAAA;cAAAkD,SAAA,CAAAjC,CAAA;cAAA;YAAA;cAAAiC,SAAA,CAAAhC,CAAA;cAAA+B,GAAA,GAAAC,SAAA,CAAAzB,CAAA;cAGAhD,OAAA,CAAAU,KAAA,gCAAA8D,GAAA;cACAR,MAAA,CAAArD,QAAA,CAAAD,KAAA;cAAA,MAAA8D,GAAA;YAAA;cAAA,OAAAC,SAAA,CAAAxB,CAAA;UAAA;QAAA,GAAAgB,QAAA;MAAA;IAGA;IACAiB,iBAAA,WAAAA,kBAAA1E,KAAA;MACA,KAAAzB,sBAAA,GAAAyB,KAAA;MAEA,SAAAzB,sBAAA;QACAiB,OAAA,CAAAC,GAAA,4BAAApB,kBAAA;QAEA,KAAAC,0BAAA,QAAAD,kBAAA,CAAAsG,MAAA,WAAAzF,IAAA;UAAA,OACAA,IAAA,CAAAoF,KAAA,CAAAM,QAAA,CAAA5E,KAAA;QAAA,CACA;MACA;QAEA,KAAA1B,0BAAA,QAAAD,kBAAA;MACA;IACA;IACAkE,cAAA,WAAAA,eAAA;MAAA,IAAAsC,MAAA;MACA,IAAAC,oBAAA,IAAAnE,IAAA,WAAAC,GAAA;QACApB,OAAA,CAAAC,GAAA,mBAAAmB,GAAA;QACA,IAAAA,GAAA,CAAAC,IAAA;UACAgE,MAAA,CAAAxG,kBAAA,GAAAuC,GAAA,CAAAE,IAAA,CAAA7B,GAAA,WAAAC,IAAA;YAAA;cACAoF,KAAA,EAAApF,IAAA,CAAA6F,WAAA;cACAC,KAAA,EAAA9F,IAAA,CAAA6F;YACA;UAAA;UACAF,MAAA,CAAAvG,0BAAA,GAAAuG,MAAA,CAAAxG,kBAAA;QACA;UACAwG,MAAA,CAAA1E,QAAA,CAAAD,KAAA,CAAAU,GAAA,CAAAG,OAAA;QACA;MACA,GAAAC,KAAA,WAAAC,GAAA;QACAzB,OAAA,CAAAU,KAAA,0BAAAe,GAAA;QACA4D,MAAA,CAAA1E,QAAA,CAAAD,KAAA;MACA;IACA;IACAkC,WAAA,WAAAA,YAAAxH,OAAA;MAAA,IAAAqK,MAAA;MAAA,WAAAzD,kBAAA,CAAAC,OAAA,mBAAAC,aAAA,CAAAD,OAAA,IAAAE,CAAA,UAAAuD,SAAA;QAAA,IAAApB,QAAA,EAAAqB,GAAA;QAAA,WAAAzD,aAAA,CAAAD,OAAA,IAAAK,CAAA,WAAAsD,SAAA;UAAA,kBAAAA,SAAA,CAAApD,CAAA;YAAA;cAAAoD,SAAA,CAAAnD,CAAA;cAAAmD,SAAA,CAAApD,CAAA;cAAA,OAEA,IAAAqD,gBAAA,EAAAzK,OAAA;YAAA;cAAAkJ,QAAA,GAAAsB,SAAA,CAAA5C,CAAA;cACAhD,OAAA,CAAAC,GAAA,eAAAqE,QAAA;cACAmB,MAAA,CAAA7G,QAAA,GAAA0F,QAAA,CAAAvJ,IAAA;cAAA6K,SAAA,CAAApD,CAAA;cAAA,OACAiD,MAAA,CAAA1B,mBAAA;YAAA;cAAA,OAAA6B,SAAA,CAAA3C,CAAA,IACAqB,QAAA;YAAA;cAAAsB,SAAA,CAAAnD,CAAA;cAAAkD,GAAA,GAAAC,SAAA,CAAA5C,CAAA;cAEAhD,OAAA,CAAAU,KAAA,uBAAAiF,GAAA;cAAA,MAAAA,GAAA;YAAA;cAAA,OAAAC,SAAA,CAAA3C,CAAA;UAAA;QAAA,GAAAyC,QAAA;MAAA;IAGA;IACAI,wBAAA,WAAAA,yBAAA;MACA,IAAA/I,mBAAA,QAAAqB,aAAA,CAAAqB,GAAA,WAAAC,IAAA;QAAA,OAAAA,IAAA,CAAAC,YAAA;MAAA,GAAAC,IAAA;MACA;MACA,KAAA3E,kBAAA;QACAC,WAAA,OAAAsD,YAAA,CAAAtD,WAAA;QACAoI,KAAA,OAAA9E,YAAA,CAAA8E,KAAA;QACAC,QAAA,OAAA/E,YAAA,CAAA+E,QAAA;QACAE,IAAA,OAAAjF,YAAA,CAAAiF,IAAA;QACAtI,MAAA,OAAAA,MAAA;QACAC,OAAA,OAAAA,OAAA;QACAC,MAAA,OAAAmD,YAAA,CAAAnD,MAAA;QACAE,eAAA;QACAC,aAAA,MAAAkI,IAAA;QACAjI,WAAA;QACAC,UAAA;QACAC,MAAA;QACAC,KAAA;QACAC,SAAA;QACAC,WAAA;QACAC,MAAA;QACAC,UAAA;QACAC,KAAA;QACAC,MAAA;QACAC,UAAA;QACAC,MAAA,OAAAoC,YAAA,CAAApC,MAAA;QAAA;QACAW,mBAAA,EAAAA,mBAAA;QACAC,aAAA,OAAA4B,QAAA,CAAA5B,aAAA;QACAC,cAAA,OAAA2B,QAAA,CAAA3B,cAAA;QACAC,YAAA;QAAA;QACAC,WAAA;QAAA;QACA;QACAd,mBAAA;QACAC,kBAAA;QACAC,cAAA;QACAC,iBAAA;QACAC,aAAA;QACAC,mBAAA;QACAC,cAAA;QACAC,kBAAA;QACAC,aAAA;QACAC,cAAA;QACAM,YAAA;MACA;MACA,KAAApC,2BAAA;IACA;IACA+K,oBAAA,WAAAA,qBAAA;MAAA,IAAAC,MAAA;MACA,SAAA/K,kBAAA,CAAAiC,YAAA;QACA,SAAAjC,kBAAA,CAAAkC,WAAA,iBAAAlC,kBAAA,CAAAkC,WAAA;UACA,KAAAwD,QAAA,CAAAD,KAAA;UACA;QACA;MACA;MAEA,IAAAuF,UAAA;MACA,SAAAzH,YAAA,CAAA0H,cAAA;QACA;QACAD,UAAA;UACAE,SAAA;YACAxI,EAAA,OAAAY,UAAA;YACApD,MAAA,OAAAA,MAAA;YACAC,OAAA,OAAAA,OAAA;YACA;YACAK,WAAA,OAAAR,kBAAA,CAAAQ,WAAA;YACAC,UAAA,OAAAT,kBAAA,CAAAS,UAAA;YACAC,MAAA,OAAAV,kBAAA,CAAAW,KAAA,QAAAX,kBAAA,CAAAY,SAAA;YACAC,WAAA,OAAAb,kBAAA,CAAAa,WAAA;YACAC,MAAA,OAAAd,kBAAA,CAAAc,MAAA;YACAC,UAAA,OAAAf,kBAAA,CAAAe,UAAA;YACAC,KAAA,OAAAhB,kBAAA,CAAAgB,KAAA;YACAC,MAAA,OAAAjB,kBAAA,CAAAiB,MAAA;YACAE,MAAA,OAAAoC,YAAA,CAAApC,MAAA;YACAoH,UAAA,OAAAhF,YAAA,CAAAgF,UAAA;YACA0C,cAAA;YAEA9I,YAAA,OAAAnC,kBAAA,CAAAmC,YAAA;YAAA;;YAEA;YACAf,mBAAA,OAAApB,kBAAA,CAAAoB,mBAAA;YACAC,kBAAA,OAAArB,kBAAA,CAAAqB,kBAAA;YACAC,cAAA,OAAAtB,kBAAA,CAAAwB,aAAA,QAAAxB,kBAAA,CAAAuB,iBAAA;YACAE,mBAAA,OAAAzB,kBAAA,CAAAyB,mBAAA;YACAC,cAAA,OAAA1B,kBAAA,CAAA0B,cAAA;YACAC,kBAAA,OAAA3B,kBAAA,CAAA2B,kBAAA;YACAC,aAAA,OAAA5B,kBAAA,CAAA4B,aAAA;YACAC,cAAA,OAAA7B,kBAAA,CAAA6B,cAAA;YACA;YACA;YACAX,UAAA;YACAb,QAAA,OAAAkD,YAAA,CAAAlD;UACA;UACA4I,SAAA,OAAAtF,QAAA;UACAwH,iBAAA,OAAAhI,aAAA;QACA;MACA;QACA;QACA6H,UAAA;UACAE,SAAA;YACAxI,EAAA,OAAAY,UAAA;YACApD,MAAA,OAAAA,MAAA;YACAC,OAAA,OAAAA,OAAA;YACA;YACAK,WAAA,OAAAR,kBAAA,CAAAQ,WAAA;YACAC,UAAA,OAAAT,kBAAA,CAAAS,UAAA;YACAC,MAAA,OAAAV,kBAAA,CAAAW,KAAA,QAAAX,kBAAA,CAAAY,SAAA;YACAC,WAAA,OAAAb,kBAAA,CAAAa,WAAA;YACAC,MAAA,OAAAd,kBAAA,CAAAc,MAAA;YACAC,UAAA,OAAAf,kBAAA,CAAAe,UAAA;YACAC,KAAA,OAAAhB,kBAAA,CAAAgB,KAAA;YACAC,MAAA,OAAAjB,kBAAA,CAAAiB,MAAA;YACAE,MAAA,OAAAoC,YAAA,CAAApC,MAAA;YACAoH,UAAA,OAAAhF,YAAA,CAAAgF,UAAA;YACA0C,cAAA;YAAA;YACA9I,YAAA,OAAAnC,kBAAA,CAAAmC,YAAA;YAAA;YACA6D,kBAAA,OAAAhG,kBAAA,CAAAkC,WAAA;YACA;YACAd,mBAAA,OAAApB,kBAAA,CAAAoB,mBAAA;YACAC,kBAAA,OAAArB,kBAAA,CAAAqB,kBAAA;YACAC,cAAA,OAAAtB,kBAAA,CAAAwB,aAAA,QAAAxB,kBAAA,CAAAuB,iBAAA;YACAE,mBAAA,OAAAzB,kBAAA,CAAAyB,mBAAA;YACAC,cAAA,OAAA1B,kBAAA,CAAA0B,cAAA;YACAC,kBAAA,OAAA3B,kBAAA,CAAA2B,kBAAA;YACAC,aAAA,OAAA5B,kBAAA,CAAA4B,aAAA;YACAC,cAAA,OAAA7B,kBAAA,CAAA6B,cAAA;YACA;YACA;YACAX,UAAA;YACAb,QAAA,OAAAkD,YAAA,CAAAlD;UACA;UACA4I,SAAA,OAAAtF,QAAA;UACAwH,iBAAA,OAAAhI,aAAA;QACA;MACA;MAIA,IAAAiI,gBAAA;QACA;QACAjL,OAAA,OAAAH,kBAAA,CAAAkC,WAAA;QACA7B,QAAA;QACAa,UAAA;QACAoH,QAAA,OAAA/E,YAAA,CAAA+E,QAAA;QACA+C,YAAA,OAAA9H,YAAA,CAAA8H,YAAA;QACAjL,MAAA,OAAAmD,YAAA,CAAAnD,MAAA;QACAmI,UAAA,OAAAhF,YAAA,CAAAgF,UAAA;QACA+C,GAAA,OAAA/H,YAAA,CAAA+H,GAAA;QACAC,WAAA,OAAAhI,YAAA,CAAAgI,WAAA;QACAC,QAAA,OAAAjI,YAAA,CAAAiI,QAAA;QACArK,MAAA,OAAAoC,YAAA,CAAApC,MAAA;QACAsK,wBAAA,OAAAlI,YAAA,CAAAkI,wBAAA;QACAC,OAAA,OAAAnI,YAAA,CAAAmI,OAAA;QACAC,iBAAA,OAAApI,YAAA,CAAAoI,iBAAA;QACAC,gBAAA,OAAArI,YAAA,CAAAqI,gBAAA;QACA3L,WAAA,OAAAsD,YAAA,CAAAtD,WAAA;QACAgL,cAAA;MACA;MAEA,IAAAY,4BAAA,QAAA1I,aAAA;MAEA,SAAAnD,kBAAA,CAAAiC,YAAA,iBAAAjC,kBAAA,CAAAkC,WAAA,iBAAAlC,kBAAA,CAAAkC,WAAA;QACA8I,UAAA,CAAAE,SAAA,CAAAD,cAAA;QACAD,UAAA,CAAAI,gBAAA,GAAAA,gBAAA;QACAJ,UAAA,CAAAa,4BAAA,GAAAA,4BAAA;MACA;MAEA,IAAAC,kBAAA,EAAAd,UAAA,EAAA9E,IAAA,WAAAC,GAAA;QACApB,OAAA,CAAAC,GAAA,iBAAAmB,GAAA;QACA,IAAAA,GAAA,CAAAC,IAAA;UACA2E,MAAA,CAAArF,QAAA,CAAAqG,OAAA;UACAhB,MAAA,CAAAhL,2BAAA;UACAgL,MAAA,CAAAlD,cAAA,CAAAkD,MAAA,CAAA7K,MAAA;UACA6K,MAAA,CAAAtD,WAAA;QACA;UACA;UACAsD,MAAA,CAAArF,QAAA,CAAAD,KAAA,CAAAU,GAAA,CAAAG,OAAA;QACA;MACA,GAAAC,KAAA,WAAAC,GAAA;QACAzB,OAAA,CAAAU,KAAA,8BAAAe,GAAA;QACAuE,MAAA,CAAArF,QAAA,CAAAD,KAAA;MACA;IACA;IAEAuG,qBAAA,WAAAA,sBAAA;MAAA,IAAAC,MAAA;MAEA;MACA,IAAArF,KAAA,QAAAF,MAAA,CAAAC,OAAA,CAAAC,KAAA;MACA,KAAAA,KAAA,CAAAuD,QAAA;QACA,KAAAzE,QAAA,CAAAD,KAAA;QACA;MACA;MACA;MACA,IAAAuF,UAAA;QACAE,SAAA;UACA;UACAxI,EAAA,OAAAY,UAAA;UACApD,MAAA,OAAAA,MAAA;UACAC,OAAA,OAAAA,OAAA;UACA;UACAiB,mBAAA,OAAApB,kBAAA,CAAAoB,mBAAA;UACAC,kBAAA,OAAArB,kBAAA,CAAAqB,kBAAA;UACAC,cAAA,OAAAtB,kBAAA,CAAAwB,aAAA,QAAAxB,kBAAA,CAAAuB,iBAAA;UACAE,mBAAA,OAAAzB,kBAAA,CAAAyB,mBAAA;UACAC,cAAA,OAAA1B,kBAAA,CAAA0B,cAAA;UACAC,kBAAA,OAAA3B,kBAAA,CAAA2B,kBAAA;UACAC,aAAA,OAAA5B,kBAAA,CAAA4B,aAAA;UACAC,cAAA,OAAA7B,kBAAA,CAAA6B,cAAA;UACA;UACAX,UAAA;UACAC,MAAA,OAAAoC,YAAA,CAAApC;QACA;QACA8H,SAAA,OAAAtF,QAAA;QACAwH,iBAAA,OAAAhI,aAAA;MACA;MAEA,IAAA+I,oBAAA,EAAAlB,UAAA,EAAA9E,IAAA,WAAAC,GAAA;QACApB,OAAA,CAAAC,GAAA,mBAAAmB,GAAA;QACA,IAAAA,GAAA,CAAAC,IAAA;UACA6F,MAAA,CAAAvG,QAAA,CAAAqG,OAAA;UACAE,MAAA,CAAAlM,2BAAA;UACAkM,MAAA,CAAApE,cAAA,CAAAoE,MAAA,CAAA/L,MAAA;UACA+L,MAAA,CAAAxE,WAAA;QACA;UACA;UACAwE,MAAA,CAAAvG,QAAA,CAAAD,KAAA,CAAAU,GAAA,CAAAG,OAAA;QACA;MACA,GAAAC,KAAA,WAAAC,GAAA;QACAzB,OAAA,CAAAU,KAAA,8BAAAe,GAAA;QACAyF,MAAA,CAAAvG,QAAA,CAAAD,KAAA;MACA;IACA;IAEA0G,oBAAA,WAAAA,qBAAA;MAAA,IAAAC,MAAA;MACA,SAAA5J,iBAAA;QACA,KAAAkD,QAAA,CAAA2G,OAAA;QACA;MACA;;MAGA;MACA;MACA,IAAAC,YAAA;MACAA,YAAA,CAAAC,OAAA;MACAD,YAAA,CAAApM,MAAA,QAAAA,MAAA;MACAoM,YAAA,CAAAnM,OAAA,QAAAA,OAAA;MACAmM,YAAA,CAAAE,IAAA;MAGA,IAAAC,eAAA;MACA;MACAA,eAAA,CAAA/J,EAAA,QAAAa,YAAA,CAAAb,EAAA;MACA+J,eAAA,CAAAnM,eAAA;MACAmM,eAAA,CAAAlM,aAAA,OAAAkI,IAAA;MACAgE,eAAA,CAAAvL,UAAA;MAEA,IAAAwL,KAAA;MACAA,KAAA,CAAAC,gBAAA,QAAAxJ,aAAA;MACAuJ,KAAA,CAAAE,QAAA,GAAAN,YAAA;MACAI,KAAA,CAAAxB,SAAA,GAAAuB,eAAA;MACAC,KAAA,CAAAlJ,WAAA,QAAAA,WAAA;MAEA,IAAAqJ,kDAAA,EAAAH,KAAA,EAAAxG,IAAA,WAAAC,GAAA;QACApB,OAAA,CAAAC,GAAA,iDAAAmB,GAAA;QACA,IAAAA,GAAA,CAAAC,IAAA;UACAgG,MAAA,CAAA1G,QAAA,CAAAqG,OAAA;UACAK,MAAA,CAAAvE,cAAA,CAAAuE,MAAA,CAAAlM,MAAA;UACAkM,MAAA,CAAA3E,WAAA;QACA;UACA;UACA2E,MAAA,CAAA1G,QAAA,CAAAD,KAAA,CAAAU,GAAA,CAAAG,OAAA;QACA;MACA,GAAAC,KAAA,WAAAC,GAAA;QACAzB,OAAA,CAAAU,KAAA,gCAAAe,GAAA;QACA4F,MAAA,CAAA1G,QAAA,CAAAD,KAAA;MACA;IACA;IAGAqH,oBAAA,WAAAA,qBAAA;MAAA,IAAAC,MAAA;MACA,SAAAxK,iBAAA;QACA,KAAAmD,QAAA,CAAA2G,OAAA;QACA;MACA;MAEA,IAAAC,YAAA;MACAA,YAAA,CAAAC,OAAA;MACAD,YAAA,CAAApM,MAAA,QAAAA,MAAA;MACAoM,YAAA,CAAAnM,OAAA,QAAAA,OAAA;MACAmM,YAAA,CAAAE,IAAA;MAIA,IAAAQ,eAAA;MACAA,eAAA,CAAAtK,EAAA,QAAAa,YAAA,CAAAb,EAAA;MACA,SAAAa,YAAA,CAAAlD,QAAA;QACA2M,eAAA,CAAA9L,UAAA;QACA8L,eAAA,CAAAC,SAAA,OAAAxE,IAAA,GAAAyE,WAAA,GAAAC,KAAA,QAAAC,OAAA;QACA;MACA,gBAAA7J,YAAA,CAAAlD,QAAA,cAAAmD,WAAA;QACAwJ,eAAA,CAAA9L,UAAA;QACA8L,eAAA,CAAAK,SAAA,OAAA5E,IAAA,GAAAyE,WAAA,GAAAC,KAAA,QAAAC,OAAA;QACA;MACA,gBAAA7J,YAAA,CAAAlD,QAAA,cAAAmD,WAAA;QACAwJ,eAAA,CAAA9L,UAAA;QACA8L,eAAA,CAAAK,SAAA,OAAA5E,IAAA,GAAAyE,WAAA,GAAAC,KAAA,QAAAC,OAAA;QACA;MACA,gBAAA7J,YAAA,CAAAlD,QAAA,cAAAkD,YAAA,CAAArC,UAAA;QACA8L,eAAA,CAAA9L,UAAA;QACA8L,eAAA,CAAAC,SAAA,OAAAxE,IAAA,GAAAyE,WAAA,GAAAC,KAAA,QAAAC,OAAA;QACA;MACA,gBAAA7J,YAAA,CAAAlD,QAAA,cAAAmD,WAAA,cAAAD,YAAA,CAAArC,UAAA;QACA8L,eAAA,CAAA9L,UAAA;QACA8L,eAAA,CAAAK,SAAA,OAAA5E,IAAA,GAAAyE,WAAA,GAAAC,KAAA,QAAAC,OAAA;QACA;MACA,gBAAA7J,YAAA,CAAAlD,QAAA,cAAAmD,WAAA,cAAAD,YAAA,CAAArC,UAAA;QACA8L,eAAA,CAAA9L,UAAA;QACA8L,eAAA,CAAAK,SAAA,OAAA5E,IAAA,GAAAyE,WAAA,GAAAC,KAAA,QAAAC,OAAA;QACA;MACA;MAEA,IAAAV,KAAA;MACAA,KAAA,CAAAC,gBAAA,QAAAxJ,aAAA;MACAuJ,KAAA,CAAAE,QAAA,GAAAN,YAAA;MACAI,KAAA,CAAAxB,SAAA,GAAA8B,eAAA;MACAN,KAAA,CAAAlJ,WAAA,QAAAA,WAAA;MAEA,IAAAqJ,kDAAA,EAAAH,KAAA,EAAAxG,IAAA,WAAAC,GAAA;QACApB,OAAA,CAAAC,GAAA,iDAAAmB,GAAA;QACA,IAAAA,GAAA,CAAAC,IAAA;UACA2G,MAAA,CAAArH,QAAA,CAAAqG,OAAA;UACAgB,MAAA,CAAAlF,cAAA,CAAAkF,MAAA,CAAA7M,MAAA;UACA6M,MAAA,CAAAtF,WAAA;QACA;UACA;UACAsF,MAAA,CAAArH,QAAA,CAAAD,KAAA,CAAAU,GAAA,CAAAG,OAAA;QACA;MACA,GAAAC,KAAA,WAAAC,GAAA;QACAzB,OAAA,CAAAU,KAAA,gCAAAe,GAAA;QACAuG,MAAA,CAAArH,QAAA,CAAAD,KAAA;MACA;;MAEA;MACA;MACA;MACA;MACA;MACA;MACA6G,YAAA,CAAApM,MAAA,QAAAA,MAAA;MACAoM,YAAA,CAAAnM,OAAA,QAAAA,OAAA;MACAmM,YAAA,CAAAE,IAAA;MACA;MACA;;MAEA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;;MAEA;MACA;MACA;IAEA;IAEAc,2BAAA,WAAAA,4BAAA;MAAA,IAAAC,OAAA;MACA;MACA,IAAA3G,KAAA,QAAAF,MAAA,CAAAC,OAAA,CAAAC,KAAA;MACA,KAAAA,KAAA,CAAAuD,QAAA;QACA,KAAAzE,QAAA,CAAAD,KAAA;QACA;MACA;MAEA,IAAA6G,YAAA;MACAA,YAAA,CAAAC,OAAA;MACAD,YAAA,CAAApM,MAAA,QAAAA,MAAA;MACAoM,YAAA,CAAAnM,OAAA,QAAAA,OAAA;MACA,SAAAoD,YAAA,CAAArC,UAAA;QACAoL,YAAA,CAAAE,IAAA,yBAAArJ,aAAA,CAAAqB,GAAA,WAAAC,IAAA;UAAA,OAAAA,IAAA,CAAAC,YAAA;QAAA,GAAAC,IAAA;MACA;QACA2H,YAAA,CAAAE,IAAA,yBAAArJ,aAAA,CAAAqB,GAAA,WAAAC,IAAA;UAAA,OAAAA,IAAA,CAAAC,YAAA;QAAA,GAAAC,IAAA;MACA;MAEA,IAAAqI,eAAA;MACAA,eAAA,CAAAtK,EAAA,QAAAa,YAAA,CAAAb,EAAA;MACA,SAAAa,YAAA,CAAAlD,QAAA;QACA2M,eAAA,CAAA9L,UAAA;QACA8L,eAAA,CAAAC,SAAA,OAAAxE,IAAA,GAAAyE,WAAA,GAAAC,KAAA,QAAAC,OAAA;QACA;MACA,gBAAA7J,YAAA,CAAAlD,QAAA,cAAAmD,WAAA;QACAwJ,eAAA,CAAA9L,UAAA;QACA8L,eAAA,CAAAK,SAAA,OAAA5E,IAAA,GAAAyE,WAAA,GAAAC,KAAA,QAAAC,OAAA;QACA;MACA,gBAAA7J,YAAA,CAAAlD,QAAA,cAAAmD,WAAA;QACAwJ,eAAA,CAAA9L,UAAA;QACA8L,eAAA,CAAAK,SAAA,OAAA5E,IAAA,GAAAyE,WAAA,GAAAC,KAAA,QAAAC,OAAA;QACA;MACA,gBAAA7J,YAAA,CAAAlD,QAAA,cAAAkD,YAAA,CAAArC,UAAA;QACA8L,eAAA,CAAA9L,UAAA;QACA8L,eAAA,CAAAC,SAAA,OAAAxE,IAAA,GAAAyE,WAAA,GAAAC,KAAA,QAAAC,OAAA;QACA;MACA,gBAAA7J,YAAA,CAAAlD,QAAA,cAAAmD,WAAA,cAAAD,YAAA,CAAArC,UAAA;QACA8L,eAAA,CAAA9L,UAAA;QACA8L,eAAA,CAAAK,SAAA,OAAA5E,IAAA,GAAAyE,WAAA,GAAAC,KAAA,QAAAC,OAAA;QACA;MACA,gBAAA7J,YAAA,CAAAlD,QAAA,cAAAmD,WAAA,cAAAD,YAAA,CAAArC,UAAA;QACA8L,eAAA,CAAA9L,UAAA;QACA8L,eAAA,CAAAK,SAAA,OAAA5E,IAAA,GAAAyE,WAAA,GAAAC,KAAA,QAAAC,OAAA;QACA;MACA;MAEA,IAAAV,KAAA;MACAA,KAAA,CAAAC,gBAAA,QAAAxJ,aAAA;MACAuJ,KAAA,CAAAE,QAAA,GAAAN,YAAA;MACAI,KAAA,CAAAxB,SAAA,GAAA8B,eAAA;MACAN,KAAA,CAAAlJ,WAAA,QAAAA,WAAA;MAEA,IAAAqJ,kDAAA,EAAAH,KAAA,EAAAxG,IAAA,WAAAC,GAAA;QACApB,OAAA,CAAAC,GAAA,iDAAAmB,GAAA;QACA,IAAAA,GAAA,CAAAC,IAAA;UACAmH,OAAA,CAAA7H,QAAA,CAAAqG,OAAA;UACAwB,OAAA,CAAA1F,cAAA,CAAA0F,OAAA,CAAArN,MAAA;UACAqN,OAAA,CAAA9F,WAAA;QACA;UACA;UACA8F,OAAA,CAAA7H,QAAA,CAAAD,KAAA,CAAAU,GAAA,CAAAG,OAAA;QACA;MACA,GAAAC,KAAA,WAAAC,GAAA;QACAzB,OAAA,CAAAU,KAAA,gCAAAe,GAAA;QACA+G,OAAA,CAAA7H,QAAA,CAAAD,KAAA;MACA;MACA;IAEA;IACA;IACA+H,WAAA,WAAAA,YAAA;MACA,SAAAjK,YAAA,CAAAkK,aAAA;QACA,KAAAC,KAAA,CAAAC,MAAA,CAAAC,SAAA;QACA,IAAAC,QAAA,OAAAC,iBAAA,MAAAJ,KAAA,CAAAC,MAAA;UACAI,IAAA,OAAAxK,YAAA,CAAAkK,aAAA;UAAA;UACAO,KAAA;UACAC,MAAA;UACAC,SAAA;UACAC,UAAA;UACAC,YAAA,EAAAN,iBAAA,CAAAO,YAAA,CAAAC;QACA;MACA;IACA;IACAzG,cAAA,WAAAA,eAAA3H,MAAA;MAAA,IAAAqO,OAAA;MACA,IAAAC,OAAA;MACAA,OAAA,CAAAtO,MAAA,GAAAA,MAAA;MACA,IAAAuO,iBAAA,EAAAD,OAAA,EAAAtI,IAAA,WAAAmD,QAAA;QACAtE,OAAA,CAAAC,GAAA,gBAAAqE,QAAA;QACA;QACA,IAAAqF,IAAA,GAAArF,QAAA,CAAAhD,IAAA;QACA;QACA,IAAAsI,YAAA,GAAAD,IAAA,CAAAxE,MAAA,WAAAlF,GAAA;UAAA,OAAAA,GAAA,CAAAwH,IAAA,IAAAxH,GAAA,CAAAwH,IAAA,CAAArC,QAAA;QAAA;QACA,IAAAyE,SAAA,GAAAF,IAAA,CAAAxE,MAAA,WAAAlF,GAAA;UAAA,SAAAA,GAAA,CAAAwH,IAAA,IAAAxH,GAAA,CAAAwH,IAAA,CAAArC,QAAA;QAAA;QACA;QACAoE,OAAA,CAAAnL,QAAA,MAAAyL,MAAA,KAAAC,mBAAA,CAAA9H,OAAA,EAAA2H,YAAA,OAAAG,mBAAA,CAAA9H,OAAA,EAAA4H,SAAA;MACA;IAEA;IACAlH,mBAAA,WAAAA,oBAAAxH,MAAA;MAAA,IAAA6O,OAAA;MAAA,WAAAhI,kBAAA,CAAAC,OAAA,mBAAAC,aAAA,CAAAD,OAAA,IAAAE,CAAA,UAAA8H,SAAA;QAAA,IAAAC,aAAA,EAAA5F,QAAA,EAAA6F,GAAA;QAAA,WAAAjI,aAAA,CAAAD,OAAA,IAAAK,CAAA,WAAA8H,SAAA;UAAA,kBAAAA,SAAA,CAAA5H,CAAA;YAAA;cAAA4H,SAAA,CAAA3H,CAAA;cAEAzC,OAAA,CAAAC,GAAA;cACAiK,aAAA;cACAA,aAAA,CAAA/O,MAAA,GAAAA,MAAA;cAAAiP,SAAA,CAAA5H,CAAA;cAAA,OACA,IAAA6H,sBAAA,EAAAH,aAAA;YAAA;cAAA5F,QAAA,GAAA8F,SAAA,CAAApH,CAAA;cACAgH,OAAA,CAAA5L,aAAA,GAAAkG,QAAA,CAAAhD,IAAA;cACA;cACA0I,OAAA,CAAA5L,aAAA,CAAA0D,OAAA,WAAApC,IAAA;gBACAA,IAAA,CAAAyD,iBAAA,GAAAzD,IAAA,CAAA0D,OAAA;gBACApD,OAAA,CAAAC,GAAA,kBAAA+J,OAAA,CAAApL,QAAA,CAAA6B,QAAA;gBACA,IAAAuJ,OAAA,CAAApL,QAAA,CAAA6B,QAAA,SAAAuJ,OAAA,CAAApL,QAAA,CAAA6B,QAAA;kBACAf,IAAA,CAAA2D,mBAAA,GAAA3D,IAAA,CAAA0D,OAAA;gBACA;cACA;cACApD,OAAA,CAAAC,GAAA,kBAAA+J,OAAA,CAAA5L,aAAA;cAAA,OAAAgM,SAAA,CAAAnH,CAAA,IACAqB,QAAA;YAAA;cAAA8F,SAAA,CAAA3H,CAAA;cAAA0H,GAAA,GAAAC,SAAA,CAAApH,CAAA;cAEAhD,OAAA,CAAAU,KAAA,+BAAAyJ,GAAA;cAAA,MAAAA,GAAA;YAAA;cAAA,OAAAC,SAAA,CAAAnH,CAAA;UAAA;QAAA,GAAAgH,QAAA;MAAA;IAGA;IACAK,cAAA,WAAAA,eAAAC,GAAA;MACAA,GAAA,CAAAC,OAAA,GAAAC,IAAA,CAAAC,KAAA,CAAAD,IAAA,CAAAE,SAAA,CAAAJ,GAAA;MACA,KAAAtL,UAAA,GAAAsL,GAAA;MACA,KAAA/M,iBAAA;MACAwC,OAAA,CAAAC,GAAA,wBAAAsK,GAAA;IACA;IACAK,cAAA,WAAAA,eAAA;MACA,KAAAC,eAAA,GAAAJ,IAAA,CAAAC,KAAA,CAAAD,IAAA,CAAAE,SAAA,MAAAvM,aAAA;MACA,KAAAX,iBAAA;IACA;IACAqN,iBAAA,WAAAA,kBAAAP,GAAA;MACA;MACA,IAAAA,GAAA,CAAAC,OAAA;QACA;QACAO,MAAA,CAAAC,MAAA,CAAAT,GAAA,EAAAA,GAAA,CAAAC,OAAA;QACA,OAAAD,GAAA,CAAAC,OAAA;MACA;MAAA;MACA,KAAAvL,UAAA;MACA,KAAAzB,iBAAA;IACA;IACAyN,iBAAA,WAAAA,kBAAA;MACA,KAAA7M,aAAA,GAAAqM,IAAA,CAAAC,KAAA,CAAAD,IAAA,CAAAE,SAAA,MAAAE,eAAA;MACA7K,OAAA,CAAAC,GAAA,4BAAA7B,aAAA;MACA,KAAAX,iBAAA;IACA;IAEAyN,gBAAA,WAAAA,iBAAA;MAAA,IAAAC,OAAA;MACA;MACA,IAAAtJ,KAAA,QAAAF,MAAA,CAAAC,OAAA,CAAAC,KAAA;MACA,KAAAA,KAAA,CAAAuD,QAAA;QACA,KAAAzE,QAAA,CAAAD,KAAA;QACA;MACA;MAEA,SAAAtC,aAAA,CAAAmB,MAAA;QACAS,OAAA,CAAAC,GAAA,uBAAA7B,aAAA;QACA,KAAAuC,QAAA,CAAA2G,OAAA;QACA;MACA;;MAEA;MAAA,IAAA8D,UAAA,OAAAzG,2BAAA,CAAA1C,OAAA,EACA,KAAA7D,aAAA;QAAAiN,MAAA;MAAA;QAAA,KAAAD,UAAA,CAAAxG,CAAA,MAAAyG,MAAA,GAAAD,UAAA,CAAA5I,CAAA,IAAAqC,IAAA;UAAA,IAAAnF,IAAA,GAAA2L,MAAA,CAAAvG,KAAA;UACA,IAAApF,IAAA,CAAA2D,mBAAA,KAAA3D,IAAA,CAAA0D,OAAA;YACA,KAAAzC,QAAA,CAAA2G,OAAA,kBAAAwC,MAAA,CAAApK,IAAA,CAAAC,YAAA,+DAAAmK,MAAA,CAAApK,IAAA,CAAA2D,mBAAA,sCAAAyG,MAAA,CAAApK,IAAA,CAAA0D,OAAA;YACA;UACA;QACA;MAAA,SAAA3B,GAAA;QAAA2J,UAAA,CAAApG,CAAA,CAAAvD,GAAA;MAAA;QAAA2J,UAAA,CAAAnG,CAAA;MAAA;MAEA,IAAAsC,YAAA;MACAA,YAAA,CAAAC,OAAA;MACAD,YAAA,CAAApM,MAAA,QAAAA,MAAA;MACAoM,YAAA,CAAAnM,OAAA,QAAAA,OAAA;MACAmM,YAAA,CAAAE,IAAA,yBAAArJ,aAAA,CAAAqB,GAAA,WAAAC,IAAA;QAAA,OAAAA,IAAA,CAAAC,YAAA;MAAA,GAAAC,IAAA;MAEA,IAAAqI,eAAA;MACAA,eAAA,CAAAtK,EAAA,QAAAa,YAAA,CAAAb,EAAA;MACA,SAAAa,YAAA,CAAAlD,QAAA;QACA2M,eAAA,CAAA9L,UAAA;QACA8L,eAAA,CAAAC,SAAA,OAAAxE,IAAA,GAAAyE,WAAA,GAAAC,KAAA,QAAAC,OAAA;QACA;MACA,gBAAA7J,YAAA,CAAAlD,QAAA,cAAAmD,WAAA;QACAwJ,eAAA,CAAA9L,UAAA;QACA8L,eAAA,CAAAK,SAAA,OAAA5E,IAAA,GAAAyE,WAAA,GAAAC,KAAA,QAAAC,OAAA;QACA;MACA,gBAAA7J,YAAA,CAAAlD,QAAA,cAAAmD,WAAA;QACAwJ,eAAA,CAAA9L,UAAA;QACA8L,eAAA,CAAAK,SAAA,OAAA5E,IAAA,GAAAyE,WAAA,GAAAC,KAAA,QAAAC,OAAA;QACA;MACA,gBAAA7J,YAAA,CAAAlD,QAAA,cAAAkD,YAAA,CAAArC,UAAA;QACA8L,eAAA,CAAA9L,UAAA;QACA8L,eAAA,CAAAC,SAAA,OAAAxE,IAAA,GAAAyE,WAAA,GAAAC,KAAA,QAAAC,OAAA;QACA;MACA,gBAAA7J,YAAA,CAAAlD,QAAA,cAAAmD,WAAA,cAAAD,YAAA,CAAArC,UAAA;QACA8L,eAAA,CAAA9L,UAAA;QACA8L,eAAA,CAAAK,SAAA,OAAA5E,IAAA,GAAAyE,WAAA,GAAAC,KAAA,QAAAC,OAAA;QACA;MACA,gBAAA7J,YAAA,CAAAlD,QAAA,cAAAmD,WAAA,cAAAD,YAAA,CAAArC,UAAA;QACA8L,eAAA,CAAA9L,UAAA;QACA8L,eAAA,CAAAK,SAAA,OAAA5E,IAAA,GAAAyE,WAAA,GAAAC,KAAA,QAAAC,OAAA;QACA;MACA;MAEA,IAAAV,KAAA;QACAC,gBAAA,OAAAxJ,aAAA;QACAyJ,QAAA,EAAAN,YAAA;QACApB,SAAA,EAAA8B,eAAA;QACAxJ,WAAA,OAAAA;MACA;MAEAuB,OAAA,CAAAC,GAAA,iDAAA0H,KAAA,OAAAnJ,YAAA,CAAAlD,QAAA;MAGA,IAAAwM,kDAAA,EAAAH,KAAA,EAAAxG,IAAA,WAAAC,GAAA;QACApB,OAAA,CAAAC,GAAA,iDAAAmB,GAAA;QACA,IAAAA,GAAA,CAAAC,IAAA;UACA8J,OAAA,CAAAxK,QAAA,CAAAqG,OAAA;UACAmE,OAAA,CAAArI,cAAA,CAAAqI,OAAA,CAAAhQ,MAAA;UACAgQ,OAAA,CAAAzI,WAAA;QACA;UACA;UACAyI,OAAA,CAAAxK,QAAA,CAAAD,KAAA,CAAAU,GAAA,CAAAG,OAAA;QACA;MACA,GAAAC,KAAA,WAAAC,GAAA;QACAzB,OAAA,CAAAU,KAAA,gCAAAe,GAAA;QACA0J,OAAA,CAAAxK,QAAA,CAAAD,KAAA;MACA;MAEA,KAAAlD,iBAAA;IACA;IAEA8N,cAAA,WAAAA,eAAA;MAAA,IAAAC,OAAA;MACA;MACA,IAAA1J,KAAA,QAAAF,MAAA,CAAAC,OAAA,CAAAC,KAAA;MACA7B,OAAA,CAAAC,GAAA,UAAA4B,KAAA;MACA,KAAAA,KAAA,CAAAuD,QAAA;QACA,KAAAzE,QAAA,CAAAD,KAAA;QACA;MACA;MAEA,SAAAtC,aAAA,CAAAmB,MAAA;QACAS,OAAA,CAAAC,GAAA,uBAAA7B,aAAA;QACA,KAAAuC,QAAA,CAAA2G,OAAA;QACA;MACA;;MAEA;MAAA,IAAAkE,UAAA,OAAA7G,2BAAA,CAAA1C,OAAA,EACA,KAAA7D,aAAA;QAAAqN,MAAA;MAAA;QAAA,KAAAD,UAAA,CAAA5G,CAAA,MAAA6G,MAAA,GAAAD,UAAA,CAAAhJ,CAAA,IAAAqC,IAAA;UAAA,IAAAnF,IAAA,GAAA+L,MAAA,CAAA3G,KAAA;UACA,IAAApF,IAAA,CAAAyD,iBAAA,KAAAzD,IAAA,CAAA0D,OAAA;YACA,KAAAzC,QAAA,CAAA2G,OAAA,kBAAAwC,MAAA,CAAApK,IAAA,CAAAC,YAAA,mDAAAmK,MAAA,CAAApK,IAAA,CAAAyD,iBAAA,sCAAA2G,MAAA,CAAApK,IAAA,CAAA0D,OAAA;YACA;UACA;QACA;MAAA,SAAA3B,GAAA;QAAA+J,UAAA,CAAAxG,CAAA,CAAAvD,GAAA;MAAA;QAAA+J,UAAA,CAAAvG,CAAA;MAAA;MAEA,IAAAsC,YAAA;MACAA,YAAA,CAAAC,OAAA;MACAD,YAAA,CAAApM,MAAA,QAAAA,MAAA;MACAoM,YAAA,CAAAnM,OAAA,QAAAA,OAAA;MACAmM,YAAA,CAAAE,IAAA,yBAAArJ,aAAA,CAAAqB,GAAA,WAAAC,IAAA;QAAA,OAAAA,IAAA,CAAAC,YAAA;MAAA,GAAAC,IAAA;MAEA,IAAAqI,eAAA;MACAA,eAAA,CAAAtK,EAAA,QAAAa,YAAA,CAAAb,EAAA;MACA,SAAAa,YAAA,CAAAlD,QAAA;QACA2M,eAAA,CAAA9L,UAAA;QACA8L,eAAA,CAAAC,SAAA,OAAAxE,IAAA,GAAAyE,WAAA,GAAAC,KAAA,QAAAC,OAAA;QACA;MACA,gBAAA7J,YAAA,CAAAlD,QAAA,cAAAmD,WAAA;QACAwJ,eAAA,CAAA9L,UAAA;QACA8L,eAAA,CAAAK,SAAA,OAAA5E,IAAA,GAAAyE,WAAA,GAAAC,KAAA,QAAAC,OAAA;QACA;MACA,gBAAA7J,YAAA,CAAAlD,QAAA,cAAAmD,WAAA;QACAwJ,eAAA,CAAA9L,UAAA;QACA8L,eAAA,CAAAK,SAAA,OAAA5E,IAAA,GAAAyE,WAAA,GAAAC,KAAA,QAAAC,OAAA;QACA;MACA,gBAAA7J,YAAA,CAAAlD,QAAA,cAAAkD,YAAA,CAAArC,UAAA;QACA8L,eAAA,CAAA9L,UAAA;QACA8L,eAAA,CAAAC,SAAA,OAAAxE,IAAA,GAAAyE,WAAA,GAAAC,KAAA,QAAAC,OAAA;QACA;MACA,gBAAA7J,YAAA,CAAAlD,QAAA,cAAAmD,WAAA,cAAAD,YAAA,CAAArC,UAAA;QACA8L,eAAA,CAAA9L,UAAA;QACA8L,eAAA,CAAAK,SAAA,OAAA5E,IAAA,GAAAyE,WAAA,GAAAC,KAAA,QAAAC,OAAA;QACA;MACA,gBAAA7J,YAAA,CAAAlD,QAAA,cAAAmD,WAAA,cAAAD,YAAA,CAAArC,UAAA;QACA8L,eAAA,CAAA9L,UAAA;QACA8L,eAAA,CAAAK,SAAA,OAAA5E,IAAA,GAAAyE,WAAA,GAAAC,KAAA,QAAAC,OAAA;QACA;MACA;MAEA,IAAAV,KAAA;QACAC,gBAAA,OAAAxJ,aAAA;QACAyJ,QAAA,EAAAN,YAAA;QACApB,SAAA,EAAA8B,eAAA;QACAxJ,WAAA,OAAAA;MACA;MAEAuB,OAAA,CAAAC,GAAA,iDAAA0H,KAAA,OAAAnJ,YAAA,CAAAlD,QAAA;MAGA,IAAAwM,kDAAA,EAAAH,KAAA,EAAAxG,IAAA,WAAAC,GAAA;QACApB,OAAA,CAAAC,GAAA,iDAAAmB,GAAA;QACA,IAAAA,GAAA,CAAAC,IAAA;UACAkK,OAAA,CAAA5K,QAAA,CAAAqG,OAAA;UACAuE,OAAA,CAAAzI,cAAA,CAAAyI,OAAA,CAAApQ,MAAA;UACAoQ,OAAA,CAAA7I,WAAA;QACA;UACA;UACA6I,OAAA,CAAA5K,QAAA,CAAAD,KAAA,CAAAU,GAAA,CAAAG,OAAA;QACA;MACA,GAAAC,KAAA,WAAAC,GAAA;QACAzB,OAAA,CAAAU,KAAA,gCAAAe,GAAA;QACA8J,OAAA,CAAA5K,QAAA,CAAAD,KAAA;MACA;MAEA,KAAAlD,iBAAA;IACA;IAGAkO,cAAA,WAAAA,eAAA;MAEA,KAAAjO,iBAAA;IACA;IAEA4C,iBAAA,WAAAA,kBAAA;MACA,KAAA7B,YAAA;IACA;IAEAkE,WAAA,WAAAA,YAAA;MAAA,IAAAiJ,OAAA;MAAA,WAAA3J,kBAAA,CAAAC,OAAA,mBAAAC,aAAA,CAAAD,OAAA,IAAAE,CAAA,UAAAyJ,SAAA;QAAA,IAAAtH,QAAA,EAAAuH,GAAA;QAAA,WAAA3J,aAAA,CAAAD,OAAA,IAAAK,CAAA,WAAAwJ,SAAA;UAAA,kBAAAA,SAAA,CAAAtJ,CAAA;YAAA;cAAAsJ,SAAA,CAAArJ,CAAA;cAAAqJ,SAAA,CAAAtJ,CAAA;cAAA,OAEA,IAAAuJ,aAAA,EAAAJ,OAAA,CAAApN,UAAA;YAAA;cAAA+F,QAAA,GAAAwH,SAAA,CAAA9I,CAAA;cACA2I,OAAA,CAAAnN,YAAA,GAAA8F,QAAA,CAAAvJ,IAAA;cACAiF,OAAA,CAAAC,GAAA,sBAAA0L,OAAA,CAAAnN,YAAA;cACA,IAAAmN,OAAA,CAAAnN,YAAA,CAAAwN,iBAAA;gBACAL,OAAA,CAAAnN,YAAA,CAAAwN,iBAAA;cACA,WAAAL,OAAA,CAAAnN,YAAA,CAAAwN,iBAAA;gBACAL,OAAA,CAAAnN,YAAA,CAAAwN,iBAAA;cACA,WAAAL,OAAA,CAAAnN,YAAA,CAAAwN,iBAAA;gBACAL,OAAA,CAAAnN,YAAA,CAAAwN,iBAAA;cACA,WAAAL,OAAA,CAAAnN,YAAA,CAAAwN,iBAAA;gBACAL,OAAA,CAAAnN,YAAA,CAAAwN,iBAAA;cACA;cACAhM,OAAA,CAAAC,GAAA,sBAAA0L,OAAA,CAAAnN,YAAA;cACA;cACAmN,OAAA,CAAAM,SAAA;gBACAN,OAAA,CAAAlD,WAAA;cACA;cAAA,OAAAqD,SAAA,CAAA7I,CAAA,IACAqB,QAAA;YAAA;cAAAwH,SAAA,CAAArJ,CAAA;cAAAoJ,GAAA,GAAAC,SAAA,CAAA9I,CAAA;cAEAhD,OAAA,CAAAU,KAAA,uBAAAmL,GAAA;cAAA,MAAAA,GAAA;YAAA;cAAA,OAAAC,SAAA,CAAA7I,CAAA;UAAA;QAAA,GAAA2I,QAAA;MAAA;IAGA;IAGAM,aAAA,WAAAA,cAAAC,QAAA;MACA,IAAAC,WAAA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;MACA;MACA,OAAAA,WAAA,CAAAD,QAAA;IACA;IAEA;IACAE,iBAAA,WAAAA,kBAAAF,QAAA;MACA,IAAAC,WAAA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;MACA;MACA,OAAAA,WAAA,CAAAD,QAAA;IACA;IACA;IACAG,wBAAA,WAAAA,yBAAAH,QAAA;MACA,IAAAC,WAAA;QACA;QACA;QACA;MACA;MACA,OAAAA,WAAA,CAAAD,QAAA;IACA;IAEA;IACAI,2BAAA,WAAAA,4BAAAJ,QAAA;MACA,IAAAK,OAAA;QACA;QAAA;QACA;QAAA;QACA;MACA;MACA,OAAAA,OAAA,CAAAL,QAAA;IACA;IAEA;IACAM,qBAAA,WAAAA,sBAAAC,MAAA;MACA,IAAAC,SAAA;QACA;QACA;QACA;QACA;MACA;MACA,OAAAA,SAAA,CAAAD,MAAA;IACA;IAEA;IACAE,qBAAA,WAAAA,sBAAAF,MAAA;MACA,IAAAF,OAAA;QACA;QAAA;QACA;QAAA;QACA;QAAA;QACA;MACA;MACA,OAAAA,OAAA,CAAAE,MAAA;IACA;IAEA;IACAG,WAAA,WAAAA,YAAA5M,GAAA;MACA,IAAA6M,eAAA;QACA;QAAA;QACA;QAAA;QACA;QAAA;QACA;QAAA;QACA;MACA;MACA,OAAAA,eAAA,CAAA7M,GAAA,CAAA8M,IAAA;IACA;IAEA;IACAC,MAAA,WAAAA,OAAA;MACA,KAAAC,OAAA,CAAAC,EAAA;IACA;IAEA;IACAC,aAAA,WAAAA,cAAA5O,UAAA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;IAAA,CACA;IACA6O,wBAAA,WAAAA,yBAAAC,GAAA;MACA,KAAAA,GAAA;QACA,KAAApS,kBAAA,CAAAkC,WAAA;MACA;IACA;IACAmQ,gBAAA,WAAAA,iBAAA;MAAA,IAAAC,OAAA;MACA,KAAAlQ,mBAAA;MACA,KAAAmQ,WAAA;MACA;MACA,KAAA7O,cAAA;MACA,KAAAsN,SAAA;QACA,IAAAsB,OAAA,CAAA5E,KAAA,CAAA8E,WAAA;UACAF,OAAA,CAAA5E,KAAA,CAAA8E,WAAA,CAAAC,cAAA;QACA;MACA;IACA;IACAC,qBAAA,WAAAA,sBAAAC,SAAA;MACA;MACA,IAAAA,SAAA,CAAArO,MAAA;QACA,IAAAsO,YAAA,GAAAD,SAAA,CAAAA,SAAA,CAAArO,MAAA;QACA,KAAAoJ,KAAA,CAAA8E,WAAA,CAAAC,cAAA;QACA,KAAA/E,KAAA,CAAA8E,WAAA,CAAAK,kBAAA,CAAAD,YAAA;QACA,KAAAlP,cAAA,GAAAkP,YAAA;MACA;QACA,KAAAlP,cAAA,GAAAiP,SAAA;MACA;IACA;IACAG,sBAAA,WAAAA,uBAAA;MACA,UAAApP,cAAA;QACA,KAAAgC,QAAA,CAAA2G,OAAA;QACA;MACA;MAEA,KAAArM,kBAAA,CAAAkC,WAAA,QAAAwB,cAAA,CAAAvD,OAAA;;MAEA;MACA;MACA;;MAEA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;;MAEA,KAAAiC,mBAAA;MACA,KAAAsD,QAAA,CAAAqG,OAAA;IAIA;IACAwG,WAAA,WAAAA,YAAA;MAAA,IAAAQ,OAAA;MACA;MACA,KAAAzQ,UAAA,QAAAyB,oBAAA;MACA,KAAAzB,UAAA,CAAAuE,OAAA,WAAApC,IAAA;QACAA,IAAA,CAAAuO,UAAA,GAAAD,OAAA,CAAA3B,iBAAA,CAAA3M,IAAA,CAAAuO,UAAA;MACA;MACAjO,OAAA,CAAAC,GAAA,oBAAA1C,UAAA;IACA;IACA2Q,uBAAA,WAAAA,wBAAAC,QAAA;MACA,IAAAC,WAAA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;MACA;MACA,OAAAA,WAAA,CAAAD,QAAA;IACA;IACAE,aAAA,WAAAA,cAAA;MAAA,IAAAC,OAAA;MACA;MACA,IAAAC,YAAA,SAAAjR,UAAA,CAAAjC,MAAA,QAAAmT,WAAA;MACA,IAAAC,aAAA,SAAAnR,UAAA,CAAAlC,OAAA,QAAAoT,WAAA;MACA,IAAAE,oBAAA,SAAApR,UAAA,CAAAL,cAAA,QAAAuR,WAAA;;MAEA;MACA,KAAAjR,UAAA,QAAAyB,oBAAA,CAAAmG,MAAA,WAAAzF,IAAA;QACA,IAAArE,MAAA,IAAAqE,IAAA,CAAArE,MAAA,QAAAsT,QAAA,GAAAH,WAAA;QACA,IAAApT,OAAA,IAAAsE,IAAA,CAAAtE,OAAA,QAAAuT,QAAA,GAAAH,WAAA;QACA,IAAAvR,cAAA,IAAAyC,IAAA,CAAAzC,cAAA,QAAA0R,QAAA,GAAAH,WAAA;;QAEA;QACA,IAAAI,WAAA,IAAAL,YAAA,IAAAlT,MAAA,CAAA+J,QAAA,CAAAmJ,YAAA;QACA,IAAAM,YAAA,IAAAJ,aAAA,IAAArT,OAAA,CAAAgK,QAAA,CAAAqJ,aAAA;QACA,IAAAK,mBAAA,IAAAJ,oBAAA,IAAAzR,cAAA,CAAAmI,QAAA,CAAAsJ,oBAAA;QAEA,OAAAE,WAAA,IAAAC,YAAA,IAAAC,mBAAA;MACA;;MAEA;MACA,KAAAvR,UAAA,CAAAuE,OAAA,WAAApC,IAAA;QACAA,IAAA,CAAAuO,UAAA,GAAAK,OAAA,CAAAjC,iBAAA,CAAA3M,IAAA,CAAAuO,UAAA;MACA;IACA;IACAc,WAAA,WAAAA,YAAA;MACA,KAAAzR,UAAA;QACAjC,MAAA;QACAD,OAAA;QACA6B,cAAA;MACA;MACA,KAAAuQ,WAAA;IACA;IACAwB,eAAA,WAAAA,gBAAAjC,IAAA;MACA,IAAAP,OAAA;QACA;QACA;QACA;MACA;MACA,OAAAA,OAAA,CAAAO,IAAA;IACA;IACA;IACA;IACA;IACA;IACA;IAEA;IACAkC,qBAAA,WAAAA,sBAAArB,SAAA;MACA,KAAA1O,YAAA,GAAA0O,SAAA;IACA;IAEA;IACAsB,8BAAA,WAAAA,+BAAA;MAAA,IAAAC,OAAA;MACA,IAAAtN,KAAA,QAAAF,MAAA,CAAAC,OAAA,CAAAC,KAAA;MACA,KAAAA,KAAA,CAAAuD,QAAA;QACA,KAAAzE,QAAA,CAAAD,KAAA;QACA;MACA;MACA,IAAA0O,SAAA;MACA,KAAAlQ,YAAA,CAAA4C,OAAA,WAAApC,IAAA;QACA,IAAAA,IAAA,CAAAyD,iBAAA,KAAAzD,IAAA,CAAA0D,OAAA;UACA+L,OAAA,CAAAxO,QAAA,CAAA2G,OAAA;UACA8H,SAAA;QACA;MACA;MAEA,IAAAA,SAAA;QACA;MACA;;MAEA;MACA;MACA;MACA;;MAEA;MACA,IAAA7H,YAAA;QACAC,OAAA;QACArM,MAAA,OAAAA,MAAA;QACAC,OAAA,OAAAA,OAAA;QACAqM,IAAA,wBAAArJ,aAAA,CAAAqB,GAAA,WAAAC,IAAA;UAAA,OAAAA,IAAA,CAAAC,YAAA;QAAA,GAAAC,IAAA;MACA;;MAEA;MACA,IAAA8H,eAAA;QACA/J,EAAA,OAAAa,YAAA,CAAAb,EAAA;QACApC,eAAA;QAAA;QACAC,aAAA,MAAAkI,IAAA;QACAvH,UAAA;MACA;MAEA,KAAA+C,YAAA,CAAA4C,OAAA,WAAApC,IAAA;QACA;QACAA,IAAA,CAAA2P,iBAAA,GAAA3P,IAAA,CAAAyD,iBAAA;MACA;;MAEA;MACA,IAAAwE,KAAA;QACAC,gBAAA,OAAA1I,YAAA;QAAA;QACA2I,QAAA,EAAAN,YAAA;QACApB,SAAA,EAAAuB,eAAA;QACAjJ,WAAA,OAAAA;MACA;;MAEA;MACA,IAAAqJ,kDAAA,EAAAH,KAAA,EAAAxG,IAAA,WAAAC,GAAA;QACA,IAAAA,GAAA,CAAAC,IAAA;UACA8N,OAAA,CAAAxO,QAAA,CAAAqG,OAAA;UACAmI,OAAA,CAAArM,cAAA,CAAAqM,OAAA,CAAAhU,MAAA;UACAgU,OAAA,CAAAzM,WAAA;UACA;UACAyM,OAAA,CAAAjQ,YAAA;QACA;UACAiQ,OAAA,CAAAxO,QAAA,CAAAD,KAAA,CAAAU,GAAA,CAAAG,OAAA;QACA;MACA,GAAAC,KAAA,WAAAC,GAAA;QACAzB,OAAA,CAAAU,KAAA,0CAAAe,GAAA;QACA0N,OAAA,CAAAxO,QAAA,CAAAD,KAAA;MACA;IACA;IACA4O,iBAAA,WAAAA,kBAAA;MACAtP,OAAA,CAAAC,GAAA,2BAAAd,kBAAA;MACA,IAAAZ,UAAA,QAAAY,kBAAA,CAAAZ,UAAA;MACA,IAAAnD,OAAA,GAAAmU,MAAA,MAAApQ,kBAAA,CAAA/D,OAAA;MACA,IAAAqD,WAAA,QAAAU,kBAAA,CAAAV,WAAA;MACA,IAAAgC,QAAA,QAAAtB,kBAAA,CAAAsB,QAAA;MACA,IAAAtF,MAAA,GAAAoU,MAAA,MAAApQ,kBAAA,CAAAhE,MAAA;MACA,IAAAqU,GAAA,kDAAA1F,MAAA,CAAAvL,UAAA,eAAAuL,MAAA,CAAA1O,OAAA,mBAAA0O,MAAA,CAAArL,WAAA,gBAAAqL,MAAA,CAAArJ,QAAA,cAAAqJ,MAAA,CAAA3O,MAAA;MACA0I,MAAA,CAAAC,IAAA,CAAA0L,GAAA;IACA;EACA;AACA", "ignoreList": []}]}