{"remainingRequest": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\src\\views\\dataReport\\form\\admin.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\src\\views\\dataReport\\form\\admin.vue", "mtime": 1755499162047}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgVHJlZVZpZXcgZnJvbSAiQC9jb21wb25lbnRzL1RyZWVWaWV3IjsNCmltcG9ydCB7DQogIHJvb3RMaXN0RGltZW5zaW9uYWxpdHksDQogIGdldFJvb3RMaXN0QnlJZCwNCiAgYWRkRGltZW5zaW9uYWxpdHksDQogIGdldFN0YXR1c0xpc3RXaXRoYWRtaW4NCn0gZnJvbSAiQC9hcGkvdFlqeS9kaW1lbnNpb25hbGl0eSI7DQoNCmltcG9ydCB7DQogIGxpc3RQZXJtaXNzaW9uLA0KfSBmcm9tICJAL2FwaS90WWp5L2RpbWVuc2lvbmFsaXR5cGVybWlzc2lvbiI7DQoNCmltcG9ydCB7DQogIGRlYWRsaW5lYnJhbmNoLA0KICB1cGRhdGVGb3JtLA0KfSBmcm9tICJAL2FwaS90WWp5L2Zvcm0iOw0KDQppbXBvcnQgeyBsaXN0RGVwdCB9IGZyb20gIkAvYXBpL3RZankvZGVwdCI7DQppbXBvcnQgYXhpb3MgZnJvbSAiYXhpb3MiOw0KaW1wb3J0ICogYXMgeGxzeCBmcm9tICd4bHN4JzsNCmV4cG9ydCBkZWZhdWx0IHsNCiAgbmFtZTogIkRpbWVuc2lvbmFsaXR5IiwNCiAgY29tcG9uZW50czogew0KICAgIFRyZWVWaWV3LA0KICB9LA0KICBkYXRhKCkgew0KICAgIHJldHVybiB7DQogICAgICAvLyDpga7nvanlsYINCiAgICAgIGxvYWRpbmc6IHRydWUsDQogICAgICBuZXdPcGVuOmZhbHNlLA0KICAgICAgU3BlY2lhbEltcG9ydE9wZW46ZmFsc2UsDQogICAgICBtb3V0aEltcG9ydE9wZW46ZmFsc2UsDQogICAgICBzZWFyY2hvcGVuOmZhbHNlLA0KICAgICAgdG90YWw6MCwNCiAgICAgIHBhZ2VTaXplczpbMjAsNTAsMTAwXSwNCiAgICAgIHF1ZXJ5UGFyYW1zOiB7DQogICAgICAgIHBhZ2VOdW06IDEsDQogICAgICAgIHBhZ2VTaXplOiAyMCwNCiAgICAgICAgZGltZW5zaW9uYWxpdHlOYW1lOiBudWxsLA0KICAgICAgICBpc1VzZTogbnVsbCwNCiAgICAgIH0sDQogICAgICByb290TGlzdDogW10sDQogICAgICBkZXRhaWw6IHt9LA0KICAgICAgcm9vdElkOm51bGwsDQogICAgICBkcmF3ZXI6ZmFsc2UsDQogICAgICBxdWVyeTp7DQogICAgICAgIHN0YXJ0RGF0ZTpudWxsLA0KICAgICAgICBlbmREYXRlOm51bGwsDQogICAgICAgIHJvb3RJZDpudWxsLA0KICAgICAgICB0aXRsZTpudWxsLA0KICAgICAgfSwNCiAgICAgIGV4cG9ydE9wZW46ZmFsc2UsDQogICAgICBkZWFkbGluZU9wZW46ZmFsc2UsDQogICAgICBkZWFkbGluZVRpdGxlOiLmibnph4/kv67mlLnmiKrmraLml6XmnJ8iLA0KICAgICAgZGVhZGxpbmVGb3JtOg0KICAgICAgew0KICAgICAgICBkaW1lbnNpb25hbGl0eVBhdGg6bnVsbA0KICAgICAgfSwNCiAgICAgIGRhdGVWYWx1ZTpudWxsLA0KICAgICAgZGVwdExpc3Q6IFtdLA0KICAgICAgZm9ybTp7fSwNCiAgICAgIHVzZXJMaXN0OltdLA0KICAgICAgYWRtaW5PcGVuOmZhbHNlLA0KICAgICAgYWRtaW5UaXRsZToi566h55CG5ZGY5ZCN5Y2VIiwNCiAgICAgIHNwZWNpYWxGY0RhdGU6bnVsbCwNCiAgICAgIGRpbWVuc2lvbmFsaXR5TmFtZTpudWxsLA0KICAgICAgZGltZW5zaW9uYWxpdHlJZDpudWxsLA0KDQoNCiAgICB9Ow0KICB9LA0KICBjcmVhdGVkKCkgew0KICAgIHRoaXMuZ2V0TGlzdCgpOw0KICAgIHRoaXMuZ2V0RGVwdCgpOw0KICB9LA0KICBtZXRob2RzOiB7DQogICAgY2xpY2tOb2RlKCRldmVudCwgbm9kZSkgew0KICAgICAgJGV2ZW50LnRhcmdldC5wYXJlbnRFbGVtZW50LnBhcmVudEVsZW1lbnQuZmlyc3RFbGVtZW50Q2hpbGQuY2xpY2soKTsNCiAgICB9LA0KICAgIGdldExpc3QoKSB7DQogICAgICB0aGlzLmxvYWRpbmcgPSB0cnVlOw0KICAgICAgZ2V0U3RhdHVzTGlzdFdpdGhhZG1pbih0aGlzLnF1ZXJ5UGFyYW1zKS50aGVuKChyZXMpID0+IHsNCiAgICAgICAgdGhpcy5yb290TGlzdCA9IHJlcy5yb3dzOw0KICAgICAgICBmb3IobGV0IGk9MDtpPHRoaXMucm9vdExpc3QubGVuZ3RoO2krKykNCiAgICAgICAgew0KICAgICAgICAgIGlmKHRoaXMucm9vdExpc3RbaV0uaWQ9PTI3MyB8fCB0aGlzLnJvb3RMaXN0W2ldLmlkPT04NDAgfHwgdGhpcy5yb290TGlzdFtpXS5pZD09ODczIHx8IHRoaXMucm9vdExpc3RbaV0uaWQ9PTEwNzcgfHwgdGhpcy5yb290TGlzdFtpXS5pZD09MTA1OSB8fCB0aGlzLmNvbnRhaW5zU3Vic3RyaW5nKCflronlhajotKPku7vlt6XotYQnLHRoaXMucm9vdExpc3RbaV0uZGltZW5zaW9uYWxpdHlOYW1lKSkNCiAgICAgICAgICB7DQogICAgICAgICAgICB0aGlzLnJvb3RMaXN0W2ldLnNob3dib290PTENCiAgICAgICAgICB9DQogICAgICAgICAgZWxzZQ0KICAgICAgICAgIHsNCiAgICAgICAgICAgIHRoaXMucm9vdExpc3RbaV0uc2hvd2Jvb3Q9MA0KICAgICAgICAgIH0NCiAgICAgICAgICBpZih0aGlzLmNvbnRhaW5zU3Vic3RyaW5nKCflt6Xoo4UnLHRoaXMucm9vdExpc3RbaV0uZGltZW5zaW9uYWxpdHlOYW1lKSkNCiAgICAgICAgICB7DQogICAgICAgICAgICB0aGlzLnJvb3RMaXN0W2ldLnNob3dNb3V0aD0xDQogICAgICAgICAgfQ0KICAgICAgICAgIGVsc2UNCiAgICAgICAgICB7DQogICAgICAgICAgICB0aGlzLnJvb3RMaXN0W2ldLnNob3dNb3V0aD0wDQogICAgICAgICAgfQ0KICAgICAgICB9DQogICAgICAgIHRoaXMudG90YWwgPSByZXMudG90YWw7DQogICAgICAgIHRoaXMubG9hZGluZyA9IGZhbHNlOw0KICAgICAgfSk7DQogICAgICAvLyByb290TGlzdERpbWVuc2lvbmFsaXR5KHRoaXMucXVlcnlQYXJhbXMpLnRoZW4oKHJlcykgPT4gew0KICAgICAgLy8gICB0aGlzLnJvb3RMaXN0ID0gcmVzLnJvd3M7DQogICAgICAvLyAgIHRoaXMudG90YWwgPSByZXMudG90YWw7DQogICAgICAvLyAgIHRoaXMubG9hZGluZyA9IGZhbHNlOw0KICAgICAgLy8gfSk7DQogICAgfSwNCiAgICBnZXREZXB0KCkgew0KICAgICAgbGlzdERlcHQoKS50aGVuKChyZXMpID0+IHsNCiAgICAgICAgdGhpcy5kZXB0TGlzdCA9IHJlcy5yb3dzWzBdLmNoaWxkcmVuOw0KICAgICAgICBjb25zb2xlLmxvZyhyZXMpOw0KICAgICAgICBmb3IobGV0IGk9MDtpPHRoaXMuZGVwdExpc3QubGVuZ3RoO2krKykNCiAgICAgICAgew0KICAgICAgICAgIHRoaXMuZGVhbGRlcHRMaXN0KHRoaXMuZGVwdExpc3RbaV0sMCkNCiAgICAgICAgfQ0KICAgICAgfSk7DQogICAgfSwNCiAgICBkZWFsZGVwdExpc3Qocm93LGNvdW50KQ0KICAgIHsNCiAgICAgICByb3cudmFsdWU9cm93LnBhdGgNCiAgICAgICByb3cubGFiZWw9cm93LmRlcHROYW1lDQogICAgICAgaWYocm93LmNoaWxkcmVuLmxlbmd0aD4wICYmIGNvdW50PDEpDQogICAgICAgew0KICAgICAgICAgIGZvcihsZXQgaT0wO2k8cm93LmNoaWxkcmVuLmxlbmd0aDtpKyspDQogICAgICAgICAgew0KICAgICAgICAgICAgdGhpcy5kZWFsZGVwdExpc3Qocm93LmNoaWxkcmVuW2ldLGNvdW50KzEpDQogICAgICAgICAgfQ0KICAgICAgIH0NCiAgICAgICBlbHNlDQogICAgICAgew0KICAgICAgICAgIHJvdy5jaGlsZHJlbj1udWxsDQogICAgICAgfQ0KICAgIH0sDQogICAgaGFuZGxlUXVlcnlEZXB0KCkgew0KICAgICAgdGhpcy4kcmVmcy5jYXNjYWRlckhhbmRsZS5kcm9wRG93blZpc2libGUgPSBmYWxzZTsNCiAgICAgIHRoaXMucXVlcnlQYXJhbXMucGFnZU51bSA9IDE7DQogICAgICB0aGlzLmdldExpc3QoKTsNCiAgICB9LA0KDQogICAgLyoqIOmHjee9ruaMiemSruaTjeS9nCAqLw0KICAgIHJlc2V0UXVlcnkoKSB7DQogICAgICB0aGlzLnJlc2V0Rm9ybSgicXVlcnlGb3JtIik7DQogICAgICB0aGlzLmhhbmRsZVF1ZXJ5KCk7DQogICAgfSwNCiAgICAvKiog5pCc57Si5oyJ6ZKu5pON5L2cICovDQogICAgaGFuZGxlUXVlcnkoKSB7DQogICAgICB0aGlzLnF1ZXJ5UGFyYW1zLnBhZ2VOdW0gPSAxOw0KICAgICAgdGhpcy5nZXRMaXN0KCk7DQogICAgfSwNCg0KDQogICAgaGFuZGxlQWRkKCkgew0KICAgICAgdGhpcy5uZXdPcGVuPXRydWUNCiAgICAgIC8vIGxldCB0aGF0ID0gdGhpczsNCiAgICAgIC8vIHRoaXMuJHByb21wdCgi6K+36L6T5YWl5ZCN56ewIiwgIuaPkOekuiIsIHsNCiAgICAgIC8vICAgY29uZmlybUJ1dHRvblRleHQ6ICLnoa7lrpoiLA0KICAgICAgLy8gICBjYW5jZWxCdXR0b25UZXh0OiAi5Y+W5raIIiwNCiAgICAgIC8vIH0pDQogICAgICAvLyAgIC50aGVuKCh7IHZhbHVlIH0pID0+IHsNCiAgICAgIC8vICAgICBsZXQgZm9ybSA9IHt9Ow0KICAgICAgLy8gICAgIGZvcm0uZGltZW5zaW9uYWxpdHlOYW1lID0gdmFsdWU7DQogICAgICAvLyAgICAgYWRkRGltZW5zaW9uYWxpdHkoZm9ybSkudGhlbigocmVzKSA9PiB7DQogICAgICAvLyAgICAgICB0aGF0LmdldExpc3QoKTsNCiAgICAgIC8vICAgICB9KTsNCiAgICAgIC8vICAgfSkNCiAgICAgIC8vICAgLmNhdGNoKCgpID0+IHsNCiAgICAgIC8vICAgICB0aGF0LiRtZXNzYWdlKHsNCiAgICAgIC8vICAgICAgIHR5cGU6ICJpbmZvIiwNCiAgICAgIC8vICAgICAgIG1lc3NhZ2U6ICLlj5bmtojmk43kvZwiLA0KICAgICAgLy8gICAgIH0pOw0KICAgICAgLy8gICB9KTsNCiAgICB9LA0KICAgIGhhbmRsZURldGFpbChyb3cpew0KICAgICAgdGhpcy5yb290SWQgPSByb3cuaWQ7DQogICAgICB0aGlzLnJvb3RSdWxlVHlwZSA9IHJvdy5ydWxlVHlwZTsNCiAgICAgIHRoaXMuZ2V0RGV0YWlsKCk7DQogICAgICB0aGlzLmRyYXdlciA9IHRydWU7DQogICAgfSwNCiAgICBoYW5kbGVEZWFkTGluZShyb3cpew0KICAgICAgdGhpcy5kZWFkbGluZUZvcm09e2RpbWVuc2lvbmFsaXR5UGF0aDpudWxsfQ0KICAgICAgdGhpcy5kZWFkbGluZUZvcm0uZGltZW5zaW9uYWxpdHlQYXRoPXJvdy5wYXRoDQogICAgICB0aGlzLmRlYWRsaW5lT3Blbj10cnVlDQogICAgfSwNCiAgICAvKiog5o+Q5Lqk5oyJ6ZKuICovDQogICAgc3VibWl0Rm9ybSgpIHsNCiAgICAgIGlmKHRoaXMuZGVhZGxpbmVGb3JtLmRlYWRsaW5lU3dpdGNoPT0xKQ0KICAgICAgew0KICAgICAgICBpZih0aGlzLmRlYWRsaW5lRm9ybS5kZWFkbGluZURhdGU9PW51bGwpDQogICAgICAgIHsNCiAgICAgICAgICB0aGlzLiRtb2RhbC5tc2dFcnJvcigi5oiq5q2i5pel5pyf5LiN6IO95Li656m6Iik7DQogICAgICAgICAgcmV0dXJuDQogICAgICAgIH0NCiAgICAgICAgbGV0IGRlYWRsaW5lRGF0ZUNoZWNrPXRoaXMuZGVhZGxpbmVGb3JtLmRlYWRsaW5lRGF0ZS5zcGxpdCgiLyIpDQogICAgICAgIGlmKGRlYWRsaW5lRGF0ZUNoZWNrLmxlbmd0aCE9MykNCiAgICAgICAgew0KICAgICAgICAgIHRoaXMuJG1vZGFsLm1zZ0Vycm9yKCLmiKrmraLml6XmnJ/moLzlvI/kuI3mraPnoa7vvIzmraPnoa7moLzlvI/mmK8g5bm0L+aciC/ml6UgIik7DQogICAgICAgICAgcmV0dXJuDQogICAgICAgIH0NCiAgICAgICAgaWYoIS9eLT8oMHwoWzEtOV0/XGQpfDEwMCkkLy50ZXN0KGRlYWRsaW5lRGF0ZUNoZWNrWzBdKSkNCiAgICAgICAgew0KICAgICAgICAgIHRoaXMuJG1vZGFsLm1zZ0Vycm9yKCLmiKrmraLml6XmnJ/kuK3lubTlupTmmK/lnKgtMTAw5YiwMTAw5LmL6Ze055qE5pW05pWwIik7DQogICAgICAgICAgcmV0dXJuDQogICAgICAgIH0NCiAgICAgICAgaWYoIS9eLT8oMHwoWzBdP1xkKXwxMXwxMikkLy50ZXN0KGRlYWRsaW5lRGF0ZUNoZWNrWzFdKSkNCiAgICAgICAgew0KICAgICAgICAgIHRoaXMuJG1vZGFsLm1zZ0Vycm9yKCLmiKrmraLml6XmnJ/kuK3mnIjlupTmmK/lnKgtMTLliLAxMuS5i+mXtOeahOaVtOaVsCIpOw0KICAgICAgICAgIHJldHVybg0KICAgICAgICB9DQogICAgICAgIGlmKCEvXi0/KDB8KFsxLTJdP1xkKXwzMXwzMCkkLy50ZXN0KGRlYWRsaW5lRGF0ZUNoZWNrWzJdKSkNCiAgICAgICAgew0KICAgICAgICAgIHRoaXMuJG1vZGFsLm1zZ0Vycm9yKCLmiKrmraLml6XmnJ/kuK3ml6XlupTmmK/lnKgtMzHliLAzMeS5i+mXtOeahOaVtOaVsCIpOw0KICAgICAgICAgIHJldHVybg0KICAgICAgICB9DQogICAgICB9DQogICAgICBkZWFkbGluZWJyYW5jaCh0aGlzLmRlYWRsaW5lRm9ybSkudGhlbigocmVzcG9uc2UpID0+IA0KICAgICAgew0KICAgICAgICB0aGlzLm1zZ1N1Y2Nlc3MoIuaJuemHj+S/ruaUueaIquatouaXpeacn+aIkOWKnyIpOw0KICAgICAgICB0aGlzLmRlYWRsaW5lT3BlbiA9IGZhbHNlOw0KICAgICAgfSk7DQogICAgfSwNCg0KICAgIGNhbmNlbCgpIHsNCiAgICAgIHRoaXMuZGVhZGxpbmVPcGVuID0gZmFsc2U7DQogICAgfSwNCg0KICAgIGdldERldGFpbCgpew0KICAgIGdldFJvb3RMaXN0QnlJZCh7aWQgOiB0aGlzLnJvb3RJZCxydWxlVHlwZTp0aGlzLnJvb3RSdWxlVHlwZX0pLnRoZW4ocmVzID0+IHsNCiAgICAgICAgdGhpcy5kZXRhaWwgPSByZXMuZGF0YTsNCiAgICAgICAgaWYodGhpcy5kZXRhaWwgPT0gbnVsbCB8fCB0aGlzLmRldGFpbCA9PSB1bmRlZmluZWQpdGhpcy5kZXRhaWwgPSB7fQ0KICAgICAgICBjb25zb2xlLmxvZyh0aGlzLmRldGFpbCkNCiAgICAgICAgdGhpcy4kZm9yY2VVcGRhdGUoKTsNCiAgICAgIH0pOw0KICAgIH0sDQogICAgaGFuZGxlQ2xvc2UoKXsNCiAgICAgIHRoaXMuZHJhd2VyID0gZmFsc2U7DQogICAgICB0aGlzLmdldExpc3QoKTsNCiAgICAgIHRoaXMuJGZvcmNlVXBkYXRlKCk7DQogICAgfSwNCiAgICBoYW5kbGVFeHBvcnQocm93KXsNCiAgICAgIHRoaXMucXVlcnkucm9vdElkICA9IHJvdy5pZDsNCiAgICAgIHRoaXMucXVlcnkudGl0bGUgPSByb3cuZGltZW5zaW9uYWxpdHlOYW1lOw0KICAgICAgdGhpcy5jbGlja0NoYW5nZVRpbWUoKTsNCiAgICAgIHRoaXMuZXhwb3J0T3BlbiA9IHRydWU7DQogICAgfSwNCg0KICAgIGFkZENsaWNrKCkgew0KICAgICAgLy8gdGhpcy5mb3JtLmRlcHRJZD1wYXJzZUludCh0aGlzLmZvcm0uZGVwdElkLnNwbGl0KCIsIilbLTFdKQ0KICAgICAgYWRkRGltZW5zaW9uYWxpdHkodGhpcy5mb3JtKS50aGVuKChyZXMpID0+IHsNCiAgICAgICAgdGhpcy5uZXdPcGVuID0gZmFsc2U7DQogICAgICAgIHRoaXMuZ2V0TGlzdCgpOw0KICAgICAgICB0aGlzLmZvcm09e307DQogICAgICB9KTsNCiAgICB9LA0KICAgIGV4cG9ydERhdGEoKSB7DQogICAgICBpZiAoDQogICAgICAgIHRoaXMucXVlcnkuc3RhcnREYXRlID09IG51bGwgfHwNCiAgICAgICAgdGhpcy5xdWVyeS5zdGFydERhdGUgPT0gIiJ8fA0KICAgICAgICB0aGlzLnF1ZXJ5LmVuZERhdGUgPT0gbnVsbHx8DQogICAgICAgIHRoaXMucXVlcnkuZW5kRGF0ZSA9PSAiIg0KICAgICAgKSB7DQogICAgICAgIHRoaXMuJG5vdGlmeS5lcnJvcih7DQogICAgICAgICAgdGl0bGU6ICLplJnor68iLA0KICAgICAgICAgIG1lc3NhZ2U6ICLlr7zlh7rliY3or7flhYjovpPlhaXlvIDlp4vnu5PmnZ/ml7bpl7QiLA0KICAgICAgICB9KTsNCiAgICAgICAgcmV0dXJuOw0KICAgICAgfQ0KICAgICAgdGhpcy5kb3dubG9hZEZpbGUoDQogICAgICAgICIvd2ViL1RZankvZGltZW5zaW9uYWxpdHkvZXhwb3J0U3RhdGlzdGljcyIsDQogICAgICAgIHsNCiAgICAgICAgICAuLi50aGlzLnF1ZXJ5LA0KICAgICAgICB9LA0KICAgICAgICAiKCIgKw0KICAgICAgICAgIHRoaXMucXVlcnkuc3RhcnREYXRlICsNCiAgICAgICAgICAiLSIgKw0KICAgICAgICAgIHRoaXMucXVlcnkuZW5kRGF0ZSArDQogICAgICAgICAgIikiICsNCiAgICAgICAgICB0aGlzLnF1ZXJ5LnRpdGxlICsNCiAgICAgICAgICBgLnhsc3hgDQogICAgICApOw0KICAgIH0sDQogICAgZXhwb3J0RGF0YVByZXZpZXcoKQ0KICAgIHsNCiAgICAgIGlmICgNCiAgICAgICAgdGhpcy5xdWVyeS5zdGFydERhdGUgPT0gbnVsbCB8fA0KICAgICAgICB0aGlzLnF1ZXJ5LnN0YXJ0RGF0ZSA9PSAiInx8DQogICAgICAgIHRoaXMucXVlcnkuZW5kRGF0ZSA9PSBudWxsfHwNCiAgICAgICAgdGhpcy5xdWVyeS5lbmREYXRlID09ICIiDQogICAgICApIHsNCiAgICAgICAgdGhpcy4kbm90aWZ5LmVycm9yKHsNCiAgICAgICAgICB0aXRsZTogIumUmeivryIsDQogICAgICAgICAgbWVzc2FnZTogIuWvvOWHuuWJjeivt+WFiOi+k+WFpeW8gOWni+e7k+adn+aXtumXtCIsDQogICAgICAgIH0pOw0KICAgICAgICByZXR1cm47DQogICAgICB9DQogICAgICB0aGlzLmRvd25sb2FkWGxzeCgNCiAgICAgICAgIi93ZWIvVFlqeS9kaW1lbnNpb25hbGl0eS9leHBvcnRTdGF0aXN0aWNzIiwNCiAgICAgICAgew0KICAgICAgICAgIC4uLnRoaXMucXVlcnksDQogICAgICAgIH0sDQogICAgICAgIHRoaXMuZGltZW5zaW9uYWxpdHlOYW1lKyIoIiArdGhpcy5zcGVjaWFsRmNEYXRlKw0KICAgICAgICAgICIpIiArDQogICAgICAgICAgYOaVsOaNri54bHN4YA0KICAgICAgKS50aGVuKChibG9iKSA9PiB7DQogICAgICAgIGxldCByZWFkZXIgPSBuZXcgRmlsZVJlYWRlcigpOw0KICAgICAgICByZWFkZXIucmVhZEFzQXJyYXlCdWZmZXIoYmxvYik7DQogICAgICAgIHJlYWRlci5vbmxvYWQgPSAoZXZ0KSA9PiB7DQogICAgICAgICAgdGhpcy5jdXN0b21CbG9iQ29udGVudD1yZWFkZXIucmVzdWx0Ow0KICAgICAgICAgIGxldCBpbnRzID0gbmV3IFVpbnQ4QXJyYXkoZXZ0LnRhcmdldC5yZXN1bHQpOyAvL+imgeS9v+eUqOivu+WPlueahOWGheWuue+8jOaJgOS7peWwhuivu+WPluWGheWuuei9rOWMluaIkFVpbnQ4QXJyYXkNCiAgICAgICAgICBpbnRzID0gaW50cy5zbGljZSgwLCBibG9iLnNpemUpOw0KICAgICAgICAgIGxldCB3b3JrQm9vayA9IHhsc3gucmVhZChpbnRzLCB7IHR5cGU6ICJhcnJheSIgfSk7DQogICAgICAgICAgbGV0IHNoZWV0TmFtZXMgPSB3b3JrQm9vay5TaGVldE5hbWVzOw0KICAgICAgICAgIGxldCBzaGVldE5hbWUgPSBzaGVldE5hbWVzWzBdOw0KICAgICAgICAgIGxldCB3b3JrU2hlZXQgPSB3b3JrQm9vay5TaGVldHNbc2hlZXROYW1lXTsNCiAgICAgICAgICAvL+iOt+WPlkV4Y2xl5YaF5a6577yM5bm25bCG56m65YaF5a6555So56m65YC85L+d5a2YDQogICAgICAgICAgbGV0IGV4Y2VsVGFibGUgPSB4bHN4LnV0aWxzLnNoZWV0X3RvX2pzb24od29ya1NoZWV0KTsNCiAgICAgICAgICAvLyDojrflj5ZFeGNlbOWktOmDqA0KICAgICAgICAgIGxldCB0YWJsZVRoZWFkID0gQXJyYXkuZnJvbShPYmplY3Qua2V5cyhleGNlbFRhYmxlWzBdKSkubWFwKA0KICAgICAgICAgICAgKGl0ZW0pID0+IHsNCiAgICAgICAgICAgICAgcmV0dXJuIGl0ZW0NCiAgICAgICAgICAgIH0NCiAgICAgICAgICApOw0KICAgICAgICAgIHRoaXMuZXhjZWxEYXRhID0gZXhjZWxUYWJsZTsNCiAgICAgICAgICB0aGlzLmV4Y2VsdGl0bGU9dGFibGVUaGVhZA0KICAgICAgICAgIHRoaXMuZXhjZWxIdG1sPSBleGNlbFRhYmxlDQogICAgICAgICAgdGhpcy5zZWFyY2hvcGVuID0gdHJ1ZTsNCiAgICAgICAgfQ0KICAgICAgfSk7DQogICAgfSwNCiAgICBleHBvcnRNb3V0aERhdGFQcmV2aWV3KCl7DQogICAgICBpZiAoDQogICAgICAgIHRoaXMucXVlcnkuc3RhcnREYXRlID09IG51bGwgfHwNCiAgICAgICAgdGhpcy5xdWVyeS5zdGFydERhdGUgPT0gIiJ8fA0KICAgICAgICB0aGlzLnF1ZXJ5LmVuZERhdGUgPT0gbnVsbHx8DQogICAgICAgIHRoaXMucXVlcnkuZW5kRGF0ZSA9PSAiIg0KICAgICAgKSB7DQogICAgICAgIHRoaXMuJG5vdGlmeS5lcnJvcih7DQogICAgICAgICAgdGl0bGU6ICLplJnor68iLA0KICAgICAgICAgIG1lc3NhZ2U6ICLlr7zlh7rliY3or7flhYjovpPlhaXlvIDlp4vnu5PmnZ/ml7bpl7QiLA0KICAgICAgICB9KTsNCiAgICAgICAgcmV0dXJuOw0KICAgICAgfQ0KICAgICAgdGhpcy5xdWVyeS5yb290SWQgPSB0aGlzLmRpbWVuc2lvbmFsaXR5SWQNCiAgICAgIHRoaXMucXVlcnkudHlwZT0iMSINCiAgICAgIHRoaXMuZG93bmxvYWRYbHN4KA0KICAgICAgICAgICIvd2ViL1RZankvYW5zd2VyL2V4cG9ydEV2ZXJ5bW91dGgiLA0KICAgICAgICAgIHsNCiAgICAgICAgICAgIC4uLnRoaXMucXVlcnksDQogICAgICAgICAgfSwNCiAgICAgICAgICB0aGlzLmRpbWVuc2lvbmFsaXR5TmFtZSsiKCIgK3RoaXMuc3BlY2lhbEZjRGF0ZSsNCiAgICAgICAgICAgICIpIiArDQogICAgICAgICAgICBg5pWw5o2uLnhsc3hgDQogICAgICAgICkudGhlbigoYmxvYikgPT4gew0KICAgICAgICAgIGxldCByZWFkZXIgPSBuZXcgRmlsZVJlYWRlcigpOw0KICAgICAgICAgIHJlYWRlci5yZWFkQXNBcnJheUJ1ZmZlcihibG9iKTsNCiAgICAgICAgICANCiAgICAgICAgICByZWFkZXIub25sb2FkID0gKGV2dCkgPT4gew0KICAgICAgICAgICAgdGhpcy5jdXN0b21CbG9iQ29udGVudD1yZWFkZXIucmVzdWx0Ow0KICAgICAgICAgICAgbGV0IGludHMgPSBuZXcgVWludDhBcnJheShldnQudGFyZ2V0LnJlc3VsdCk7IC8v6KaB5L2/55So6K+75Y+W55qE5YaF5a6577yM5omA5Lul5bCG6K+75Y+W5YaF5a656L2s5YyW5oiQVWludDhBcnJheQ0KICAgICAgICAgICAgaW50cyA9IGludHMuc2xpY2UoMCwgYmxvYi5zaXplKTsNCiAgICAgICAgICAgIGxldCB3b3JrQm9vayA9IHhsc3gucmVhZChpbnRzLCB7IHR5cGU6ICJhcnJheSIgfSk7DQogICAgICAgICAgICBsZXQgc2hlZXROYW1lcyA9IHdvcmtCb29rLlNoZWV0TmFtZXM7DQogICAgICAgICAgICBsZXQgc2hlZXROYW1lID0gc2hlZXROYW1lc1swXTsNCiAgICAgICAgICAgIGxldCB3b3JrU2hlZXQgPSB3b3JrQm9vay5TaGVldHNbc2hlZXROYW1lXTsNCiAgICAgICAgICAgIC8v6I635Y+WRXhjbGXlhoXlrrnvvIzlubblsIbnqbrlhoXlrrnnlKjnqbrlgLzkv53lrZgNCiAgICAgICAgICAgIGxldCBleGNlbFRhYmxlID0geGxzeC51dGlscy5zaGVldF90b19qc29uKHdvcmtTaGVldCk7DQogICAgICAgICAgICAvLyDojrflj5ZFeGNlbOWktOmDqA0KICAgICAgICAgICAgbGV0IHRhYmxlVGhlYWQgPSBBcnJheS5mcm9tKE9iamVjdC5rZXlzKGV4Y2VsVGFibGVbMF0pKS5tYXAoDQogICAgICAgICAgICAgIChpdGVtKSA9PiB7DQogICAgICAgICAgICAgICAgcmV0dXJuIGl0ZW0NCiAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgKTsNCiAgICAgICAgICAgIHRoaXMuZXhjZWxEYXRhID0gZXhjZWxUYWJsZTsNCiAgICAgICAgICAgIHRoaXMuZXhjZWx0aXRsZT10YWJsZVRoZWFkDQogICAgICAgICAgICB0aGlzLmV4Y2VsSHRtbD0gZXhjZWxUYWJsZQ0KICAgICAgICAgICAgdGhpcy5zZWFyY2hvcGVuID0gdHJ1ZTsNCiAgICAgICAgICB9DQogICAgICAgIH0pOw0KICAgIH0sDQogICAgZXhwb3J0TW91dGhEYXRhKCl7DQogICAgICBpZiAoDQogICAgICAgIHRoaXMucXVlcnkuc3RhcnREYXRlID09IG51bGwgfHwNCiAgICAgICAgdGhpcy5xdWVyeS5zdGFydERhdGUgPT0gIiJ8fA0KICAgICAgICB0aGlzLnF1ZXJ5LmVuZERhdGUgPT0gbnVsbHx8DQogICAgICAgIHRoaXMucXVlcnkuZW5kRGF0ZSA9PSAiIg0KICAgICAgKSB7DQogICAgICAgIHRoaXMuJG5vdGlmeS5lcnJvcih7DQogICAgICAgICAgdGl0bGU6ICLplJnor68iLA0KICAgICAgICAgIG1lc3NhZ2U6ICLlr7zlh7rliY3or7flhYjovpPlhaXlvIDlp4vnu5PmnZ/ml7bpl7QiLA0KICAgICAgICB9KTsNCiAgICAgICAgcmV0dXJuOw0KICAgICAgfQ0KICAgICAgdGhpcy5xdWVyeS5yb290SWQgPSB0aGlzLmRpbWVuc2lvbmFsaXR5SWQNCiAgICAgIHRoaXMucXVlcnkudHlwZT0iMSINCiAgICAgIHRoaXMuZG93bmxvYWRGaWxlKA0KICAgICAgICAiL3dlYi9UWWp5L2Fuc3dlci9leHBvcnRFdmVyeW1vdXRoIiwNCiAgICAgICAgew0KICAgICAgICAgIC4uLnRoaXMucXVlcnksDQogICAgICAgIH0sDQogICAgICAgICIoIiArDQogICAgICAgICAgdGhpcy5xdWVyeS5zdGFydERhdGUgKw0KICAgICAgICAgICItIiArDQogICAgICAgICAgdGhpcy5xdWVyeS5lbmREYXRlICsNCiAgICAgICAgICAiKSIgKw0KICAgICAgICAgIHRoaXMuZGltZW5zaW9uYWxpdHlOYW1lICsNCiAgICAgICAgICBgLnhsc3hgDQogICAgICApOw0KICAgIH0sDQogICAgb25EYXRlQ2hhbmdlKCl7DQogICAgICBjb25zb2xlLmxvZyh0aGlzLmRhdGVWYWx1ZSkNCiAgICAgIGlmKHRoaXMuZGF0ZVZhbHVlICE9IG51bGwgJiYgdGhpcy5kYXRlVmFsdWUgIT0gIiIpew0KICAgICAgICB0aGlzLnF1ZXJ5LnN0YXJ0RGF0ZSA9IHRoaXMuZGF0ZVZhbHVlWzBdIDsNCiAgICAgICAgdGhpcy5xdWVyeS5lbmREYXRlID0gdGhpcy5kYXRlVmFsdWVbMV07DQogICAgICB9ZWxzZXsNCiAgICAgICAgdGhpcy5xdWVyeS5zdGFydERhdGUgPSAiIjsNCiAgICAgICAgdGhpcy5xdWVyeS5lbmREYXRlID0gIiI7DQogICAgICB9DQogICAgfSwNCiAgICB0b1VwZGF0ZVVzZXJzKHJvdyl7DQogICAgICBjb25zdCBkaW1lbnNpb25hbGl0eUlkID0gcm93LmlkOw0KICAgICAgdGhpcy4kcm91dGVyLnB1c2goIi9kYXRhUmVwb3J0L2RpbWVuc2lvbmFsaXR5LWF1dGgvZGltZW5zaW9uYWxpdHlQZXJtaXNzaW9uLyIgKyBkaW1lbnNpb25hbGl0eUlkKTsNCiAgICAgIC8vIHRoaXMuJHJvdXRlci5nbygwKQ0KICAgIH0sDQogICAgaGFuZGxlRGF0ZUNoYW5nZSgpIHsNCiAgICAgIHRoaXMuZ2V0TGlzdCgpOw0KICAgIH0sDQogICAgaGFuZGxlYUFkbWluTGlzdChyb3cpew0KDQogICAgICBjb25zdCBkaW1lbnNpb25hbGl0eUlkID0gcm93LmlkOw0KICAgICAgbGlzdFBlcm1pc3Npb24oe2RpbWVuc2lvbmFsaXR5SWQ6ZGltZW5zaW9uYWxpdHlJZH0pLnRoZW4oKHJlc3BvbnNlKSA9PiB7DQogICAgICAgIHRoaXMudXNlckxpc3QgPSByZXNwb25zZS5yb3dzOw0KICAgICAgICAvLyB0aGlzLnRvdGFsID0gcmVzcG9uc2UudG90YWw7DQogICAgICAgIC8vIHRoaXMubG9hZGluZyA9IGZhbHNlOw0KICAgICAgICB0aGlzLmFkbWluT3BlbiA9IHRydWU7DQogICAgICB9KTsNCg0KICAgICAgLy8gY29uc3QgZmNEYXRlID0gdGhpcy5xdWVyeVBhcmFtcy5mY0RhdGU7DQogICAgICAvLyBjb25zdCBkaW1lbnNpb25hbGl0eU5hbWUgPSByb3cuZGltZW5zaW9uYWxpdHlOYW1lOw0KICAgICAgLy8gdGhpcy4kcm91dGVyLnB1c2goeyBwYXRoOiAnL2RhdGFSZXBvcnQvYWRtaW5maWxsLWF1dGgvYWRtaW5maWxsc3RhdHVzJywgcXVlcnk6IHsgZGltZW5zaW9uYWxpdHlJZDpkaW1lbnNpb25hbGl0eUlkLCBmY0RhdGU6ZmNEYXRlLGRpbWVuc2lvbmFsaXR5TmFtZTpkaW1lbnNpb25hbGl0eU5hbWV9IH0pOw0KICAgIH0sDQogICAgaGFuZGxlZmlsbChyb3cpew0KICAgICAgLy8gY29uc3QgZGltZW5zaW9uYWxpdHlJZCA9IHJvdy5pZDsNCiAgICAgIC8vIHRoaXMuJHJvdXRlci5wdXNoKCIvZGF0YVJlcG9ydC9hZG1pbmZpbGwtYXV0aC9hZG1pbmZpbGxzdGF0dXMvIiArIGRpbWVuc2lvbmFsaXR5SWQpOw0KICAgICAgY29uc3QgZGltZW5zaW9uYWxpdHlJZCA9IHJvdy5pZDsNCiAgICAgIGNvbnN0IGZjRGF0ZSA9IHRoaXMucXVlcnlQYXJhbXMuZmNEYXRlOw0KICAgICAgY29uc3QgZGltZW5zaW9uYWxpdHlOYW1lID0gcm93LmRpbWVuc2lvbmFsaXR5TmFtZTsNCiAgICAgIHRoaXMuJHJvdXRlci5wdXNoKHsgcGF0aDogJy9kYXRhUmVwb3J0L2FkbWluZmlsbC1hdXRoL2FkbWluZmlsbHN0YXR1cy8nKyBkaW1lbnNpb25hbGl0eUlkLCBxdWVyeTogeyBkaW1lbnNpb25hbGl0eUlkOmRpbWVuc2lvbmFsaXR5SWQsIGZjRGF0ZTpmY0RhdGUsZGltZW5zaW9uYWxpdHlOYW1lOmRpbWVuc2lvbmFsaXR5TmFtZX0gfSk7DQogICAgfSwNCiAgICBoYW5kbGVBbnN3ZXIocm93KXsNCiAgICAgIC8vIGNvbnN0IGRpbWVuc2lvbmFsaXR5SWQgPSByb3cuaWQ7DQogICAgICAvLyB0aGlzLiRyb3V0ZXIucHVzaCgiL2RhdGFSZXBvcnQvYWRtaW5maWxsLWF1dGgvYWRtaW5maWxsc3RhdHVzLyIgKyBkaW1lbnNpb25hbGl0eUlkKTsNCiAgICAgIGNvbnN0IGRpbWVuc2lvbmFsaXR5SWQgPSByb3cuaWQ7DQogICAgICBjb25zdCBmY0RhdGUgPSB0aGlzLnF1ZXJ5UGFyYW1zLmZjRGF0ZTsNCiAgICAgIGNvbnN0IGRpbWVuc2lvbmFsaXR5TmFtZT0gcm93LmRpbWVuc2lvbmFsaXR5TmFtZTsNCiAgICAgIHRoaXMuJHJvdXRlci5wdXNoKHsgcGF0aDogJy9kYXRhUmVwb3J0L2Fuc3dlclNob3ctYXV0aC9hbnN3ZXJTaG93LycrIGRpbWVuc2lvbmFsaXR5SWQsIHF1ZXJ5OiB7IGRpbWVuc2lvbmFsaXR5SWQ6ZGltZW5zaW9uYWxpdHlJZCwgZmNEYXRlOmZjRGF0ZSxkaW1lbnNpb25hbGl0eU5hbWU6ZGltZW5zaW9uYWxpdHlOYW1lfSB9KTsNCiAgICB9LA0KDQogIGhhbmRsZVNwZWNpYWwocm93KXsNCiAgICAgIC8vIHRoaXMucXVlcnkucm9vdElkICA9IHJvdy5pZDsNCiAgICAgIHRoaXMuZGltZW5zaW9uYWxpdHlOYW1lID0gcm93LmRpbWVuc2lvbmFsaXR5TmFtZTsNCiAgICAgIHRoaXMuZGltZW5zaW9uYWxpdHlJZD1yb3cuaWQ7DQogICAgICB0aGlzLlNwZWNpYWxJbXBvcnRPcGVuID0gdHJ1ZTsNCiAgICAgIA0KICAgIH0sDQogIGhhbmRsZU1vdXRoKHJvdyl7DQogICAgICAvLyB0aGlzLnF1ZXJ5LnJvb3RJZCAgPSByb3cuaWQ7DQogICAgICB0aGlzLmRpbWVuc2lvbmFsaXR5TmFtZSA9IHJvdy5kaW1lbnNpb25hbGl0eU5hbWU7DQogICAgICB0aGlzLmRpbWVuc2lvbmFsaXR5SWQ9cm93LmlkOw0KICAgICAgdGhpcy5jbGlja0NoYW5nZVRpbWUoKTsNCiAgICAgIHRoaXMubW91dGhJbXBvcnRPcGVuID0gdHJ1ZTsNCiAgICB9LA0KICBkb3dubG9hZFRlbXBsYXRlU3BlY2lhbFByZXZpZXcoKXsNCiAgICBpZiAodGhpcy5zcGVjaWFsRmNEYXRlID09IG51bGwgKSB7DQogICAgICAgIHRoaXMuc3BlY2lhbEZjRGF0ZT0gdGhpcy5xdWVyeVBhcmFtcy5mY0RhdGUNCiAgICAgIH0NCg0KICAgICAgLy8gaWYgKHRoaXMuc3BlY2lhbEZjRGF0ZSA9PSBudWxsICkgew0KICAgICAgLy8gICB0aGlzLiRub3RpZnkuZXJyb3Ioew0KICAgICAgLy8gICAgIHRpdGxlOiAi6ZSZ6K+vIiwNCiAgICAgIC8vICAgICBtZXNzYWdlOiAi5pyq6YCJ5oup5pe26Ze0IiwNCiAgICAgIC8vICAgfSk7DQogICAgICAvLyAgIHJldHVybjsNCiAgICAgIC8vIH0NCiAgICAgIGxldCBxdWVyeUltcG9ydD17fQ0KICAgICAgcXVlcnlJbXBvcnQucm9vdElkID0gdGhpcy5kaW1lbnNpb25hbGl0eUlkDQogICAgICBxdWVyeUltcG9ydC5mY0RhdGUgPSB0aGlzLnNwZWNpYWxGY0RhdGUNCiAgICAgIHF1ZXJ5SW1wb3J0LnR5cGU9IjEiDQogICAgICBsZXQgdXJsPSIiDQogICAgICBpZih0aGlzLmRpbWVuc2lvbmFsaXR5TmFtZT09J+eglOeptumZouebruagh+aMh+agh+S4gOiniCcpDQogICAgICB7DQogICAgICAgIHVybD0iL3dlYi9UWWp5L2Fuc3dlci9leHBvcnRXaXRoVGVtcGxhdGUiDQogICAgICB9DQogICAgICBlbHNlDQogICAgICB7DQogICAgICAgIHVybD0iL3dlYi9UWWp5L2Fuc3dlci9leHBvcnRUZW1wbGF0ZVNwZWNpYWwiDQogICAgICB9DQogICAgICB0aGlzLmRvd25sb2FkWGxzeCgNCiAgICAgICAgICB1cmwsDQogICAgICAgICAgew0KICAgICAgICAgICAgLi4ucXVlcnlJbXBvcnQsDQogICAgICAgICAgfSwNCiAgICAgICAgICB0aGlzLmRpbWVuc2lvbmFsaXR5TmFtZSsiKCIgK3RoaXMuc3BlY2lhbEZjRGF0ZSsNCiAgICAgICAgICAgICIpIiArDQogICAgICAgICAgICBg5pWw5o2uLnhsc3hgDQogICAgICAgICkudGhlbigoYmxvYikgPT4gew0KICAgICAgICAgIGxldCByZWFkZXIgPSBuZXcgRmlsZVJlYWRlcigpOw0KICAgICAgICAgIHJlYWRlci5yZWFkQXNBcnJheUJ1ZmZlcihibG9iKTsNCiAgICAgICAgICANCiAgICAgICAgICByZWFkZXIub25sb2FkID0gKGV2dCkgPT4gew0KICAgICAgICAgICAgdGhpcy5jdXN0b21CbG9iQ29udGVudD1yZWFkZXIucmVzdWx0Ow0KICAgICAgICAgICAgbGV0IGludHMgPSBuZXcgVWludDhBcnJheShldnQudGFyZ2V0LnJlc3VsdCk7IC8v6KaB5L2/55So6K+75Y+W55qE5YaF5a6577yM5omA5Lul5bCG6K+75Y+W5YaF5a656L2s5YyW5oiQVWludDhBcnJheQ0KICAgICAgICAgICAgaW50cyA9IGludHMuc2xpY2UoMCwgYmxvYi5zaXplKTsNCiAgICAgICAgICAgIGxldCB3b3JrQm9vayA9IHhsc3gucmVhZChpbnRzLCB7IHR5cGU6ICJhcnJheSIgfSk7DQogICAgICAgICAgICBsZXQgc2hlZXROYW1lcyA9IHdvcmtCb29rLlNoZWV0TmFtZXM7DQogICAgICAgICAgICBsZXQgc2hlZXROYW1lID0gc2hlZXROYW1lc1swXTsNCiAgICAgICAgICAgIGxldCB3b3JrU2hlZXQgPSB3b3JrQm9vay5TaGVldHNbc2hlZXROYW1lXTsNCiAgICAgICAgICAgIC8v6I635Y+WRXhjbGXlhoXlrrnvvIzlubblsIbnqbrlhoXlrrnnlKjnqbrlgLzkv53lrZgNCiAgICAgICAgICAgIGxldCBleGNlbFRhYmxlID0geGxzeC51dGlscy5zaGVldF90b19qc29uKHdvcmtTaGVldCk7DQogICAgICAgICAgICAvLyDojrflj5ZFeGNlbOWktOmDqA0KICAgICAgICAgICAgbGV0IHRhYmxlVGhlYWQgPSBBcnJheS5mcm9tKE9iamVjdC5rZXlzKGV4Y2VsVGFibGVbMF0pKS5tYXAoDQogICAgICAgICAgICAgIChpdGVtKSA9PiB7DQogICAgICAgICAgICAgICAgcmV0dXJuIGl0ZW0NCiAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgKTsNCiAgICAgICAgICAgIHRoaXMuZXhjZWxEYXRhID0gZXhjZWxUYWJsZTsNCiAgICAgICAgICAgIHRoaXMuZXhjZWx0aXRsZT10YWJsZVRoZWFkDQogICAgICAgICAgICB0aGlzLmV4Y2VsSHRtbD0gZXhjZWxUYWJsZQ0KICAgICAgICAgICAgdGhpcy5zZWFyY2hvcGVuID0gdHJ1ZTsNCiAgICAgICAgICB9DQogICAgICAgIH0pOw0KICB9LA0KICBkb3dubG9hZFRlbXBsYXRlU3BlY2lhbCgpew0KICAgICAgaWYgKHRoaXMuc3BlY2lhbEZjRGF0ZSA9PSBudWxsICkgew0KICAgICAgICB0aGlzLnNwZWNpYWxGY0RhdGU9IHRoaXMucXVlcnlQYXJhbXMuZmNEYXRlDQogICAgICB9DQoNCiAgICAgIC8vIGlmICh0aGlzLnNwZWNpYWxGY0RhdGUgPT0gbnVsbCApIHsNCiAgICAgIC8vICAgdGhpcy4kbm90aWZ5LmVycm9yKHsNCiAgICAgIC8vICAgICB0aXRsZTogIumUmeivryIsDQogICAgICAvLyAgICAgbWVzc2FnZTogIuacqumAieaLqeaXtumXtCIsDQogICAgICAvLyAgIH0pOw0KICAgICAgLy8gICByZXR1cm47DQogICAgICAvLyB9DQogICAgICBsZXQgcXVlcnlJbXBvcnQ9e30NCiAgICAgIHF1ZXJ5SW1wb3J0LnJvb3RJZCA9IHRoaXMuZGltZW5zaW9uYWxpdHlJZA0KICAgICAgcXVlcnlJbXBvcnQuZmNEYXRlID0gdGhpcy5zcGVjaWFsRmNEYXRlDQogICAgICBxdWVyeUltcG9ydC50eXBlPSIxIg0KICAgICAgbGV0IHVybD0iIg0KICAgICAgaWYodGhpcy5kaW1lbnNpb25hbGl0eU5hbWU9PSfnoJTnqbbpmaLnm67moIfmjIfmoIfkuIDop4gnKQ0KICAgICAgew0KICAgICAgICB1cmw9Ii93ZWIvVFlqeS9hbnN3ZXIvZXhwb3J0V2l0aFRlbXBsYXRlIg0KICAgICAgfQ0KICAgICAgZWxzZQ0KICAgICAgew0KICAgICAgICB1cmw9Ii93ZWIvVFlqeS9hbnN3ZXIvZXhwb3J0VGVtcGxhdGVTcGVjaWFsIg0KICAgICAgfQ0KICAgICAgdGhpcy5kb3dubG9hZEZpbGUoDQogICAgICAgIHVybCwNCiAgICAgICAgew0KICAgICAgICAgIC4uLnF1ZXJ5SW1wb3J0LA0KICAgICAgICB9LA0KICAgICAgICB0aGlzLmRpbWVuc2lvbmFsaXR5TmFtZSsiKCIgK3RoaXMuc3BlY2lhbEZjRGF0ZSsNCiAgICAgICAgICAiKSIgKw0KICAgICAgICAgIGDmlbDmja4ueGxzeGANCiAgICAgICk7DQogICAgfSwNCg0KICAgIC8v5q2k5aSE5Li65oyJ6ZKu5Yik5pat566h55CGDQogICAgY29udGFpbnNTdWJzdHJpbmcoc3Vic3RyaW5nLCBzdHJpbmcpIA0KICAgIHsNCiAgICAgIHJldHVybiBzdHJpbmcuaW5jbHVkZXMoc3Vic3RyaW5nKTsNCiAgICB9LA0KICAgIC8v5pSv5oyB6aKE6KeI5ZKM5a+85Ye6DQogICAgbW91dGhDaGVjayhzdHJpbmcpDQogICAgew0KICAgICAgaWYoc3RyaW5nPT0gJ+WFreWMluaMh+aghycpDQogICAgICB7DQogICAgICAgIHJldHVybiB0cnVlOw0KICAgICAgfQ0KICAgICAgaWYoc3RyaW5nPT0gJ+W3peijheS4iue6v+mqjOaUtuWQiOagvOeOh+e7n+iuoScpDQogICAgICB7DQogICAgICAgIHJldHVybiB0cnVlOw0KICAgICAgfQ0KICAgICAgaWYoc3RyaW5nPT0gJzIwMjXlubTnu4/mtY7otKPku7vliLbmioDnu4/mjIfmoIcnKQ0KICAgICAgew0KICAgICAgICByZXR1cm4gdHJ1ZTsNCiAgICAgIH0NCiAgICAgIGlmKHN0cmluZz09ICfnoJTnqbbpmaLmioDnu4/mj5DljYfmjIfmoIfot5/ouKonKQ0KICAgICAgew0KICAgICAgICByZXR1cm4gdHJ1ZTsNCiAgICAgIH0NCiAgICAgIHJldHVybiBmYWxzZTsNCiAgICB9LA0KICAgIC8v5pSv5oyB5Y2V5ZGo5pyf5a+85Ye6DQogICAgYWxvbmVMaXN0KHN0cmluZykgew0KICAgICAgaWYoc3RyaW5nPT0gJ+awlOS9k+e7k+eul+aciOaKpScpDQogICAgICB7DQogICAgICAgIHJldHVybiB0cnVlOw0KICAgICAgfQ0KICAgICAgaWYoc3RyaW5nPT0gJ+mrmOeCieOAgei9rOeCieeFpOawlOaciOaKpeihqCcpDQogICAgICB7DQogICAgICAgIHJldHVybiB0cnVlOw0KICAgICAgfQ0KICAgICAgaWYoc3RyaW5nPT0gJ+WkqeeEtuawlOa2iOiAl+aciOaKpeihqCcpDQogICAgICB7DQogICAgICAgIHJldHVybiB0cnVlOw0KICAgICAgfQ0KICAgICAgaWYoc3RyaW5nPT0gJ+iSuOaxvea2iOiAl+aciOaKpeihqCcpDQogICAgICB7DQogICAgICAgIHJldHVybiB0cnVlOw0KICAgICAgfQ0KICAgICAgaWYoc3RyaW5nPT0gJ+eUtemHj+aciOaKpeihqCcpDQogICAgICB7DQogICAgICAgIHJldHVybiB0cnVlOw0KICAgICAgfQ0KICAgICAgaWYoc3RyaW5nPT0gJ+eJueadv+S6i+S4mumDqDIwMjXlubTnu4/mtY7otKPku7vliLblpZbnvZrmsYfmgLsnKQ0KICAgICAgew0KICAgICAgICByZXR1cm4gdHJ1ZTsNCiAgICAgIH0NCiAgICAgIGlmKHN0cmluZz09ICfnoJTnqbbpmaLnm67moIfmjIfmoIfkuIDop4gnKQ0KICAgICAgew0KICAgICAgICByZXR1cm4gdHJ1ZTsNCiAgICAgIH0NCiAgICAgIGlmKHN0cmluZz09ICflronlhajotKPku7vlt6XotYTogIPmoLjooagnKQ0KICAgICAgew0KICAgICAgICByZXR1cm4gdHJ1ZTsNCiAgICAgIH0NCiAgICAgIGlmKHN0cmluZz09ICflronlhajotKPku7vlt6XotYTogIPmoLjmsYfmgLsnKQ0KICAgICAgew0KICAgICAgICByZXR1cm4gdHJ1ZTsNCiAgICAgIH0NCiAgICAgIHJldHVybiBmYWxzZTsNCiAgICB9LA0KDQogICAgLy/mlbDmja7pooTop4jmqKHlnZflpITnkIYNCiAgICBoYW5kbGVQcmV2aWV3KCkgew0KICAgICAgbGV0IHF1ZXJ5SW1wb3J0PXt9DQogICAgICBxdWVyeUltcG9ydC5yb290SWQgPSB0aGlzLnF1ZXJ5UGFyYW1zLmRpbWVuc2lvbmFsaXR5SWQNCiAgICAgIHF1ZXJ5SW1wb3J0LmZjRGF0ZSA9IHRoaXMucXVlcnlQYXJhbXMuZmNEYXRlDQogICAgICBxdWVyeUltcG9ydC50eXBlPSIxIg0KICAgICAgaWYodGhpcy5kaW1lbnNpb25hbGl0eU5hbWU9PSfnoJTnqbbpmaLnm67moIfmjIfmoIfkuIDop4gnKQ0KICAgICAgew0KICAgICAgICAgIHRoaXMuZG93bmxvYWRYbHN4KA0KICAgICAgICAgICIvd2ViL1RZankvYW5zd2VyL2V4cG9ydFdpdGhUZW1wbGF0ZSIsDQogICAgICAgICAgew0KICAgICAgICAgICAgLi4ucXVlcnlJbXBvcnQsDQogICAgICAgICAgfSwNCiAgICAgICAgICB0aGlzLmRpbWVuc2lvbmFsaXR5TmFtZSsiKCIgK3RoaXMuc3BlY2lhbEZjRGF0ZSsNCiAgICAgICAgICAgICIpIiArDQogICAgICAgICAgICBg5pWw5o2uLnhsc3hgDQogICAgICAgICkudGhlbigoYmxvYikgPT4gew0KICAgICAgICAgIGxldCByZWFkZXIgPSBuZXcgRmlsZVJlYWRlcigpOw0KICAgICAgICAgIHJlYWRlci5yZWFkQXNBcnJheUJ1ZmZlcihibG9iKTsNCiAgICAgICAgICANCiAgICAgICAgICByZWFkZXIub25sb2FkID0gKGV2dCkgPT4gew0KICAgICAgICAgICAgdGhpcy5jdXN0b21CbG9iQ29udGVudD1yZWFkZXIucmVzdWx0Ow0KICAgICAgICAgICAgbGV0IGludHMgPSBuZXcgVWludDhBcnJheShldnQudGFyZ2V0LnJlc3VsdCk7IC8v6KaB5L2/55So6K+75Y+W55qE5YaF5a6577yM5omA5Lul5bCG6K+75Y+W5YaF5a656L2s5YyW5oiQVWludDhBcnJheQ0KICAgICAgICAgICAgaW50cyA9IGludHMuc2xpY2UoMCwgYmxvYi5zaXplKTsNCiAgICAgICAgICAgIGxldCB3b3JrQm9vayA9IHhsc3gucmVhZChpbnRzLCB7IHR5cGU6ICJhcnJheSIgfSk7DQogICAgICAgICAgICBsZXQgc2hlZXROYW1lcyA9IHdvcmtCb29rLlNoZWV0TmFtZXM7DQogICAgICAgICAgICBsZXQgc2hlZXROYW1lID0gc2hlZXROYW1lc1swXTsNCiAgICAgICAgICAgIGxldCB3b3JrU2hlZXQgPSB3b3JrQm9vay5TaGVldHNbc2hlZXROYW1lXTsNCiAgICAgICAgICAgIC8v6I635Y+WRXhjbGXlhoXlrrnvvIzlubblsIbnqbrlhoXlrrnnlKjnqbrlgLzkv53lrZgNCiAgICAgICAgICAgIGxldCBleGNlbFRhYmxlID0geGxzeC51dGlscy5zaGVldF90b19qc29uKHdvcmtTaGVldCk7DQogICAgICAgICAgICAvLyDojrflj5ZFeGNlbOWktOmDqA0KICAgICAgICAgICAgbGV0IHRhYmxlVGhlYWQgPSBBcnJheS5mcm9tKE9iamVjdC5rZXlzKGV4Y2VsVGFibGVbMF0pKS5tYXAoDQogICAgICAgICAgICAgIChpdGVtKSA9PiB7DQogICAgICAgICAgICAgICAgcmV0dXJuIGl0ZW0NCiAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgKTsNCiAgICAgICAgICAgIHRoaXMuZXhjZWxEYXRhID0gZXhjZWxUYWJsZTsNCiAgICAgICAgICAgIHRoaXMuZXhjZWx0aXRsZT10YWJsZVRoZWFkDQogICAgICAgICAgICB0aGlzLmV4Y2VsSHRtbD0gZXhjZWxUYWJsZQ0KICAgICAgICAgICAgdGhpcy5zZWFyY2hvcGVuID0gdHJ1ZTsNCiAgICAgICAgICB9DQogICAgICAgIH0pOw0KICAgICAgfQ0KICAgICAgZWxzZQ0KICAgICAgew0KICAgICAgICB0aGlzLmRvd25sb2FkWGxzeCgNCiAgICAgICAgIi93ZWIvVFlqeS9hbnN3ZXIvZXhwb3J0VGVtcGxhdGVTcGVjaWFsIiwNCiAgICAgICAgew0KICAgICAgICAgIC4uLnF1ZXJ5SW1wb3J0LA0KICAgICAgICB9LA0KICAgICAgICB0aGlzLmRpbWVuc2lvbmFsaXR5TmFtZSsiKCIgK3RoaXMuc3BlY2lhbEZjRGF0ZSsNCiAgICAgICAgICAiKSIgKw0KICAgICAgICAgIGDmlbDmja4ueGxzeGANCiAgICAgICkudGhlbigoYmxvYikgPT4gew0KICAgICAgICBsZXQgcmVhZGVyID0gbmV3IEZpbGVSZWFkZXIoKTsNCiAgICAgICAgcmVhZGVyLnJlYWRBc0FycmF5QnVmZmVyKGJsb2IpOw0KICAgICAgICByZWFkZXIub25sb2FkID0gKGV2dCkgPT4gew0KICAgICAgICAgIHRoaXMuY3VzdG9tQmxvYkNvbnRlbnQ9cmVhZGVyLnJlc3VsdDsNCiAgICAgICAgICBsZXQgaW50cyA9IG5ldyBVaW50OEFycmF5KGV2dC50YXJnZXQucmVzdWx0KTsgLy/opoHkvb/nlKjor7vlj5bnmoTlhoXlrrnvvIzmiYDku6XlsIbor7vlj5blhoXlrrnovazljJbmiJBVaW50OEFycmF5DQogICAgICAgICAgaW50cyA9IGludHMuc2xpY2UoMCwgYmxvYi5zaXplKTsNCiAgICAgICAgICBsZXQgd29ya0Jvb2sgPSB4bHN4LnJlYWQoaW50cywgeyB0eXBlOiAiYXJyYXkiIH0pOw0KICAgICAgICAgIGxldCBzaGVldE5hbWVzID0gd29ya0Jvb2suU2hlZXROYW1lczsNCiAgICAgICAgICBsZXQgc2hlZXROYW1lID0gc2hlZXROYW1lc1swXTsNCiAgICAgICAgICBsZXQgd29ya1NoZWV0ID0gd29ya0Jvb2suU2hlZXRzW3NoZWV0TmFtZV07DQogICAgICAgICAgLy/ojrflj5ZFeGNsZeWGheWuue+8jOW5tuWwhuepuuWGheWuueeUqOepuuWAvOS/neWtmA0KICAgICAgICAgIGxldCBleGNlbFRhYmxlID0geGxzeC51dGlscy5zaGVldF90b19qc29uKHdvcmtTaGVldCk7DQogICAgICAgICAgLy8g6I635Y+WRXhjZWzlpLTpg6gNCiAgICAgICAgICBsZXQgdGFibGVUaGVhZCA9IEFycmF5LmZyb20oT2JqZWN0LmtleXMoZXhjZWxUYWJsZVswXSkpLm1hcCgNCiAgICAgICAgICAgIChpdGVtKSA9PiB7DQogICAgICAgICAgICAgIHJldHVybiBpdGVtDQogICAgICAgICAgICB9DQogICAgICAgICAgKTsNCiAgICAgICAgICB0aGlzLmV4Y2VsRGF0YSA9IGV4Y2VsVGFibGU7DQogICAgICAgICAgdGhpcy5leGNlbHRpdGxlPXRhYmxlVGhlYWQNCiAgICAgICAgICB0aGlzLmV4Y2VsSHRtbD0gZXhjZWxUYWJsZQ0KICAgICAgICAgIHRoaXMuc2VhcmNob3BlbiA9IHRydWU7DQogICAgICAgIH0NCiAgICAgIH0pOw0KICAgICAgfQ0KICAgIH0sDQogICAgLy8g5pe26Ze05q616aKE6KeIDQogICAgaGFuZGxlUHJldmlldzEoKSB7DQogICAgICAvLyBsZXQgcXVlcnlJbXBvcnQ9e30NCiAgICAgIC8vIHF1ZXJ5SW1wb3J0LnJvb3RJZCA9IHRoaXMucXVlcnlQYXJhbXMuZGltZW5zaW9uYWxpdHlJZA0KICAgICAgLy8gcXVlcnlJbXBvcnQuZmNEYXRlID0gdGhpcy5xdWVyeVBhcmFtcy5mY0RhdGUNCiAgICAgIC8vIHF1ZXJ5SW1wb3J0LnR5cGU9IjEiDQogICAgICAgIGlmICgNCiAgICAgICAgdGhpcy5xdWVyeUltcG9ydC5zdGFydERhdGUgPT0gbnVsbCB8fA0KICAgICAgICB0aGlzLnF1ZXJ5SW1wb3J0LnN0YXJ0RGF0ZSA9PSAiInx8DQogICAgICAgIHRoaXMucXVlcnlJbXBvcnQuZW5kRGF0ZSA9PSBudWxsfHwNCiAgICAgICAgdGhpcy5xdWVyeUltcG9ydC5lbmREYXRlID09ICIiDQogICAgICApIHsNCiAgICAgICAgdGhpcy4kbm90aWZ5LmVycm9yKHsNCiAgICAgICAgICB0aXRsZTogIumUmeivryIsDQogICAgICAgICAgbWVzc2FnZTogIuWvvOWHuuWJjeivt+WFiOi+k+WFpeW8gOWni+e7k+adn+aXtumXtCIsDQogICAgICAgIH0pOw0KICAgICAgICByZXR1cm47DQogICAgICB9DQogICAgICB0aGlzLnF1ZXJ5SW1wb3J0LnJvb3RJZCA9IHRoaXMucXVlcnlQYXJhbXMuZGltZW5zaW9uYWxpdHlJZA0KICAgICAgdGhpcy5xdWVyeUltcG9ydC50eXBlPSIxIg0KICAgICAgdGhpcy5kb3dubG9hZFhsc3goDQogICAgICAgICIvd2ViL1RZankvYW5zd2VyL2V4cG9ydFRlbXBsYXRlTm9tcmFsIiwNCiAgICAgICAgew0KICAgICAgICAgIC4uLnRoaXMucXVlcnlJbXBvcnQsDQogICAgICAgIH0sDQogICAgICAgIHRoaXMuZGltZW5zaW9uYWxpdHlOYW1lKyIoIiArdGhpcy5zcGVjaWFsRmNEYXRlKw0KICAgICAgICAgICIpIiArDQogICAgICAgICAgYOaVsOaNri54bHN4YA0KICAgICAgKS50aGVuKChibG9iKSA9PiB7DQogICAgICAgIGxldCByZWFkZXIgPSBuZXcgRmlsZVJlYWRlcigpOw0KICAgICAgICByZWFkZXIucmVhZEFzQXJyYXlCdWZmZXIoYmxvYik7DQogICAgICAgIA0KICAgICAgICByZWFkZXIub25sb2FkID0gKGV2dCkgPT4gew0KICAgICAgICAgIHRoaXMuY3VzdG9tQmxvYkNvbnRlbnQ9cmVhZGVyLnJlc3VsdDsNCiAgICAgICAgICBsZXQgaW50cyA9IG5ldyBVaW50OEFycmF5KGV2dC50YXJnZXQucmVzdWx0KTsgLy/opoHkvb/nlKjor7vlj5bnmoTlhoXlrrnvvIzmiYDku6XlsIbor7vlj5blhoXlrrnovazljJbmiJBVaW50OEFycmF5DQogICAgICAgICAgaW50cyA9IGludHMuc2xpY2UoMCwgYmxvYi5zaXplKTsNCiAgICAgICAgICBsZXQgd29ya0Jvb2sgPSB4bHN4LnJlYWQoaW50cywgeyB0eXBlOiAiYXJyYXkiIH0pOw0KICAgICAgICAgIGxldCBzaGVldE5hbWVzID0gd29ya0Jvb2suU2hlZXROYW1lczsNCiAgICAgICAgICBsZXQgc2hlZXROYW1lID0gc2hlZXROYW1lc1swXTsNCiAgICAgICAgICBsZXQgd29ya1NoZWV0ID0gd29ya0Jvb2suU2hlZXRzW3NoZWV0TmFtZV07DQogICAgICAgICAgLy/ojrflj5ZFeGNsZeWGheWuue+8jOW5tuWwhuepuuWGheWuueeUqOepuuWAvOS/neWtmA0KICAgICAgICAgIGxldCBleGNlbFRhYmxlID0geGxzeC51dGlscy5zaGVldF90b19qc29uKHdvcmtTaGVldCk7DQogICAgICAgICAgLy8g6I635Y+WRXhjZWzlpLTpg6gNCiAgICAgICAgICBsZXQgdGFibGVUaGVhZCA9IEFycmF5LmZyb20oT2JqZWN0LmtleXMoZXhjZWxUYWJsZVswXSkpLm1hcCgNCiAgICAgICAgICAgIChpdGVtKSA9PiB7DQogICAgICAgICAgICAgIHJldHVybiBpdGVtDQogICAgICAgICAgICB9DQogICAgICAgICAgKTsNCiAgICAgICAgICB0aGlzLmV4Y2VsRGF0YSA9IGV4Y2VsVGFibGU7DQogICAgICAgICAgdGhpcy5leGNlbHRpdGxlPXRhYmxlVGhlYWQNCiAgICAgICAgICB0aGlzLmV4Y2VsSHRtbD0gZXhjZWxUYWJsZQ0KICAgICAgICAgIHRoaXMuc2VhcmNob3BlbiA9IHRydWU7DQogICAgICAgIH0NCiAgICAgIH0pOw0KICAgIH0sDQoNCiAgICBjbGlja0NoYW5nZVRpbWUoKQ0KICAgIHsNCiAgICAgICAgIGxldCBub3cgPW5ldyBEYXRlKCk7DQogICAgICAgICB0aGlzLnF1ZXJ5LnN0YXJ0RGF0ZT10aGlzLmdldEZpcnN0T2ZZZWFyKG5vdyk7DQogICAgICAgICB0aGlzLnF1ZXJ5LmVuZERhdGU9dGhpcy5nZXRGaXJzdE9mTW9udGgobm93KTsNCiAgICAgICAgIHRoaXMuZGF0ZVZhbHVlPVtdOw0KICAgICAgICAgdGhpcy5kYXRlVmFsdWUucHVzaCh0aGlzLnF1ZXJ5LnN0YXJ0RGF0ZSk7DQogICAgICAgICB0aGlzLmRhdGVWYWx1ZS5wdXNoKHRoaXMucXVlcnkuZW5kRGF0ZSk7DQogICAgfSwNCiAgICAvLyDojrflj5bml7bpl7TnmoTkvJjljJblpITnkIYNCiAgICBnZXRGaXJzdE9mWWVhcihub3cpDQogICAgew0KICAgICAgbGV0ICBmaXJzdERheU9mWWVhciA9IG5ldyBEYXRlKG5vdy5nZXRGdWxsWWVhcigpLCAwLCAxKTsNCiAgICAgIHJldHVybiB0aGlzLmZvcm1hdERhdGUoZmlyc3REYXlPZlllYXIpOw0KICAgIH0sDQogICAgZ2V0Rmlyc3RPZk1vbnRoKG5vdykNCiAgICB7DQogICAgICBsZXQgZmlyc3REYXlPZk1vbnRoID0gbmV3IERhdGUobm93LmdldEZ1bGxZZWFyKCksIG5vdy5nZXRNb250aCgpLCAxKTsNCiAgICAgIHJldHVybiB0aGlzLmZvcm1hdERhdGUoZmlyc3REYXlPZk1vbnRoKTsNCiAgICB9LA0KICAgIC8vIOaXpeacn+agvOW8j+WMluWHveaVsO+8iOi9rOS4uiB5eXl5LU1NLWRk77yJDQogICAgZm9ybWF0RGF0ZShkYXRlKSANCiAgICB7DQogICAgICBjb25zdCB5ZWFyID0gZGF0ZS5nZXRGdWxsWWVhcigpOw0KICAgICAgY29uc3QgbW9udGggPSBTdHJpbmcoZGF0ZS5nZXRNb250aCgpICsgMSkucGFkU3RhcnQoMiwgJzAnKTsgLy8g5pyI5Lu95LuOMOW8gOWni+mcgCsxDQogICAgICBjb25zdCBkYXkgPSBTdHJpbmcoZGF0ZS5nZXREYXRlKCkpLnBhZFN0YXJ0KDIsICcwJyk7DQogICAgICByZXR1cm4gYCR7eWVhcn0tJHttb250aH0tJHtkYXl9YDsNCiAgICB9LA0KDQogIH0sDQp9Ow0K"}, {"version": 3, "sources": ["admin.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAuZA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA", "file": "admin.vue", "sourceRoot": "src/views/dataReport/form", "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <el-form\r\n      :model=\"queryParams\"\r\n      ref=\"queryForm\"\r\n      :inline=\"true\"\r\n      label-width=\"68px\"\r\n    >\r\n      <el-form-item label=\"扎口部门\" prop=\"deptCode\">\r\n        <el-cascader\r\n          ref=\"cascaderHandle\"\r\n          :options=\"deptList\"\r\n          clearable\r\n          filterable\r\n          v-model=\"queryParams.deptCode\"\r\n          :props=\"{ expandTrigger: 'hover', emitPath: false,checkStrictly: true }\"\r\n          :show-all-levels=\"false\"\r\n          @change=\"handleQueryDept\"\r\n        >\r\n        <span\r\n              slot-scope=\"{ node, data }\"\r\n              style=\"margin-left: -10px; padding-left: 10px; display: block\"\r\n              @click=\"clickNode($event, node)\"\r\n              >{{ data.label }}</span\r\n            >\r\n        </el-cascader>\r\n      </el-form-item>\r\n      <el-form-item label=\"报表名称\" prop=\"dimensionalityName\">\r\n        <el-input\r\n          v-model=\"queryParams.dimensionalityName\"\r\n          placeholder=\"请输入名称\"\r\n          clearable\r\n          size=\"small\"\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"填报时间\">\r\n                <el-date-picker\r\n                  v-model=\"queryParams.fcDate\"\r\n                  value-format=\"yyyy-MM-dd\"\r\n                  type=\"date\"\r\n                  @change=\"handleDateChange\"\r\n                  placeholder=\"选择日期\">\r\n                </el-date-picker>\r\n      </el-form-item>\r\n      <!-- <el-form-item label=\"是否在用启用\" prop=\"isUse\">\r\n        <el-select v-model=\"queryParams.isUse\" placeholder=\"请选择\">\r\n          <el-option label=\"启用\" value=\"1\"></el-option>\r\n          <el-option label=\"停用\" value=\"0\"></el-option>\r\n        </el-select>\r\n      </el-form-item> -->\r\n      <el-form-item>\r\n        <el-button\r\n          type=\"cyan\"\r\n          icon=\"el-icon-search\"\r\n          size=\"mini\"\r\n          @click=\"handleQuery\"\r\n          >搜索</el-button\r\n        >\r\n        <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\"\r\n          >重置</el-button\r\n        >\r\n      </el-form-item>\r\n    </el-form>\r\n    <el-row :gutter=\"10\" class=\"mb8\">\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"primary\"\r\n          icon=\"el-icon-plus\"\r\n          size=\"mini\"\r\n          @click=\"handleAdd\"\r\n          >新建报表</el-button\r\n        >\r\n      </el-col>\r\n    </el-row>\r\n    <el-table v-loading=\"loading\" :data=\"rootList\" border>\r\n      <el-table-column label=\"扎口部门\" align=\"center\" prop=\"deptName\" width=\"240\"/>\r\n      <!-- <el-table-column\r\n        label=\"扎口部门及人员\"\r\n        align=\"center\"\r\n        class-name=\"small-padding fixed-width\"\r\n      >\r\n        <template slot-scope=\"scope\">\r\n          <el-button\r\n              size=\"mini\"\r\n              type=\"text\"\r\n              @click=\"handleaAdminList(scope.row)\"\r\n              >{{scope.row.deptName}}</el-button\r\n            >\r\n        </template>\r\n      </el-table-column> -->\r\n      <!-- <el-table-column label=\"报表名称\" align=\"center\" prop=\"dimensionalityName\"/> -->\r\n      <el-table-column\r\n        label=\"报表名称\"\r\n        align=\"center\"\r\n        class-name=\"small-padding fixed-width\"\r\n      >\r\n        <template slot-scope=\"scope\">\r\n          <el-button\r\n              size=\"mini\"\r\n              type=\"text\"\r\n              @click=\"handleAnswer(scope.row)\"\r\n              >{{scope.row.dimensionalityName}}</el-button\r\n            >\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"当期完成率\" align=\"center\" prop=\"countRate\" width=\"160\"/>\r\n      <el-table-column label=\"当期应填数量\" align=\"center\" prop=\"shouldCount\" width=\"160\" />\r\n      <!-- <el-table-column\r\n        label=\"当期应填数量\"\r\n        align=\"center\"\r\n        class-name=\"small-padding fixed-width\"\r\n      >\r\n        <template slot-scope=\"scope\">\r\n          <el-button\r\n              size=\"mini\"\r\n              type=\"text\"\r\n              @click=\"handlefill(scope.row)\"\r\n              >{{scope.row.shouldCount}}</el-button\r\n            >\r\n        </template>\r\n      </el-table-column> -->\r\n\r\n      <el-table-column\r\n        label=\"当期未填数量\"\r\n        align=\"center\"\r\n        class-name=\"small-padding fixed-width\"\r\n        width=\"160\"\r\n      >\r\n        <template slot-scope=\"scope\">\r\n          <el-button\r\n              size=\"mini\"\r\n              type=\"text\"\r\n              @click=\"handlefill(scope.row)\"\r\n              >{{scope.row.notCount}}</el-button\r\n            >\r\n        </template>\r\n      </el-table-column>\r\n\r\n      <!-- <el-table-column label=\"当期应填数量\" align=\"center\" prop=\"\"  @cell-click=\"handleDetail(scope.row)\"/>\r\n      <el-table-column label=\"当期未填数量\" align=\"center\" prop=\"hasCount\"/> -->\r\n      \r\n      <!-- <el-table-column label=\"是否在用\" align=\"center\" prop=\"isUse\">\r\n        <template slot-scope=\"scope\">\r\n          <el-tag\r\n            style=\"margin-left: 10px\"\r\n            :type=\"scope.row.isUse == '1'? 'success' : 'danger'\"\r\n            >{{ scope.row.isUse == \"1\" ? \"启用\" : \"停用\" }}</el-tag\r\n          >\r\n        </template>\r\n      </el-table-column> -->\r\n      <el-table-column\r\n        label=\"操作\"\r\n        align=\"center\"\r\n        class-name=\"small-padding fixed-width\"\r\n      >\r\n        <template slot-scope=\"scope\">\r\n          <el-button\r\n            v-if=\"scope.row.ruleType != '5' \"\r\n            size=\"mini\"\r\n            type=\"text\"\r\n            icon=\"el-icon-edit\"\r\n            @click=\"handleDetail(scope.row)\"\r\n            >维度管理</el-button\r\n          >\r\n          <el-button\r\n            v-if=\"scope.row.ruleType != '5' && scope.row.ruleType != '0'\"\r\n            size=\"mini\"\r\n            type=\"text\"\r\n            icon=\"el-icon-edit\"\r\n            @click=\"handleDeadLine(scope.row)\"\r\n            >截止日期</el-button\r\n          >\r\n          <el-button\r\n            v-if=\"\r\n                  scope.row.ruleType == '1' ||\r\n                  scope.row.ruleType == '3' ||\r\n                  scope.row.ruleType == '4'\r\n                 \"\r\n            size=\"mini\"\r\n            type=\"text\"\r\n            icon=\"el-icon-edit\"\r\n            @click=\"toUpdateUsers(scope.row)\"\r\n            >分配权限</el-button\r\n          >\r\n          <el-button\r\n              v-if=\"\r\n                  scope.row.ruleType == '0' ||\r\n                  scope.row.ruleType == '2' ||\r\n                  scope.row.ruleType == '4' ||\r\n                  scope.row.ruleType == '5'\r\n                 \"\r\n            size=\"mini\"\r\n            type=\"text\"\r\n            icon=\"el-icon-edit\"\r\n            @click=\"handleExport(scope.row)\"\r\n            >导出数据</el-button>\r\n            <el-button\r\n              v-if=\"\r\n                  (scope.row.ruleType == '0' ||\r\n                  scope.row.ruleType == '2' ||\r\n                  scope.row.ruleType == '4' ||\r\n                  scope.row.ruleType == '5') &&\r\n                  aloneList(scope.row.dimensionalityName)\r\n                 \"\r\n            size=\"mini\"\r\n            type=\"text\"\r\n            icon=\"el-icon-edit\"\r\n            @click=\"handleSpecial(scope.row)\"\r\n            >单周期数据导出</el-button>\r\n            <el-button\r\n              v-if=\"\r\n                  (scope.row.ruleType == '0' ||\r\n                  scope.row.ruleType == '2' ||\r\n                  scope.row.ruleType == '4' ||\r\n                  scope.row.ruleType == '5') &&\r\n                  containsSubstring('工装',scope.row.dimensionalityName)\r\n                 \"\r\n            size=\"mini\"\r\n            type=\"text\"\r\n            icon=\"el-icon-edit\"\r\n            @click=\"handleMouth(scope.row)\"\r\n            >规整化数据导出</el-button>\r\n        </template>\r\n      </el-table-column>\r\n    </el-table>\r\n\r\n    <pagination\r\n      v-show=\"total > 0\"\r\n      :total=\"total\"\r\n      :page.sync=\"queryParams.pageNum\"\r\n      :limit.sync=\"queryParams.pageSize\"\r\n      :pageSizes=\"pageSizes\"\r\n      @pagination=\"getList\"\r\n    />\r\n\r\n    <el-drawer\r\n      title=\"详情\"\r\n      :visible.sync=\"drawer\"\r\n      direction=\"rtl\"\r\n      size=\"80%\"\r\n      :before-close=\"handleClose\"\r\n    >\r\n      <tree-view :node=\"detail\" @refreshData=\"getDetail\"></tree-view>\r\n    </el-drawer>\r\n    <el-dialog title=\"选择导出范围\" :visible.sync=\"exportOpen\" width=\"400px\" append-to-body destroy-on-close>\r\n      <span>数据日期范围：</span>\r\n        <el-date-picker\r\n          v-model=\"dateValue\"\r\n          type=\"daterange\"\r\n          range-separator=\"至\"\r\n          start-placeholder=\"开始日期\"\r\n          end-placeholder=\"结束日期\"\r\n          value-format=\"yyyy-MM-dd\"\r\n          @change=\"onDateChange\">\r\n        </el-date-picker>        \r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n      <el-button type=\"success\" @click=\"exportDataPreview\">预 览</el-button>\r\n      <el-button type=\"primary\" @click=\"exportData\">导 出</el-button>\r\n      <!-- <el-button @click=\"exportOpen = false\">取 消</el-button> -->\r\n    </div>\r\n    </el-dialog>\r\n\r\n    \r\n    <el-dialog title=\"选择导出范围\" :visible.sync=\"mouthImportOpen\" width=\"400px\" append-to-body destroy-on-close>\r\n      <span>数据日期范围：</span>\r\n        <el-date-picker\r\n          v-model=\"dateValue\"\r\n          type=\"daterange\"\r\n          range-separator=\"至\"\r\n          start-placeholder=\"开始日期\"\r\n          end-placeholder=\"结束日期\"\r\n          value-format=\"yyyy-MM-dd\"\r\n          @change=\"onDateChange\">\r\n        </el-date-picker>        \r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n      <el-button type=\"primary\" @click=\"exportMouthDataPreview\">预 览</el-button>\r\n      <el-button type=\"primary\" @click=\"exportMouthData\">导 出</el-button>\r\n      <!-- <el-button @click=\"mouthImportOpen = false\">取 消</el-button> -->\r\n    </div>\r\n    </el-dialog>\r\n\r\n    <el-dialog title=\"选择导出范围\" :visible.sync=\"newOpen\" width=\"400px\" append-to-body destroy-on-close>\r\n      <el-form ref=\"form\" :model=\"form\" label-width=\"80px\">\r\n        <el-form-item label=\"名称\" prop=\"dimensionalityName\">\r\n          <el-input\r\n            v-model=\"form.dimensionalityName\"\r\n            placeholder=\"请输入名称\"\r\n            clearable\r\n            size=\"small\"\r\n            @keyup.enter.native=\"handleQuery\"\r\n          />\r\n        </el-form-item>\r\n        <el-form-item label=\"扎口部门\" prop=\"deptCode\">\r\n          <el-cascader\r\n            :options=\"deptList\"\r\n            clearable\r\n            v-model=\"form.deptCode\"\r\n            :props=\"{ expandTrigger: 'hover', emitPath: false,checkStrictly: true }\"\r\n            :show-all-levels=\"false\"\r\n          >\r\n          </el-cascader>\r\n        </el-form-item>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button type=\"primary\" @click=\"addClick\">确 定</el-button>\r\n        <el-button @click=\"newOpen = false\">取 消</el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <el-dialog :title=\"adminTitle\" :visible.sync=\"adminOpen\" width=\"1000px\" append-to-body>\r\n      <el-table v-loading=\"loading\" :data=\"userList\">\r\n        <el-table-column label=\"用户工号\" align=\"center\" prop=\"workNo\" />\r\n        <el-table-column label=\"用户姓名\" align=\"center\" prop=\"userName\" />\r\n        <!-- <el-table-column label=\"用户姓名\" align=\"center\" prop=\"userName\" /> -->\r\n      </el-table>\r\n    </el-dialog>\r\n\r\n    <el-dialog :title=\"deadlineTitle\" :visible.sync=\"deadlineOpen\" width=\"800px\" append-to-body>\r\n      <el-form ref=\"deadlineForm\" :model=\"deadlineForm\" label-width=\"160px\">\r\n        <el-form-item label=\"截止日期开关\" prop=\"deadlineSwitch\">\r\n          <el-switch\r\n            v-model=\"deadlineForm.deadlineSwitch\"\r\n            active-color=\"#13ce66\"\r\n            inactive-color=\"#ff4949\"\r\n            active-value=\"1\"\r\n            inactive-value=\"0\"\r\n            >\r\n          </el-switch>\r\n        </el-form-item>\r\n        <el-form-item v-if=\" deadlineForm.deadlineSwitch == '1' \" label=\"截止日期\" prop=\"deadlineDate\">\r\n          <el-input type=\"text\"\r\n                    v-model=\"deadlineForm.deadlineDate\" \r\n                    placeholder=\"截止日期格式为(年/月/日)\"></el-input>\r\n        </el-form-item>\r\n        <el-form-item v-if=\" deadlineForm.deadlineSwitch == '1' \"  label=\"邮件通知开关\" prop=\"mailSwitch\">\r\n          <el-switch\r\n            v-model=\"deadlineForm.mailSwitch\"\r\n            active-color=\"#13ce66\"\r\n            inactive-color=\"#ff4949\"\r\n            active-value=\"1\"\r\n            inactive-value=\"0\"\r\n            >\r\n          </el-switch>\r\n        </el-form-item>\r\n        <el-form-item v-if=\" deadlineForm.mailSwitch == '1' \" label=\"通知时间\" prop=\"deadlineDate\">\r\n          <el-input type=\"text\"\r\n                    v-model=\"deadlineForm.countdown\" \r\n                    placeholder=\"设置在截止日期前几天进行通知\"></el-input>\r\n        </el-form-item>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button type=\"primary\" @click=\"submitForm\">确 定</el-button>\r\n        <el-button @click=\"cancel\">取 消</el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <!-- <el-dialog\r\n      title=\"单周期报表导出\"\r\n      :visible.sync=\"SpecialImportOpen\"\r\n      width=\"400px\"\r\n      append-to-body\r\n      destroy-on-close\r\n    >\r\n      <span>选择导出时间：</span>\r\n      <el-date-picker\r\n            v-model=\"specialFcDate\"\r\n            value-format=\"yyyy-MM-dd\"\r\n            type=\"date\"\r\n            :default-value=\"new Date()\"\r\n            placeholder=\"选择时间\"\r\n          >\r\n      </el-date-picker>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button type=\"primary\" @click=\"downloadTemplateSpecial\">数据下载</el-button>\r\n      </div>\r\n    </el-dialog> -->\r\n\r\n    <el-dialog title=\"单周期报表导出\" :visible.sync=\"SpecialImportOpen\" width=\"400px\" append-to-body destroy-on-close>\r\n      <span>选择导出时间：</span>\r\n      <el-date-picker\r\n            v-model=\"specialFcDate\"\r\n            value-format=\"yyyy-MM-dd\"\r\n            type=\"date\"\r\n            :default-value=\"new Date()\"\r\n            placeholder=\"选择时间\"\r\n          >\r\n      </el-date-picker>     \r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n      <el-button type=\"success\" @click=\"downloadTemplateSpecialPreview\">数据预览</el-button>\r\n      <el-button type=\"primary\" @click=\"downloadTemplateSpecial\">数据下载</el-button>\r\n    </div>\r\n    </el-dialog>\r\n\r\n    <el-dialog  title=\"数据预览\"  :visible.sync=\"searchopen\" width=\"1800px\" >\r\n        <div class=\"test\">\r\n          <vue-office-excel\r\n              :src=\"customBlobContent\"\r\n              style=\"height: 100vh;\"\r\n          />\r\n        </div>\r\n    </el-dialog>\r\n\r\n  </div>\r\n</template>\r\n  \r\n  <script>\r\nimport TreeView from \"@/components/TreeView\";\r\nimport {\r\n  rootListDimensionality,\r\n  getRootListById,\r\n  addDimensionality,\r\n  getStatusListWithadmin\r\n} from \"@/api/tYjy/dimensionality\";\r\n\r\nimport {\r\n  listPermission,\r\n} from \"@/api/tYjy/dimensionalitypermission\";\r\n\r\nimport {\r\n  deadlinebranch,\r\n  updateForm,\r\n} from \"@/api/tYjy/form\";\r\n\r\nimport { listDept } from \"@/api/tYjy/dept\";\r\nimport axios from \"axios\";\r\nimport * as xlsx from 'xlsx';\r\nexport default {\r\n  name: \"Dimensionality\",\r\n  components: {\r\n    TreeView,\r\n  },\r\n  data() {\r\n    return {\r\n      // 遮罩层\r\n      loading: true,\r\n      newOpen:false,\r\n      SpecialImportOpen:false,\r\n      mouthImportOpen:false,\r\n      searchopen:false,\r\n      total:0,\r\n      pageSizes:[20,50,100],\r\n      queryParams: {\r\n        pageNum: 1,\r\n        pageSize: 20,\r\n        dimensionalityName: null,\r\n        isUse: null,\r\n      },\r\n      rootList: [],\r\n      detail: {},\r\n      rootId:null,\r\n      drawer:false,\r\n      query:{\r\n        startDate:null,\r\n        endDate:null,\r\n        rootId:null,\r\n        title:null,\r\n      },\r\n      exportOpen:false,\r\n      deadlineOpen:false,\r\n      deadlineTitle:\"批量修改截止日期\",\r\n      deadlineForm:\r\n      {\r\n        dimensionalityPath:null\r\n      },\r\n      dateValue:null,\r\n      deptList: [],\r\n      form:{},\r\n      userList:[],\r\n      adminOpen:false,\r\n      adminTitle:\"管理员名单\",\r\n      specialFcDate:null,\r\n      dimensionalityName:null,\r\n      dimensionalityId:null,\r\n\r\n\r\n    };\r\n  },\r\n  created() {\r\n    this.getList();\r\n    this.getDept();\r\n  },\r\n  methods: {\r\n    clickNode($event, node) {\r\n      $event.target.parentElement.parentElement.firstElementChild.click();\r\n    },\r\n    getList() {\r\n      this.loading = true;\r\n      getStatusListWithadmin(this.queryParams).then((res) => {\r\n        this.rootList = res.rows;\r\n        for(let i=0;i<this.rootList.length;i++)\r\n        {\r\n          if(this.rootList[i].id==273 || this.rootList[i].id==840 || this.rootList[i].id==873 || this.rootList[i].id==1077 || this.rootList[i].id==1059 || this.containsSubstring('安全责任工资',this.rootList[i].dimensionalityName))\r\n          {\r\n            this.rootList[i].showboot=1\r\n          }\r\n          else\r\n          {\r\n            this.rootList[i].showboot=0\r\n          }\r\n          if(this.containsSubstring('工装',this.rootList[i].dimensionalityName))\r\n          {\r\n            this.rootList[i].showMouth=1\r\n          }\r\n          else\r\n          {\r\n            this.rootList[i].showMouth=0\r\n          }\r\n        }\r\n        this.total = res.total;\r\n        this.loading = false;\r\n      });\r\n      // rootListDimensionality(this.queryParams).then((res) => {\r\n      //   this.rootList = res.rows;\r\n      //   this.total = res.total;\r\n      //   this.loading = false;\r\n      // });\r\n    },\r\n    getDept() {\r\n      listDept().then((res) => {\r\n        this.deptList = res.rows[0].children;\r\n        console.log(res);\r\n        for(let i=0;i<this.deptList.length;i++)\r\n        {\r\n          this.dealdeptList(this.deptList[i],0)\r\n        }\r\n      });\r\n    },\r\n    dealdeptList(row,count)\r\n    {\r\n       row.value=row.path\r\n       row.label=row.deptName\r\n       if(row.children.length>0 && count<1)\r\n       {\r\n          for(let i=0;i<row.children.length;i++)\r\n          {\r\n            this.dealdeptList(row.children[i],count+1)\r\n          }\r\n       }\r\n       else\r\n       {\r\n          row.children=null\r\n       }\r\n    },\r\n    handleQueryDept() {\r\n      this.$refs.cascaderHandle.dropDownVisible = false;\r\n      this.queryParams.pageNum = 1;\r\n      this.getList();\r\n    },\r\n\r\n    /** 重置按钮操作 */\r\n    resetQuery() {\r\n      this.resetForm(\"queryForm\");\r\n      this.handleQuery();\r\n    },\r\n    /** 搜索按钮操作 */\r\n    handleQuery() {\r\n      this.queryParams.pageNum = 1;\r\n      this.getList();\r\n    },\r\n\r\n\r\n    handleAdd() {\r\n      this.newOpen=true\r\n      // let that = this;\r\n      // this.$prompt(\"请输入名称\", \"提示\", {\r\n      //   confirmButtonText: \"确定\",\r\n      //   cancelButtonText: \"取消\",\r\n      // })\r\n      //   .then(({ value }) => {\r\n      //     let form = {};\r\n      //     form.dimensionalityName = value;\r\n      //     addDimensionality(form).then((res) => {\r\n      //       that.getList();\r\n      //     });\r\n      //   })\r\n      //   .catch(() => {\r\n      //     that.$message({\r\n      //       type: \"info\",\r\n      //       message: \"取消操作\",\r\n      //     });\r\n      //   });\r\n    },\r\n    handleDetail(row){\r\n      this.rootId = row.id;\r\n      this.rootRuleType = row.ruleType;\r\n      this.getDetail();\r\n      this.drawer = true;\r\n    },\r\n    handleDeadLine(row){\r\n      this.deadlineForm={dimensionalityPath:null}\r\n      this.deadlineForm.dimensionalityPath=row.path\r\n      this.deadlineOpen=true\r\n    },\r\n    /** 提交按钮 */\r\n    submitForm() {\r\n      if(this.deadlineForm.deadlineSwitch==1)\r\n      {\r\n        if(this.deadlineForm.deadlineDate==null)\r\n        {\r\n          this.$modal.msgError(\"截止日期不能为空\");\r\n          return\r\n        }\r\n        let deadlineDateCheck=this.deadlineForm.deadlineDate.split(\"/\")\r\n        if(deadlineDateCheck.length!=3)\r\n        {\r\n          this.$modal.msgError(\"截止日期格式不正确，正确格式是 年/月/日 \");\r\n          return\r\n        }\r\n        if(!/^-?(0|([1-9]?\\d)|100)$/.test(deadlineDateCheck[0]))\r\n        {\r\n          this.$modal.msgError(\"截止日期中年应是在-100到100之间的整数\");\r\n          return\r\n        }\r\n        if(!/^-?(0|([0]?\\d)|11|12)$/.test(deadlineDateCheck[1]))\r\n        {\r\n          this.$modal.msgError(\"截止日期中月应是在-12到12之间的整数\");\r\n          return\r\n        }\r\n        if(!/^-?(0|([1-2]?\\d)|31|30)$/.test(deadlineDateCheck[2]))\r\n        {\r\n          this.$modal.msgError(\"截止日期中日应是在-31到31之间的整数\");\r\n          return\r\n        }\r\n      }\r\n      deadlinebranch(this.deadlineForm).then((response) => \r\n      {\r\n        this.msgSuccess(\"批量修改截止日期成功\");\r\n        this.deadlineOpen = false;\r\n      });\r\n    },\r\n\r\n    cancel() {\r\n      this.deadlineOpen = false;\r\n    },\r\n\r\n    getDetail(){\r\n    getRootListById({id : this.rootId,ruleType:this.rootRuleType}).then(res => {\r\n        this.detail = res.data;\r\n        if(this.detail == null || this.detail == undefined)this.detail = {}\r\n        console.log(this.detail)\r\n        this.$forceUpdate();\r\n      });\r\n    },\r\n    handleClose(){\r\n      this.drawer = false;\r\n      this.getList();\r\n      this.$forceUpdate();\r\n    },\r\n    handleExport(row){\r\n      this.query.rootId  = row.id;\r\n      this.query.title = row.dimensionalityName;\r\n      this.clickChangeTime();\r\n      this.exportOpen = true;\r\n    },\r\n\r\n    addClick() {\r\n      // this.form.deptId=parseInt(this.form.deptId.split(\",\")[-1])\r\n      addDimensionality(this.form).then((res) => {\r\n        this.newOpen = false;\r\n        this.getList();\r\n        this.form={};\r\n      });\r\n    },\r\n    exportData() {\r\n      if (\r\n        this.query.startDate == null ||\r\n        this.query.startDate == \"\"||\r\n        this.query.endDate == null||\r\n        this.query.endDate == \"\"\r\n      ) {\r\n        this.$notify.error({\r\n          title: \"错误\",\r\n          message: \"导出前请先输入开始结束时间\",\r\n        });\r\n        return;\r\n      }\r\n      this.downloadFile(\r\n        \"/web/TYjy/dimensionality/exportStatistics\",\r\n        {\r\n          ...this.query,\r\n        },\r\n        \"(\" +\r\n          this.query.startDate +\r\n          \"-\" +\r\n          this.query.endDate +\r\n          \")\" +\r\n          this.query.title +\r\n          `.xlsx`\r\n      );\r\n    },\r\n    exportDataPreview()\r\n    {\r\n      if (\r\n        this.query.startDate == null ||\r\n        this.query.startDate == \"\"||\r\n        this.query.endDate == null||\r\n        this.query.endDate == \"\"\r\n      ) {\r\n        this.$notify.error({\r\n          title: \"错误\",\r\n          message: \"导出前请先输入开始结束时间\",\r\n        });\r\n        return;\r\n      }\r\n      this.downloadXlsx(\r\n        \"/web/TYjy/dimensionality/exportStatistics\",\r\n        {\r\n          ...this.query,\r\n        },\r\n        this.dimensionalityName+\"(\" +this.specialFcDate+\r\n          \")\" +\r\n          `数据.xlsx`\r\n      ).then((blob) => {\r\n        let reader = new FileReader();\r\n        reader.readAsArrayBuffer(blob);\r\n        reader.onload = (evt) => {\r\n          this.customBlobContent=reader.result;\r\n          let ints = new Uint8Array(evt.target.result); //要使用读取的内容，所以将读取内容转化成Uint8Array\r\n          ints = ints.slice(0, blob.size);\r\n          let workBook = xlsx.read(ints, { type: \"array\" });\r\n          let sheetNames = workBook.SheetNames;\r\n          let sheetName = sheetNames[0];\r\n          let workSheet = workBook.Sheets[sheetName];\r\n          //获取Excle内容，并将空内容用空值保存\r\n          let excelTable = xlsx.utils.sheet_to_json(workSheet);\r\n          // 获取Excel头部\r\n          let tableThead = Array.from(Object.keys(excelTable[0])).map(\r\n            (item) => {\r\n              return item\r\n            }\r\n          );\r\n          this.excelData = excelTable;\r\n          this.exceltitle=tableThead\r\n          this.excelHtml= excelTable\r\n          this.searchopen = true;\r\n        }\r\n      });\r\n    },\r\n    exportMouthDataPreview(){\r\n      if (\r\n        this.query.startDate == null ||\r\n        this.query.startDate == \"\"||\r\n        this.query.endDate == null||\r\n        this.query.endDate == \"\"\r\n      ) {\r\n        this.$notify.error({\r\n          title: \"错误\",\r\n          message: \"导出前请先输入开始结束时间\",\r\n        });\r\n        return;\r\n      }\r\n      this.query.rootId = this.dimensionalityId\r\n      this.query.type=\"1\"\r\n      this.downloadXlsx(\r\n          \"/web/TYjy/answer/exportEverymouth\",\r\n          {\r\n            ...this.query,\r\n          },\r\n          this.dimensionalityName+\"(\" +this.specialFcDate+\r\n            \")\" +\r\n            `数据.xlsx`\r\n        ).then((blob) => {\r\n          let reader = new FileReader();\r\n          reader.readAsArrayBuffer(blob);\r\n          \r\n          reader.onload = (evt) => {\r\n            this.customBlobContent=reader.result;\r\n            let ints = new Uint8Array(evt.target.result); //要使用读取的内容，所以将读取内容转化成Uint8Array\r\n            ints = ints.slice(0, blob.size);\r\n            let workBook = xlsx.read(ints, { type: \"array\" });\r\n            let sheetNames = workBook.SheetNames;\r\n            let sheetName = sheetNames[0];\r\n            let workSheet = workBook.Sheets[sheetName];\r\n            //获取Excle内容，并将空内容用空值保存\r\n            let excelTable = xlsx.utils.sheet_to_json(workSheet);\r\n            // 获取Excel头部\r\n            let tableThead = Array.from(Object.keys(excelTable[0])).map(\r\n              (item) => {\r\n                return item\r\n              }\r\n            );\r\n            this.excelData = excelTable;\r\n            this.exceltitle=tableThead\r\n            this.excelHtml= excelTable\r\n            this.searchopen = true;\r\n          }\r\n        });\r\n    },\r\n    exportMouthData(){\r\n      if (\r\n        this.query.startDate == null ||\r\n        this.query.startDate == \"\"||\r\n        this.query.endDate == null||\r\n        this.query.endDate == \"\"\r\n      ) {\r\n        this.$notify.error({\r\n          title: \"错误\",\r\n          message: \"导出前请先输入开始结束时间\",\r\n        });\r\n        return;\r\n      }\r\n      this.query.rootId = this.dimensionalityId\r\n      this.query.type=\"1\"\r\n      this.downloadFile(\r\n        \"/web/TYjy/answer/exportEverymouth\",\r\n        {\r\n          ...this.query,\r\n        },\r\n        \"(\" +\r\n          this.query.startDate +\r\n          \"-\" +\r\n          this.query.endDate +\r\n          \")\" +\r\n          this.dimensionalityName +\r\n          `.xlsx`\r\n      );\r\n    },\r\n    onDateChange(){\r\n      console.log(this.dateValue)\r\n      if(this.dateValue != null && this.dateValue != \"\"){\r\n        this.query.startDate = this.dateValue[0] ;\r\n        this.query.endDate = this.dateValue[1];\r\n      }else{\r\n        this.query.startDate = \"\";\r\n        this.query.endDate = \"\";\r\n      }\r\n    },\r\n    toUpdateUsers(row){\r\n      const dimensionalityId = row.id;\r\n      this.$router.push(\"/dataReport/dimensionality-auth/dimensionalityPermission/\" + dimensionalityId);\r\n      // this.$router.go(0)\r\n    },\r\n    handleDateChange() {\r\n      this.getList();\r\n    },\r\n    handleaAdminList(row){\r\n\r\n      const dimensionalityId = row.id;\r\n      listPermission({dimensionalityId:dimensionalityId}).then((response) => {\r\n        this.userList = response.rows;\r\n        // this.total = response.total;\r\n        // this.loading = false;\r\n        this.adminOpen = true;\r\n      });\r\n\r\n      // const fcDate = this.queryParams.fcDate;\r\n      // const dimensionalityName = row.dimensionalityName;\r\n      // this.$router.push({ path: '/dataReport/adminfill-auth/adminfillstatus', query: { dimensionalityId:dimensionalityId, fcDate:fcDate,dimensionalityName:dimensionalityName} });\r\n    },\r\n    handlefill(row){\r\n      // const dimensionalityId = row.id;\r\n      // this.$router.push(\"/dataReport/adminfill-auth/adminfillstatus/\" + dimensionalityId);\r\n      const dimensionalityId = row.id;\r\n      const fcDate = this.queryParams.fcDate;\r\n      const dimensionalityName = row.dimensionalityName;\r\n      this.$router.push({ path: '/dataReport/adminfill-auth/adminfillstatus/'+ dimensionalityId, query: { dimensionalityId:dimensionalityId, fcDate:fcDate,dimensionalityName:dimensionalityName} });\r\n    },\r\n    handleAnswer(row){\r\n      // const dimensionalityId = row.id;\r\n      // this.$router.push(\"/dataReport/adminfill-auth/adminfillstatus/\" + dimensionalityId);\r\n      const dimensionalityId = row.id;\r\n      const fcDate = this.queryParams.fcDate;\r\n      const dimensionalityName= row.dimensionalityName;\r\n      this.$router.push({ path: '/dataReport/answerShow-auth/answerShow/'+ dimensionalityId, query: { dimensionalityId:dimensionalityId, fcDate:fcDate,dimensionalityName:dimensionalityName} });\r\n    },\r\n\r\n  handleSpecial(row){\r\n      // this.query.rootId  = row.id;\r\n      this.dimensionalityName = row.dimensionalityName;\r\n      this.dimensionalityId=row.id;\r\n      this.SpecialImportOpen = true;\r\n      \r\n    },\r\n  handleMouth(row){\r\n      // this.query.rootId  = row.id;\r\n      this.dimensionalityName = row.dimensionalityName;\r\n      this.dimensionalityId=row.id;\r\n      this.clickChangeTime();\r\n      this.mouthImportOpen = true;\r\n    },\r\n  downloadTemplateSpecialPreview(){\r\n    if (this.specialFcDate == null ) {\r\n        this.specialFcDate= this.queryParams.fcDate\r\n      }\r\n\r\n      // if (this.specialFcDate == null ) {\r\n      //   this.$notify.error({\r\n      //     title: \"错误\",\r\n      //     message: \"未选择时间\",\r\n      //   });\r\n      //   return;\r\n      // }\r\n      let queryImport={}\r\n      queryImport.rootId = this.dimensionalityId\r\n      queryImport.fcDate = this.specialFcDate\r\n      queryImport.type=\"1\"\r\n      let url=\"\"\r\n      if(this.dimensionalityName=='研究院目标指标一览')\r\n      {\r\n        url=\"/web/TYjy/answer/exportWithTemplate\"\r\n      }\r\n      else\r\n      {\r\n        url=\"/web/TYjy/answer/exportTemplateSpecial\"\r\n      }\r\n      this.downloadXlsx(\r\n          url,\r\n          {\r\n            ...queryImport,\r\n          },\r\n          this.dimensionalityName+\"(\" +this.specialFcDate+\r\n            \")\" +\r\n            `数据.xlsx`\r\n        ).then((blob) => {\r\n          let reader = new FileReader();\r\n          reader.readAsArrayBuffer(blob);\r\n          \r\n          reader.onload = (evt) => {\r\n            this.customBlobContent=reader.result;\r\n            let ints = new Uint8Array(evt.target.result); //要使用读取的内容，所以将读取内容转化成Uint8Array\r\n            ints = ints.slice(0, blob.size);\r\n            let workBook = xlsx.read(ints, { type: \"array\" });\r\n            let sheetNames = workBook.SheetNames;\r\n            let sheetName = sheetNames[0];\r\n            let workSheet = workBook.Sheets[sheetName];\r\n            //获取Excle内容，并将空内容用空值保存\r\n            let excelTable = xlsx.utils.sheet_to_json(workSheet);\r\n            // 获取Excel头部\r\n            let tableThead = Array.from(Object.keys(excelTable[0])).map(\r\n              (item) => {\r\n                return item\r\n              }\r\n            );\r\n            this.excelData = excelTable;\r\n            this.exceltitle=tableThead\r\n            this.excelHtml= excelTable\r\n            this.searchopen = true;\r\n          }\r\n        });\r\n  },\r\n  downloadTemplateSpecial(){\r\n      if (this.specialFcDate == null ) {\r\n        this.specialFcDate= this.queryParams.fcDate\r\n      }\r\n\r\n      // if (this.specialFcDate == null ) {\r\n      //   this.$notify.error({\r\n      //     title: \"错误\",\r\n      //     message: \"未选择时间\",\r\n      //   });\r\n      //   return;\r\n      // }\r\n      let queryImport={}\r\n      queryImport.rootId = this.dimensionalityId\r\n      queryImport.fcDate = this.specialFcDate\r\n      queryImport.type=\"1\"\r\n      let url=\"\"\r\n      if(this.dimensionalityName=='研究院目标指标一览')\r\n      {\r\n        url=\"/web/TYjy/answer/exportWithTemplate\"\r\n      }\r\n      else\r\n      {\r\n        url=\"/web/TYjy/answer/exportTemplateSpecial\"\r\n      }\r\n      this.downloadFile(\r\n        url,\r\n        {\r\n          ...queryImport,\r\n        },\r\n        this.dimensionalityName+\"(\" +this.specialFcDate+\r\n          \")\" +\r\n          `数据.xlsx`\r\n      );\r\n    },\r\n\r\n    //此处为按钮判断管理\r\n    containsSubstring(substring, string) \r\n    {\r\n      return string.includes(substring);\r\n    },\r\n    //支持预览和导出\r\n    mouthCheck(string)\r\n    {\r\n      if(string== '六化指标')\r\n      {\r\n        return true;\r\n      }\r\n      if(string== '工装上线验收合格率统计')\r\n      {\r\n        return true;\r\n      }\r\n      if(string== '2025年经济责任制技经指标')\r\n      {\r\n        return true;\r\n      }\r\n      if(string== '研究院技经提升指标跟踪')\r\n      {\r\n        return true;\r\n      }\r\n      return false;\r\n    },\r\n    //支持单周期导出\r\n    aloneList(string) {\r\n      if(string== '气体结算月报')\r\n      {\r\n        return true;\r\n      }\r\n      if(string== '高炉、转炉煤气月报表')\r\n      {\r\n        return true;\r\n      }\r\n      if(string== '天然气消耗月报表')\r\n      {\r\n        return true;\r\n      }\r\n      if(string== '蒸汽消耗月报表')\r\n      {\r\n        return true;\r\n      }\r\n      if(string== '电量月报表')\r\n      {\r\n        return true;\r\n      }\r\n      if(string== '特板事业部2025年经济责任制奖罚汇总')\r\n      {\r\n        return true;\r\n      }\r\n      if(string== '研究院目标指标一览')\r\n      {\r\n        return true;\r\n      }\r\n      if(string== '安全责任工资考核表')\r\n      {\r\n        return true;\r\n      }\r\n      if(string== '安全责任工资考核汇总')\r\n      {\r\n        return true;\r\n      }\r\n      return false;\r\n    },\r\n\r\n    //数据预览模块处理\r\n    handlePreview() {\r\n      let queryImport={}\r\n      queryImport.rootId = this.queryParams.dimensionalityId\r\n      queryImport.fcDate = this.queryParams.fcDate\r\n      queryImport.type=\"1\"\r\n      if(this.dimensionalityName=='研究院目标指标一览')\r\n      {\r\n          this.downloadXlsx(\r\n          \"/web/TYjy/answer/exportWithTemplate\",\r\n          {\r\n            ...queryImport,\r\n          },\r\n          this.dimensionalityName+\"(\" +this.specialFcDate+\r\n            \")\" +\r\n            `数据.xlsx`\r\n        ).then((blob) => {\r\n          let reader = new FileReader();\r\n          reader.readAsArrayBuffer(blob);\r\n          \r\n          reader.onload = (evt) => {\r\n            this.customBlobContent=reader.result;\r\n            let ints = new Uint8Array(evt.target.result); //要使用读取的内容，所以将读取内容转化成Uint8Array\r\n            ints = ints.slice(0, blob.size);\r\n            let workBook = xlsx.read(ints, { type: \"array\" });\r\n            let sheetNames = workBook.SheetNames;\r\n            let sheetName = sheetNames[0];\r\n            let workSheet = workBook.Sheets[sheetName];\r\n            //获取Excle内容，并将空内容用空值保存\r\n            let excelTable = xlsx.utils.sheet_to_json(workSheet);\r\n            // 获取Excel头部\r\n            let tableThead = Array.from(Object.keys(excelTable[0])).map(\r\n              (item) => {\r\n                return item\r\n              }\r\n            );\r\n            this.excelData = excelTable;\r\n            this.exceltitle=tableThead\r\n            this.excelHtml= excelTable\r\n            this.searchopen = true;\r\n          }\r\n        });\r\n      }\r\n      else\r\n      {\r\n        this.downloadXlsx(\r\n        \"/web/TYjy/answer/exportTemplateSpecial\",\r\n        {\r\n          ...queryImport,\r\n        },\r\n        this.dimensionalityName+\"(\" +this.specialFcDate+\r\n          \")\" +\r\n          `数据.xlsx`\r\n      ).then((blob) => {\r\n        let reader = new FileReader();\r\n        reader.readAsArrayBuffer(blob);\r\n        reader.onload = (evt) => {\r\n          this.customBlobContent=reader.result;\r\n          let ints = new Uint8Array(evt.target.result); //要使用读取的内容，所以将读取内容转化成Uint8Array\r\n          ints = ints.slice(0, blob.size);\r\n          let workBook = xlsx.read(ints, { type: \"array\" });\r\n          let sheetNames = workBook.SheetNames;\r\n          let sheetName = sheetNames[0];\r\n          let workSheet = workBook.Sheets[sheetName];\r\n          //获取Excle内容，并将空内容用空值保存\r\n          let excelTable = xlsx.utils.sheet_to_json(workSheet);\r\n          // 获取Excel头部\r\n          let tableThead = Array.from(Object.keys(excelTable[0])).map(\r\n            (item) => {\r\n              return item\r\n            }\r\n          );\r\n          this.excelData = excelTable;\r\n          this.exceltitle=tableThead\r\n          this.excelHtml= excelTable\r\n          this.searchopen = true;\r\n        }\r\n      });\r\n      }\r\n    },\r\n    // 时间段预览\r\n    handlePreview1() {\r\n      // let queryImport={}\r\n      // queryImport.rootId = this.queryParams.dimensionalityId\r\n      // queryImport.fcDate = this.queryParams.fcDate\r\n      // queryImport.type=\"1\"\r\n        if (\r\n        this.queryImport.startDate == null ||\r\n        this.queryImport.startDate == \"\"||\r\n        this.queryImport.endDate == null||\r\n        this.queryImport.endDate == \"\"\r\n      ) {\r\n        this.$notify.error({\r\n          title: \"错误\",\r\n          message: \"导出前请先输入开始结束时间\",\r\n        });\r\n        return;\r\n      }\r\n      this.queryImport.rootId = this.queryParams.dimensionalityId\r\n      this.queryImport.type=\"1\"\r\n      this.downloadXlsx(\r\n        \"/web/TYjy/answer/exportTemplateNomral\",\r\n        {\r\n          ...this.queryImport,\r\n        },\r\n        this.dimensionalityName+\"(\" +this.specialFcDate+\r\n          \")\" +\r\n          `数据.xlsx`\r\n      ).then((blob) => {\r\n        let reader = new FileReader();\r\n        reader.readAsArrayBuffer(blob);\r\n        \r\n        reader.onload = (evt) => {\r\n          this.customBlobContent=reader.result;\r\n          let ints = new Uint8Array(evt.target.result); //要使用读取的内容，所以将读取内容转化成Uint8Array\r\n          ints = ints.slice(0, blob.size);\r\n          let workBook = xlsx.read(ints, { type: \"array\" });\r\n          let sheetNames = workBook.SheetNames;\r\n          let sheetName = sheetNames[0];\r\n          let workSheet = workBook.Sheets[sheetName];\r\n          //获取Excle内容，并将空内容用空值保存\r\n          let excelTable = xlsx.utils.sheet_to_json(workSheet);\r\n          // 获取Excel头部\r\n          let tableThead = Array.from(Object.keys(excelTable[0])).map(\r\n            (item) => {\r\n              return item\r\n            }\r\n          );\r\n          this.excelData = excelTable;\r\n          this.exceltitle=tableThead\r\n          this.excelHtml= excelTable\r\n          this.searchopen = true;\r\n        }\r\n      });\r\n    },\r\n\r\n    clickChangeTime()\r\n    {\r\n         let now =new Date();\r\n         this.query.startDate=this.getFirstOfYear(now);\r\n         this.query.endDate=this.getFirstOfMonth(now);\r\n         this.dateValue=[];\r\n         this.dateValue.push(this.query.startDate);\r\n         this.dateValue.push(this.query.endDate);\r\n    },\r\n    // 获取时间的优化处理\r\n    getFirstOfYear(now)\r\n    {\r\n      let  firstDayOfYear = new Date(now.getFullYear(), 0, 1);\r\n      return this.formatDate(firstDayOfYear);\r\n    },\r\n    getFirstOfMonth(now)\r\n    {\r\n      let firstDayOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);\r\n      return this.formatDate(firstDayOfMonth);\r\n    },\r\n    // 日期格式化函数（转为 yyyy-MM-dd）\r\n    formatDate(date) \r\n    {\r\n      const year = date.getFullYear();\r\n      const month = String(date.getMonth() + 1).padStart(2, '0'); // 月份从0开始需+1\r\n      const day = String(date.getDate()).padStart(2, '0');\r\n      return `${year}-${month}-${day}`;\r\n    },\r\n\r\n  },\r\n};\r\n</script>\r\n<style lang=\"less\">\r\n.v-modal {\r\n  display: none;\r\n}\r\n</style>\r\n  "]}]}